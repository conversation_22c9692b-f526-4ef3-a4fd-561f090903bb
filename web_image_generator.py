#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
火山引擎图像生成Web接口
基于Flask的简单Web服务
"""

import os
import asyncio
import logging
import base64
import uuid
from datetime import datetime
from pathlib import Path
from flask import Flask, request, jsonify, render_template_string, send_file
from simple_image_generator import SimpleImageGenerator

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# 创建输出目录
OUTPUT_DIR = Path("generated_images")
OUTPUT_DIR.mkdir(exist_ok=True)

# 全局生成器实例
generator = None

def init_generator():
    """初始化图像生成器"""
    global generator
    try:
        generator = SimpleImageGenerator()
        logger.info("图像生成器初始化成功")
        return True
    except Exception as e:
        logger.error(f"图像生成器初始化失败: {str(e)}")
        return False

# HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>火山引擎图像生成器</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .form-row {
            display: flex;
            gap: 15px;
        }
        .form-row .form-group {
            flex: 1;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 5px;
            display: none;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }
        .image-result {
            text-align: center;
            margin-top: 20px;
        }
        .image-result img {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
        .image-info {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
        }
        .preset-prompts {
            margin-bottom: 20px;
        }
        .preset-btn {
            display: inline-block;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 5px 10px;
            margin: 2px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            color: #495057;
        }
        .preset-btn:hover {
            background: #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 火山引擎图像生成器</h1>
        
        <form id="generateForm">
            <div class="preset-prompts">
                <label>快速提示词:</label>
                <span class="preset-btn" onclick="setPrompt('一位古代美女，穿着华丽的汉服，在花园中翩翩起舞')">古代美女</span>
                <span class="preset-btn" onclick="setPrompt('夕阳下的山峰，云雾缭绕，意境深远')">山水风景</span>
                <span class="preset-btn" onclick="setPrompt('可爱的小猫咪，毛茸茸的，大眼睛，卡通风格')">可爱小猫</span>
                <span class="preset-btn" onclick="setPrompt('科幻城市，未来建筑，霓虹灯光，赛博朋克风格')">科幻城市</span>
                <span class="preset-btn" onclick="setPrompt('水墨画风格，竹林，清雅淡然，中国传统艺术')">水墨竹林</span>
            </div>
            
            <div class="form-group">
                <label for="prompt">图像提示词 *</label>
                <textarea id="prompt" name="prompt" placeholder="请输入详细的图像描述..." required></textarea>
            </div>
            
            <div class="form-group">
                <label for="negative_prompt">负面提示词</label>
                <textarea id="negative_prompt" name="negative_prompt" placeholder="不希望出现的内容..."></textarea>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="width">宽度</label>
                    <select id="width" name="width">
                        <option value="512">512</option>
                        <option value="720" selected>720</option>
                        <option value="1024">1024</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="height">高度</label>
                    <select id="height" name="height">
                        <option value="512">512</option>
                        <option value="720">720</option>
                        <option value="1280" selected>1280</option>
                    </select>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="seed">随机种子</label>
                    <input type="number" id="seed" name="seed" value="-1" min="-1">
                </div>
                <div class="form-group">
                    <label for="scale">引导比例</label>
                    <input type="number" id="scale" name="scale" value="7.5" step="0.1" min="1" max="20">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="steps">推理步数</label>
                    <input type="number" id="steps" name="steps" value="16" min="1" max="50">
                </div>
                <div class="form-group">
                    <label for="use_sr">超分辨率</label>
                    <select id="use_sr" name="use_sr">
                        <option value="true" selected>启用</option>
                        <option value="false">禁用</option>
                    </select>
                </div>
            </div>
            
            <button type="submit" id="generateBtn">🎨 生成图像</button>
        </form>
        
        <div id="result" class="result">
            <div id="resultContent"></div>
        </div>
    </div>

    <script>
        function setPrompt(text) {
            document.getElementById('prompt').value = text;
        }
        
        document.getElementById('generateForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const btn = document.getElementById('generateBtn');
            const result = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            
            // 显示加载状态
            btn.disabled = true;
            btn.textContent = '🔄 生成中...';
            result.className = 'result';
            result.style.display = 'block';
            resultContent.innerHTML = '<div class="loading">正在生成图像，请稍候...</div>';
            
            try {
                const formData = new FormData(this);
                const response = await fetch('/generate', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    result.className = 'result success';
                    resultContent.innerHTML = `
                        <h3>✅ 生成成功!</h3>
                        <div class="image-result">
                            <img src="/image/${data.filename}" alt="Generated Image">
                            <div class="image-info">
                                文件名: ${data.filename}<br>
                                生成时间: ${data.timestamp}<br>
                                <a href="/image/${data.filename}" download>下载图像</a>
                            </div>
                        </div>
                    `;
                } else {
                    result.className = 'result error';
                    resultContent.innerHTML = `<h3>❌ 生成失败</h3><p>${data.error}</p>`;
                }
            } catch (error) {
                result.className = 'result error';
                resultContent.innerHTML = `<h3>❌ 请求失败</h3><p>${error.message}</p>`;
            } finally {
                btn.disabled = false;
                btn.textContent = '🎨 生成图像';
            }
        });
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """主页"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/generate', methods=['POST'])
def generate_image():
    """生成图像API"""
    try:
        if not generator:
            return jsonify({
                'success': False,
                'error': '图像生成器未初始化，请检查环境变量配置'
            })
        
        # 获取参数
        prompt = request.form.get('prompt', '').strip()
        if not prompt:
            return jsonify({
                'success': False,
                'error': '请输入图像提示词'
            })
        
        negative_prompt = request.form.get('negative_prompt', '')
        width = int(request.form.get('width', 720))
        height = int(request.form.get('height', 1280))
        seed = int(request.form.get('seed', -1))
        scale = float(request.form.get('scale', 7.5))
        steps = int(request.form.get('steps', 16))
        use_sr = request.form.get('use_sr', 'true').lower() == 'true'
        
        logger.info(f"生成图像请求: {prompt[:50]}...")
        
        # 生成图像
        response = generator.generate_image_sync(
            prompt=prompt,
            negative_prompt=negative_prompt,
            width=width,
            height=height,
            seed=seed,
            scale=scale,
            ddim_steps=steps,
            use_sr=use_sr
        )
        
        # 处理响应
        image_url = generator.get_image_url_from_response(response)
        
        if image_url == "base64_data_available":
            # 保存base64图像
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"generated_{timestamp}_{uuid.uuid4().hex[:8]}.jpg"
            output_path = OUTPUT_DIR / filename
            
            if generator.save_base64_image(response, str(output_path)):
                return jsonify({
                    'success': True,
                    'filename': filename,
                    'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                })
            else:
                return jsonify({
                    'success': False,
                    'error': '保存图像失败'
                })
        elif image_url:
            return jsonify({
                'success': True,
                'image_url': image_url,
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })
        else:
            return jsonify({
                'success': False,
                'error': '未能获取图像'
            })
            
    except Exception as e:
        logger.error(f"生成图像时发生错误: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/image/<filename>')
def serve_image(filename):
    """提供图像文件"""
    try:
        image_path = OUTPUT_DIR / filename
        if image_path.exists():
            return send_file(str(image_path))
        else:
            return "图像不存在", 404
    except Exception as e:
        return f"错误: {str(e)}", 500

@app.route('/health')
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'ok',
        'generator_ready': generator is not None
    })

if __name__ == '__main__':
    # 初始化生成器
    if init_generator():
        print("🚀 启动Web服务...")
        print("📝 访问 http://localhost:9999 使用图像生成器")
        app.run(host='0.0.0.0', port=9999, debug=True)
    else:
        print("❌ 初始化失败，请检查环境变量配置")
