#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
火山引擎图像生成API完整调用Demo
包含API密钥配置和完整的调用示例
"""

import os
import base64
import logging
from volcengine.visual.VisualService import VisualService

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class VolcengineImageAPIDemo:
    """火山引擎图像生成API演示类"""
    
    def __init__(self):
        """初始化API配置"""
        
        # ========== API密钥配置 ==========
        # 方式1: 直接在代码中配置（仅用于演示，生产环境请使用环境变量）
        self.access_key = "AKLTNTZhNDdhYWVkYzdlNGU3YzhjMjg0ZmRmMzZhYTlhYzc"
        self.secret_key = "TVRjNE9UZGhZV1kwWlRJek5ESmhZMkZqTXpBeU5USm1PVE5qWVRCa05tWQ=="
        
        # 方式2: 从环境变量获取（推荐）
        # self.access_key = os.environ.get("VOLCENGINE_ACCESS_KEY")
        # self.secret_key = os.environ.get("VOLCENGINE_SECRET_KEY")
        
        # 解码base64编码的密钥
        try:
            self.access_key = base64.b64decode(self.access_key).decode('utf-8')
            self.secret_key = base64.b64decode(self.secret_key).decode('utf-8')
            logger.info("使用解码后的API密钥")
        except:
            logger.info("使用原始API密钥")
        
        # 模型配置
        self.model = "high_aes_general_v20_L"  # 火山引擎图像生成模型
        
        logger.info(f"API配置完成")
        logger.info(f"Access Key: {self.access_key[:10]}...")
        logger.info(f"使用模型: {self.model}")
    
    def generate_image(self, 
                      prompt: str,
                      negative_prompt: str = "",
                      width: int = 720,
                      height: int = 1280,
                      seed: int = -1,
                      scale: float = 7.5,
                      ddim_steps: int = 16,
                      use_sr: bool = True,
                      use_pre_llm: bool = True,
                      return_url: bool = True,
                      add_logo: bool = False) -> dict:
        """
        生成图像
        
        Args:
            prompt: 图像提示词
            negative_prompt: 负面提示词
            width: 图像宽度
            height: 图像高度
            seed: 随机种子 (-1为随机)
            scale: 引导比例 (1.0-20.0)
            ddim_steps: 推理步数
            use_sr: 是否使用超分辨率
            use_pre_llm: 是否使用预处理LLM
            return_url: 是否返回URL
            add_logo: 是否添加logo
            
        Returns:
            API响应结果
        """
        try:
            # 创建VisualService实例
            visual_service = VisualService()
            visual_service.set_ak(self.access_key)
            visual_service.set_sk(self.secret_key)
            
            # 构建请求参数
            form = {
                "req_key": self.model,
                "prompt": prompt,
                "negative_prompt": negative_prompt,
                "seed": seed,
                "scale": scale,
                "ddim_steps": ddim_steps,
                "width": width,
                "height": height,
                "use_sr": use_sr,
                "use_pre_llm": use_pre_llm,
                "return_url": return_url,
                "logo_info": {"add_logo": add_logo},
            }
            
            logger.info(f"正在生成图像...")
            logger.info(f"提示词: {prompt}")
            logger.info(f"参数: {width}x{height}, seed={seed}, scale={scale}, steps={ddim_steps}")
            
            # 调用API
            response = visual_service.cv_process(form)
            
            # 检查响应
            if response.get("code") == 10000:
                logger.info("✅ 图像生成成功")
                return response
            else:
                error_msg = response.get("message", "未知错误")
                logger.error(f"❌ 图像生成失败: {error_msg}")
                return response
                
        except Exception as e:
            logger.error(f"❌ API调用异常: {str(e)}")
            return {"code": -1, "message": str(e)}
    
    def get_image_url_from_response(self, response: dict) -> str:
        """从响应中提取图像URL"""
        try:
            data = response.get("data", {})
            
            # 尝试获取URL
            if "image_url" in data:
                return data["image_url"]
            elif "image_urls" in data and data["image_urls"]:
                return data["image_urls"][0]
            elif "binary_data_base64" in data and data["binary_data_base64"]:
                return "base64_data_available"
            else:
                logger.warning("响应中未找到图像URL")
                return None
                
        except Exception as e:
            logger.error(f"解析响应时发生错误: {str(e)}")
            return None
    
    def save_base64_image(self, response: dict, output_path: str) -> bool:
        """保存base64格式的图像"""
        try:
            data = response.get("data", {})
            base64_data = data.get("binary_data_base64")
            
            if not base64_data:
                logger.error("响应中没有base64图像数据")
                return False
                
            # 解码并保存
            image_data = base64.b64decode(base64_data[0])
            with open(output_path, 'wb') as f:
                f.write(image_data)
                
            logger.info(f"图像已保存到: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存图像时发生错误: {str(e)}")
            return False


def demo_basic_usage():
    """基本使用演示"""
    print("🎨 火山引擎图像生成API - 基本使用演示")
    print("=" * 50)
    
    # 创建API实例
    api = VolcengineImageAPIDemo()
    
    # 生成图像
    prompt = "一位美丽的古代女子，穿着华丽的汉服，在樱花树下翩翩起舞，唯美画风，高清细节"
    
    response = api.generate_image(
        prompt=prompt,
        width=720,
        height=1280,
        seed=12345,  # 固定种子，可重现结果
        scale=7.5,
        ddim_steps=16
    )
    
    # 处理结果
    if response.get("code") == 10000:
        image_url = api.get_image_url_from_response(response)
        
        if image_url == "base64_data_available":
            # 保存base64图像
            if api.save_base64_image(response, "demo_output.jpg"):
                print("✅ 图像已保存为 demo_output.jpg")
        elif image_url:
            print(f"✅ 图像URL: {image_url}")
        else:
            print("❌ 未能获取图像")
    else:
        print(f"❌ 生成失败: {response.get('message')}")


def demo_parameter_variations():
    """参数变化演示"""
    print("\n🔧 火山引擎图像生成API - 参数变化演示")
    print("=" * 50)
    
    api = VolcengineImageAPIDemo()
    
    # 测试不同参数组合
    test_cases = [
        {
            "name": "高引导比例",
            "params": {
                "prompt": "可爱的小猫咪，卡通风格",
                "scale": 15.0,
                "seed": 100
            }
        },
        {
            "name": "低引导比例",
            "params": {
                "prompt": "可爱的小猫咪，卡通风格", 
                "scale": 3.0,
                "seed": 100
            }
        },
        {
            "name": "高推理步数",
            "params": {
                "prompt": "科幻城市夜景",
                "ddim_steps": 30,
                "seed": 200
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases):
        print(f"\n📝 测试 {i+1}: {test_case['name']}")
        
        response = api.generate_image(**test_case['params'])
        
        if response.get("code") == 10000:
            image_url = api.get_image_url_from_response(response)
            if image_url:
                print(f"✅ 生成成功: {image_url[:50]}...")
            else:
                print("❌ 未能获取图像URL")
        else:
            print(f"❌ 生成失败: {response.get('message')}")


def demo_error_handling():
    """错误处理演示"""
    print("\n⚠️ 火山引擎图像生成API - 错误处理演示")
    print("=" * 50)
    
    api = VolcengineImageAPIDemo()
    
    # 测试空提示词
    print("📝 测试空提示词...")
    response = api.generate_image(prompt="")
    print(f"结果: {response.get('message', '未知错误')}")
    
    # 测试无效参数
    print("\n📝 测试无效参数...")
    response = api.generate_image(
        prompt="测试图像",
        width=0,  # 无效宽度
        height=0   # 无效高度
    )
    print(f"结果: {response.get('message', '未知错误')}")


def main():
    """主函数"""
    print("🚀 火山引擎图像生成API完整演示")
    print("=" * 60)
    
    try:
        # 基本使用演示
        demo_basic_usage()
        
        # 参数变化演示
        demo_parameter_variations()
        
        # 错误处理演示
        demo_error_handling()
        
        print("\n🎉 演示完成！")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {str(e)}")


if __name__ == "__main__":
    main()
