# 火山引擎图像生成工具

基于项目中的 `generate_volcengine_image` 函数创建的简单图像生成工具，包含命令行脚本和Web界面。

## 功能特性

- 🎨 **简单易用**: 基于火山引擎的图像生成API
- 🖥️ **双重接口**: 支持命令行和Web界面
- ⚙️ **参数可调**: 支持调整图像尺寸、种子、引导比例等参数
- 💾 **自动保存**: 自动保存生成的图像到本地
- 🔄 **错误重试**: 内置重试机制，提高成功率

## 使用的大模型

**火山引擎图像生成模型**: `high_aes_general_v20_L`
- 这是火山引擎提供的高质量图像生成模型
- 支持中文提示词
- 支持多种图像尺寸和风格

## 环境配置

### 1. 安装依赖

```bash
pip install volcengine-python-sdk flask python-dotenv
```

### 2. 环境变量配置

在项目根目录的 `.env` 文件中设置以下环境变量：

```bash
# 火山引擎API密钥（必需）
VOLCENGINE_ACCESS_KEY="your_access_key"
VOLCENGINE_SECRET_KEY="your_secret_key"

# 图像生成模型（可选，默认为 high_aes_general_v20_L）
VOLCENGINE_IMAGE_GEN_MODEL="high_aes_general_v20_L"
```

**注意**: 
- 如果你的密钥是base64编码的，工具会自动解码
- 确保你的火山引擎账户有图像生成API的使用权限

## 使用方法

### 1. 命令行使用

```bash
# 基本使用
python simple_image_generator.py "一位古代美女，穿着华丽的汉服，在花园中翩翩起舞"

# 指定参数
python simple_image_generator.py "科幻城市夜景" \
    --width 1024 \
    --height 1024 \
    --seed 12345 \
    --scale 8.0 \
    --steps 20 \
    --output my_image.jpg

# 添加负面提示词
python simple_image_generator.py "美丽的风景画" \
    --negative "模糊,低质量,变形" \
    --width 720 \
    --height 1280
```

#### 命令行参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `prompt` | 图像提示词（必需） | - |
| `--negative` | 负面提示词 | "" |
| `--width` | 图像宽度 | 720 |
| `--height` | 图像高度 | 1280 |
| `--seed` | 随机种子（-1为随机） | -1 |
| `--scale` | 引导比例 | 7.5 |
| `--steps` | 推理步数 | 16 |
| `--output` | 输出文件路径 | generated_image.jpg |

### 2. Web界面使用

```bash
# 启动Web服务
python web_image_generator.py
```

然后在浏览器中访问 `http://localhost:5000`

#### Web界面功能

- 📝 **可视化表单**: 直观的参数设置界面
- 🎯 **快速提示词**: 预设的常用提示词按钮
- 🖼️ **实时预览**: 生成后立即显示图像
- 💾 **一键下载**: 支持下载生成的图像
- 📊 **参数调节**: 支持调整所有生成参数

## API接口

### 生成图像 API

**POST** `/generate`

**参数**:
- `prompt` (string, 必需): 图像提示词
- `negative_prompt` (string, 可选): 负面提示词
- `width` (int, 可选): 图像宽度，默认720
- `height` (int, 可选): 图像高度，默认1280
- `seed` (int, 可选): 随机种子，默认-1
- `scale` (float, 可选): 引导比例，默认7.5
- `steps` (int, 可选): 推理步数，默认16
- `use_sr` (bool, 可选): 是否使用超分辨率，默认true

**响应**:
```json
{
  "success": true,
  "filename": "generated_20241231_123456_abc12345.jpg",
  "timestamp": "2024-12-31 12:34:56"
}
```

### 健康检查 API

**GET** `/health`

**响应**:
```json
{
  "status": "ok",
  "generator_ready": true
}
```

## 提示词建议

### 高质量提示词示例

1. **人物肖像**:
   ```
   一位优雅的古代女子，穿着精美的汉服，长发飘逸，在竹林中静坐，水墨画风格，高清细节
   ```

2. **风景画**:
   ```
   夕阳西下的山峰，云雾缭绕，金色阳光洒在山间，意境深远，中国传统山水画风格
   ```

3. **现代艺术**:
   ```
   抽象艺术作品，色彩丰富，几何图形组合，现代艺术风格，高饱和度
   ```

4. **科幻场景**:
   ```
   未来科幻城市，高楼大厦，霓虹灯光，飞行汽车，赛博朋克风格，夜景
   ```

### 负面提示词建议

```
模糊, 低质量, 变形, 多余的手指, 错误的解剖结构, 水印, 签名, 文字
```

## 常见问题

### 1. 环境变量错误
**问题**: `请设置环境变量 VOLCENGINE_ACCESS_KEY`
**解决**: 检查 `.env` 文件中的环境变量配置是否正确

### 2. API调用失败
**问题**: `API调用失败: xxx`
**解决**: 
- 检查网络连接
- 确认API密钥有效
- 检查账户余额和权限

### 3. 图像生成失败
**问题**: 生成的图像质量不佳或不符合预期
**解决**:
- 优化提示词描述
- 调整引导比例（scale）
- 增加推理步数（steps）
- 使用负面提示词排除不需要的内容

### 4. 内存不足
**问题**: 生成高分辨率图像时内存不足
**解决**:
- 降低图像分辨率
- 关闭超分辨率功能
- 减少并发请求

## 文件结构

```
.
├── simple_image_generator.py    # 命令行图像生成脚本
├── web_image_generator.py       # Web界面服务
├── generated_images/            # 生成的图像保存目录
├── .env                        # 环境变量配置文件
└── README_IMAGE_GENERATOR.md   # 本说明文档
```

## 扩展开发

### 自定义参数

可以通过修改 `SimpleImageGenerator` 类来添加更多自定义参数：

```python
# 在 simple_image_generator.py 中修改
def generate_image_sync(self, prompt: str, **kwargs):
    # 添加新的参数处理逻辑
    custom_param = kwargs.get('custom_param', default_value)
    # ...
```

### 集成到其他项目

```python
from simple_image_generator import SimpleImageGenerator

# 创建生成器
generator = SimpleImageGenerator()

# 生成图像
response = generator.generate_image_sync(
    prompt="你的提示词",
    width=1024,
    height=1024
)

# 获取结果
image_url = generator.get_image_url_from_response(response)
```

## 许可证

本工具基于项目原有代码开发，遵循相同的许可证条款。
