#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
火山引擎图像生成API配置示例
包含所有可用的参数和配置选项
"""

# ========== API密钥配置 ==========
API_CONFIG = {
    # 从你的项目.env文件中获取的配置
    "VOLCENGINE_ACCESS_KEY": "AKLTNTZhNDdhYWVkYzdlNGU3YzhjMjg0ZmRmMzZhYTlhYzc",
    "VOLCENGINE_SECRET_KEY": "TVRjNE9UZGhZV1kwWlRJek5ESmhZMkZqTXpBeU5USm1PVE5qWVRCa05tWQ==",
    "VOLCENGINE_IMAGE_GEN_MODEL": "high_aes_general_v20_L"
}

# ========== 图像生成参数配置 ==========
IMAGE_GENERATION_PARAMS = {
    # 基础参数
    "prompt": "一位美丽的古代女子，穿着华丽的汉服",  # 图像提示词（必需）
    "negative_prompt": "模糊,低质量,变形,多余的手指",    # 负面提示词（可选）
    
    # 图像尺寸
    "width": 720,    # 图像宽度（像素）
    "height": 1280,  # 图像高度（像素）
    
    # 生成控制参数
    "seed": -1,         # 随机种子 (-1=随机, 固定数字=可重现)
    "scale": 7.5,       # 引导比例 (1.0-20.0, 控制对提示词的遵循程度)
    "ddim_steps": 16,   # 推理步数 (8-50, 影响质量和速度)
    
    # 高级参数
    "use_sr": True,      # 是否使用超分辨率 (提升图像清晰度)
    "use_pre_llm": True, # 是否使用预处理LLM (优化提示词)
    "return_url": True,  # 是否返回URL (True=URL, False=base64)
    
    # Logo设置
    "logo_info": {
        "add_logo": False  # 是否添加水印logo
    }
}

# ========== 预设参数组合 ==========
PRESET_CONFIGS = {
    # 快速生成 - 速度优先
    "fast": {
        "ddim_steps": 8,
        "use_sr": False,
        "scale": 7.5
    },
    
    # 标准生成 - 平衡质量和速度
    "standard": {
        "ddim_steps": 16,
        "use_sr": True,
        "scale": 7.5
    },
    
    # 高质量生成 - 质量优先
    "high_quality": {
        "ddim_steps": 25,
        "use_sr": True,
        "scale": 8.0
    },
    
    # 创意生成 - 更多变化
    "creative": {
        "ddim_steps": 16,
        "use_sr": True,
        "scale": 5.0
    },
    
    # 精确生成 - 严格遵循提示词
    "precise": {
        "ddim_steps": 20,
        "use_sr": True,
        "scale": 12.0
    }
}

# ========== 常用图像尺寸 ==========
IMAGE_SIZES = {
    "square_small": {"width": 512, "height": 512},      # 小正方形
    "square_large": {"width": 1024, "height": 1024},    # 大正方形
    "portrait": {"width": 720, "height": 1280},         # 竖屏 (9:16)
    "landscape": {"width": 1280, "height": 720},        # 横屏 (16:9)
    "phone_wallpaper": {"width": 720, "height": 1280},  # 手机壁纸
    "desktop_wallpaper": {"width": 1920, "height": 1080} # 桌面壁纸
}

# ========== 提示词模板 ==========
PROMPT_TEMPLATES = {
    "古风人物": "一位{性别}，穿着{朝代}服饰，{动作}，{场景}，{风格}，高清细节",
    "现代人物": "一位{性别}，{年龄}，{服装}，{动作}，{场景}，{风格}",
    "动物": "一只{动物}，{特征}，{环境}，{风格}",
    "风景": "{时间}{天气}的{地点}，{特色}，{风格}",
    "建筑": "{类型}建筑，{风格}，{环境}，{光线}，高清细节"
}

# ========== 负面提示词库 ==========
NEGATIVE_PROMPTS = {
    "通用": "模糊,低质量,变形,噪点,水印,签名,文字",
    "人物": "模糊,低质量,变形,多余的手指,错误的解剖结构,畸形,丑陋",
    "风景": "模糊,低质量,过度饱和,噪点,人工痕迹",
    "动物": "模糊,低质量,变形,不自然的姿势,错误的解剖结构"
}

# ========== 使用示例 ==========
def get_config_example():
    """获取配置示例"""
    
    # 基础配置
    base_config = IMAGE_GENERATION_PARAMS.copy()
    
    # 应用预设
    preset = PRESET_CONFIGS["standard"]
    base_config.update(preset)
    
    # 设置尺寸
    size = IMAGE_SIZES["portrait"]
    base_config.update(size)
    
    # 设置负面提示词
    base_config["negative_prompt"] = NEGATIVE_PROMPTS["通用"]
    
    return base_config

# ========== 参数说明 ==========
PARAMETER_DESCRIPTIONS = {
    "prompt": {
        "description": "图像描述提示词",
        "type": "string",
        "required": True,
        "example": "一位美丽的古代女子，穿着华丽的汉服"
    },
    "negative_prompt": {
        "description": "负面提示词，描述不希望出现的内容",
        "type": "string",
        "required": False,
        "example": "模糊,低质量,变形"
    },
    "width": {
        "description": "图像宽度（像素）",
        "type": "integer",
        "range": "64-2048",
        "default": 720
    },
    "height": {
        "description": "图像高度（像素）",
        "type": "integer", 
        "range": "64-2048",
        "default": 1280
    },
    "seed": {
        "description": "随机种子，控制生成的随机性",
        "type": "integer",
        "range": "-1 或 0-2147483647",
        "default": -1,
        "note": "-1表示随机，固定数字可重现结果"
    },
    "scale": {
        "description": "引导比例，控制对提示词的遵循程度",
        "type": "float",
        "range": "1.0-20.0",
        "default": 7.5,
        "note": "值越高越严格遵循提示词，值越低越有创意"
    },
    "ddim_steps": {
        "description": "推理步数，影响图像质量和生成时间",
        "type": "integer",
        "range": "1-50",
        "default": 16,
        "note": "步数越多质量越高但速度越慢"
    },
    "use_sr": {
        "description": "是否使用超分辨率技术",
        "type": "boolean",
        "default": True,
        "note": "启用可提升图像清晰度"
    },
    "use_pre_llm": {
        "description": "是否使用预处理LLM优化提示词",
        "type": "boolean", 
        "default": True,
        "note": "启用可自动优化提示词效果"
    }
}

if __name__ == "__main__":
    # 打印配置示例
    print("🔧 火山引擎图像生成API配置示例")
    print("=" * 50)
    
    config = get_config_example()
    for key, value in config.items():
        print(f"{key}: {value}")
    
    print("\n📋 参数说明:")
    for param, info in PARAMETER_DESCRIPTIONS.items():
        print(f"\n{param}:")
        for detail_key, detail_value in info.items():
            print(f"  {detail_key}: {detail_value}")
