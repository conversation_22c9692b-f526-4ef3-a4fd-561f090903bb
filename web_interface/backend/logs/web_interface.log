2025-08-01 10:47:53 | INFO     | app.utils.logger:setup_logger:92 | 日志系统初始化完成
2025-08-01 10:51:21 | INFO     | app.utils.logger:setup_logger:92 | 日志系统初始化完成
2025-08-01 10:53:24 | INFO     | app.services.novel_service:get_all_novels:49 | 找到 5 部小说
2025-08-01 10:53:38 | INFO     | app.services.novel_service:get_all_novels:49 | 找到 5 部小说
2025-08-01 10:53:40 | INFO     | app.services.novel_service:get_all_novels:49 | 找到 5 部小说
2025-08-01 10:53:47 | INFO     | app.services.novel_service:get_novel_chapters:99 | 小说 暴发户怎么了，有本事别求我啊 找到 69 个章节
2025-08-01 10:54:01 | ERROR    | app.services.chapter_service:get_chapter_storyboards:69 | 获取分镜数据失败 暴发户怎么了，有本事别求我啊/chapter133_云山惹祸的原因: 4 validation errors for StoryboardSegment
story_board_id
  Input should be a valid string [type=string_type, input_value=1, input_type=int]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
characters.0.from_chapter
  Input should be a valid list [type=list_type, input_value=1, input_type=int]
    For further information visit https://errors.pydantic.dev/2.5/v/list_type
characters.1.from_chapter
  Input should be a valid list [type=list_type, input_value=63, input_type=int]
    For further information visit https://errors.pydantic.dev/2.5/v/list_type
characters.2.from_chapter
  Input should be a valid list [type=list_type, input_value=11, input_type=int]
    For further information visit https://errors.pydantic.dev/2.5/v/list_type
2025-08-01 10:54:03 | INFO     | app.services.novel_service:get_all_novels:49 | 找到 5 部小说
2025-08-01 10:54:06 | INFO     | app.services.novel_service:get_novel_chapters:99 | 小说 暴发户怎么了，有本事别求我啊 找到 69 个章节
2025-08-01 10:54:09 | INFO     | app.services.novel_service:get_all_novels:49 | 找到 5 部小说
2025-08-01 10:54:13 | INFO     | app.services.novel_service:get_all_novels:49 | 找到 5 部小说
2025-08-01 10:54:15 | INFO     | app.services.novel_service:get_novel_chapters:99 | 小说 王府继兄宠我如宝，亲哥却后悔了1 找到 64 个章节
2025-08-01 10:54:22 | ERROR    | app.services.chapter_service:get_chapter_storyboards:69 | 获取分镜数据失败 王府继兄宠我如宝，亲哥却后悔了1/chapter130_闻宁宁的秘密，是什么？: 2 validation errors for StoryboardSegment
story_board_id
  Input should be a valid string [type=string_type, input_value=1, input_type=int]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
characters.3.from_chapter
  Input should be a valid list [type=list_type, input_value=28, input_type=int]
    For further information visit https://errors.pydantic.dev/2.5/v/list_type
2025-08-01 10:56:43 | INFO     | app.utils.logger:setup_logger:92 | 日志系统初始化完成
2025-08-01 10:56:59 | INFO     | app.utils.logger:setup_logger:92 | 日志系统初始化完成
2025-08-01 10:57:16 | INFO     | app.utils.logger:setup_logger:92 | 日志系统初始化完成
2025-08-01 10:59:08 | INFO     | app.utils.logger:setup_logger:92 | 日志系统初始化完成
2025-08-01 11:00:20 | INFO     | app.services.chapter_service:get_chapter_storyboards:65 | 读取分镜数据成功: 王府继兄宠我如宝，亲哥却后悔了1/chapter130_闻宁宁的秘密，是什么？, 共 23 个分镜
2025-08-01 11:01:12 | INFO     | app.services.novel_service:get_all_novels:49 | 找到 5 部小说
2025-08-01 11:01:20 | INFO     | app.services.novel_service:get_all_novels:49 | 找到 5 部小说
2025-08-01 11:01:23 | INFO     | app.services.novel_service:get_novel_chapters:99 | 小说 王府继兄宠我如宝，亲哥却后悔了1 找到 64 个章节
2025-08-01 11:01:28 | INFO     | app.services.chapter_service:get_chapter_storyboards:65 | 读取分镜数据成功: 王府继兄宠我如宝，亲哥却后悔了1/chapter133_主子是要卑职监视小姐？, 共 22 个分镜
2025-08-01 11:01:39 | INFO     | app.services.novel_service:get_novel_chapters:99 | 小说 王府继兄宠我如宝，亲哥却后悔了1 找到 64 个章节
2025-08-01 11:01:44 | INFO     | app.services.chapter_service:get_chapter_storyboards:65 | 读取分镜数据成功: 王府继兄宠我如宝，亲哥却后悔了1/chapter130_闻宁宁的秘密，是什么？, 共 23 个分镜
2025-08-01 11:01:51 | INFO     | app.services.chapter_service:get_chapter_storyboards:65 | 读取分镜数据成功: 王府继兄宠我如宝，亲哥却后悔了1/chapter130_闻宁宁的秘密，是什么？, 共 23 个分镜
2025-08-01 11:01:54 | INFO     | app.services.chapter_service:get_chapter_storyboards:65 | 读取分镜数据成功: 王府继兄宠我如宝，亲哥却后悔了1/chapter130_闻宁宁的秘密，是什么？, 共 23 个分镜
2025-08-01 11:03:41 | INFO     | app.services.chapter_service:get_chapter_storyboards:65 | 读取分镜数据成功: 王府继兄宠我如宝，亲哥却后悔了1/chapter130_闻宁宁的秘密，是什么？, 共 23 个分镜
2025-08-01 11:07:11 | INFO     | app.services.chapter_service:get_chapter_storyboards:65 | 读取分镜数据成功: 王府继兄宠我如宝，亲哥却后悔了1/chapter130_闻宁宁的秘密，是什么？, 共 23 个分镜
2025-08-01 11:07:11 | INFO     | app.services.chapter_service:get_chapter_storyboards:65 | 读取分镜数据成功: 王府继兄宠我如宝，亲哥却后悔了1/chapter130_闻宁宁的秘密，是什么？, 共 23 个分镜
2025-08-01 11:07:12 | INFO     | app.services.novel_service:get_all_novels:49 | 找到 5 部小说
2025-08-01 11:09:03 | INFO     | app.services.novel_service:get_novel_chapters:99 | 小说 王府继兄宠我如宝，亲哥却后悔了1 找到 64 个章节
2025-08-01 11:09:09 | INFO     | app.services.chapter_service:get_chapter_storyboards:65 | 读取分镜数据成功: 王府继兄宠我如宝，亲哥却后悔了1/chapter133_主子是要卑职监视小姐？, 共 22 个分镜
2025-08-01 11:10:27 | INFO     | app.services.chapter_service:get_chapter_storyboards:65 | 读取分镜数据成功: 王府继兄宠我如宝，亲哥却后悔了1/chapter133_主子是要卑职监视小姐？, 共 22 个分镜
2025-08-01 11:10:27 | INFO     | app.services.chapter_service:get_chapter_storyboards:65 | 读取分镜数据成功: 王府继兄宠我如宝，亲哥却后悔了1/chapter130_闻宁宁的秘密，是什么？, 共 23 个分镜
2025-08-01 11:19:59 | INFO     | app.services.novel_service:get_all_novels:49 | 找到 5 部小说
2025-08-01 11:20:03 | INFO     | app.services.novel_service:get_all_novels:49 | 找到 5 部小说
2025-08-01 11:20:05 | INFO     | app.services.novel_service:get_novel_chapters:99 | 小说 王府继兄宠我如宝，亲哥却后悔了1 找到 64 个章节
2025-08-01 11:20:09 | INFO     | app.services.chapter_service:get_chapter_storyboards:65 | 读取分镜数据成功: 王府继兄宠我如宝，亲哥却后悔了1/chapter130_闻宁宁的秘密，是什么？, 共 23 个分镜
