# -*- coding: utf-8 -*-
"""
小说相关数据模型
"""

from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field


class Character(BaseModel):
    """角色信息模型"""
    name: str
    gender: str
    age: str
    clothes: str
    hairstyle: str
    figure: str
    identity: str
    other: Optional[str] = None
    from_chapter: Union[List[int], int]
    action: str
    expression: str
    face: str
    personality: str


class StoryboardSegment(BaseModel):
    """分镜段落模型"""
    chapter: int
    story_board: str = Field(description="分镜描述文本")
    story_board_id: Optional[Union[str, int]] = Field(default=None, description="分镜ID")
    characters: List[Character] = Field(default_factory=list)
    image_url: Optional[str] = Field(default=None, description="图片URL")
    video_url: Optional[str] = Field(default=None, description="视频URL")
    audio_url: Optional[str] = Field(default=None, description="音频URL")
    subtitle: Optional[List[Dict[str, Any]]] = Field(default=None, description="字幕数据")
    keywords: Optional[List[str]] = Field(default=None, description="关键词")
    
    # 替换状态
    is_marked_for_replacement: bool = Field(default=False, description="是否标记为需要替换")
    replacement_image_url: Optional[str] = Field(default=None, description="替换后的图片URL")


class Storyboard(BaseModel):
    """分镜数据模型"""
    segments: List[StoryboardSegment]
    total_count: int = Field(description="总分镜数量")
    
    @classmethod
    def from_json_file(cls, json_data: List[Dict[str, Any]]) -> "Storyboard":
        """从JSON数据创建分镜对象"""
        segments = [StoryboardSegment(**segment) for segment in json_data]
        return cls(segments=segments, total_count=len(segments))


class Chapter(BaseModel):
    """章节信息模型"""
    chapter_name: str = Field(description="章节名称")
    chapter_number: int = Field(description="章节号")
    novel_name: str = Field(description="小说名称")
    storyboard_file_path: str = Field(description="分镜文件路径")
    scene_info_file_path: Optional[str] = Field(default=None, description="场景信息文件路径")
    has_storyboard: bool = Field(default=False, description="是否有分镜数据")
    has_video: bool = Field(default=False, description="是否已生成视频")
    
    # 统计信息
    total_segments: int = Field(default=0, description="总分镜数量")
    video_segments: int = Field(default=0, description="视频分镜数量")
    image_segments: int = Field(default=0, description="图片分镜数量")


class Novel(BaseModel):
    """小说信息模型"""
    novel_name: str = Field(description="小说名称")
    novel_path: str = Field(description="小说目录路径")
    chapters: List[Chapter] = Field(default_factory=list)
    total_chapters: int = Field(default=0, description="总章节数")
    
    # 处理状态统计
    processed_chapters: int = Field(default=0, description="已处理章节数")
    generated_videos: int = Field(default=0, description="已生成视频数")
