{"version": 3, "file": "types.mjs", "sources": ["../../../../packages/utils/types.ts"], "sourcesContent": ["import { isArray, isObject, isString } from '@vue/shared'\nimport { isNil } from 'lodash-unified'\n\nexport {\n  isArray,\n  isFunction,\n  isObject,\n  isString,\n  isDate,\n  isPromise,\n  isSymbol,\n  isPlainObject,\n} from '@vue/shared'\n\nexport const isUndefined = (val: any): val is undefined => val === undefined\nexport const isBoolean = (val: any): val is boolean => typeof val === 'boolean'\nexport const isNumber = (val: any): val is number => typeof val === 'number'\n\nexport const isEmpty = (val: unknown) =>\n  (!val && val !== 0) ||\n  (isArray(val) && val.length === 0) ||\n  (isObject(val) && !Object.keys(val).length)\n\nexport const isElement = (e: unknown): e is Element => {\n  if (typeof Element === 'undefined') return false\n  return e instanceof Element\n}\n\nexport const isPropAbsent = (prop: unknown): prop is null | undefined =>\n  isNil(prop)\n\nexport const isStringNumber = (val: string): boolean => {\n  if (!isString(val)) {\n    return false\n  }\n  return !Number.isNaN(Number(val))\n}\n\nexport const isWindow = (val: unknown): val is Window => val === window\n"], "names": [], "mappings": ";;;;AAYY,MAAC,WAAW,GAAG,CAAC,GAAG,KAAK,GAAG,KAAK,KAAK,EAAE;AACvC,MAAC,SAAS,GAAG,CAAC,GAAG,KAAK,OAAO,GAAG,KAAK,UAAU;AAC/C,MAAC,QAAQ,GAAG,CAAC,GAAG,KAAK,OAAO,GAAG,KAAK,SAAS;AAC7C,MAAC,OAAO,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO;AACvH,MAAC,SAAS,GAAG,CAAC,CAAC,KAAK;AAChC,EAAE,IAAI,OAAO,OAAO,KAAK,WAAW;AACpC,IAAI,OAAO,KAAK,CAAC;AACjB,EAAE,OAAO,CAAC,YAAY,OAAO,CAAC;AAC9B,EAAE;AACU,MAAC,YAAY,GAAG,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,EAAE;AACtC,MAAC,cAAc,GAAG,CAAC,GAAG,KAAK;AACvC,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AACtB,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AACpC,EAAE;AACU,MAAC,QAAQ,GAAG,CAAC,GAAG,KAAK,GAAG,KAAK;;;;"}