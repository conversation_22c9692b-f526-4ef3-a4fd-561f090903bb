{"version": 3, "file": "use-basic-date-table.mjs", "sources": ["../../../../../../../packages/components/date-picker/src/composables/use-basic-date-table.ts"], "sourcesContent": ["import { computed, nextTick, ref, unref, watch } from 'vue'\nimport dayjs from 'dayjs'\nimport { flatten } from 'lodash-unified'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { castArray, isArray } from '@element-plus/utils'\nimport { buildPickerTable } from '../utils'\n\nimport type { SetupContext } from 'vue'\nimport type { Dayjs } from 'dayjs'\nimport type { DateCell } from '../date-picker.type'\nimport type {\n  BasicDateTableEmits,\n  BasicDateTableProps,\n} from '../props/basic-date-table'\n\nconst isNormalDay = (type = '') => {\n  return ['normal', 'today'].includes(type)\n}\n\nexport const useBasicDateTable = (\n  props: BasicDateTableProps,\n  emit: SetupContext<BasicDateTableEmits>['emit']\n) => {\n  const { lang } = useLocale()\n  const tbodyRef = ref<HTMLElement>()\n  const currentCellRef = ref<HTMLElement>()\n  // data\n  const lastRow = ref<number>()\n  const lastColumn = ref<number>()\n  const tableRows = ref<DateCell[][]>([[], [], [], [], [], []])\n\n  let focusWithClick = false\n\n  // todo better way to get Day.js locale object\n  const firstDayOfWeek = (props.date as any).$locale().weekStart || 7\n  const WEEKS_CONSTANT = props.date\n    .locale('en')\n    .localeData()\n    .weekdaysShort()\n    .map((_) => _.toLowerCase())\n\n  const offsetDay = computed(() => {\n    // Sunday 7(0), cal the left and right offset days, 3217654, such as Monday is -1, the is to adjust the position of the first two rows of dates\n    return firstDayOfWeek > 3 ? 7 - firstDayOfWeek : -firstDayOfWeek\n  })\n\n  const startDate = computed(() => {\n    const startDayOfMonth = props.date.startOf('month')\n    return startDayOfMonth.subtract(startDayOfMonth.day() || 7, 'day')\n  })\n\n  const WEEKS = computed(() => {\n    return WEEKS_CONSTANT.concat(WEEKS_CONSTANT).slice(\n      firstDayOfWeek,\n      firstDayOfWeek + 7\n    )\n  })\n\n  const hasCurrent = computed<boolean>(() => {\n    return flatten(unref(rows)).some((row) => {\n      return row.isCurrent\n    })\n  })\n\n  const days = computed(() => {\n    const startOfMonth = props.date.startOf('month')\n    const startOfMonthDay = startOfMonth.day() || 7 // day of first day\n    const dateCountOfMonth = startOfMonth.daysInMonth()\n\n    const dateCountOfLastMonth = startOfMonth.subtract(1, 'month').daysInMonth()\n\n    return {\n      startOfMonthDay,\n      dateCountOfMonth,\n      dateCountOfLastMonth,\n    }\n  })\n\n  const selectedDate = computed(() => {\n    return props.selectionMode === 'dates'\n      ? (castArray(props.parsedValue) as Dayjs[])\n      : ([] as Dayjs[])\n  })\n\n  // Return value indicates should the counter be incremented\n  type CellCoordinate = { columnIndex: number; rowIndex: number }\n  type CellMeta = CellCoordinate & {\n    count: number\n  }\n  const setDateText = (\n    cell: DateCell,\n    { count, rowIndex, columnIndex }: CellMeta\n  ): boolean => {\n    const { startOfMonthDay, dateCountOfMonth, dateCountOfLastMonth } =\n      unref(days)\n    const offset = unref(offsetDay)\n    if (rowIndex >= 0 && rowIndex <= 1) {\n      const numberOfDaysFromPreviousMonth =\n        startOfMonthDay + offset < 0\n          ? 7 + startOfMonthDay + offset\n          : startOfMonthDay + offset\n\n      if (columnIndex + rowIndex * 7 >= numberOfDaysFromPreviousMonth) {\n        cell.text = count\n        return true\n      } else {\n        cell.text =\n          dateCountOfLastMonth -\n          (numberOfDaysFromPreviousMonth - (columnIndex % 7)) +\n          1 +\n          rowIndex * 7\n        cell.type = 'prev-month'\n      }\n    } else {\n      if (count <= dateCountOfMonth) {\n        cell.text = count\n      } else {\n        cell.text = count - dateCountOfMonth\n        cell.type = 'next-month'\n      }\n      return true\n    }\n    return false\n  }\n\n  const setCellMetadata = (\n    cell: DateCell,\n    { columnIndex, rowIndex }: CellCoordinate,\n    count: number\n  ) => {\n    const { disabledDate, cellClassName } = props\n    const _selectedDate = unref(selectedDate)\n    const shouldIncrement = setDateText(cell, { count, rowIndex, columnIndex })\n\n    const cellDate = cell.dayjs!.toDate()\n    cell.selected = _selectedDate.find((d) => d.isSame(cell.dayjs, 'day'))\n    cell.isSelected = !!cell.selected\n    cell.isCurrent = isCurrent(cell)\n    cell.disabled = disabledDate?.(cellDate)\n    cell.customClass = cellClassName?.(cellDate)\n    return shouldIncrement\n  }\n\n  const setRowMetadata = (row: DateCell[]) => {\n    if (props.selectionMode === 'week') {\n      const [start, end] = props.showWeekNumber ? [1, 7] : [0, 6]\n      const isActive = isWeekActive(row[start + 1])\n      row[start].inRange = isActive\n      row[start].start = isActive\n      row[end].inRange = isActive\n      row[end].end = isActive\n    }\n  }\n\n  const rows = computed(() => {\n    const { minDate, maxDate, rangeState, showWeekNumber } = props\n\n    const offset = unref(offsetDay)\n    const rows_ = unref(tableRows)\n    const dateUnit = 'day'\n    let count = 1\n\n    buildPickerTable({ row: 6, column: 7 }, rows_, {\n      startDate: minDate,\n      columnIndexOffset: showWeekNumber ? 1 : 0,\n      nextEndDate:\n        rangeState.endDate ||\n        maxDate ||\n        (rangeState.selecting && minDate) ||\n        null,\n      now: dayjs().locale(unref(lang)).startOf(dateUnit),\n      unit: dateUnit,\n      relativeDateGetter: (idx: number) =>\n        unref(startDate).add(idx - offset, dateUnit),\n      setCellMetadata: (...args) => {\n        if (setCellMetadata(...args, count)) {\n          count += 1\n        }\n      },\n\n      setRowMetadata,\n    })\n\n    if (showWeekNumber) {\n      for (let rowIndex = 0; rowIndex < 6; rowIndex++) {\n        if (rows_[rowIndex][1].dayjs) {\n          rows_[rowIndex][0] = {\n            type: 'week',\n            text: rows_[rowIndex][1].dayjs!.week(),\n          }\n        }\n      }\n    }\n\n    return rows_\n  })\n\n  watch(\n    () => props.date,\n    async () => {\n      if (unref(tbodyRef)?.contains(document.activeElement)) {\n        await nextTick()\n        await focus()\n        // currentCellRef.value?.focus()\n      }\n    }\n  )\n\n  const focus = async () => unref(currentCellRef)?.focus()\n\n  const isCurrent = (cell: DateCell): boolean => {\n    return (\n      props.selectionMode === 'date' &&\n      isNormalDay(cell.type) &&\n      cellMatchesDate(cell, props.parsedValue as Dayjs)\n    )\n  }\n\n  const cellMatchesDate = (cell: DateCell, date: Dayjs) => {\n    if (!date) return false\n    return dayjs(date)\n      .locale(unref(lang))\n      .isSame(props.date.date(Number(cell.text)), 'day')\n  }\n\n  const getDateOfCell = (row: number, column: number) => {\n    const offsetFromStart =\n      row * 7 + (column - (props.showWeekNumber ? 1 : 0)) - unref(offsetDay)\n    return unref(startDate).add(offsetFromStart, 'day')\n  }\n\n  const handleMouseMove = (event: MouseEvent) => {\n    if (!props.rangeState.selecting) return\n\n    let target = event.target as HTMLElement\n    if (target.tagName === 'SPAN') {\n      target = target.parentNode?.parentNode as HTMLElement\n    }\n    if (target.tagName === 'DIV') {\n      target = target.parentNode as HTMLElement\n    }\n    if (target.tagName !== 'TD') return\n\n    const row = (target.parentNode as HTMLTableRowElement).rowIndex - 1\n    const column = (target as HTMLTableCellElement).cellIndex\n\n    // can not select disabled date\n    if (unref(rows)[row][column].disabled) return\n\n    // only update rangeState when mouse moves to a new cell\n    // this avoids frequent Date object creation and improves performance\n    if (row !== unref(lastRow) || column !== unref(lastColumn)) {\n      lastRow.value = row\n      lastColumn.value = column\n      emit('changerange', {\n        selecting: true,\n        endDate: getDateOfCell(row, column),\n      })\n    }\n  }\n\n  const isSelectedCell = (cell: DateCell) => {\n    return (\n      (!unref(hasCurrent) && cell?.text === 1 && cell.type === 'normal') ||\n      cell.isCurrent\n    )\n  }\n\n  const handleFocus = (event: FocusEvent) => {\n    if (focusWithClick || unref(hasCurrent) || props.selectionMode !== 'date')\n      return\n    handlePickDate(event, true)\n  }\n\n  const handleMouseDown = (event: MouseEvent) => {\n    const target = (event.target as HTMLElement).closest('td')\n    if (!target) return\n    focusWithClick = true\n  }\n\n  const handleMouseUp = (event: MouseEvent) => {\n    const target = (event.target as HTMLElement).closest('td')\n    if (!target) return\n    focusWithClick = false\n  }\n\n  const handleRangePick = (newDate: Dayjs) => {\n    if (!props.rangeState.selecting || !props.minDate) {\n      emit('pick', { minDate: newDate, maxDate: null })\n      emit('select', true)\n    } else {\n      if (newDate >= props.minDate) {\n        emit('pick', { minDate: props.minDate, maxDate: newDate })\n      } else {\n        emit('pick', { minDate: newDate, maxDate: props.minDate })\n      }\n      emit('select', false)\n    }\n  }\n\n  const handleWeekPick = (newDate: Dayjs) => {\n    const weekNumber = newDate.week()\n    const value = `${newDate.year()}w${weekNumber}`\n    emit('pick', {\n      year: newDate.year(),\n      week: weekNumber,\n      value,\n      date: newDate.startOf('week'),\n    })\n  }\n\n  const handleDatesPick = (newDate: Dayjs, selected: boolean) => {\n    const newValue = selected\n      ? castArray(props.parsedValue).filter(\n          (d) => d?.valueOf() !== newDate.valueOf()\n        )\n      : castArray(props.parsedValue).concat([newDate])\n    emit('pick', newValue)\n  }\n\n  const handlePickDate = (\n    event: FocusEvent | MouseEvent,\n    isKeyboardMovement = false\n  ) => {\n    const target = (event.target as HTMLElement).closest('td')\n\n    if (!target) return\n\n    const row = (target.parentNode as HTMLTableRowElement).rowIndex - 1\n    const column = (target as HTMLTableCellElement).cellIndex\n    const cell = unref(rows)[row][column]\n\n    if (cell.disabled || cell.type === 'week') return\n\n    const newDate = getDateOfCell(row, column)\n\n    switch (props.selectionMode) {\n      case 'range': {\n        handleRangePick(newDate)\n        break\n      }\n      case 'date': {\n        emit('pick', newDate, isKeyboardMovement)\n        break\n      }\n      case 'week': {\n        handleWeekPick(newDate)\n        break\n      }\n      case 'dates': {\n        handleDatesPick(newDate, !!cell.selected)\n        break\n      }\n      default: {\n        break\n      }\n    }\n  }\n\n  const isWeekActive = (cell: DateCell) => {\n    if (props.selectionMode !== 'week') return false\n    let newDate = props.date.startOf('day')\n\n    if (cell.type === 'prev-month') {\n      newDate = newDate.subtract(1, 'month')\n    }\n\n    if (cell.type === 'next-month') {\n      newDate = newDate.add(1, 'month')\n    }\n\n    newDate = newDate.date(Number.parseInt(cell.text as any, 10))\n\n    if (props.parsedValue && !isArray(props.parsedValue)) {\n      const dayOffset = ((props.parsedValue.day() - firstDayOfWeek + 7) % 7) - 1\n      const weekDate = props.parsedValue.subtract(dayOffset, 'day')\n      return weekDate.isSame(newDate, 'day')\n    }\n    return false\n  }\n\n  return {\n    WEEKS,\n    rows,\n    tbodyRef,\n    currentCellRef,\n\n    // cellMatchesDate,\n    // getDateOfCell,\n    focus,\n    isCurrent,\n    isWeekActive,\n    isSelectedCell,\n\n    handlePickDate,\n    handleMouseUp,\n    handleMouseDown,\n    handleMouseMove,\n    handleFocus,\n  }\n}\n\nexport const useBasicDateTableDOM = (\n  props: BasicDateTableProps,\n  {\n    isCurrent,\n    isWeekActive,\n  }: Pick<ReturnType<typeof useBasicDateTable>, 'isCurrent' | 'isWeekActive'>\n) => {\n  const ns = useNamespace('date-table')\n  const { t } = useLocale()\n\n  const tableKls = computed(() => [\n    ns.b(),\n    { 'is-week-mode': props.selectionMode === 'week' },\n  ])\n\n  const tableLabel = computed(() => t('el.datepicker.dateTablePrompt'))\n\n  const getCellClasses = (cell: DateCell) => {\n    const classes: string[] = []\n    if (isNormalDay(cell.type) && !cell.disabled) {\n      classes.push('available')\n      if (cell.type === 'today') {\n        classes.push('today')\n      }\n    } else {\n      classes.push(cell.type!)\n    }\n\n    if (isCurrent(cell)) {\n      classes.push('current')\n    }\n\n    if (\n      cell.inRange &&\n      (isNormalDay(cell.type) || props.selectionMode === 'week')\n    ) {\n      classes.push('in-range')\n\n      if (cell.start) {\n        classes.push('start-date')\n      }\n\n      if (cell.end) {\n        classes.push('end-date')\n      }\n    }\n\n    if (cell.disabled) {\n      classes.push('disabled')\n    }\n\n    if (cell.selected) {\n      classes.push('selected')\n    }\n\n    if (cell.customClass) {\n      classes.push(cell.customClass)\n    }\n\n    return classes.join(' ')\n  }\n\n  const getRowKls = (cell: DateCell) => [\n    ns.e('row'),\n    { current: isWeekActive(cell) },\n  ]\n\n  return {\n    tableKls,\n    tableLabel,\n    weekHeaderClass: ns.e('week-header'),\n\n    getCellClasses,\n    getRowKls,\n    t,\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAMA,MAAM,WAAW,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACnC,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC5C,CAAC,CAAC;AACU,MAAC,iBAAiB,GAAG,CAAC,KAAK,EAAE,IAAI,KAAK;AAClD,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,SAAS,EAAE,CAAC;AAC/B,EAAE,MAAM,QAAQ,GAAG,GAAG,EAAE,CAAC;AACzB,EAAE,MAAM,cAAc,GAAG,GAAG,EAAE,CAAC;AAC/B,EAAE,MAAM,OAAO,GAAG,GAAG,EAAE,CAAC;AACxB,EAAE,MAAM,UAAU,GAAG,GAAG,EAAE,CAAC;AAC3B,EAAE,MAAM,SAAS,GAAG,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AAClD,EAAE,IAAI,cAAc,GAAG,KAAK,CAAC;AAC7B,EAAE,MAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,SAAS,IAAI,CAAC,CAAC;AAC7D,EAAE,MAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,CAAC,aAAa,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;AAC1G,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM;AACnC,IAAI,OAAO,cAAc,GAAG,CAAC,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC,cAAc,CAAC;AACrE,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM;AACnC,IAAI,MAAM,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACxD,IAAI,OAAO,eAAe,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;AACvE,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM;AAC/B,IAAI,OAAO,cAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,cAAc,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC;AAC3F,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM;AACpC,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK;AAC9C,MAAM,OAAO,GAAG,CAAC,SAAS,CAAC;AAC3B,KAAK,CAAC,CAAC;AACP,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM;AAC9B,IAAI,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAI,MAAM,eAAe,GAAG,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AACpD,IAAI,MAAM,gBAAgB,GAAG,YAAY,CAAC,WAAW,EAAE,CAAC;AACxD,IAAI,MAAM,oBAAoB,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;AACjF,IAAI,OAAO;AACX,MAAM,eAAe;AACrB,MAAM,gBAAgB;AACtB,MAAM,oBAAoB;AAC1B,KAAK,CAAC;AACN,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM;AACtC,IAAI,OAAO,KAAK,CAAC,aAAa,KAAK,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;AAC/E,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,WAAW,GAAG,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK;AAClE,IAAI,MAAM,EAAE,eAAe,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;AACpF,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;AACpC,IAAI,IAAI,QAAQ,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,EAAE;AACxC,MAAM,MAAM,6BAA6B,GAAG,eAAe,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,eAAe,GAAG,MAAM,GAAG,eAAe,GAAG,MAAM,CAAC;AACnI,MAAM,IAAI,WAAW,GAAG,QAAQ,GAAG,CAAC,IAAI,6BAA6B,EAAE;AACvE,QAAQ,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;AAC1B,QAAQ,OAAO,IAAI,CAAC;AACpB,OAAO,MAAM;AACb,QAAQ,IAAI,CAAC,IAAI,GAAG,oBAAoB,IAAI,6BAA6B,GAAG,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC;AAChH,QAAQ,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC;AACjC,OAAO;AACP,KAAK,MAAM;AACX,MAAM,IAAI,KAAK,IAAI,gBAAgB,EAAE;AACrC,QAAQ,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;AAC1B,OAAO,MAAM;AACb,QAAQ,IAAI,CAAC,IAAI,GAAG,KAAK,GAAG,gBAAgB,CAAC;AAC7C,QAAQ,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC;AACjC,OAAO;AACP,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG,CAAC;AACJ,EAAE,MAAM,eAAe,GAAG,CAAC,IAAI,EAAE,EAAE,WAAW,EAAE,QAAQ,EAAE,EAAE,KAAK,KAAK;AACtE,IAAI,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG,KAAK,CAAC;AAClD,IAAI,MAAM,aAAa,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;AAC9C,IAAI,MAAM,eAAe,GAAG,WAAW,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC,CAAC;AAChF,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;AACzC,IAAI,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;AAC3E,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;AACtC,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;AACrC,IAAI,IAAI,CAAC,QAAQ,GAAG,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;AAC3E,IAAI,IAAI,CAAC,WAAW,GAAG,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,QAAQ,CAAC,CAAC;AAChF,IAAI,OAAO,eAAe,CAAC;AAC3B,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,CAAC,GAAG,KAAK;AAClC,IAAI,IAAI,KAAK,CAAC,aAAa,KAAK,MAAM,EAAE;AACxC,MAAM,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAClE,MAAM,MAAM,QAAQ,GAAG,YAAY,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;AACpD,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,OAAO,GAAG,QAAQ,CAAC;AACpC,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,QAAQ,CAAC;AAClC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,OAAO,GAAG,QAAQ,CAAC;AAClC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,QAAQ,CAAC;AAC9B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM;AAC9B,IAAI,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG,KAAK,CAAC;AACnE,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;AACpC,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;AACnC,IAAI,MAAM,QAAQ,GAAG,KAAK,CAAC;AAC3B,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC;AAClB,IAAI,gBAAgB,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE;AACnD,MAAM,SAAS,EAAE,OAAO;AACxB,MAAM,iBAAiB,EAAE,cAAc,GAAG,CAAC,GAAG,CAAC;AAC/C,MAAM,WAAW,EAAE,UAAU,CAAC,OAAO,IAAI,OAAO,IAAI,UAAU,CAAC,SAAS,IAAI,OAAO,IAAI,IAAI;AAC3F,MAAM,GAAG,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC;AACxD,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,kBAAkB,EAAE,CAAC,GAAG,KAAK,KAAK,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,MAAM,EAAE,QAAQ,CAAC;AAC/E,MAAM,eAAe,EAAE,CAAC,GAAG,IAAI,KAAK;AACpC,QAAQ,IAAI,eAAe,CAAC,GAAG,IAAI,EAAE,KAAK,CAAC,EAAE;AAC7C,UAAU,KAAK,IAAI,CAAC,CAAC;AACrB,SAAS;AACT,OAAO;AACP,MAAM,cAAc;AACpB,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,cAAc,EAAE;AACxB,MAAM,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,EAAE,QAAQ,EAAE,EAAE;AACvD,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE;AACtC,UAAU,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG;AAC/B,YAAY,IAAI,EAAE,MAAM;AACxB,YAAY,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE;AACjD,WAAW,CAAC;AACZ,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG,CAAC,CAAC;AACL,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,YAAY;AACtC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;AACvF,MAAM,MAAM,QAAQ,EAAE,CAAC;AACvB,MAAM,MAAM,KAAK,EAAE,CAAC;AACpB,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,KAAK,GAAG,YAAY;AAC5B,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,CAAC,EAAE,GAAG,KAAK,CAAC,cAAc,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;AACtE,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,CAAC,IAAI,KAAK;AAC9B,IAAI,OAAO,KAAK,CAAC,aAAa,KAAK,MAAM,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,eAAe,CAAC,IAAI,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC;AAChH,GAAG,CAAC;AACJ,EAAE,MAAM,eAAe,GAAG,CAAC,IAAI,EAAE,IAAI,KAAK;AAC1C,IAAI,IAAI,CAAC,IAAI;AACb,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AAC7F,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,CAAC,GAAG,EAAE,MAAM,KAAK;AACzC,IAAI,MAAM,eAAe,GAAG,GAAG,GAAG,CAAC,IAAI,MAAM,IAAI,KAAK,CAAC,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;AACnG,IAAI,OAAO,KAAK,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;AACxD,GAAG,CAAC;AACJ,EAAE,MAAM,eAAe,GAAG,CAAC,KAAK,KAAK;AACrC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS;AACnC,MAAM,OAAO;AACb,IAAI,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;AAC9B,IAAI,IAAI,MAAM,CAAC,OAAO,KAAK,MAAM,EAAE;AACnC,MAAM,MAAM,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC;AACzE,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,OAAO,KAAK,KAAK,EAAE;AAClC,MAAM,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC;AACjC,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,OAAO,KAAK,IAAI;AAC/B,MAAM,OAAO;AACb,IAAI,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,GAAG,CAAC,CAAC;AAC/C,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC;AACpC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ;AACzC,MAAM,OAAO;AACb,IAAI,IAAI,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC,IAAI,MAAM,KAAK,KAAK,CAAC,UAAU,CAAC,EAAE;AAChE,MAAM,OAAO,CAAC,KAAK,GAAG,GAAG,CAAC;AAC1B,MAAM,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC;AAChC,MAAM,IAAI,CAAC,aAAa,EAAE;AAC1B,QAAQ,SAAS,EAAE,IAAI;AACvB,QAAQ,OAAO,EAAE,aAAa,CAAC,GAAG,EAAE,MAAM,CAAC;AAC3C,OAAO,CAAC,CAAC;AACT,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,CAAC,IAAI,KAAK;AACnC,IAAI,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC;AACvH,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,CAAC,KAAK,KAAK;AACjC,IAAI,IAAI,cAAc,IAAI,KAAK,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,aAAa,KAAK,MAAM;AAC7E,MAAM,OAAO;AACb,IAAI,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAChC,GAAG,CAAC;AACJ,EAAE,MAAM,eAAe,GAAG,CAAC,KAAK,KAAK;AACrC,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC9C,IAAI,IAAI,CAAC,MAAM;AACf,MAAM,OAAO;AACb,IAAI,cAAc,GAAG,IAAI,CAAC;AAC1B,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,CAAC,KAAK,KAAK;AACnC,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC9C,IAAI,IAAI,CAAC,MAAM;AACf,MAAM,OAAO;AACb,IAAI,cAAc,GAAG,KAAK,CAAC;AAC3B,GAAG,CAAC;AACJ,EAAE,MAAM,eAAe,GAAG,CAAC,OAAO,KAAK;AACvC,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;AACvD,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;AACxD,MAAM,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AAC3B,KAAK,MAAM;AACX,MAAM,IAAI,OAAO,IAAI,KAAK,CAAC,OAAO,EAAE;AACpC,QAAQ,IAAI,CAAC,MAAM,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;AACnE,OAAO,MAAM;AACb,QAAQ,IAAI,CAAC,MAAM,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;AACnE,OAAO;AACP,MAAM,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AAC5B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,CAAC,OAAO,KAAK;AACtC,IAAI,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;AACtC,IAAI,MAAM,KAAK,GAAG,CAAC,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC;AACpD,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE;AAC1B,MAAM,IAAI,EAAE,UAAU;AACtB,MAAM,KAAK;AACX,MAAM,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;AACnC,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,MAAM,eAAe,GAAG,CAAC,OAAO,EAAE,QAAQ,KAAK;AACjD,IAAI,MAAM,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;AACtL,IAAI,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC3B,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,CAAC,KAAK,EAAE,kBAAkB,GAAG,KAAK,KAAK;AAChE,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC9C,IAAI,IAAI,CAAC,MAAM;AACf,MAAM,OAAO;AACb,IAAI,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,GAAG,CAAC,CAAC;AAC/C,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC;AACpC,IAAI,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;AAC1C,IAAI,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM;AAC7C,MAAM,OAAO;AACb,IAAI,MAAM,OAAO,GAAG,aAAa,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAC/C,IAAI,QAAQ,KAAK,CAAC,aAAa;AAC/B,MAAM,KAAK,OAAO,EAAE;AACpB,QAAQ,eAAe,CAAC,OAAO,CAAC,CAAC;AACjC,QAAQ,MAAM;AACd,OAAO;AACP,MAAM,KAAK,MAAM,EAAE;AACnB,QAAQ,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,kBAAkB,CAAC,CAAC;AAClD,QAAQ,MAAM;AACd,OAAO;AACP,MAAM,KAAK,MAAM,EAAE;AACnB,QAAQ,cAAc,CAAC,OAAO,CAAC,CAAC;AAChC,QAAQ,MAAM;AACd,OAAO;AACP,MAAM,KAAK,OAAO,EAAE;AACpB,QAAQ,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAClD,QAAQ,MAAM;AACd,OAAO;AAIP,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,CAAC,IAAI,KAAK;AACjC,IAAI,IAAI,KAAK,CAAC,aAAa,KAAK,MAAM;AACtC,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,IAAI,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC5C,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,EAAE;AACpC,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;AAC7C,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,EAAE;AACpC,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;AACxC,KAAK;AACL,IAAI,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;AAC3D,IAAI,IAAI,KAAK,CAAC,WAAW,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE;AAC1D,MAAM,MAAM,SAAS,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,cAAc,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC/E,MAAM,MAAM,QAAQ,GAAG,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AACpE,MAAM,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAC7C,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,KAAK;AACT,IAAI,IAAI;AACR,IAAI,QAAQ;AACZ,IAAI,cAAc;AAClB,IAAI,KAAK;AACT,IAAI,SAAS;AACb,IAAI,YAAY;AAChB,IAAI,cAAc;AAClB,IAAI,cAAc;AAClB,IAAI,aAAa;AACjB,IAAI,eAAe;AACnB,IAAI,eAAe;AACnB,IAAI,WAAW;AACf,GAAG,CAAC;AACJ,EAAE;AACU,MAAC,oBAAoB,GAAG,CAAC,KAAK,EAAE;AAC5C,EAAE,SAAS;AACX,EAAE,YAAY;AACd,CAAC,KAAK;AACN,EAAE,MAAM,EAAE,GAAG,YAAY,CAAC,YAAY,CAAC,CAAC;AACxC,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC;AAC5B,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM;AAClC,IAAI,EAAE,CAAC,CAAC,EAAE;AACV,IAAI,EAAE,cAAc,EAAE,KAAK,CAAC,aAAa,KAAK,MAAM,EAAE;AACtD,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,+BAA+B,CAAC,CAAC,CAAC;AACxE,EAAE,MAAM,cAAc,GAAG,CAAC,IAAI,KAAK;AACnC,IAAI,MAAM,OAAO,GAAG,EAAE,CAAC;AACvB,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClD,MAAM,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAChC,MAAM,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE;AACjC,QAAQ,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC9B,OAAO;AACP,KAAK,MAAM;AACX,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC9B,KAAK;AACL,IAAI,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE;AACzB,MAAM,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC9B,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,aAAa,KAAK,MAAM,CAAC,EAAE;AACpF,MAAM,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC/B,MAAM,IAAI,IAAI,CAAC,KAAK,EAAE;AACtB,QAAQ,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACnC,OAAO;AACP,MAAM,IAAI,IAAI,CAAC,GAAG,EAAE;AACpB,QAAQ,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AACjC,OAAO;AACP,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;AACvB,MAAM,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC/B,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;AACvB,MAAM,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC/B,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE;AAC1B,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AACrC,KAAK;AACL,IAAI,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7B,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,CAAC,IAAI,KAAK;AAC9B,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;AACf,IAAI,EAAE,OAAO,EAAE,YAAY,CAAC,IAAI,CAAC,EAAE;AACnC,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,QAAQ;AACZ,IAAI,UAAU;AACd,IAAI,eAAe,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC;AACxC,IAAI,cAAc;AAClB,IAAI,SAAS;AACb,IAAI,CAAC;AACL,GAAG,CAAC;AACJ;;;;"}