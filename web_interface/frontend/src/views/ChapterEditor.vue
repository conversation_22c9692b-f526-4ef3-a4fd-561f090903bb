<template>
  <div class="chapter-editor-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button :icon="ArrowLeft" @click="goBack">返回</el-button>
        <div class="header-info">
          <h1 class="page-title">{{ getChapterDisplayName(chapterName) }}</h1>
          <p class="page-subtitle">{{ novelName }}</p>
        </div>
      </div>
      <div class="page-actions">
        <el-button :icon="Refresh" @click="refreshData">刷新</el-button>
        <el-button
          type="primary"
          :icon="VideoCamera"
          :disabled="!hasMarkedSegments"
          @click="generateVideo"
        >
          生成视频
        </el-button>
      </div>
    </div>

    <!-- 章节统计 -->
    <div class="chapter-stats">
      <el-row :gutter="16">
        <el-col :span="6">
          <div class="stat-item">
            <span class="stat-number">{{ novelStore.totalSegments }}</span>
            <span class="stat-label">总分镜数</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <span class="stat-number">{{ novelStore.videoSegments }}</span>
            <span class="stat-label">视频分镜</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <span class="stat-number">{{ novelStore.imageSegments }}</span>
            <span class="stat-label">图片分镜</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <span class="stat-number">{{ markedCount }}</span>
            <span class="stat-label">待替换</span>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button @click="selectAll">全选</el-button>
        <el-button @click="selectNone">取消全选</el-button>
        <el-button @click="selectInverse">反选</el-button>
      </div>
      <div class="toolbar-right">
        <span class="selection-info">
          已选择 {{ markedCount }} 个分镜
        </span>
      </div>
    </div>

    <!-- 分镜网格 -->
    <div class="storyboard-grid">
      <div
        v-for="(segment, index) in novelStore.storyboards"
        :key="segment.story_board_id || index"
        class="storyboard-card"
        :class="{ marked: segment.is_marked_for_replacement }"
      >
        <div class="card-image">
          <img
            v-if="segment.replacement_image_url || segment.image_url"
            :src="segment.replacement_image_url || segment.image_url"
            :alt="`分镜 ${index + 1}`"
            @error="handleImageError($event, index)"
            style="max-width: 100%; height: 200px; object-fit: cover;"
          />
          <div v-else class="no-image">
            <p>暂无图片</p>
          </div>
          <div class="image-overlay">
            <div class="overlay-actions">
              <el-button
                v-if="segment.replacement_image_url || segment.image_url"
                @click="previewImage(segment.replacement_image_url || segment.image_url)"
              >
                预览
              </el-button>
              <el-button @click="triggerUpload(index)">
                上传替换
              </el-button>
            </div>
          </div>
        </div>

        <div class="card-content">
          <div class="segment-id">分镜 {{ index + 1 }}</div>
          <div class="segment-description">{{ segment.story_board }}</div>

          <!-- 显示图片URL -->
          <div v-if="segment.image_url" class="image-url">
            <strong>图片URL:</strong> {{ segment.image_url }}
          </div>

          <!-- 显示图片提示词 -->
          <div v-if="segment.image_prompt" class="image-prompt">
            <strong>图片描述:</strong>
            <div>{{ segment.image_prompt }}</div>
          </div>

          <div class="segment-actions">
            <el-checkbox
              v-model="segment.is_marked_for_replacement"
              @change="handleMarkForReplacement(index, $event)"
            >
              标记为不合规
            </el-checkbox>
          </div>
        </div>

        <!-- 隐藏的文件上传输入 -->
        <input
          :ref="`fileInput${index}`"
          type="file"
          accept="image/*"
          style="display: none"
          @change="handleFileSelect($event, index)"
        />
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="novelStore.storyboards.length === 0 && !novelStore.loading" class="empty-state">
      <el-icon size="64" color="#c0c4cc">
        <PictureFilled />
      </el-icon>
      <h3>没有分镜数据</h3>
      <p>该章节暂无分镜数据，请检查数据是否正确</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowLeft, Refresh, VideoCamera, PictureFilled, ZoomIn, Upload } from '@element-plus/icons-vue'
import { useNovelStore } from '@/stores/novel'
import { useVideoStore } from '@/stores/video'
import { useGlobalStore } from '@/stores/global'
import { imageApi } from '@/api/image'

const router = useRouter()
const route = useRoute()
const novelStore = useNovelStore()
const videoStore = useVideoStore()
const globalStore = useGlobalStore()

// 路由参数
const novelName = computed(() => route.params.novelName)
const chapterName = computed(() => route.params.chapterName)

// 计算属性
const markedCount = computed(() => {
  return novelStore.storyboards.filter(segment => segment.is_marked_for_replacement).length
})

const hasMarkedSegments = computed(() => {
  return markedCount.value > 0
})

// 页面挂载时加载数据
onMounted(async () => {
  await loadChapterData()
})

// 加载章节数据
const loadChapterData = async () => {
  try {
    await novelStore.fetchChapterStoryboards(novelName.value, chapterName.value)
  } catch (error) {
    globalStore.showError('加载章节数据失败')
  }
}

// 刷新数据
const refreshData = async () => {
  await loadChapterData()
  globalStore.showSuccess('数据刷新成功')
}

// 返回上一页
const goBack = () => {
  router.push(`/novels/${encodeURIComponent(novelName.value)}`)
}

// 获取章节显示名称
const getChapterDisplayName = (name) => {
  return name.replace(/^chapter\d+_/, '')
}

// 全选
const selectAll = () => {
  novelStore.storyboards.forEach((segment, index) => {
    novelStore.markSegmentForReplacement(index, true)
  })
  globalStore.showInfo(`已选择 ${novelStore.storyboards.length} 个分镜`)
}

// 取消全选
const selectNone = () => {
  novelStore.resetReplacements()
  globalStore.showInfo('已取消所有选择')
}

// 反选
const selectInverse = () => {
  novelStore.storyboards.forEach((segment, index) => {
    novelStore.markSegmentForReplacement(index, !segment.is_marked_for_replacement)
  })
  globalStore.showInfo(`已反选，当前选择 ${markedCount.value} 个分镜`)
}

// 处理标记替换
const handleMarkForReplacement = (index, marked) => {
  novelStore.markSegmentForReplacement(index, marked)
}

// 触发文件上传
const triggerUpload = (index) => {
  const fileInput = document.querySelector(`input[ref="fileInput${index}"]`)
  if (fileInput) {
    fileInput.click()
  }
}

// 处理文件选择
const handleFileSelect = async (event, index) => {
  const file = event.target.files[0]
  if (!file) return

  try {
    globalStore.setLoading(true, '上传图片中...')

    // 上传图片到TOS
    const segment = novelStore.storyboards[index]
    const segmentId = segment.story_board_id || `segment_${index}`

    const response = await imageApi.uploadImage(
      file,
      novelName.value,
      chapterName.value,
      segmentId
    )

    if (response.data.success) {
      // 更新分镜的替换图片URL
      novelStore.updateSegmentImage(index, response.data.image_url)
      globalStore.showSuccess('图片上传成功')
    } else {
      throw new Error(response.data.error_message || '上传失败')
    }
  } catch (error) {
    globalStore.showError(`上传图片失败: ${error.message}`)
  } finally {
    globalStore.setLoading(false)
    // 清空文件输入
    event.target.value = ''
  }
}

// 预览图片
const previewImage = (imageUrl) => {
  if (!imageUrl) return

  // 使用Element Plus的图片预览
  ElImageViewer({
    urlList: [imageUrl],
    initialIndex: 0
  })
}

// 处理图片加载错误
const handleImageError = (event, index) => {
  console.log(`分镜 ${index + 1} 图片加载失败:`, event.target.src)
  event.target.style.display = 'none'
  // 在图片容器中显示错误信息
  const container = event.target.parentElement
  if (container && !container.querySelector('.image-error')) {
    const errorDiv = document.createElement('div')
    errorDiv.className = 'image-error'
    errorDiv.innerHTML = `<p>图片加载失败</p><small>${event.target.src}</small>`
    errorDiv.style.cssText = 'text-align: center; padding: 20px; color: #999; background: #f5f5f5;'
    container.appendChild(errorDiv)
  }
}

// 生成视频
const generateVideo = async () => {
  try {
    // 首先保存当前的替换状态
    await novelStore.updateStoryboards(novelName.value, chapterName.value, novelStore.storyboards)
    
    // 创建视频生成任务
    const request = {
      novel_name: novelName.value,
      chapter_name: chapterName.value,
      skip_violation_check: true,
      skip_mosaic: true
    }
    
    await videoStore.createVideoTask(request)
    globalStore.showSuccess(`开始生成 ${getChapterDisplayName(chapterName.value)} 的视频`)
    
    // 跳转到视频任务页面
    router.push('/videos')
  } catch (error) {
    globalStore.showError('创建视频生成任务失败')
  }
}
</script>

<style lang="scss" scoped>
.chapter-editor-page {
  max-width: 1400px;
  margin: 0 auto;
  padding: $spacing-lg;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $spacing-lg;
  
  .header-left {
    display: flex;
    align-items: center;
    gap: $spacing-md;
    
    .header-info {
      .page-title {
        font-size: $font-size-xxl;
        font-weight: $font-weight-medium;
        color: var(--el-text-color-primary);
        margin: 0 0 4px 0;
      }
      
      .page-subtitle {
        font-size: $font-size-sm;
        color: var(--el-text-color-secondary);
        margin: 0;
      }
    }
  }
  
  .page-actions {
    display: flex;
    gap: $spacing-sm;
  }
}

.chapter-stats {
  margin-bottom: $spacing-lg;
  
  .stat-item {
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color-lighter);
    border-radius: $border-radius-base;
    padding: $spacing-md;
    text-align: center;
    
    .stat-number {
      display: block;
      font-size: $font-size-xl;
      font-weight: $font-weight-bold;
      color: var(--el-color-primary);
      line-height: 1;
    }
    
    .stat-label {
      font-size: $font-size-xs;
      color: var(--el-text-color-secondary);
      margin-top: $spacing-xs;
    }
  }
}

.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-md;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: $border-radius-base;
  margin-bottom: $spacing-lg;
  
  .toolbar-left {
    display: flex;
    gap: $spacing-sm;
  }
  
  .toolbar-right {
    .selection-info {
      font-size: $font-size-sm;
      color: var(--el-text-color-secondary);
    }
  }
}

.storyboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: $spacing-lg;
  margin-bottom: $spacing-lg;
}

.empty-state {
  text-align: center;
  padding: $spacing-xxl;
  color: var(--el-text-color-secondary);
  
  h3 {
    margin: $spacing-md 0 $spacing-sm;
    color: var(--el-text-color-regular);
  }
  
  p {
    margin: 0;
    font-size: $font-size-sm;
  }
}

// 响应式设计
@media (max-width: $breakpoint-md) {
  .chapter-editor-page {
    padding: $spacing-md;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-sm;
  }
  
  .storyboard-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: $spacing-md;
  }
  
  .toolbar {
    flex-direction: column;
    gap: $spacing-sm;
    
    .toolbar-left,
    .toolbar-right {
      width: 100%;
      justify-content: center;
    }
  }
}

@media (max-width: $breakpoint-sm) {
  .header-left {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-sm;
  }
  
  .storyboard-grid {
    grid-template-columns: 1fr;
  }
  
  .chapter-stats {
    .el-col {
      margin-bottom: $spacing-sm;
    }
  }
}
</style>
