#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
火山引擎图像生成API - 简化版Demo
最简单的调用示例
"""

import base64
from volcengine.visual.VisualService import VisualService

def generate_image_simple():
    """最简单的图像生成示例"""
    
    # ========== 配置API密钥 ==========
    # 从你的项目中获取的密钥
    access_key = "AKLTNTZhNDdhYWVkYzdlNGU3YzhjMjg0ZmRmMzZhYTlhYzc"
    secret_key = "TVRjNE9UZGhZV1kwWlRJek5ESmhZMkZqTXpBeU5USm1PVE5qWVRCa05tWQ=="
    
    # 解码base64密钥
    try:
        access_key = base64.b64decode(access_key).decode('utf-8')
        secret_key = base64.b64decode(secret_key).decode('utf-8')
    except:
        pass  # 如果不是base64编码，直接使用原值
    
    # ========== 创建服务实例 ==========
    visual_service = VisualService()
    visual_service.set_ak(access_key)
    visual_service.set_sk(secret_key)
    
    # ========== 设置生成参数 ==========
    form = {
        "req_key": "high_aes_general_v20_L",  # 模型名称
        "prompt": "一只可爱的小猫咪，毛茸茸的，大眼睛，卡通风格",  # 提示词
        "negative_prompt": "模糊,低质量,变形",  # 负面提示词
        "seed": -1,          # 随机种子 (-1=随机)
        "scale": 7.5,        # 引导比例
        "ddim_steps": 16,    # 推理步数
        "width": 720,        # 图像宽度
        "height": 1280,      # 图像高度
        "use_sr": True,      # 使用超分辨率
        "use_pre_llm": True, # 使用预处理LLM
        "return_url": True,  # 返回URL
        "logo_info": {"add_logo": False},  # 不添加logo
    }
    
    print("🎨 正在生成图像...")
    print(f"提示词: {form['prompt']}")
    
    # ========== 调用API ==========
    try:
        response = visual_service.cv_process(form)
        
        # ========== 处理响应 ==========
        if response.get("code") == 10000:
            print("✅ 生成成功!")
            
            # 获取图像URL
            data = response.get("data", {})
            if "image_url" in data:
                image_url = data["image_url"]
                print(f"🔗 图像URL: {image_url}")
            elif "image_urls" in data and data["image_urls"]:
                image_url = data["image_urls"][0]
                print(f"🔗 图像URL: {image_url}")
            elif "binary_data_base64" in data:
                print("📷 图像以base64格式返回")
                # 可以保存为本地文件
                base64_data = data["binary_data_base64"][0]
                image_data = base64.b64decode(base64_data)
                with open("generated_image.jpg", "wb") as f:
                    f.write(image_data)
                print("💾 图像已保存为 generated_image.jpg")
            else:
                print("❌ 未找到图像数据")
                
        else:
            error_msg = response.get("message", "未知错误")
            print(f"❌ 生成失败: {error_msg}")
            
    except Exception as e:
        print(f"❌ API调用失败: {str(e)}")

if __name__ == "__main__":
    generate_image_simple()
