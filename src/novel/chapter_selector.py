#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小说章节处理器 - 面向对象版本
自动处理所有章节，生成语义字幕和音频
"""
# 添加项目根目录到Python路径
import sys
import uuid
from pathlib import Path

project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import asyncio
import copy
import json
import os
import re
import time
import logging
from pathlib import Path
import chardet
import edge_tts
import dashscope
from dashscope import Generation
from soupsieve import select
from pathlib import Path
from dotenv import load_dotenv
import subprocess
import shutil
import librosa
import numpy as np

# 加载.env文件中的环境变量
dotenv_path = Path(__file__).parent.parent.parent / '.env'
load_dotenv(dotenv_path=dotenv_path,verbose=True,override=True)

# 添加项目根目录到Python路径
import sys
from pathlib import Path

project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入火山引擎字幕服务和TOS处理器
from src.subtitle.volcengine_subtitle_service import create_subtitle_service
from src.story_board.tos_processor import TOSProcessor

# 导入字幕错别字处理工具
from src.tools.correct_words import (
    clean_text_and_map_indices,
    correct_subtitles_by_comparing_combined_text
)

"""设置日志记录器"""
logger = logging.getLogger('NovelProcessor')
logger.setLevel(logging.INFO)
# 清除现有的处理器
logger.handlers.clear()
# 创建控制台处理器
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)
# 创建格式器
formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
console_handler.setFormatter(formatter)

logger.addHandler(console_handler)
DASHSCOPE_API_KEY = os.environ.get("DASHSCOPE_API_KEY")


class NovelProcessor:
    """小说处理器主类"""

    def __init__(self, output_base_dir: str = "output/novel_step1", api_key=None, use_volcengine_subtitle: bool = True,
                 num_segments: int = 23, first_two_segments_min_chars: int = 25, first_two_segments_max_chars: int = 50,
                 middle_segments_min_chars: int = 60, middle_segments_max_chars: int = 73,
                 speed_factor: float = 1.3, enable_subtitle_correction: bool = True,
                 subtitle_retry_config: dict = None):
        self.api_key = api_key if api_key else os.getenv("DASHSCOPE_API_KEY")
        self.num_segments = num_segments
        self.first_two_segments_min_chars = first_two_segments_min_chars
        self.first_two_segments_max_chars = first_two_segments_max_chars
        self.middle_segments_min_chars = middle_segments_min_chars
        self.middle_segments_max_chars = middle_segments_max_chars
        self.target_word_count = 1500
        self.output_base_dir = Path(output_base_dir)
        self.novel_output_dir = None
        self.speed_factor = speed_factor
        dashscope.api_key = self.api_key

        # 字幕错别字修正配置
        self.enable_subtitle_correction = enable_subtitle_correction
        self.correction_stats = {
            "total_chapters": 0,
            "corrected_chapters": 0,
            "total_corrections": 0,
            "chapter_details": []
        }

        # 🆕 重写配置
        self.rewrite_tolerance = 5  # 字数差距容忍度
        self.rewrite_model = 'qwen-plus'  # 重写使用的模型

        # 🆕 错误记录配置
        self.error_records = {
            "subtitle_service_errors": [],
            "tos_processor_errors": [],
            "audio_upload_errors": [],
            "subtitle_generation_errors": [],
            "general_errors": []
        }

        # 🆕 错误报告记录
        self.error_report = {
            "subtitle_service_errors": [],
            "tos_processor_errors": [],
            "audio_upload_errors": [],
            "subtitle_generation_errors": [],
            "general_errors": []
        }

        # 🆕 字幕服务重试配置
        self.subtitle_retry_config = subtitle_retry_config or {
            "max_retries": 3,
            "base_timeout": 15,
            "timeout_increment": 5,
            "enable_retry": True
        }

        # 初始化火山引擎字幕服务
        self.use_volcengine_subtitle = use_volcengine_subtitle
        try:
            # 现在可以使用火山引擎字幕服务了
            self.subtitle_service = create_subtitle_service(use_mock=not use_volcengine_subtitle)
            service_type = "火山引擎" if use_volcengine_subtitle else "模拟"
            logger.info(f"字幕服务初始化成功，使用{service_type}服务")

            # 🆕 显示重试配置
            if use_volcengine_subtitle and self.subtitle_retry_config.get("enable_retry", True):
                logger.info(f"🔄 字幕服务重试配置: 最大重试{self.subtitle_retry_config['max_retries']}次, "
                           f"基础超时{self.subtitle_retry_config['base_timeout']}秒")
        except Exception as e:
            error_msg = f"字幕服务初始化失败: {e}，将使用模拟服务"
            logger.warning(error_msg)
            self.subtitle_service = create_subtitle_service(use_mock=True)
            self.use_volcengine_subtitle = False
            # 🆕 记录错误
            self._record_error("subtitle_service_errors", {
                "error_type": "初始化失败",
                "error_message": str(e),
                "fallback_action": "使用模拟服务",
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            })

        # 初始化TOS处理器（用于上传音频文件）
        try:
            self.tos_processor = TOSProcessor()
            logger.info("TOS处理器初始化成功")
        except Exception as e:
            error_msg = f"TOS处理器初始化失败: {e}"
            logger.warning(error_msg)
            self.tos_processor = None
            # 🆕 记录错误
            self._record_error("tos_processor_errors", {
                "error_type": "初始化失败",
                "error_message": str(e),
                "fallback_action": "设置为None，无法上传音频",
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            })

    def _record_error(self, error_type, error_data):
        """记录错误信息到内存中"""
        if error_type in self.error_records:
            self.error_records[error_type].append(error_data)
        else:
            self.error_records["general_errors"].append({
                "category": error_type,
                **error_data
            })

    def save_chapter_report(self, chapter_info, output_dir):
        """保存章节处理报告到JSON文件"""
        try:
            report_data = {
                "chapter_info": {
                    "chapter_index": chapter_info.get('index'),
                    "chapter_title": chapter_info.get('full_title'),
                    "processing_time": time.strftime("%Y-%m-%d %H:%M:%S")
                },
                "error_summary": {
                    "subtitle_service_errors": len(self.error_records["subtitle_service_errors"]),
                    "tos_processor_errors": len(self.error_records["tos_processor_errors"]),
                    "audio_upload_errors": len(self.error_records["audio_upload_errors"]),
                    "subtitle_generation_errors": len(self.error_records["subtitle_generation_errors"]),
                    "general_errors": len(self.error_records["general_errors"])
                },
                "detailed_errors": self.error_records,
                "correction_stats": self.correction_stats
            }

            report_file = output_dir / "chapter_report.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)

            logger.info(f"📊 章节处理报告已保存: {report_file}")

        except Exception as e:
            logger.error(f"保存章节报告失败: {e}")

    def detect_encoding(self, file_path):
        """检测文件编码"""
        logger.info(f"开始检测文件编码: {file_path}")

        with open(file_path, 'rb') as f:
            raw_data = f.read()

        result = chardet.detect(raw_data)
        encoding = result['encoding']
        logger.debug(f"chardet检测到的编码: {encoding}")

        encodings_to_try = [encoding, 'utf-8', 'gbk', 'gb2312', 'big5']

        for enc in encodings_to_try:
            if enc:
                try:
                    with open(file_path, 'r', encoding=enc) as f:
                        content = f.read()
                    logger.info(f"成功使用编码 {enc} 读取文件，文件大小: {len(content)} 字符")
                    return content, enc
                except (UnicodeDecodeError, UnicodeError):
                    logger.debug(f"编码 {enc} 读取失败")
                    continue

        logger.error("无法确定文件编码")
        raise ValueError("无法确定文件编码")

    def find_all_chapters(self, content):
        """查找所有章节"""
        logger.info("开始查找章节...")

        chapter_patterns = [
            r'第([一二三四五六七八九十\d]+)章[：:\s]*([^\n]*)',
            r'第([一二三四五六七八九十\d]+)回[：:\s]*([^\n]*)',
        ]

        lines = content.split('\n')
        chapters = []

        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue

            # 检查是否是章节标题
            for pattern in chapter_patterns:
                match = re.match(pattern, line)
                if match:
                    chapter_num = match.group(1)
                    chapter_title = match.group(2) if match.group(2) else ""
                    chapters.append({
                        'index': len(chapters) + 1,
                        'line_number': i,
                        'full_title': line,
                        'chapter_num': chapter_num,
                        'title': chapter_title
                    })
                    logger.debug(f"找到章节 {len(chapters)}: {line}")
                    break

        logger.info(f"总共找到 {len(chapters)} 个章节")
        return chapters

    def extract_chapter_content(self, content, chapter_info, all_chapters):
        """提取指定章节的内容"""
        logger.debug(f"提取第 {chapter_info['index']} 章内容: {chapter_info['full_title']}")

        lines = content.split('\n')
        start_line = chapter_info['line_number']

        # 找到下一章的开始位置
        end_line = len(lines)
        current_index = chapter_info['index']

        if current_index < len(all_chapters):
            next_chapter = all_chapters[current_index]
            end_line = next_chapter['line_number']

        # 提取章节内容（跳过标题行）
        chapter_lines = lines[start_line + 1:end_line]
        chapter_content = '\n'.join(line.strip() for line in chapter_lines if line.strip())

        logger.debug(f"第 {chapter_info['index']} 章原始内容长度: {len(chapter_content)} 字符")
        return chapter_content

    async def clean_content_with_llm(self, content):
        """使用阿里云大模型清洗内容"""
        logger.info(f"开始使用LLM清洗内容，原始长度: {len(content)} 字符")

        try:
            system_prompt = """
你是一位合规内容生成器，请确保生成内容：
1. 不含暴力、色情、歧视、毒品等违法违规元素；
2. 符合道德伦理标准；
3. 适合公开发布。

"""
            negative_prompt = """
避免露骨文字要求：露骨文案和镜头画面注意要点
露骨文案不局限于一个词或一句话，要结合文案的整体来理解。不是说你把词谐音，这段文案不露骨了。这句话，是不正确的。要看这一句话，或者前后几句话是不是露平文案。
比如：一句台词中，会经常出现：睡  睡觉 拥抱 双修  采补、 吸精、吸精气 、乱摸、乱动乱摸、
赤裸裸、风韵、韵味、味道、服侍、爆浆、口味、尝鲜、床上、大宝贝、勾引、色情
偷人、欲望、互动、鼎炉、温柔、温热、目光、润湿、洗澡、不轨、大腿、怀孕、抱起、姿势、春药、媚药、软床、丝袜、诱惑、催情、允吸、怀里、亲吻等等
类似：我和你一起睡   打扰我和XX一起睡  好久没抱着她睡觉   我不喜欢和别人睡在一起。
那么整个句子不要用。带明显倾向或暗示的以及情色类的，句子都不要有。
如出现回房睡觉，最好改成休息。 用一些不敏感的词语替代。
很容易让人联想词的露骨文案的词，这些词可能是正面的句子，也可能的负面的句子，要根据句子判断。如果是言情类，爱情类，一定要注意。
 内心空虚、挤眉弄眼、衣衫不整、脸色潮红、蹭了蹭、双腿间、我想和你在一起、生命大和谐、欲望上头、房间互动、皮肤发烫、浑身燥热、轻轻划过、躺在床上、抱入怀中、抱起、
没有联想词的句子也会被判定露骨文案。
如：你沾了我女儿的身子。 /贴在衣服的包裹下。/身体微微的起浮，面部发烫的感觉
   /将你搂在怀中/完美S型身材/几乎将衣服撑爆、
关于;镜头画面，最好不要出现 男女拥抱或或者男女抱着的画面镜头；男女目光对视的镜头；男女动作轻呢的镜头。
"""
            prompt = f"""
请将以下小说章节内容改写成{self.target_word_count}字左右的解说文字，要求故事开头有爆点，语言直白易懂，且全文不能出现违禁词及露骨文案、镜头画面。

关键要点：
1. 开场需采用热开场（高潮先行、动作引爆、强烈情绪、疑问开头等方向）或前提开场，以吸引读者注意力
2. 语言风格要洗成白话，避免复杂句式和生僻词汇，确保内容直白易懂
3. 严格规避违禁词，如"睡""拥抱""欲望"等，同时注意整体语境是否存在露骨暗示
4. 避免出现男女拥抱、目光对视等敏感镜头画面
5. 保持故事情节完整，增加细节描述和情节张力
6. 控制字数在{self.target_word_count}字左右，不要过长或过短，一定要{self.target_word_count}字左右！！！！
7. 语言要生动有趣，适合视频解说的节奏
8. 直接开始内容，不要包含章节标题、章节号等信息
9. 不要以"第X章"、"本章"等形式开头





原文：
{content}

改写后的解说文字：
"""

            logger.debug("调用阿里云大模型API进行内容清洗...")
            response = Generation.call(
                model='qwen-turbo',
                system_prompt=system_prompt,
                negative_prompt=negative_prompt,
                prompt=prompt,
                max_tokens=2500,
                temperature=0.7,
                top_p=0.8,
                api_key=DASHSCOPE_API_KEY
            )

            if response.status_code == 200:
                cleaned_content = response.output.text.strip()
                if cleaned_content.startswith('改写后的解说文字：'):
                    cleaned_content = cleaned_content.replace('改写后的解说文字：', '').strip()

                logger.info(f"内容清洗成功，清洗后长度: {len(cleaned_content)} 字符")

                # 检查字数是否接近目标
                if len(cleaned_content) < self.target_word_count * 0.8:
                    logger.warning(f"内容偏短: {len(cleaned_content)} 字符，目标: {self.target_word_count} 字符")
                elif len(cleaned_content) > self.target_word_count * 1.2:
                    logger.warning(f"内容偏长: {len(cleaned_content)} 字符，目标: {self.target_word_count} 字符")
                else:
                    logger.info(f"字数控制良好: {len(cleaned_content)} 字符")

                return cleaned_content
            else:
                logger.error(f"LLM API调用失败: {response.status_code} - {response.message}")
                return content

        except Exception as e:
            logger.error(f"内容清洗异常: {e}")
            raise
            # return content

    async def extract_keywords(self, text):
        """使用大模型提取关键词"""
        logger.debug("开始提取关键词...")

        try:
            prompt = f"""
请从以下文本中提取4-7个重要的关键词，重点关注以下类型：

优先提取：
1. 关键物品：武器装备（如：龙泉剑、凤翅镏金镋、虎符、玉玺）、珍贵物件（如：夜明珠、龙袍、金印、秘籍）、特殊道具（如：传国玉玺、免死金牌、兵符、密信）
2. 特殊地点：新出现的具体场所（如：养心殿、御书房、兵营、密室、藏书阁、演武场）、重要建筑（如：城楼、关隘、行宫）
3. 核心人物：主要角色姓名（避免"太子"、"皇帝"等通用称谓，优先具体姓名）
4. 重要事件：关键动作或转折（如：夺嫡、政变、密谋、征讨）

避免提取：
- 通用词汇：太子、皇帝、大臣、朝廷等
- 普通地点：宫中、府内、房间等
- 情绪词汇：愤怒、高兴、担心等
- 动作词汇：看着、说道、走向等

要求：
- 优先选择文中新出现的、具有特色的词汇
- 关键物品和特殊地点是重点
- 只输出关键词，用逗号分隔
- 不要任何说明文字

文本：
{text}

关键词：
"""

            response = Generation.call(
                model='qwen-turbo',

                prompt=prompt,
                max_tokens=200,
                temperature=0.3,
                api_key=DASHSCOPE_API_KEY
            )

            if response.status_code == 200:
                keywords_text = response.output.text.strip()
                if keywords_text.startswith('关键词：'):
                    keywords_text = keywords_text.replace('关键词：', '').strip()

                keywords = [kw.strip() for kw in keywords_text.split(',') if kw.strip()]
                filtered_keywords = self._filter_keywords(keywords)

                logger.debug(f"提取到关键词: {', '.join(filtered_keywords)}")
                return filtered_keywords
            else:
                logger.error(f"关键词提取API调用失败: {response.status_code}")
                return []

        except Exception as e:
            logger.error(f"关键词提取异常: {e}")
            return []

    def _filter_keywords(self, keywords):
        """过滤关键词"""
        filtered_keywords = []
        meaningless_words = {
            '看着', '说道', '心中', '眼前', '这个', '那个', '一个', '什么', '怎么',
            '非常', '很', '十分', '特别', '真的', '确实', '当然', '或许', '可能',
            '突然', '立刻', '马上', '然后', '接着', '于是', '因此', '所以',
            '的', '了', '着', '过', '在', '与', '和', '或', '但', '却', '而',
            '太子', '皇帝', '大臣', '朝廷', '宫中', '府内', '房间', '愤怒', '高兴', '担心'
        }

        item_keywords = {
            '剑', '刀', '枪', '戟', '弓', '箭', '盾', '甲', '印', '玺', '符', '令', '书', '信',
            '金', '银', '珠', '宝', '玉', '镜', '鼎', '钟', '鼓', '旗', '袍', '冠', '带', '佩',
            '丹', '药', '酒', '茶', '琴', '棋', '画', '卷', '册', '谱', '图', '牌', '牒', '诏'
        }

        place_keywords = {
            '殿', '堂', '院', '楼', '阁', '亭', '台', '轩', '斋', '室', '营', '寨', '关', '隘',
            '城', '池', '府', '邸', '宅', '园', '苑', '场', '坊', '街', '巷', '桥', '门', '楼'
        }

        for keyword in keywords:
            if len(keyword) >= 2 and keyword not in meaningless_words:
                if (any(char in keyword for char in item_keywords) or
                        any(char in keyword for char in place_keywords) or
                        any(char in keyword for char in
                            '李王赵刘陈张杨黄周吴徐孙胡朱高林何郭马罗梁宋郑谢韩唐冯于董萧程曹袁邓许傅沈曾彭吕苏卢蒋蔡贾丁魏薛叶阎余潘杜戴夏钟汪田任姜范方石姚谭廖邹熊金陆郝孔白崔康毛邱秦江史顾侯邵孟龙万段雷钱汤尹黎易常武乔贺赖龚文') or
                        keyword in ['登基', '册封', '造反', '起义', '征战', '议政', '朝会', '封赏', '处决', '流放',
                                    '夺嫡', '政变', '密谋', '征讨']):

                    if keyword not in ['太子', '皇帝', '大臣', '将军', '侍卫', '宫女', '太监']:
                        filtered_keywords.append(keyword)

        return filtered_keywords[:7]

    def split_text_into_segments(self, text):
        """将文本分割成指定数量的段落"""
        logger.debug(f"开始分割文本，目标段落数: {self.num_segments}，前两个段落字数范围: {self.first_two_segments_min_chars}-{self.first_two_segments_max_chars}")

        sentences = re.split(r'([。！？])', text)

        # 重新组合句子（包含标点符号）
        full_sentences = []
        for i in range(0, len(sentences) - 1, 2):
            if i + 1 < len(sentences):
                sentence = sentences[i] + sentences[i + 1]
                sentence = sentence.strip()
                if sentence and len(sentence) > 1:
                    full_sentences.append(sentence)

        # 如果最后一个元素不是标点符号
        if len(sentences) % 2 == 1 and sentences[-1].strip():
            last_sentence = sentences[-1].strip()
            if len(last_sentence) > 1:
                full_sentences.append(last_sentence)

        total_sentences = len(full_sentences)
        logger.debug(f"总句子数: {total_sentences}")

        if total_sentences <= self.num_segments:
            segments = full_sentences[:]
            while len(segments) < self.num_segments:
                segments.append("")
            logger.debug(f"句子数不足，每个句子一个段落，补充空段落")

            # 📝 第一阶段：基础分段和字数调整
            logger.info("📝 第一阶段：文本分段（句子数不足情况）")
            segments = self._adjust_all_segments_with_range(segments)
            logger.info(f"分段完成：共{len(segments)}个段落")

            # 📏 第二阶段和第三阶段：检查字数要求并重写
            final_segments = self.ensure_all_segments_meet_requirements(segments)

            logger.info(f"✅ 文本分割和重写完成，共 {len(final_segments)} 个段落")
            return final_segments

        base_sentences_per_segment = total_sentences // self.num_segments
        extra_sentences = total_sentences % self.num_segments

        segments = []
        current_index = 0

        for segment_num in range(self.num_segments):
            sentences_in_this_segment = base_sentences_per_segment
            if segment_num < extra_sentences:
                sentences_in_this_segment += 1

            # 提取当前段落的句子
            segment_sentences = full_sentences[current_index:current_index + sentences_in_this_segment]
            segment_text = ''.join(segment_sentences)

            segments.append(segment_text)
            current_index += sentences_in_this_segment

            logger.debug(f"第 {segment_num + 1} 段落: {sentences_in_this_segment} 个句子, {len(segment_text)} 字符")

        # 📝 第一阶段：基础分段和字数调整
        logger.info("📝 第一阶段：文本分段")
        segments = self._adjust_all_segments_with_range(segments)
        logger.info(f"分段完成：共{len(segments)}个段落")

        # 📏 第二阶段和第三阶段：检查字数要求并重写
        final_segments = self.ensure_all_segments_meet_requirements(segments)

        logger.info(f"✅ 文本分割和重写完成，共 {len(final_segments)} 个段落")
        return final_segments

    def _split_into_complete_sentences(self, text):
        """将文本分割成完整的句子"""
        if not text.strip():
            return []

        sentences = re.split(r'([。！？])', text)
        complete_sentences = []

        for i in range(0, len(sentences) - 1, 2):
            if i + 1 < len(sentences):
                sentence = sentences[i] + sentences[i + 1]
                sentence = sentence.strip()
                if sentence and len(sentence) > 1:
                    complete_sentences.append(sentence)

        # 处理最后一个元素（如果不是标点符号）
        if len(sentences) % 2 == 1 and sentences[-1].strip():
            last_sentence = sentences[-1].strip()
            if len(last_sentence) > 1:
                complete_sentences.append(last_sentence)

        return complete_sentences

    def _adjust_first_two_segments_with_range(self, segments):
        """调整前两个段落的字数，确保在指定范围内，同时保证句意完整"""
        if len(segments) < 2:
            return segments

        adjusted_segments = segments[:]

        logger.debug(f"调整前 - 第1段落: {len(segments[0])}字, 第2段落: {len(segments[1])}字")

        # 处理第一个段落
        adjusted_segments[0], overflow1, need_supplement1 = self._adjust_segment_with_sentence_integrity(
            segments[0], self.first_two_segments_min_chars, self.first_two_segments_max_chars, 1
        )

        # 处理第二个段落（包含第一个段落的溢出内容）
        second_segment_content = overflow1 + segments[1]
        adjusted_segments[1], overflow2, need_supplement2 = self._adjust_segment_with_sentence_integrity(
            second_segment_content, self.first_two_segments_min_chars, self.first_two_segments_max_chars, 2
        )

        # 处理需要补充内容的情况
        if need_supplement1 and len(adjusted_segments) > 1:
            adjusted_segments[0], adjusted_segments[1] = self._supplement_segment_content(
                adjusted_segments[0], adjusted_segments[1], self.first_two_segments_min_chars, 1
            )

        if need_supplement2 and len(adjusted_segments) > 2:
            adjusted_segments[1], adjusted_segments[2] = self._supplement_segment_content(
                adjusted_segments[1], adjusted_segments[2], self.first_two_segments_min_chars, 2
            )

        # 将第二个段落的溢出内容分配到后续段落
        if overflow2 and len(adjusted_segments) > 2:
            adjusted_segments[2] = overflow2 + adjusted_segments[2]
            logger.debug(f"第2段落溢出内容已合并到第3段落，新长度: {len(adjusted_segments[2])} 字符")

        logger.debug(f"调整后 - 第1段落: {len(adjusted_segments[0])}字, 第2段落: {len(adjusted_segments[1])}字")

        return adjusted_segments

    def _adjust_all_segments_with_range(self, segments):
        """调整所有段落的字数，分层处理不同段落"""
        if len(segments) < 2:
            return segments

        adjusted_segments = segments[:]

        # 第一步：调整前两个段落（25-50字符）
        adjusted_segments = self._adjust_first_two_segments_with_range(adjusted_segments)

        # 第二步：调整中间段落（62-72字符，索引2-22）
        if len(adjusted_segments) > 2:
            adjusted_segments = self._adjust_middle_segments_with_range(adjusted_segments)

        return adjusted_segments

    def _adjust_middle_segments_with_range(self, segments):
        """调整中间段落的字数，确保在62-72字符范围内"""
        if len(segments) <= 2:
            return segments

        overflow = ""

        # 处理中间段落（索引2-22）
        for i in range(2, min(23, len(segments))):
            # 当前段落内容 = 溢出内容 + 原始内容
            current_content = overflow + segments[i]

            # 调整到指定范围
            adjusted_content, new_overflow, need_supplement = self._adjust_segment_with_fine_granularity(
                current_content, self.middle_segments_min_chars, self.middle_segments_max_chars, i + 1
            )

            # 如果需要补充且不是最后一个中间段落
            if need_supplement and i < 22 and i + 1 < len(segments):
                adjusted_content, segments[i + 1] = self._supplement_segment_content(
                    adjusted_content, segments[i + 1], self.middle_segments_min_chars, i + 1
                )

            segments[i] = adjusted_content
            overflow = new_overflow

            logger.debug(f"中间段落 {i + 1} 调整完成: {len(adjusted_content)}字符")

        # 最后的溢出内容给最后一个段落（索引23）
        if overflow and len(segments) > 23:
            segments[23] = overflow + segments[23]
            logger.debug(f"溢出内容已合并到最后段落，新长度: {len(segments[23])} 字符")

        return segments

    def _adjust_segment_with_fine_granularity(self, segment_text, min_chars, max_chars, segment_num):
        """
        使用分层切分策略调整段落字数

        Args:
            segment_text: 段落文本
            min_chars: 最小字符数
            max_chars: 最大字符数
            segment_num: 段落序号

        Returns:
            (调整后的段落, 溢出内容, 是否需要补充内容)
        """
        if not segment_text.strip():
            return segment_text, "", True

        current_length = len(segment_text)

        # 如果在范围内，直接返回
        if min_chars <= current_length <= max_chars:
            logger.debug(f"段落{segment_num}字数({current_length})在范围内，无需调整")
            return segment_text, "", False

        if current_length < min_chars:
            # 字数不足，需要补充
            logger.debug(f"段落{segment_num}字数不足: {current_length} < {min_chars}")
            return segment_text, "", True

        elif current_length > max_chars:
            # 字数过多，尝试分层调整
            # 第一层：按句子切分
            result = self._adjust_by_sentences(segment_text, min_chars, max_chars, segment_num)
            if result is not None:
                return result

            # 第二层：按逗号切分
            result = self._adjust_by_commas(segment_text, min_chars, max_chars, segment_num)
            if result is not None:
                return result

            # 兜底：保持原文
            logger.info(f"段落{segment_num}无法调整到目标范围，保持原文({current_length}字)")
            return segment_text, "", False

        return segment_text, "", False

    def _adjust_by_sentences(self, segment_text, min_chars, max_chars, segment_num):
        """按句子调整段落"""
        sentences = self._split_into_complete_sentences(segment_text)

        if len(sentences) <= 1:
            # 只有一个句子，无法通过移除句子来调整
            return None

        kept_sentences = []
        current_length = 0

        for sentence in sentences:
            if current_length + len(sentence) <= max_chars:
                kept_sentences.append(sentence)
                current_length += len(sentence)
            else:
                break

        # 检查是否满足最小字数要求
        if current_length >= min_chars and kept_sentences:
            kept_text = ''.join(kept_sentences)
            overflow_text = ''.join(sentences[len(kept_sentences):])
            logger.debug(f"段落{segment_num}通过句子调整: {len(segment_text)}字 → {len(kept_text)}字")
            return kept_text, overflow_text, False

        return None  # 句子级别无法满足要求

    def _adjust_by_commas(self, segment_text, min_chars, max_chars, segment_num):
        """按逗号进行细粒度调整"""
        # 按逗号切分，保留逗号
        parts = re.split(r'(，)', segment_text)

        # 重新组合，确保逗号跟在前面的部分
        comma_segments = []
        for i in range(0, len(parts) - 1, 2):
            if i + 1 < len(parts):
                segment = parts[i] + parts[i + 1]  # 文本 + 逗号
                if segment.strip():
                    comma_segments.append(segment)

        # 处理最后一个部分（可能没有逗号）
        if len(parts) % 2 == 1 and parts[-1].strip():
            comma_segments.append(parts[-1])

        if len(comma_segments) <= 1:
            # 无法进一步切分，返回None
            return None

        # 尝试找到合适的切分点
        kept_parts = []
        current_length = 0

        for part in comma_segments:
            if current_length + len(part) <= max_chars:
                kept_parts.append(part)
                current_length += len(part)
            else:
                break

        # 检查结果
        if current_length >= min_chars and kept_parts:
            kept_text = ''.join(kept_parts)
            overflow_text = ''.join(comma_segments[len(kept_parts):])
            logger.debug(f"段落{segment_num}通过逗号调整: {len(segment_text)}字 → {len(kept_text)}字")
            return kept_text, overflow_text, False

        return None  # 逗号级别也无法满足要求

    def _adjust_segment_with_sentence_integrity(self, segment_text, min_chars, max_chars, segment_num):
        """
        在保证句子完整性的前提下调整段落字数

        Args:
            segment_text: 段落文本
            min_chars: 最小字符数
            max_chars: 最大字符数
            segment_num: 段落序号（用于日志）

        Returns:
            (调整后的段落, 溢出内容, 是否需要补充内容)
        """
        if not segment_text.strip():
            return segment_text, "", True

        current_length = len(segment_text)

        # 如果在范围内，直接返回
        if min_chars <= current_length <= max_chars:
            logger.debug(f"第{segment_num}段落字数({current_length})在范围内，无需调整")
            return segment_text, "", False

        # 分割成完整句子
        sentences = self._split_into_complete_sentences(segment_text)

        if current_length < min_chars:
            # 字数不足，需要补充
            logger.debug(f"第{segment_num}段落字数不足: {current_length} < {min_chars}")
            return segment_text, "", True

        elif current_length > max_chars:
            # 字数过多，尝试移除句子
            if len(sentences) <= 1:
                # 只有一个句子且超过上限，保持完整
                logger.info(f"第{segment_num}段落包含单个长句子({current_length}字)，保持完整性，允许超过{max_chars}字上限")
                return segment_text, "", False
            else:
                # 多个句子，移除后面的句子直到符合要求
                kept_sentences = []
                current_length = 0

                for sentence in sentences:
                    if current_length + len(sentence) <= max_chars:
                        kept_sentences.append(sentence)
                        current_length += len(sentence)
                    else:
                        break

                # 确保至少保留一个句子
                if not kept_sentences:
                    kept_sentences = [sentences[0]]
                    current_length = len(sentences[0])
                    logger.info(f"第{segment_num}段落第一个句子({current_length}字)超过上限，保持完整性")

                kept_text = ''.join(kept_sentences)
                overflow_text = ''.join(sentences[len(kept_sentences):])

                # 检查保留部分是否少于最小值
                if len(kept_text) < min_chars and overflow_text:
                    # 如果移除句子后少于最小值，则保留原文
                    logger.info(f"第{segment_num}段落移除句子后字数({len(kept_text)})少于最小值({min_chars})，保持原段落完整")
                    return segment_text, "", False

                logger.debug(f"第{segment_num}段落调整: {len(segment_text)}字 → {len(kept_text)}字，溢出: {len(overflow_text)}字")
                return kept_text, overflow_text, False

        return segment_text, "", False

    def _supplement_segment_content(self, target_segment, source_segment, min_chars, segment_num):
        """
        从源段落补充内容到目标段落，确保达到最小字符数

        Args:
            target_segment: 需要补充的目标段落
            source_segment: 提供内容的源段落
            min_chars: 最小字符数
            segment_num: 段落序号（用于日志）

        Returns:
            (补充后的目标段落, 剩余的源段落)
        """
        if len(target_segment) >= min_chars:
            return target_segment, source_segment

        if not source_segment.strip():
            logger.warning(f"第{segment_num}段落字数不足({len(target_segment)}字)，但后续段落为空，无法补充")
            return target_segment, source_segment

        # 分割源段落的句子
        source_sentences = self._split_into_complete_sentences(source_segment)

        supplemented_target = target_segment
        remaining_sentences = source_sentences[:]

        # 逐句添加直到达到最小字符数
        for i, sentence in enumerate(source_sentences):
            if len(supplemented_target) >= min_chars:
                break

            supplemented_target += sentence
            remaining_sentences = source_sentences[i + 1:]

        remaining_source = ''.join(remaining_sentences)

        logger.debug(f"第{segment_num}段落补充内容: {len(target_segment)}字 → {len(supplemented_target)}字")

        return supplemented_target, remaining_source

    def _clean_text_for_tts(self, text):
        """清理文本用于TTS"""
        clean_text = re.sub(r'[^\u4e00-\u9fff。，！？；：""''（）]', '', text)
        clean_text = clean_text.strip()

        if len(clean_text) < 2 or re.match(r'^[。，！？；：""''（）]+$', clean_text):
            return ""

        return clean_text

    def _get_segment_char_limits(self, segment_index):
        """根据段落位置获取字数限制"""

        if segment_index == 0 or segment_index == 1:  # 第1、2段落
            return self.first_two_segments_min_chars, self.first_two_segments_max_chars
        else:  # 其他段落（第3段及以后）
            return self.middle_segments_min_chars, self.middle_segments_max_chars

    def _call_llm_api(self, prompt, model_name='qwen-plus'):
        """调用大模型API"""
        try:
            response = Generation.call(
                model=model_name,  # 🆕 支持更换模型
                prompt=prompt,
                max_tokens=300,
                temperature=0.3,
                api_key=os.getenv("DASHSCOPE_API_KEY")
            )

            if response.status_code == 200:
                return response.output.text.strip()
            else:
                logger.error(f"❌ 大模型调用失败: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"❌ 大模型重写异常: {e}")
            return None

    def _call_llm_for_rewrite_with_strategy(self, text, current_length, min_chars, max_chars, segment_num, attempt):
        """根据尝试次数使用不同的重写策略"""

        # 🆕 专注于压缩文本，因为只处理字数超出的情况
        if attempt == 0:
            # 第1次：压缩到范围内
            constraint = f"将文本压缩到{max_chars}字符以内，保持在{min_chars}-{max_chars}字符范围"

        elif attempt == 1:
            # 第2次：强调不超过最大值
            constraint = f"必须将文本压缩到{max_chars}字符以内，保留核心内容"

        else:  # attempt == 2
            # 第3次：只要求不超过最大值
            constraint = f"绝对不能超过{max_chars}字符，可以适当删减次要内容"

        # 根据段落位置给出不同的重写指导
        if segment_num <= 2:
            segment_type = "开头段落"
            style_instruction = "作为开头段落，需要简洁有力，快速吸引读者注意力"
        else:
            segment_type = "正文段落"
            style_instruction = "作为正文段落，需要内容充实，推进故事情节发展"

        prompt = f"""你是专业的文本编辑专家。这是第{attempt+1}次重写尝试。

原文：{text}
当前字数：{current_length}字符
段落编号：第{segment_num}段（共{self.num_segments}段）
段落类型：{segment_type}

重写要求：
1. {constraint}
2. {style_instruction}
3. 保持原文核心内容和情节
4. 语言自然流畅，适合语音朗读
5. 只返回重写后的文本，不要解释

重写后的文本："""

        return self._call_llm_api(prompt, model_name=self.rewrite_model)  # 🆕 使用配置的模型

    def _smart_truncate_to_max(self, text, max_chars):
        """智能截断到最大字数"""
        clean_text = self._clean_text_for_tts(text)

        if len(clean_text) <= max_chars:
            return text

        # 按句子截断，保持完整性
        sentences = re.split(r'([。！？])', text)
        result = ""

        for i in range(0, len(sentences), 2):
            if i + 1 < len(sentences):
                sentence_with_punct = sentences[i] + sentences[i + 1]
            else:
                sentence_with_punct = sentences[i]

            test_result = result + sentence_with_punct
            if len(self._clean_text_for_tts(test_result)) <= max_chars:
                result = test_result
            else:
                break

        return result if result else text[:max_chars]  # 最后的保底



    def _fallback_rewrite_strategy(self, text, min_chars, max_chars, segment_num):
        """保底方案：当大模型重写失败时的处理（只处理超出的情况）"""

        clean_text = self._clean_text_for_tts(text)
        current_length = len(clean_text)

        logger.info(f"🛡️ 第{segment_num}段落启用保底方案: {current_length}字 → 目标({min_chars}-{max_chars})")

        if current_length > max_chars:
            # 超过最大值：智能截断到最大字数
            truncated_text = self._smart_truncate_to_max(text, max_chars)
            logger.info(f"📏 第{segment_num}段落截断处理: {current_length}字 → {len(self._clean_text_for_tts(truncated_text))}字")
            return truncated_text
        else:
            # 🆕 其他情况（包括字数不足）都保持原文
            logger.info(f"📏 第{segment_num}段落保持原文: {current_length}字")
            return text

    def _rewrite_with_llm_until_valid(self, text, min_chars, max_chars, segment_num, max_attempts=3):
        """
        使用大模型重写文本直到符合字数要求

        Args:
            text: 原始文本
            min_chars: 最小字符数
            max_chars: 最大字符数
            segment_num: 段落编号
            max_attempts: 最大重写尝试次数（默认3次）
        """

        for attempt in range(max_attempts):
            current_text = text
            clean_text = self._clean_text_for_tts(current_text)
            current_length = len(clean_text)

            # 检查是否已符合要求
            if min_chars <= current_length <= max_chars:
                logger.info(f"✅ 第{segment_num}段落第{attempt+1}次尝试成功: {current_length}字 ({min_chars}-{max_chars})")
                return current_text

            # 需要重写
            logger.info(f"🔄 第{segment_num}段落第{attempt+1}次重写: {current_length}字 → 目标({min_chars}-{max_chars})")

            # 根据尝试次数调整策略
            rewritten_text = self._call_llm_for_rewrite_with_strategy(
                text, current_length, min_chars, max_chars, segment_num, attempt
            )

            if rewritten_text and rewritten_text != text:
                text = rewritten_text
            else:
                logger.warning(f"⚠️ 第{segment_num}段落第{attempt+1}次重写无效果")

        # 所有重写尝试都失败，使用保底方案
        logger.error(f"❌ 第{segment_num}段落{max_attempts}次重写都失败，使用保底方案")
        return self._fallback_rewrite_strategy(text, min_chars, max_chars, segment_num)

    def ensure_all_segments_meet_requirements(self, segments):
        """确保所有段落都符合各自的字数要求"""

        logger.info("📏 第二阶段：确定字数要求")
        segment_requirements = []

        for i, segment in enumerate(segments):
            min_chars, max_chars = self._get_segment_char_limits(i)
            clean_length = len(self._clean_text_for_tts(segment))

            # 🆕 只处理字数超出的情况，字数不足的不管
            needs_rewrite = False
            tolerance = self.rewrite_tolerance  # 使用配置的容忍度
            gap = 0  # 初始化gap变量

            if clean_length < min_chars:
                gap = min_chars - clean_length
                needs_rewrite = False  # 🆕 字数不足不重写
                status_detail = f"少{gap}字(忽略)"
            elif clean_length > max_chars:
                gap = clean_length - max_chars
                needs_rewrite = gap > tolerance  # 只有超出容忍度才重写
                status_detail = f"多{gap}字" if gap > tolerance else f"多{gap}字(容忍)"
            else:
                gap = 0
                status_detail = "符合"

            segment_requirements.append({
                'index': i,
                'text': segment,
                'min_chars': min_chars,
                'max_chars': max_chars,
                'clean_length': clean_length,
                'needs_rewrite': needs_rewrite,
                'gap': gap
            })

            status = "✅符合" if not needs_rewrite else "❌需重写"
            logger.info(f"  第{i+1}段: {clean_length}字 ({min_chars}-{max_chars}) {status} ({status_detail})")

        # 统一重写不符合要求的段落
        logger.info("✍️ 第三阶段：重写不符合要求的段落")
        rewrite_count = sum(1 for req in segment_requirements if req['needs_rewrite'])

        if rewrite_count > 0:
            logger.info(f"需要重写 {rewrite_count} 个段落")
            final_segments = []

            for req in segment_requirements:
                if req['needs_rewrite']:
                    logger.info(f"🔄 重写第{req['index']+1}段落...")
                    rewritten_text = self._rewrite_with_llm_until_valid(
                        req['text'], req['min_chars'], req['max_chars'], req['index'] + 1
                    )
                    final_segments.append(rewritten_text)
                else:
                    final_segments.append(req['text'])
        else:
            logger.info("所有段落都符合字数要求，无需重写")
            final_segments = segments

        return final_segments

    def create_ssml_text(self, text: str, speed_factor: float = 1.3) -> str:
        """
        创建带有语速控制的SSML文本

        Args:
            text: 原始文本
            speed_factor: 语速倍数，1.3表示1.3倍速

        Returns:
            SSML格式的文本
        """
        # 将倍速转换为百分比格式
        speed_percent = f"{int(speed_factor * 100)}%"

        # 使用SSML的prosody标签控制语速
        ssml_text = f'<speak><prosody rate="{speed_percent}">{text}</prosody></speak>'

        return ssml_text

    def adjust_subtitle_timestamps(self, subtitle_file_path, speed_factor):
        """
        调整字幕时间戳以匹配倍速音频

        Args:
            subtitle_file_path: 原始字幕文件路径
            speed_factor: 倍速因子（如1.3表示1.3倍速）

        Returns:
            str: 调整后的字幕文件路径
        """
        try:
            subtitle_file = Path(subtitle_file_path)
            if not subtitle_file.exists():
                logger.warning(f"字幕文件不存在: {subtitle_file}")
                return None

            # 读取原始字幕内容
            with open(subtitle_file, 'r', encoding='utf-8') as f:
                content = f.read()

            if not content.strip():
                logger.warning(f"字幕文件为空: {subtitle_file}")
                return None

            # 调整时间戳
            adjusted_content = self._adjust_srt_timestamps(content, speed_factor)

            # 生成新的文件名
            stem = subtitle_file.stem
            suffix = subtitle_file.suffix
            speed_suffix = f"_{speed_factor}x" if speed_factor != 1.0 else ""
            new_filename = f"{stem}{speed_suffix}{suffix}"
            new_file_path = subtitle_file.parent / new_filename

            # 保存调整后的字幕
            with open(new_file_path, 'w', encoding='utf-8') as f:
                f.write(adjusted_content)

            logger.debug(f"字幕时间戳调整完成: {new_file_path.name}")
            return str(new_file_path)

        except Exception as e:
            logger.error(f"字幕时间戳调整失败: {e}")
            return None

    def _adjust_srt_timestamps(self, srt_content, speed_factor):
        """
        调整SRT字幕内容中的时间戳

        Args:
            srt_content: SRT字幕内容
            speed_factor: 倍速因子

        Returns:
            str: 调整后的SRT内容
        """
        import re

        def adjust_timestamp(match):
            """调整单个时间戳"""
            start_time = match.group(1)
            end_time = match.group(2)

            # 转换时间戳
            start_ms = self._timestamp_to_milliseconds(start_time)
            end_ms = self._timestamp_to_milliseconds(end_time)

            # 除以倍速因子
            adjusted_start_ms = int(start_ms / speed_factor)
            adjusted_end_ms = int(end_ms / speed_factor)

            # 转换回时间戳格式
            adjusted_start = self._milliseconds_to_timestamp(adjusted_start_ms)
            adjusted_end = self._milliseconds_to_timestamp(adjusted_end_ms)

            return f"{adjusted_start} --> {adjusted_end}"

        # 匹配SRT时间戳格式: 00:00:01,500 --> 00:00:03,200
        timestamp_pattern = r'(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})'
        adjusted_content = re.sub(timestamp_pattern, adjust_timestamp, srt_content)

        return adjusted_content

    def _timestamp_to_milliseconds(self, timestamp):
        """将时间戳转换为毫秒"""
        # 格式: 00:00:01,500
        time_part, ms_part = timestamp.split(',')
        hours, minutes, seconds = map(int, time_part.split(':'))
        milliseconds = int(ms_part)

        total_ms = (hours * 3600 + minutes * 60 + seconds) * 1000 + milliseconds
        return total_ms

    def _milliseconds_to_timestamp(self, milliseconds):
        """将毫秒转换为时间戳格式"""
        ms = milliseconds % 1000
        seconds = (milliseconds // 1000) % 60
        minutes = (milliseconds // 60000) % 60
        hours = milliseconds // 3600000

        return f"{hours:02d}:{minutes:02d}:{seconds:02d},{ms:03d}"

    async def generate_audio_with_retry(self, text, output_file, max_retries=3):
        """生成音频和字幕"""
        logger.debug(f"开始生成音频: {output_file.name}")

        try:
            clean_text = self._clean_text_for_tts(text)
            if not clean_text:
                logger.warning(f"文本清理后为空，跳过音频生成: {output_file.name}")
                return False, None

            for attempt in range(max_retries):
                try:
                    # 🆕 添加0.5秒延迟，避免频繁调用edge-tts
                    await asyncio.sleep(0.5)

                    # 使用原始文本创建Communicate对象（一倍速）
                    communicate = edge_tts.Communicate(clean_text, 'zh-CN-YunjianNeural')
                    submaker = edge_tts.SubMaker()

                    # 生成音频并收集字幕数据
                    with open(output_file, "wb") as file:
                        async for chunk in communicate.stream():
                            if chunk["type"] == "audio":
                                file.write(chunk["data"])
                            elif chunk["type"] == "WordBoundary":
                                submaker.feed(chunk)

                    if output_file.exists() and output_file.stat().st_size > 0:
                        # 获取字幕内容
                        subtitle_content = submaker.get_srt()
                        logger.debug(f"音频和字幕生成成功: {output_file.name}")
                        return True, subtitle_content
                    else:
                        logger.warning(f"音频文件生成失败，重试 {attempt + 1}/{max_retries}: {output_file.name}")

                except Exception as e:
                    logger.warning(f"音频生成异常 {attempt + 1}/{max_retries}: {e}")

                if attempt < max_retries - 1:
                    await asyncio.sleep(2)

            logger.error(f"音频生成最终失败: {output_file.name}")
            return False, None

        except Exception as e:
            logger.error(f"音频生成异常: {e}")
            return False, None

    def speed_up_audio(self, input_file, output_file, speed_factor=None):
        """
        使用ffmpeg对音频进行倍速处理

        Args:
            input_file: 输入音频文件路径
            output_file: 输出音频文件路径
            speed_factor: 倍速因子，如果为None则使用self.speed_factor

        Returns:
            bool: 处理是否成功
        """
        if speed_factor is None:
            speed_factor = self.speed_factor

        try:
            # 检查ffmpeg是否可用
            result = subprocess.run(['ffmpeg', '-version'],
                                    capture_output=True, text=True, timeout=5)
            if result.returncode != 0:
                logger.error("ffmpeg不可用，跳过音频倍速处理")
                # 如果ffmpeg不可用，直接复制原文件
                shutil.copy2(input_file, output_file)
                return True

            # 使用ffmpeg进行倍速处理
            cmd = [
                'ffmpeg', '-i', str(input_file),
                '-filter:a', f'atempo={speed_factor}',
                '-y',  # 覆盖输出文件
                str(output_file)
            ]

            logger.info(f"开始音频倍速处理: {speed_factor}x")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                logger.info(f"音频倍速处理成功: {output_file}")
                return True
            else:
                logger.error(f"音频倍速处理失败: {result.stderr}")
                # 处理失败时复制原文件
                shutil.copy2(input_file, output_file)
                return True

        except subprocess.TimeoutExpired:
            logger.error("音频倍速处理超时")
            shutil.copy2(input_file, output_file)
            return True
        except Exception as e:
            logger.error(f"音频倍速处理异常: {e}")
            # 异常时复制原文件
            shutil.copy2(input_file, output_file)
            return True

    def get_audio_duration(self, audio_file_path):
        """
        获取音频文件的时长

        Args:
            audio_file_path: 音频文件路径

        Returns:
            float: 音频时长（秒），获取失败返回0
        """
        try:
            # 使用ffprobe获取音频时长
            cmd = [
                'ffprobe', '-v', 'quiet', '-show_entries', 'format=duration',
                '-of', 'csv=p=0', str(audio_file_path)
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                duration = float(result.stdout.strip())
                return duration
            else:
                return 0.0

        except subprocess.TimeoutExpired:
            logger.error("获取音频时长超时")
            return 0.0
        except Exception as e:
            logger.error(f"获取音频时长异常: {e}")
            return 0.0

    async def upload_audio_and_generate_subtitle(self, audio_file_path, segment_id, segment_text=""):
        """
        上传音频文件到TOS并生成字幕

        Args:
            audio_file_path: 本地音频文件路径
            segment_id: 段落ID
            segment_text: 段落文本内容

        Returns:
            tuple: (audio_oss_url, subtitles_list, audio_duration_s)
        """
        audio_oss_url = ""
        subtitles = []
        audio_duration_s = 0.0

        try:
            # 1. 对音频进行1.3倍速处理
            processed_audio_path = None
            if audio_file_path and os.path.exists(audio_file_path):
                # 创建倍速处理后的文件路径
                audio_path = Path(audio_file_path)
                processed_audio_path = audio_path.parent / f"{audio_path.stem}_{self.speed_factor}x{audio_path.suffix}"

                # 进行倍速处理
                speed_success = self.speed_up_audio(audio_file_path, processed_audio_path)
                if not speed_success:
                    logger.warning("音频倍速处理失败，使用原音频")
                    processed_audio_path = audio_file_path

                # 获取处理后音频的时长
                audio_duration_s = self.get_audio_duration(processed_audio_path)
                logger.info(f"倍速处理后音频时长: {audio_duration_s:.2f}秒")

            # 2. 上传处理后的音频文件到TOS（带重试机制）
            if processed_audio_path and os.path.exists(processed_audio_path):
                try:
                    object_key = f"audios/chapter_audio_{uuid.uuid4()}_{self.speed_factor}x.wav"
                    audio_oss_url = await self.upload_audio_with_retry(
                        str(processed_audio_path),
                        object_key,
                        max_retries=3
                    )
                finally:
                    # 清理临时的倍速音频文件（如果不是原文件）
                    if processed_audio_path != audio_file_path and os.path.exists(processed_audio_path):
                        try:
                            os.remove(processed_audio_path)
                            logger.debug(f"清理临时倍速音频文件: {processed_audio_path}")
                        except Exception as e:
                            logger.warning(f"清理临时文件失败: {e}")
            else:
                audio_oss_url = ""

            # 3. 使用火山引擎字幕服务生成字幕（基于1.3倍速音频）
            if audio_oss_url and self.use_volcengine_subtitle:
                try:
                    volcengine_subtitles = self.subtitle_service.process_audio_subtitle(audio_oss_url)
                    if volcengine_subtitles:
                        # 转换火山引擎字幕格式为项目格式
                        # 注意：字幕时间戳是基于1.3倍速音频的，实际播放时需要相应调整
                        subtitles = self._convert_volcengine_subtitles(volcengine_subtitles, segment_id)
                        logger.info(f"火山引擎字幕生成成功（1.3倍速音频），共 {len(subtitles)} 个字幕")
                    else:
                        error_msg = "火山引擎字幕生成失败，返回空字幕列表"
                        logger.warning(error_msg)
                        subtitles = []
                        # 🆕 记录错误
                        self._record_error("subtitle_generation_errors", {
                            "error_type": "火山引擎字幕生成失败",
                            "error_message": "API返回空结果",
                            "audio_url": audio_oss_url,
                            "segment_id": segment_id,
                            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                        })
                except Exception as e:
                    error_msg = f"火山引擎字幕生成异常: {e}，返回空字幕列表"
                    logger.error(error_msg)
                    subtitles = []
                    # 🆕 记录错误
                    self._record_error("subtitle_generation_errors", {
                        "error_type": "火山引擎字幕生成异常",
                        "error_message": str(e),
                        "audio_url": audio_oss_url,
                        "segment_id": segment_id,
                        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                    })
            else:
                # 没有音频URL，返回空字幕列表
                error_msg = "没有音频URL，无法生成字幕"
                logger.warning(error_msg)
                subtitles = []
                # 🆕 记录错误
                self._record_error("subtitle_generation_errors", {
                    "error_type": "没有音频URL",
                    "error_message": "音频上传失败或TOS服务不可用",
                    "audio_file": str(audio_file_path) if audio_file_path else "None",
                    "segment_id": segment_id,
                    "use_volcengine": self.use_volcengine_subtitle,
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                })

        except Exception as e:
            logger.error(f"音频处理和字幕生成异常: {e}")
            # 返回空字幕列表
            subtitles = []

        return audio_oss_url, subtitles, audio_duration_s

    async def upload_audio_with_retry(self, audio_file_path, object_key, max_retries=3):
        """
        带重试机制的音频上传

        Args:
            audio_file_path: 音频文件路径
            object_key: TOS对象键名
            max_retries: 最大重试次数

        Returns:
            str: 上传成功的URL，失败返回空字符串
        """
        if not self.tos_processor:
            logger.warning("TOS处理器未初始化，无法上传音频")
            return ""

        if not os.path.exists(audio_file_path):
            logger.warning(f"音频文件不存在: {audio_file_path}")
            return ""

        for attempt in range(max_retries):
            try:
                logger.info(f"🔄 音频上传尝试 {attempt + 1}/{max_retries}: {os.path.basename(audio_file_path)}")

                audio_oss_url = self.tos_processor.upload_file(
                    str(audio_file_path),
                    object_key
                )

                logger.info(f"✅ 音频上传成功 (第{attempt + 1}次尝试): {audio_oss_url}")
                return audio_oss_url

            except Exception as e:
                error_msg = f"音频上传失败 (第{attempt + 1}次尝试): {e}"

                if attempt < max_retries - 1:
                    # 还有重试机会
                    wait_time = 2 ** attempt  # 指数退避: 1s, 2s, 4s
                    logger.warning(f"{error_msg}，{wait_time}秒后重试...")
                    await asyncio.sleep(wait_time)
                else:
                    # 最后一次尝试失败
                    logger.error(f"{error_msg}，已达到最大重试次数")
                    # 记录错误
                    self._record_error("audio_upload_errors", {
                        "error_type": "音频上传失败",
                        "error_message": str(e),
                        "audio_file": str(audio_file_path),
                        "object_key": object_key,
                        "max_retries": max_retries,
                        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                    })
                    return ""

        return ""

    async def extract_keywords(self, text):
        """使用大模型提取关键词"""
        logger.debug("开始提取关键词...")

        try:
            prompt = f"""
请从以下文本中提取7-10个重要的关键词，重点关注以下类型：

优先提取：
1. 关键物品：武器装备（如：龙泉剑、凤翅镏金镋、虎符、玉玺）、珍贵物件（如：夜明珠、龙袍、金印、秘籍）、特殊道具（如：传国玉玺、免死金牌、兵符、密信）
2. 特殊地点：新出现的具体场所（如：养心殿、御书房、兵营、密室、藏书阁、演武场）、重要建筑（如：城楼、关隘、行宫）
3. 核心人物：主要角色姓名（避免"太子"、"皇帝"等通用称谓，优先具体姓名）

避免提取：
- 通用称谓：太子、皇帝、大臣、将军等
- 常见动作：走、看、说、想、来、去等
- 时间词汇：今日、明日、此时、当时等
- 语气词汇：啊、呢、吧、了、的、是等
- 动作词汇：看着、说道、走向等

要求：
- 优先选择文中新出现的、具有特色的词汇
- 关键物品和特殊地点是重点
- 只输出关键词，用逗号分隔
- 不要任何说明文字

文本：
{text}

关键词：
"""

            # 添加重试机制
            max_api_retries = 3
            response = None
            for retry in range(max_api_retries):
                try:
                    response = Generation.call(
                        model='qwen-turbo',
                        prompt=prompt,
                        max_tokens=200,
                        temperature=0.3,
                        api_key=DASHSCOPE_API_KEY
                    )
                    break  # 成功则跳出重试循环
                except Exception as e:
                    logger.warning(f"关键词提取API调用失败 (第{retry + 1}次重试): {e}")
                    if retry < max_api_retries - 1:
                        await asyncio.sleep(2 * (retry + 1))  # 递增等待时间
                    else:
                        raise  # 最后一次重试失败则抛出异常

            if response and response.status_code == 200:
                keywords_text = response.output.text.strip()
                if keywords_text.startswith('关键词：'):
                    keywords_text = keywords_text.replace('关键词：', '').strip()

                keywords = [kw.strip() for kw in keywords_text.split(',') if kw.strip()]
                filtered_keywords = self._filter_keywords(keywords)

                logger.debug(f"提取到关键词: {', '.join(filtered_keywords)}")
                return filtered_keywords
            else:
                logger.error(f"关键词提取API调用失败: {response.status_code if response else 'No response'}")
                return []

        except Exception as e:
            logger.error(f"关键词提取异常: {e}")
            return []

    async def extract_keywords_for_subtitles(self, subtitles):
        """为每条字幕提取一个关键词（批量处理）"""
        logger.debug(f"开始为 {len(subtitles)} 条字幕提取关键词...")

        if not subtitles:
            return []

        try:
            # 构建字幕文本列表
            subtitle_texts = []
            for i, subtitle in enumerate(subtitles):
                subtitle_text = subtitle.get('text', '').strip()
                subtitle_texts.append(f"{i + 1}. {subtitle_text}")

            subtitles_content = '\n'.join(subtitle_texts)

            prompt = f"""
请为以下每条字幕提取一个最重要的关键词。每条字幕对应一个关键词，按顺序输出。

提取规则：
1. 优先提取：人物姓名、地点名称、物品名称、重要动作
2. 避免提取：语气词、连接词、通用词汇（如：的、了、在、是、有、说、看）
3. 如果字幕中没有明显关键词，提取最重要的名词
4. 每个关键词2-4个字，要具体有意义

字幕列表：
{subtitles_content}

请按顺序输出关键词，每行一个，格式如下：
1. 关键词1
2. 关键词2
3. 关键词3
...

输出：
"""

            # 添加重试机制
            max_api_retries = 3
            response = None
            for retry in range(max_api_retries):
                try:
                    response = Generation.call(
                        model='qwen-turbo',
                        prompt=prompt,
                        max_tokens=500,
                        temperature=0.2,
                        api_key=DASHSCOPE_API_KEY
                    )
                    break  # 成功则跳出重试循环
                except Exception as e:
                    logger.warning(f"字幕关键词提取API调用失败 (第{retry + 1}次重试): {e}")
                    if retry < max_api_retries - 1:
                        await asyncio.sleep(2 * (retry + 1))  # 递增等待时间
                    else:
                        raise  # 最后一次重试失败则抛出异常

            if response and response.status_code == 200:
                keywords_text = response.output.text.strip()

                # 解析返回的关键词
                keywords = []
                lines = keywords_text.split('\n')

                for line in lines:
                    line = line.strip()
                    if not line:
                        continue

                    # 提取序号后的关键词
                    if '. ' in line:
                        keyword = line.split('. ', 1)[1].strip()
                    elif '.' in line and line[0].isdigit():
                        keyword = line.split('.', 1)[1].strip()
                    else:
                        keyword = line.strip()

                    # 清理关键词
                    keyword = self._clean_keyword(keyword)
                    if keyword:
                        keywords.append(keyword)

                # 确保关键词数量与字幕数量匹配
                while len(keywords) < len(subtitles):
                    keywords.append("内容")  # 默认关键词

                keywords = keywords[:len(subtitles)]  # 截取到字幕数量

                logger.debug(f"提取到关键词: {keywords}")
                return keywords
            else:
                logger.error(f"字幕关键词提取API调用失败: {response.status_code if response else 'No response'}")
                return ["内容"] * len(subtitles)

        except Exception as e:
            logger.error(f"字幕关键词提取异常: {e}")
            return ["内容"] * len(subtitles)

    def _clean_keyword(self, keyword):
        """清理和验证关键词"""
        if not keyword:
            return ""

        # 移除标点符号和多余空格
        keyword = re.sub(r'[，。！？；：""''（）【】\\s]+', '', keyword)

        # 过滤无意义的词汇
        meaningless_words = {'的', '了', '在', '是', '有', '说', '看', '着', '道', '中', '前', '个', '什么', '怎么'}
        if keyword in meaningless_words:
            return ""

        # 长度检查
        if len(keyword) < 1 or len(keyword) > 6:
            return ""

        return keyword[:4]  # 限制长度为4个字符

    def _filter_keywords(self, keywords):
        """过滤关键词"""
        filtered_keywords = []
        meaningless_words = {
            '看着', '说道', '心中', '眼前', '这个', '那个', '一个', '什么', '怎么',
            '非常', '很', '十分', '特别', '真的', '确实', '当然', '或许', '可能',
            '突然', '立刻', '马上', '然后', '接着', '于是', '因此', '所以',
            '但是', '不过', '只是', '而且', '并且', '或者', '还是', '就是',
            '现在', '刚才', '以前', '之前', '之后', '后来', '最后', '终于',
            '今天', '昨天', '明天', '今日', '昨日', '明日', '此时', '当时',
            '这里', '那里', '哪里', '到处', '处处', '各处', '别处', '何处',
            '太子', '皇帝', '大臣', '将军', '侍卫', '宫女', '太监', '奴才',
            '陛下', '殿下', '大人', '先生', '夫人', '公主', '王子', '皇后'
        }

        for keyword in keywords:
            if keyword and keyword not in meaningless_words and len(keyword) >= 2:
                filtered_keywords.append(keyword)

        return filtered_keywords[:10]  # 最多返回10个关键词

    def _convert_volcengine_subtitles(self, volcengine_subtitles, segment_id):
        """
        转换火山引擎字幕格式为项目格式

        Args:
            volcengine_subtitles: 火山引擎字幕列表
            segment_id: 段落ID

        Returns:
            转换后的字幕列表
        """
        converted_subtitles = []

        for subtitle in volcengine_subtitles:
            converted_subtitle = {
                "subtitle_id": subtitle.get('subtitle_id', 0),
                "text": subtitle.get('text', ''),
                "timestamp": subtitle.get('timestamp', ''),
                "duration": subtitle.get('duration', 0),
                "char_count": subtitle.get('char_count', 0),
                "start_time_s": subtitle.get('start_time', 0) / 1000.0,  # 转换为秒
                "end_time_s": subtitle.get('end_time', 0) / 1000.0,  # 转换为秒
                "words": subtitle.get('words', [])
            }
            converted_subtitles.append(converted_subtitle)

        return converted_subtitles

    async def process_single_segment(self, segment_index, segment_text, chapter_info, audio_dir, str_dir, segment_processing_semaphore):
        """处理单个段落（异步并发）"""
        async with segment_processing_semaphore:
            if not segment_text.strip():
                return None

            logger.info(f"📝 第{chapter_info['index']}章 - 段落 {segment_index + 1}")

            audio_filename = f"chapter{chapter_info['index']}_segment_{segment_index + 1:02d}.wav"
            audio_file = audio_dir / audio_filename

            # 字幕文件路径（与音频文件同名，但扩展名为.srt）
            subtitle_filename = f"chapter{chapter_info['index']}_segment_{segment_index + 1:02d}.srt"
            subtitle_file = str_dir / subtitle_filename

            # 生成音频和字幕
            audio_success, subtitle_content = await self.generate_audio_with_retry(segment_text, audio_file)

            # 保存字幕文件
            if audio_success and subtitle_content:
                try:
                    with open(subtitle_file, "w", encoding="utf-8") as f:
                        f.write(subtitle_content)
                    logger.debug(f"字幕文件保存成功: {subtitle_file.name}")

                    # 生成倍速字幕文件
                    if self.speed_factor != 1.0:
                        adjusted_subtitle_file = self.adjust_subtitle_timestamps(str(subtitle_file), self.speed_factor)
                        if adjusted_subtitle_file:
                            logger.debug(f"倍速字幕文件生成成功: {Path(adjusted_subtitle_file).name}")
                        else:
                            logger.warning(f"倍速字幕文件生成失败")

                except Exception as e:
                    logger.warning(f"字幕文件保存失败: {e}")

            # 上传音频并生成字幕
            audio_oss_url, subtitles, actual_audio_duration = await self.upload_audio_and_generate_subtitle(
                str(audio_file) if audio_success else None,
                segment_index + 1
            )

            # 提取段落级别关键词（用于统计）
            keywords = await self.extract_keywords(segment_text)

            # 为每条字幕提取关键词
            subtitle_keywords = await self.extract_keywords_for_subtitles(subtitles)

            # 将关键词分配给每条字幕
            for j, subtitle in enumerate(subtitles):
                if j < len(subtitle_keywords):
                    subtitle['keyword'] = subtitle_keywords[j]
                else:
                    subtitle['keyword'] = "内容"  # 默认关键词

            # 收集所有字幕的关键词
            all_keywords = [subtitle.get('keyword', '') for subtitle in subtitles if subtitle.get('keyword')]

            story_board = {
                "story_board_id": segment_index + 1,
                "story_board": segment_text,
                "content_segment": f"第{chapter_info['index']}章_段落{segment_index + 1}",
                "keywords": keywords,
                "all_keywords": all_keywords,
                "short_subtitles": subtitles,
                "subtitle_count": len(subtitles),
                "audio_file": str(audio_file) if audio_success else None,
                "audio_oss_url": audio_oss_url,
                "audio_generated": audio_success,
                "char_count": len(segment_text),
                "estimated_duration": len(segment_text) * 0.3,
                "audio_duration": actual_audio_duration,
                "avg_subtitle_length": sum(sub['char_count'] for sub in subtitles) / len(
                    subtitles) if subtitles else 0,
                "subtitle_source": "火山引擎" if self.use_volcengine_subtitle and audio_oss_url else "本地生成"
            }

            return story_board, audio_success

    def correct_chapter_subtitles(self, chapter_data, chapter_info):
        """
        智能字幕修正：优先原方法，检测到剩余字符时启用LCS补救

        Args:
            chapter_data: 章节数据字典
            chapter_info: 章节信息

        Returns:
            tuple: (修正后的数据, 修正统计)
        """
        if not self.enable_subtitle_correction:
            logger.info(f"第{chapter_info['index']}章：字幕修正功能已禁用")
            return chapter_data, {"corrections": 0, "story_boards": 0}

        # 简洁的控制台输出
        logger.info(f"🔧 第{chapter_info['index']}章：开始智能字幕修正...")

        chapter_corrections = 0
        corrected_story_boards = 0
        has_problem_subtitles = False  # 🆕 章节级别问题标记
        problem_story_boards = []      # 🆕 问题故事板列表
        detailed_log = []  # 详细日志，保存到JSON

        try:
            if "story_boards" in chapter_data and isinstance(chapter_data["story_boards"], list):
                for i, story_board in enumerate(chapter_data["story_boards"]):

                    # 记录处理前的状态
                    story_board_log = {
                        "story_board_index": i + 1,
                        "story_board_id": story_board.get("story_board_id", i + 1),
                        "original_story_board": story_board.get("story_board", "")[:100] + "..." if len(story_board.get("story_board", "")) > 100 else story_board.get("story_board", ""),
                        "original_subtitles_count": len(story_board.get("short_subtitles", [])),
                        "processing_stages": [],
                        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                    }

                    # 🆕 调用智能修正函数（新逻辑：字数相同修正，字数不同标记）
                    corrections = correct_subtitles_by_comparing_combined_text(story_board)

                    # 🆕 检查是否是问题字幕
                    if corrections == -1:  # 问题字幕
                        has_problem_subtitles = True
                        story_board_id = story_board.get('story_board_id', 'N/A')
                        problem_story_boards.append(story_board_id)

                        logger.warning(f"⚠️ 发现问题字幕: {story_board_id}")
                        logger.warning(f"   原因: {story_board.get('review_reason', 'N/A')}")
                        logger.warning(f"   字符差异: {story_board.get('char_difference', 'N/A')}")

                        # 记录问题信息到日志
                        story_board_log["is_problem_subtitle"] = True
                        story_board_log["review_reason"] = story_board.get('review_reason', 'N/A')
                        story_board_log["char_difference"] = story_board.get('char_difference', 0)
                        story_board_log["problem_severity"] = story_board.get('problem_severity', 'N/A')

                    # 🆕 检查是否被标记为需要人工审核（兼容旧逻辑）
                    elif story_board.get('needs_manual_review', False):
                        # 字数不同的情况，输出标识信息
                        story_board_id = story_board.get('story_board_id', 'N/A')
                        subtitle_ids = [sub.get('subtitle_id', 'N/A') for sub in story_board.get('short_subtitles', [])]

                        logger.warning(f"⚠️ 字数不匹配需要人工审核:")
                        logger.warning(f"   story_board_id: {story_board_id}")
                        logger.warning(f"   subtitle_ids: {subtitle_ids}")
                        logger.warning(f"   原因: {story_board.get('review_reason', 'N/A')}")

                        # 记录标记信息到日志
                        story_board_log["marked_for_review"] = True
                        story_board_log["review_reason"] = story_board.get('review_reason', 'N/A')
                        story_board_log["char_difference"] = story_board.get('char_difference', 0)

                    # 记录修正结果
                    story_board_log["corrections"] = corrections if corrections else 0
                    story_board_log["final_subtitles_count"] = len(story_board.get("short_subtitles", []))

                    detailed_log.append(story_board_log)

                    if corrections and corrections > 0:
                        chapter_corrections += corrections
                        corrected_story_boards += 1

            # 🆕 章节级别标记
            if has_problem_subtitles:
                chapter_data['has_problem_subtitles'] = True
                chapter_data['problem_story_boards'] = problem_story_boards
                logger.warning(f"🚨 第{chapter_info['index']}章包含问题字幕，不计入成功章节")
                logger.warning(f"   问题故事板: {', '.join(problem_story_boards)}")

                # 简洁的完成信息（问题章节）
                logger.info(f"⚠️ 第{chapter_info['index']}章：修正完成，共修正 {chapter_corrections} 处，但包含 {len(problem_story_boards)} 个问题字幕")
            else:
                # 简洁的完成信息（成功章节）
                logger.info(f"✅ 第{chapter_info['index']}章：修正完成，共修正 {chapter_corrections} 处")

            # 详细统计信息（保存到JSON）
            correction_stats = {
                "corrections": chapter_corrections,
                "story_boards": corrected_story_boards,
                "total_story_boards": len(chapter_data.get("story_boards", [])),
                "correction_rate": f"{(corrected_story_boards / len(chapter_data.get('story_boards', [1])) * 100):.1f}%",
                "is_successful": not has_problem_subtitles,  # 🆕 标记是否成功
                "has_problem_subtitles": has_problem_subtitles,  # 🆕 是否有问题字幕
                "problem_story_boards_count": len(problem_story_boards),  # 🆕 问题故事板数量
                "detailed_log": detailed_log,  # 详细修正过程
                "processing_summary": {
                    "total_story_boards": len(detailed_log),
                    "corrected_story_boards": corrected_story_boards,
                    "problem_story_boards": len(problem_story_boards),  # 🆕 问题故事板数
                    "unchanged_story_boards": len(detailed_log) - corrected_story_boards - len(problem_story_boards),
                    "average_corrections_per_story_board": round(chapter_corrections / len(detailed_log), 2) if detailed_log else 0
                }
            }

            return chapter_data, correction_stats

        except Exception as e:
            logger.error(f"❌ 第{chapter_info['index']}章：修正失败 - {e}")
            return chapter_data, {"corrections": 0, "story_boards": 0, "error": str(e), "detailed_log": []}

    def _capture_correction_details(self, story_board, story_board_index):
        """
        捕获单个故事板的详细修正过程（预留接口）
        """
        return {
            "story_board_index": story_board_index,
            "story_board_id": story_board.get("story_board_id", story_board_index),
            "processing_stages": [],
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }

    def _update_correction_stats(self, chapter_info, correction_stats):
        """🆕 更新修正统计信息（区分成功章节和问题章节）"""
        self.correction_stats["total_chapters"] += 1

        # 🆕 区分成功章节和问题章节
        if correction_stats.get("is_successful", True):
            # 成功章节：没有问题字幕
            if correction_stats.get("corrections", 0) > 0:
                self.correction_stats["corrected_chapters"] += 1
        else:
            # 问题章节：包含问题字幕，不计入成功章节
            pass

        self.correction_stats["total_corrections"] += correction_stats.get("corrections", 0)

        # 记录章节详细信息（包含详细日志）
        chapter_detail = {
            "chapter_index": chapter_info['index'],
            "chapter_title": chapter_info['title'],
            "corrections": correction_stats.get("corrections", 0),
            "corrected_story_boards": correction_stats.get("story_boards", 0),
            "total_story_boards": correction_stats.get("total_story_boards", 0),
            "correction_rate": correction_stats.get("correction_rate", "0%"),
            "is_successful": correction_stats.get("is_successful", True),  # 🆕 是否成功
            "has_problem_subtitles": correction_stats.get("has_problem_subtitles", False),  # 🆕 是否有问题字幕
            "problem_story_boards_count": correction_stats.get("problem_story_boards_count", 0),  # 🆕 问题故事板数
            "processing_summary": correction_stats.get("processing_summary", {}),
            "detailed_log": correction_stats.get("detailed_log", []),  # 详细修正过程
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }

        if "error" in correction_stats:
            chapter_detail["error"] = correction_stats["error"]

        self.correction_stats["chapter_details"].append(chapter_detail)

    def generate_correction_report(self):
        """生成字幕修正报告"""
        if not self.enable_subtitle_correction:
            return None

        # 🆕 统计详细信息（包含问题章节）
        total_story_boards = sum(detail.get("total_story_boards", 0) for detail in self.correction_stats["chapter_details"])
        total_corrected_story_boards = sum(detail.get("corrected_story_boards", 0) for detail in self.correction_stats["chapter_details"])

        # 🆕 统计问题章节
        problem_chapters = []
        total_problem_story_boards = 0

        for detail in self.correction_stats["chapter_details"]:
            if not detail.get("is_successful", True):
                problem_chapters.append({
                    "chapter_index": detail.get("chapter_index"),
                    "chapter_title": detail.get("chapter_title"),
                    "problem_story_boards": detail.get("problem_story_boards_count", 0)
                })
                total_problem_story_boards += detail.get("problem_story_boards_count", 0)

        # 统计处理策略使用情况（从详细日志中分析）
        strategy_stats = {
            "original_method_success": 0,
            "lcs_rescue_used": 0,
            "completion_added": 0
        }

        for detail in self.correction_stats["chapter_details"]:
            summary = detail.get("processing_summary", {})
            strategy_stats["original_method_success"] += summary.get("unchanged_story_boards", 0)
            # 其他策略统计可以从detailed_log中进一步分析

        report = {
            "subtitle_correction_summary": {
                "enabled": True,
                "version": "智能LCS增强版",
                "total_chapters_processed": self.correction_stats["total_chapters"],
                "chapters_with_corrections": self.correction_stats["corrected_chapters"],
                "problem_chapters_count": len(problem_chapters),  # 🆕 问题章节数
                "total_corrections_made": self.correction_stats["total_corrections"],
                "total_story_boards_processed": total_story_boards,
                "total_story_boards_corrected": total_corrected_story_boards,
                "total_problem_story_boards": total_problem_story_boards,  # 🆕 问题故事板总数
                "chapter_correction_rate": f"{(self.correction_stats['corrected_chapters'] / max(self.correction_stats['total_chapters'], 1) * 100):.1f}%",
                "story_board_correction_rate": f"{(total_corrected_story_boards / max(total_story_boards, 1) * 100):.1f}%",
                "average_corrections_per_chapter": f"{(self.correction_stats['total_corrections'] / max(self.correction_stats['total_chapters'], 1)):.1f}",
                "average_corrections_per_story_board": f"{(self.correction_stats['total_corrections'] / max(total_story_boards, 1)):.2f}",
                "processing_strategy_stats": strategy_stats
            },
            "problem_chapters": problem_chapters,  # 🆕 问题章节详情
            "chapter_details": self.correction_stats["chapter_details"]
        }

        return report

    async def process_single_chapter(self, chapter_info, content, all_chapters, output_dir):
        """处理单个章节"""
        logger.info(f"开始处理第 {chapter_info['index']} 章: {chapter_info['full_title']}")

        try:
            chapter_content = self.extract_chapter_content(content, chapter_info, all_chapters)
            if not chapter_content.strip():
                logger.warning(f"第 {chapter_info['index']} 章内容为空，跳过")
                return None

            cleaned_content = await self.clean_content_with_llm(chapter_content)

            # 🎯 新的分段和重写流程
            logger.info(f"🎯 第{chapter_info['index']}章开始智能分段和重写流程")
            segments = self.split_text_into_segments(cleaned_content)

            chapter_dir = output_dir / f"chapter{chapter_info['index']}_{chapter_info['title']}"
            audio_dir = chapter_dir / "audio_segments"
            str_dir = chapter_dir / "str_segments"

            for dir_path in [chapter_dir, audio_dir, str_dir]:
                dir_path.mkdir(parents=True, exist_ok=True)

            logger.info(f"🎵 第四阶段：生成音频和字幕")
            logger.info(f"📚 第{chapter_info['index']}章：开始并发处理 {len(segments)} 个段落")

            # 创建所有段落处理任务
            tasks = []
            segment_processing_semaphore = asyncio.Semaphore(2)  # 🆕 最多2个段落并发处理（避免Edge-TTS过载）
            for i, segment_text in enumerate(segments):
                task = self.process_single_segment(i, segment_text, chapter_info, audio_dir, str_dir, segment_processing_semaphore)
                tasks.append(task)

            # 并发执行所有段落处理任务
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果并保持原始顺序
            story_boards = []
            successful_audio_count = 0

            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"段落 {i + 1} 处理失败: {result}")
                    continue

                if result is None:  # 空段落
                    continue

                story_board, audio_success = result
                story_boards.append(story_board)

                if audio_success:
                    successful_audio_count += 1

            # 确保story_boards按照story_board_id排序（保持原始顺序）
            story_boards.sort(key=lambda x: x['story_board_id'])

            logger.info(f"✅ 第{chapter_info['index']}章段落处理完成：{len(story_boards)} 个段落，{successful_audio_count} 个音频成功")

            # 生成章节JSON文件
            chapter_json = {
                "chapter_info": {
                    "chapter_index": chapter_info['index'],
                    "chapter_title": chapter_info['full_title'],
                    "total_segments": len(story_boards),
                    "total_subtitles": sum(sb['subtitle_count'] for sb in story_boards),
                    "total_keywords": sum(len(sb['keywords']) for sb in story_boards),
                    "total_all_keywords": sum(len(sb['all_keywords']) for sb in story_boards),  # 新增：所有字幕关键词总数
                    "estimated_total_duration": sum(sb['estimated_duration'] for sb in story_boards),
                    "original_length": len(chapter_content),
                    "cleaned_length": len(cleaned_content),
                    "avg_subtitle_length": sum(sb['avg_subtitle_length'] for sb in story_boards) / len(
                        story_boards) if story_boards else 0
                },
                "story_boards": story_boards
            }

            # 🆕 字幕错别字修正
            corrected_data, correction_stats = self.correct_chapter_subtitles(chapter_json, chapter_info)

            # 更新修正统计信息
            self._update_correction_stats(chapter_info, correction_stats)

            json_file = chapter_dir / f"chapter{chapter_info['index']}_story_boards.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(corrected_data, f, ensure_ascii=False, indent=2)

            # 生成章节报告
            total_subtitles = sum(sb['subtitle_count'] for sb in story_boards)
            total_keywords = sum(len(sb['keywords']) for sb in story_boards)
            total_all_keywords = sum(len(sb['all_keywords']) for sb in story_boards)  # 新增：统计所有字幕关键词
            avg_subtitle_length = sum(sb['avg_subtitle_length'] for sb in story_boards) / len(
                story_boards) if story_boards else 0

            chapter_report = {
                "处理时间": time.strftime("%Y-%m-%d %H:%M:%S"),
                "章节信息": {
                    "章节序号": chapter_info['index'],
                    "章节标题": chapter_info['full_title'],
                    "原始长度": len(chapter_content),
                    "清洗后长度": len(cleaned_content)
                },
                "处理结果": {
                    "分割段落数": len(story_boards),
                    "总字幕数": total_subtitles,
                    "总关键词数": total_keywords,
                    "总字幕关键词数": total_all_keywords,  # 新增：所有字幕关键词总数
                    "音频成功数": successful_audio_count,
                    "音频成功率": f"{successful_audio_count / len(story_boards) * 100:.1f}%" if story_boards else "0%",
                    "平均字幕长度": f"{avg_subtitle_length:.1f}字"
                },
                "输出文件": {
                    "JSON文件": str(json_file),
                    "音频目录": str(audio_dir),
                    "字幕目录": str(str_dir)
                },
                "字幕修正": {
                    "修正数量": correction_stats.get("corrections", 0),
                    "修正故事板数": correction_stats.get("story_boards", 0),
                    "总故事板数": correction_stats.get("total_story_boards", 0),
                    "修正率": correction_stats.get("correction_rate", "0%"),
                    "修正状态": "智能LCS增强版" if self.enable_subtitle_correction else "已禁用"
                }
            }

            report_file = chapter_dir / f"chapter{chapter_info['index']}_report.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(chapter_report, f, ensure_ascii=False, indent=2)

            # 🆕 保存错误报告
            self.save_chapter_report(chapter_info, chapter_dir)

            logger.info(f"第 {chapter_info['index']} 章处理完成")
            logger.info(f"  - 段落数: {len(story_boards)}")
            logger.info(f"  - 字幕数: {total_subtitles}")
            logger.info(f"  - 关键词数: {total_keywords}")
            logger.info(f"  - 字幕关键词数: {total_all_keywords}")  # 新增：显示字幕关键词统计
            logger.info(
                f"  - 音频成功率: {successful_audio_count}/{len(story_boards)} ({successful_audio_count / len(story_boards) * 100:.1f}%)")
            logger.info(f"  - 平均字幕长度: {avg_subtitle_length:.1f}字")
            logger.info(f"  - 字幕修正: {correction_stats.get('corrections', 0)} 处 (修正率: {correction_stats.get('correction_rate', '0%')})")

            return chapter_report

        except Exception as e:
            logger.error(f"第 {chapter_info['index']} 章处理失败: {e}")
            return None

    def calculate_chapter_skip_strategy(self, total_chapters, target_min=60, target_max=70):
        """计算章节跳过策略，确保最终章节数在目标范围内

        Args:
            total_chapters: 总章节数
            target_min: 目标最小章节数
            target_max: 目标最大章节数

        Returns:
            dict: 包含跳过策略的字典
        """
        if total_chapters <= target_max:
            # 如果总章节数不超过目标最大值，处理所有章节
            return {
                'strategy': 'all',
                'skip_interval': 0,
                'final_count': total_chapters,
                'selected_indices': list(range(total_chapters)),
                'description': f"总章节数({total_chapters})不超过{target_max}，处理所有章节"
            }

        # 计算最优的跳过策略
        # 目标是选择尽可能接近target_max的章节数，但不超过
        best_strategy = None
        best_count = 0

        # 尝试不同的跳过间隔
        for skip_interval in range(total_chapters // target_max, total_chapters // target_min + 1):
            selected_indices = []

            # 使用固定间隔跳过策略
            for i in range(total_chapters):
                # 每隔skip_interval章跳过1章
                if i % skip_interval == 0:
                    selected_indices.append(i)

                # 如果已经达到目标最大数量，停止选择
                if len(selected_indices) >= target_max:
                    break

            # 检查这个策略是否更好
            count = len(selected_indices)
            if target_min <= count <= target_max and count > best_count:
                best_strategy = {
                    'strategy': 'skip',
                    'skip_interval': skip_interval,
                    'final_count': count,
                    'selected_indices': selected_indices,
                    'description': f"总章节数({total_chapters})超过{target_max}，每隔{skip_interval}章跳过1章，最终处理{count}章"
                }
                best_count = count

        # 如果没有找到合适的策略，使用简单的等间距选择
        if best_strategy is None:
            step = total_chapters / target_max
            selected_indices = []
            for i in range(target_max):
                idx = int(i * step)
                if idx < total_chapters and idx not in selected_indices:
                    selected_indices.append(idx)

            best_strategy = {
                'strategy': 'uniform',
                'skip_interval': step,
                'final_count': len(selected_indices),
                'selected_indices': selected_indices,
                'description': f"总章节数({total_chapters})超过{target_max}，使用等间距选择，最终处理{len(selected_indices)}章"
            }

        return best_strategy

    def _get_original_chapter_range(self, all_chapters):
        """获取原始章节列表的选择"""
        print("\n" + "=" * 60)
        print("📚 章节列表")
        print("=" * 60)

        # 显示所有章节
        for i, chapter in enumerate(all_chapters, 1):
            print(f"{i:3d}. {chapter['full_title']}")

        print("\n" + "=" * 60)
        print("请选择要处理的章节范围:")
        print("格式说明:")
        print("  - 单个章节: 5")
        print("  - 连续章节: 1-10")
        print("  - 多个范围: 1-5,8,10-15")
        print("  - 处理全部: all")
        print("  - 返回推荐列表: back")
        print("=" * 60)

        while True:
            try:
                user_input = input("请输入章节范围: ").strip()

                if user_input.lower() == 'back':
                    return None  # 返回None表示回到推荐列表

                if user_input.lower() == 'all':
                    selected_indices = list(range(len(all_chapters)))
                    print(f"⚠️ 警告: 选择了所有 {len(all_chapters)} 个章节，这可能超出推荐的50-60章范围")
                else:
                    selected_indices = []

                    # 解析用户输入
                    parts = user_input.split(',')
                    for part in parts:
                        part = part.strip()
                        if '-' in part:
                            # 范围输入
                            start, end = part.split('-')
                            start_idx = int(start.strip()) - 1
                            end_idx = int(end.strip()) - 1
                            if 0 <= start_idx <= end_idx < len(all_chapters):
                                selected_indices.extend(range(start_idx, end_idx + 1))
                            else:
                                raise ValueError(f"章节范围 {part} 超出有效范围")
                        else:
                            # 单个章节
                            idx = int(part) - 1
                            if 0 <= idx < len(all_chapters):
                                selected_indices.append(idx)
                            else:
                                raise ValueError(f"章节 {part} 超出有效范围")

                    # 去重并排序
                    selected_indices = sorted(list(set(selected_indices)))

                if not selected_indices:
                    print("❌ 未选择任何章节，请重新输入")
                    continue

                # 确认选择
                print(f"\n✅ 已选择 {len(selected_indices)} 个章节:")
                for idx in selected_indices:
                    print(f"  {idx + 1}. {all_chapters[idx]['full_title']}")

                confirm = input("\n确认处理这些章节吗？(y/n): ").strip().lower()
                if confirm in ['y', 'yes', '是']:
                    return selected_indices
                else:
                    print("请重新选择章节范围")

            except ValueError as e:
                print(f"❌ 输入格式错误: {e}")
                print("请按照格式重新输入")
            except KeyboardInterrupt:
                print("\n用户取消操作")
                return []

    def get_chapter_range(self, all_chapters):
        """获取用户选择的章节范围"""
        print("\n" + "=" * 60)
        print("📚 智能章节选择")
        print("=" * 60)

        total_chapters = len(all_chapters)
        print(f"原始总章节数: {total_chapters}")

        # 计算自动跳过策略
        skip_strategy = self.calculate_chapter_skip_strategy(total_chapters)

        print(f"\n🤖 智能筛选结果:")
        print(f"   {skip_strategy['description']}")

        # 获取智能选择后的章节列表
        recommended_indices = skip_strategy['selected_indices']
        recommended_chapters = [all_chapters[i] for i in recommended_indices]

        print(f"\n📖 推荐章节列表 (共{len(recommended_chapters)}章):")
        print("=" * 60)

        # 显示推荐的章节列表
        for i, chapter_idx in enumerate(recommended_indices):
            chapter = all_chapters[chapter_idx]
            print(f"{i + 1:3d}. 第{chapter_idx + 1}章 - {chapter['full_title']}")

        print("\n" + "=" * 60)
        print("请选择要处理的章节:")
        print("格式说明:")
        print("  - 单个章节: 5 (表示推荐列表中的第5章)")
        print("  - 连续章节: 1-10 (表示推荐列表中的第1-10章)")
        print("  - 多个范围: 1-5,8,10-15")
        print("  - 处理全部推荐: all (处理所有推荐章节)")
        print("  - 查看原始列表: original (切换到原始章节列表)")
        print("=" * 60)

        while True:
            try:
                user_input = input("请输入选择: ").strip()

                if user_input.lower() == 'all':
                    # 处理所有推荐章节
                    selected_indices = recommended_indices
                    print(f"\n✅ 选择处理所有推荐的 {len(selected_indices)} 个章节")

                elif user_input.lower() == 'original':
                    # 切换到原始章节列表模式
                    original_result = self._get_original_chapter_range(all_chapters)
                    if original_result is None:
                        # 用户选择返回推荐列表，重新显示推荐列表
                        print("\n返回推荐章节列表...")
                        continue
                    else:
                        return original_result

                else:
                    # 在推荐列表中选择章节
                    selected_recommended_indices = []

                    # 解析用户输入
                    parts = user_input.split(',')
                    for part in parts:
                        part = part.strip()
                        if '-' in part:
                            # 范围输入
                            start, end = part.split('-')
                            start_idx = int(start.strip()) - 1
                            end_idx = int(end.strip()) - 1
                            if 0 <= start_idx <= end_idx < len(recommended_indices):
                                selected_recommended_indices.extend(range(start_idx, end_idx + 1))
                            else:
                                raise ValueError(f"章节范围 {part} 超出推荐列表范围 (1-{len(recommended_indices)})")
                        else:
                            # 单个章节
                            idx = int(part) - 1
                            if 0 <= idx < len(recommended_indices):
                                selected_recommended_indices.append(idx)
                            else:
                                raise ValueError(f"章节 {part} 超出推荐列表范围 (1-{len(recommended_indices)})")

                    # 去重并排序
                    selected_recommended_indices = sorted(list(set(selected_recommended_indices)))

                    if not selected_recommended_indices:
                        print("❌ 未选择任何章节，请重新输入")
                        continue

                    # 将推荐列表中的索引转换为原始章节索引
                    selected_indices = [recommended_indices[i] for i in selected_recommended_indices]

                # 显示选中的章节
                print(f"\n✅ 已选择 {len(selected_indices)} 个章节:")
                for i, original_idx in enumerate(selected_indices):
                    chapter = all_chapters[original_idx]
                    print(f"  {i + 1}. 第{original_idx + 1}章 - {chapter['full_title']}")
                    if i >= 9 and len(selected_indices) > 10:  # 只显示前10个
                        remaining = len(selected_indices) - 10
                        print(f"  ... 还有 {remaining} 个章节")
                        break

                # 给出处理建议
                if len(selected_indices) > 60:
                    print(f"⚠️ 警告: 选择了 {len(selected_indices)} 个章节，超出推荐的50-60章范围")
                elif len(selected_indices) < 50 and total_chapters >= 50:
                    print(f"ℹ️ 提示: 选择了 {len(selected_indices)} 个章节，少于推荐的50-60章范围")
                else:
                    print(f"✅ 章节数量合适 ({len(selected_indices)}章)")

                confirm = input(f"\n确认处理这 {len(selected_indices)} 个章节吗？(y/n): ").strip().lower()
                if confirm in ['y', 'yes', '是']:
                    return selected_indices
                else:
                    print("请重新选择章节")

            except ValueError as e:
                print(f"❌ 输入格式错误: {e}")
                print("请按照格式重新输入")
            except KeyboardInterrupt:
                print("\n用户取消操作")
                return []

    async def process_novel(self, input_file, output_dir=None):
        """处理整本小说"""
        logger.info(f"开始处理小说: {input_file}")

        input_path = Path(input_file)
        if not input_path.exists():
            logger.error(f"输入文件不存在: {input_file}")
            raise FileNotFoundError(f"输入文件不存在: {input_file}")

        content, encoding = self.detect_encoding(input_file)
        all_chapters = self.find_all_chapters(content)

        if not all_chapters:
            logger.error("未找到任何章节")
            raise ValueError("未找到任何章节")

        # 获取用户选择的章节范围
        selected_indices = self.get_chapter_range(all_chapters)
        if not selected_indices:
            logger.info("用户取消操作")
            return None

        # 根据选择筛选章节
        selected_chapters = [all_chapters[i] for i in selected_indices]

        if output_dir is None:
            output_dir = self.output_base_dir / input_path.stem

        output_base_dir = Path(output_dir)
        output_base_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"输出目录: {output_base_dir}")

        # 设置文件日志记录器
        file_handler = logging.FileHandler(
            output_base_dir / "processing.log",
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

        all_reports = []
        success_count = 0
        start_time = time.time()

        logger.info(f"开始并发处理 {len(selected_chapters)} 个选中章节，并发量: 3")

        # 🆕 创建信号量控制并发数量（降低并发避免Edge-TTS过载）
        semaphore = asyncio.Semaphore(3)

        async def process_chapter_with_semaphore(chapter_info, chapter_index, total_selected):
            """带信号量控制的章节处理函数"""
            async with semaphore:
                logger.info(f"\n{'=' * 60}")
                logger.info(
                    f"开始处理: 第{chapter_index}/{total_selected}章 ({chapter_index / total_selected * 100:.1f}%)")
                logger.info(f"章节: {chapter_info['full_title']}")
                logger.info(f"{'=' * 60}")

                try:
                    report = await self.process_single_chapter(chapter_info, content, all_chapters, output_base_dir)
                    if report:
                        logger.info(f"第{chapter_index}章处理成功: {chapter_info['full_title']}")
                        return report, True
                    else:
                        logger.error(f"第{chapter_index}章处理失败: {chapter_info['full_title']}")
                        return None, False
                except Exception as e:
                    logger.error(f"第{chapter_index}章处理异常: {chapter_info['full_title']} - {e}")
                    return None, False

        # 创建选中章节的处理任务
        tasks = [
            process_chapter_with_semaphore(chapter_info, i, len(selected_chapters))
            for i, chapter_info in enumerate(selected_chapters, 1)
        ]

        # 并发执行所有任务
        logger.info("开始并发执行章节处理任务...")
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 🆕 处理结果（区分成功章节和问题章节）
        problem_chapters = []  # 🆕 记录问题章节

        for i, result in enumerate(results, 1):
            if isinstance(result, Exception):
                logger.error(f"第{i}章处理出现异常: {result}")
                continue

            report, success = result
            if success and report:
                all_reports.append(report)

                # 🆕 检查是否有问题字幕
                chapter_detail = None
                for detail in self.correction_stats["chapter_details"]:
                    if detail.get("chapter_index") == i:
                        chapter_detail = detail
                        break

                if chapter_detail and not chapter_detail.get("is_successful", True):
                    # 有问题字幕的章节，不计入成功章节
                    problem_chapters.append({
                        "chapter_index": i,
                        "chapter_title": chapter_detail.get("chapter_title", "N/A"),
                        "problem_story_boards": chapter_detail.get("problem_story_boards_count", 0)
                    })
                    logger.warning(f"第{i}章包含问题字幕，不计入成功章节")
                else:
                    # 正常成功的章节
                    success_count += 1

        # 生成总体报告
        end_time = time.time()
        processing_time = end_time - start_time

        logger.info("\n" + "=" * 60)
        logger.info("开始生成总体报告")
        logger.info("=" * 60)

        total_segments = sum(report['处理结果']['分割段落数'] for report in all_reports)
        total_subtitles = sum(report['处理结果']['总字幕数'] for report in all_reports)
        total_keywords = sum(report['处理结果']['总关键词数'] for report in all_reports)
        total_all_keywords = sum(report['处理结果']['总字幕关键词数'] for report in all_reports)  # 新增：统计所有字幕关键词
        total_audio_success = sum(report['处理结果']['音频成功数'] for report in all_reports)
        avg_subtitle_length = sum(
            float(report['处理结果']['平均字幕长度'].replace('字', '')) for report in all_reports) / len(
            all_reports) if all_reports else 0

        # 🆕 生成字幕修正总体报告
        correction_report = self.generate_correction_report()

        overall_report = {
            "处理时间": time.strftime("%Y-%m-%d %H:%M:%S"),
            "处理耗时": f"{processing_time:.2f}秒",
            "输入文件": str(input_file),
            "文件编码": encoding,
            "总体统计": {
                "总章节数": len(all_chapters),
                "选中章节数": len(selected_chapters),
                "处理章节数": len(selected_chapters),
                "成功章节数": success_count,  # 🆕 不包含问题章节
                "问题章节数": len(problem_chapters),  # 🆕 新增问题章节统计
                "成功率": f"{success_count / len(selected_chapters) * 100:.1f}%" if selected_chapters else "0%",
                "总段落数": total_segments,
                "总字幕数": total_subtitles,
                "总关键词数": total_keywords,
                "总字幕关键词数": total_all_keywords,  # 新增：所有字幕关键词总数
                "总音频成功数": total_audio_success,
                "平均字幕长度": f"{avg_subtitle_length:.1f}字"
            },
            "字幕修正": correction_report,  # 🆕 新增修正报告
            "章节详情": all_reports,
            "输出目录": str(output_base_dir)
        }

        overall_report_file = output_base_dir / "overall_report.json"
        with open(overall_report_file, 'w', encoding='utf-8') as f:
            json.dump(overall_report, f, ensure_ascii=False, indent=2)

        # 输出最终统计
        logger.info("\n" + "=" * 60)
        logger.info("处理完成！最终统计")
        logger.info("=" * 60)
        logger.info(f"处理耗时: {processing_time:.2f}秒")
        logger.info(f"总章节数: {len(all_chapters)}")
        logger.info(f"成功章节数: {success_count}")
        logger.info(f"成功率: {success_count / len(all_chapters) * 100:.1f}%")
        logger.info(f"总段落数: {total_segments}")
        logger.info(f"总字幕数: {total_subtitles}")
        logger.info(f"总关键词数: {total_keywords}")
        logger.info(f"总字幕关键词数: {total_all_keywords}")  # 新增：显示字幕关键词统计
        logger.info(f"总音频成功数: {total_audio_success}")
        logger.info(f"平均字幕长度: {avg_subtitle_length:.1f}字")

        # 🆕 简化的字幕修正统计
        if correction_report:
            logger.info(f"🧠 智能字幕修正统计:")
            logger.info(f"  - 总修正数: {correction_report['subtitle_correction_summary']['total_corrections_made']}")
            logger.info(f"  - 修正章节数: {correction_report['subtitle_correction_summary']['chapters_with_corrections']}")
            logger.info(f"  - 章节修正率: {correction_report['subtitle_correction_summary']['chapter_correction_rate']}")
            logger.info(f"  - 详细信息已保存到JSON报告中")

        logger.info(f"输出目录: {output_base_dir}")
        logger.info(f"总体报告: {overall_report_file}")

        return overall_report


async def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("使用方法: python chapter_selector.py <小说文件路径>")
        print("示例: python src/novel/chapter_selector.py novel/大唐太子：开局硬刚李世民.txt")
        return 1

    input_file = sys.argv[1]

    try:
        # 🆕 配置字幕服务重试机制
        subtitle_retry_config = {
            "max_retries": 3,           # 最大重试3次
            "base_timeout": 15,         # 基础超时15秒
            "timeout_increment": 5,     # 每次重试增加5秒超时
            "enable_retry": True        # 启用重试机制
        }

        # 创建处理器，设置日志级别为INFO，启用字幕修正和重试机制
        processor = NovelProcessor(
            enable_subtitle_correction=True,
            subtitle_retry_config=subtitle_retry_config
        )

        # 处理小说
        report = await processor.process_novel(input_file)

        print("\n" + "=" * 60)
        print("🎉 处理完成！")
        print("=" * 60)
        print(f"总章节数: {report['总体统计']['总章节数']}")
        print(f"成功章节数: {report['总体统计']['成功章节数']}")  # 🆕 不包含问题章节
        print(f"问题章节数: {report['总体统计']['问题章节数']}")  # 🆕 新增问题章节显示
        print(f"成功率: {report['总体统计']['成功率']}")
        print(f"输出目录: {report['输出目录']}")
        print(f"详细日志: {Path(report['输出目录']) / 'processing.log'}")

        # 🆕 如果有问题章节，显示详细信息
        if report['总体统计']['问题章节数'] > 0:
            print("\n⚠️ 问题章节详情:")
            # 这里可以从correction_report中获取问题章节信息
            correction_report = report.get('字幕修正', {})
            if correction_report and 'problem_chapters' in correction_report:
                for problem in correction_report['problem_chapters']:
                    print(f"   第{problem['chapter_index']}章: {problem['chapter_title']} "
                          f"({problem['problem_story_boards']} 个问题故事板)")
            print("   建议: 检查这些章节的字幕生成质量")

        return 0

    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return 1



if __name__ == "__main__":
    # python src/novel/chapter_selector.py novel/大唐太子：开局硬刚李世民.txt
    sys.exit(asyncio.run(main()))