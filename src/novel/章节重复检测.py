#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
章节重复检测工具
列出所有章节名字并检测重复的章节
"""

import re
import chardet
from pathlib import Path
from collections import Counter, defaultdict

class ChapterDuplicateChecker:
    """章节重复检测器"""
    
    def __init__(self):
        # 章节标题的正则表达式模式（更严格的匹配）
        self.chapter_patterns = [
            r'^第\s*(\d+)\s*章\s*(.*)$',  # 第X章 标题
            r'^第\s*([一二三四五六七八九十百千万]+)\s*章\s*(.*)$',  # 第X章 标题（中文数字）
        ]
    
    def detect_encoding(self, file_path):
        """检测文件编码"""
        print(f"检测文件编码: {file_path}")
        
        with open(file_path, 'rb') as f:
            raw_data = f.read()
        
        result = chardet.detect(raw_data)
        encoding = result['encoding']
        
        # 尝试多种编码
        encodings_to_try = [encoding, 'utf-8', 'gbk', 'gb2312', 'big5']
        
        for enc in encodings_to_try:
            if enc:
                try:
                    with open(file_path, 'r', encoding=enc) as f:
                        content = f.read()
                    print(f"成功使用编码 {enc} 读取文件")
                    return content, enc
                except (UnicodeDecodeError, UnicodeError):
                    continue
        
        raise ValueError("无法确定文件编码")
    
    def chinese_to_arabic(self, chinese_num):
        """将中文数字转换为阿拉伯数字"""
        chinese_digits = {
            '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
            '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
            '百': 100, '千': 1000, '万': 10000
        }
        
        if chinese_num.isdigit():
            return int(chinese_num)
        
        # 简单的中文数字转换
        if chinese_num in chinese_digits:
            return chinese_digits[chinese_num]
        
        # 处理十几、几十等
        if '十' in chinese_num:
            if chinese_num == '十':
                return 10
            elif chinese_num.startswith('十'):
                return 10 + chinese_digits.get(chinese_num[1], 0)
            elif chinese_num.endswith('十'):
                return chinese_digits.get(chinese_num[0], 0) * 10
            else:
                parts = chinese_num.split('十')
                if len(parts) == 2:
                    tens = chinese_digits.get(parts[0], 0) * 10
                    ones = chinese_digits.get(parts[1], 0)
                    return tens + ones
        
        return chinese_num
    
    def extract_chapters(self, file_path):
        """提取章节信息"""
        print("=" * 60)
        print("开始提取章节信息")
        print("=" * 60)
        
        # 读取文件
        content, encoding = self.detect_encoding(file_path)
        
        # 按行分割
        lines = content.split('\n')
        print(f"文件总行数: {len(lines)}")
        
        chapters = []
        
        # 遍历每一行，查找章节标题
        for line_num, line in enumerate(lines, 1):
            original_line = line
            line = line.strip()
            if not line:
                continue
            
            # 尝试每个章节模式
            for pattern in self.chapter_patterns:
                match = re.match(pattern, line)
                if match:
                    chapter_num = match.group(1)
                    chapter_title = match.group(2).strip() if len(match.groups()) > 1 else ""
                    
                    # 转换中文数字
                    if not chapter_num.isdigit():
                        arabic_num = self.chinese_to_arabic(chapter_num)
                        if isinstance(arabic_num, int):
                            chapter_num = str(arabic_num)
                    
                    # 验证章节编号是否合理
                    if chapter_num.isdigit():
                        chapter_index = int(chapter_num)
                        if chapter_index < 1 or chapter_index > 500:  # 假设最多500章
                            continue
                        
                        chapter_info = {
                            'index': chapter_index,
                            'title': chapter_title,
                            'full_title': line,
                            'line_number': line_num,
                            'original_line': original_line.strip()
                        }
                        
                        chapters.append(chapter_info)
                        break
        
        # 按章节编号排序
        chapters.sort(key=lambda x: x['index'])
        
        return chapters, encoding
    
    def check_duplicates(self, chapters):
        """检查重复章节"""
        print("\n" + "=" * 60)
        print("检查重复章节")
        print("=" * 60)
        
        # 按章节编号分组
        chapters_by_index = defaultdict(list)
        for chapter in chapters:
            chapters_by_index[chapter['index']].append(chapter)
        
        # 按章节标题分组
        chapters_by_title = defaultdict(list)
        for chapter in chapters:
            if chapter['title']:  # 只检查有标题的章节
                chapters_by_title[chapter['title']].append(chapter)
        
        # 找出重复的章节编号
        duplicate_indices = []
        for index, chapter_list in chapters_by_index.items():
            if len(chapter_list) > 1:
                duplicate_indices.append((index, chapter_list))
        
        # 找出重复的章节标题
        duplicate_titles = []
        for title, chapter_list in chapters_by_title.items():
            if len(chapter_list) > 1:
                duplicate_titles.append((title, chapter_list))
        
        return duplicate_indices, duplicate_titles
    
    def display_results(self, chapters, duplicate_indices, duplicate_titles):
        """显示结果"""
        print(f"\n✅ 总共找到 {len(chapters)} 个章节")
        
        # 显示所有章节
        print("\n" + "=" * 60)
        print("所有章节列表")
        print("=" * 60)
        
        for chapter in chapters:
            title_display = chapter['title'] if chapter['title'] else "(无标题)"
            print(f"第{chapter['index']:3d}章: {title_display}")
        
        # 显示重复的章节编号
        if duplicate_indices:
            print("\n" + "=" * 60)
            print("⚠️  重复的章节编号")
            print("=" * 60)
            
            for index, chapter_list in duplicate_indices:
                print(f"\n第{index}章 (出现 {len(chapter_list)} 次):")
                for i, chapter in enumerate(chapter_list, 1):
                    title_display = chapter['title'] if chapter['title'] else "(无标题)"
                    print(f"  {i}. 行{chapter['line_number']:4d}: {title_display}")
        else:
            print("\n✅ 没有发现重复的章节编号")
        
        # 显示重复的章节标题
        if duplicate_titles:
            print("\n" + "=" * 60)
            print("⚠️  重复的章节标题")
            print("=" * 60)
            
            for title, chapter_list in duplicate_titles:
                print(f"\n标题: '{title}' (出现 {len(chapter_list)} 次):")
                for i, chapter in enumerate(chapter_list, 1):
                    print(f"  {i}. 第{chapter['index']}章 (行{chapter['line_number']})")
        else:
            print("\n✅ 没有发现重复的章节标题")
        
        # 检查章节编号连续性
        print("\n" + "=" * 60)
        print("章节编号连续性检查")
        print("=" * 60)
        
        if chapters:
            indices = [ch['index'] for ch in chapters]
            min_index = min(indices)
            max_index = max(indices)
            expected_indices = set(range(min_index, max_index + 1))
            actual_indices = set(indices)
            
            missing_indices = expected_indices - actual_indices
            
            print(f"章节范围: 第{min_index}章 - 第{max_index}章")
            print(f"实际章节数: {len(chapters)}")
            print(f"预期章节数: {len(expected_indices)}")
            
            if missing_indices:
                missing_list = sorted(list(missing_indices))
                print(f"⚠️  缺失的章节: {missing_list[:20]}{'...' if len(missing_list) > 20 else ''}")
            else:
                print("✅ 章节编号连续，无缺失")
    
    def save_results(self, chapters, duplicate_indices, duplicate_titles, output_file):
        """保存结果到文件"""
        print(f"\n保存结果到: {output_file}")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("章节重复检测报告\n")
            f.write("=" * 50 + "\n\n")
            
            # 写入所有章节
            f.write("所有章节列表:\n")
            f.write("-" * 30 + "\n")
            for chapter in chapters:
                title_display = chapter['title'] if chapter['title'] else "(无标题)"
                f.write(f"第{chapter['index']:3d}章: {title_display}\n")
            
            # 写入重复章节编号
            if duplicate_indices:
                f.write(f"\n重复的章节编号 ({len(duplicate_indices)}个):\n")
                f.write("-" * 30 + "\n")
                for index, chapter_list in duplicate_indices:
                    f.write(f"\n第{index}章 (出现 {len(chapter_list)} 次):\n")
                    for i, chapter in enumerate(chapter_list, 1):
                        title_display = chapter['title'] if chapter['title'] else "(无标题)"
                        f.write(f"  {i}. 行{chapter['line_number']:4d}: {title_display}\n")
            
            # 写入重复章节标题
            if duplicate_titles:
                f.write(f"\n重复的章节标题 ({len(duplicate_titles)}个):\n")
                f.write("-" * 30 + "\n")
                for title, chapter_list in duplicate_titles:
                    f.write(f"\n标题: '{title}' (出现 {len(chapter_list)} 次):\n")
                    for i, chapter in enumerate(chapter_list, 1):
                        f.write(f"  {i}. 第{chapter['index']}章 (行{chapter['line_number']})\n")
            
            f.write(f"\n总结:\n")
            f.write(f"- 总章节数: {len(chapters)}\n")
            f.write(f"- 重复编号: {len(duplicate_indices)}个\n")
            f.write(f"- 重复标题: {len(duplicate_titles)}个\n")
        
        print("✅ 结果已保存")

def main():
    """主函数"""
    print("📚 章节重复检测工具")
    
    # 输入文件路径
    input_file = r"E:\AI\ai-novel-clip\novel\大秦二世公子华.txt"
    
    # 检查文件是否存在
    if not Path(input_file).exists():
        print(f"❌ 文件不存在: {input_file}")
        return
    
    try:
        # 创建检测器
        checker = ChapterDuplicateChecker()
        
        # 提取章节
        chapters, encoding = checker.extract_chapters(input_file)
        
        # 检查重复
        duplicate_indices, duplicate_titles = checker.check_duplicates(chapters)
        
        # 显示结果
        checker.display_results(chapters, duplicate_indices, duplicate_titles)
        
        # 保存结果
        output_file = Path(input_file).parent / f"{Path(input_file).stem}_重复检测报告.txt"
        checker.save_results(chapters, duplicate_indices, duplicate_titles, output_file)
        
        print("\n" + "=" * 60)
        print("✅ 检测完成")
        print("=" * 60)
        print(f"输入文件: {input_file}")
        print(f"文件编码: {encoding}")
        print(f"总章节数: {len(chapters)}")
        print(f"重复编号: {len(duplicate_indices)}个")
        print(f"重复标题: {len(duplicate_titles)}个")
        print(f"报告文件: {output_file}")
        
    except Exception as e:
        print(f"❌ 检测失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
