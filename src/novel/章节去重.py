#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
章节去重工具
删除重复的章节，只保留每个章节的第一次出现
"""

import re
import chardet
from pathlib import Path

class ChapterDeduplicator:
    """章节去重器"""
    
    def __init__(self):
        # 章节标题的正则表达式模式
        self.chapter_patterns = [
            r'^第\s*(\d+)\s*章\s*(.*)$',  # 第X章 标题
            r'^第\s*([一二三四五六七八九十百千万]+)\s*章\s*(.*)$',  # 第X章 标题（中文数字）
        ]
    
    def detect_encoding(self, file_path):
        """检测文件编码"""
        print(f"检测文件编码: {file_path}")
        
        with open(file_path, 'rb') as f:
            raw_data = f.read()
        
        result = chardet.detect(raw_data)
        encoding = result['encoding']
        
        # 尝试多种编码
        encodings_to_try = [encoding, 'utf-8', 'gbk', 'gb2312', 'big5']
        
        for enc in encodings_to_try:
            if enc:
                try:
                    with open(file_path, 'r', encoding=enc) as f:
                        content = f.read()
                    print(f"成功使用编码 {enc} 读取文件")
                    return content, enc
                except (UnicodeDecodeError, UnicodeError):
                    continue
        
        raise ValueError("无法确定文件编码")
    
    def chinese_to_arabic(self, chinese_num):
        """将中文数字转换为阿拉伯数字"""
        chinese_digits = {
            '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
            '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
            '百': 100, '千': 1000, '万': 10000
        }
        
        if chinese_num.isdigit():
            return int(chinese_num)
        
        # 简单的中文数字转换
        if chinese_num in chinese_digits:
            return chinese_digits[chinese_num]
        
        # 处理十几、几十等
        if '十' in chinese_num:
            if chinese_num == '十':
                return 10
            elif chinese_num.startswith('十'):
                return 10 + chinese_digits.get(chinese_num[1], 0)
            elif chinese_num.endswith('十'):
                return chinese_digits.get(chinese_num[0], 0) * 10
            else:
                parts = chinese_num.split('十')
                if len(parts) == 2:
                    tens = chinese_digits.get(parts[0], 0) * 10
                    ones = chinese_digits.get(parts[1], 0)
                    return tens + ones
        
        return chinese_num
    
    def parse_chapters(self, content):
        """解析章节信息"""
        print("=" * 60)
        print("解析章节信息")
        print("=" * 60)
        
        lines = content.split('\n')
        print(f"文件总行数: {len(lines)}")
        
        chapters = []
        current_chapter = None
        
        for line_num, line in enumerate(lines):
            original_line = line
            line = line.strip()
            
            # 检查是否是章节标题
            is_chapter_title = False
            for pattern in self.chapter_patterns:
                match = re.match(pattern, line)
                if match:
                    chapter_num = match.group(1)
                    chapter_title = match.group(2).strip() if len(match.groups()) > 1 else ""
                    
                    # 转换中文数字
                    if not chapter_num.isdigit():
                        arabic_num = self.chinese_to_arabic(chapter_num)
                        if isinstance(arabic_num, int):
                            chapter_num = str(arabic_num)
                    
                    # 验证章节编号是否合理
                    if chapter_num.isdigit():
                        chapter_index = int(chapter_num)
                        if 1 <= chapter_index <= 500:  # 合理的章节范围
                            # 保存上一章节
                            if current_chapter:
                                chapters.append(current_chapter)
                            
                            # 开始新章节
                            current_chapter = {
                                'index': chapter_index,
                                'title': chapter_title,
                                'full_title': line,
                                'start_line': line_num,
                                'content_lines': [original_line],
                                'original_line': original_line
                            }
                            is_chapter_title = True
                            break
            
            # 如果不是章节标题，添加到当前章节内容
            if not is_chapter_title and current_chapter:
                current_chapter['content_lines'].append(original_line)
        
        # 保存最后一个章节
        if current_chapter:
            chapters.append(current_chapter)
        
        print(f"解析到 {len(chapters)} 个章节")
        return chapters
    
    def deduplicate_chapters(self, chapters):
        """去除重复章节"""
        print("\n" + "=" * 60)
        print("去除重复章节")
        print("=" * 60)
        
        # 记录已见过的章节编号
        seen_chapters = set()
        unique_chapters = []
        duplicate_count = 0
        
        for chapter in chapters:
            chapter_key = chapter['index']
            
            if chapter_key not in seen_chapters:
                # 第一次遇到这个章节，保留
                seen_chapters.add(chapter_key)
                unique_chapters.append(chapter)
                print(f"保留: 第{chapter['index']}章 - {chapter['title'][:30]}{'...' if len(chapter['title']) > 30 else ''}")
            else:
                # 重复章节，跳过
                duplicate_count += 1
                print(f"跳过重复: 第{chapter['index']}章 - {chapter['title'][:30]}{'...' if len(chapter['title']) > 30 else ''}")
        
        print(f"\n去重完成:")
        print(f"- 原始章节数: {len(chapters)}")
        print(f"- 去重后章节数: {len(unique_chapters)}")
        print(f"- 删除重复章节: {duplicate_count}")
        
        return unique_chapters
    
    def generate_clean_content(self, unique_chapters):
        """生成去重后的内容"""
        print("\n" + "=" * 60)
        print("生成去重后的内容")
        print("=" * 60)
        
        clean_lines = []
        
        for chapter in unique_chapters:
            # 添加章节内容
            clean_lines.extend(chapter['content_lines'])
        
        clean_content = '\n'.join(clean_lines)
        
        print(f"生成内容统计:")
        print(f"- 总行数: {len(clean_lines)}")
        print(f"- 总字符数: {len(clean_content)}")
        
        return clean_content
    
    def save_clean_file(self, clean_content, output_path, encoding='utf-8'):
        """保存去重后的文件"""
        print(f"\n保存去重后的文件: {output_path}")
        
        with open(output_path, 'w', encoding=encoding) as f:
            f.write(clean_content)
        
        print(f"✅ 文件保存成功")
        print(f"文件大小: {len(clean_content)} 字符")
    
    def generate_report(self, original_chapters, unique_chapters, output_path):
        """生成去重报告"""
        print(f"\n生成去重报告: {output_path}")
        
        duplicate_count = len(original_chapters) - len(unique_chapters)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("章节去重报告\n")
            f.write("=" * 50 + "\n\n")
            
            f.write("处理统计:\n")
            f.write(f"- 原始章节数: {len(original_chapters)}\n")
            f.write(f"- 去重后章节数: {len(unique_chapters)}\n")
            f.write(f"- 删除重复章节: {duplicate_count}\n")
            f.write(f"- 去重率: {duplicate_count/len(original_chapters)*100:.1f}%\n\n")
            
            f.write("保留的章节列表:\n")
            f.write("-" * 30 + "\n")
            for chapter in unique_chapters:
                f.write(f"第{chapter['index']:3d}章: {chapter['title']}\n")
            
            # 统计缺失的章节
            if unique_chapters:
                chapter_indices = [ch['index'] for ch in unique_chapters]
                min_index = min(chapter_indices)
                max_index = max(chapter_indices)
                expected_indices = set(range(min_index, max_index + 1))
                actual_indices = set(chapter_indices)
                missing_indices = expected_indices - actual_indices
                
                if missing_indices:
                    f.write(f"\n缺失的章节:\n")
                    f.write("-" * 30 + "\n")
                    missing_list = sorted(list(missing_indices))
                    for missing in missing_list:
                        f.write(f"第{missing}章\n")
        
        print("✅ 报告生成完成")

def main():
    """主函数"""
    print("🔧 章节去重工具")
    
    # 输入文件路径
    input_file = r"E:\AI\ai-novel-clip\novel\大秦二世公子华.txt"
    
    # 检查文件是否存在
    if not Path(input_file).exists():
        print(f"❌ 文件不存在: {input_file}")
        return
    
    try:
        # 创建去重器
        deduplicator = ChapterDeduplicator()
        
        # 读取原文件
        original_content, encoding = deduplicator.detect_encoding(input_file)
        original_size = len(original_content)
        
        # 解析章节
        chapters = deduplicator.parse_chapters(original_content)
        
        # 去重
        unique_chapters = deduplicator.deduplicate_chapters(chapters)
        
        # 生成去重后的内容
        clean_content = deduplicator.generate_clean_content(unique_chapters)
        
        # 保存去重后的文件
        input_path = Path(input_file)
        output_file = input_path.parent / f"{input_path.stem}_去重.txt"
        deduplicator.save_clean_file(clean_content, output_file, encoding)
        
        # 生成报告
        report_file = input_path.parent / f"{input_path.stem}_去重报告.txt"
        deduplicator.generate_report(chapters, unique_chapters, report_file)
        
        print("\n" + "=" * 60)
        print("✅ 去重完成")
        print("=" * 60)
        print(f"原始文件: {input_file}")
        print(f"原始大小: {original_size:,} 字符")
        print(f"去重文件: {output_file}")
        print(f"去重大小: {len(clean_content):,} 字符")
        print(f"压缩率: {(1-len(clean_content)/original_size)*100:.1f}%")
        print(f"原始章节: {len(chapters)}")
        print(f"去重章节: {len(unique_chapters)}")
        print(f"删除重复: {len(chapters) - len(unique_chapters)}")
        print(f"去重报告: {report_file}")
        
    except Exception as e:
        print(f"❌ 去重失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
