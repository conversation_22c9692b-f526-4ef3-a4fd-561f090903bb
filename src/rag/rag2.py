import os
import json
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_community.vectorstores import FAISS
from langchain.chains import create_retrieval_chain
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain_core.prompts import ChatPromptTemplate
from langchain_community.chat_models import ChatTongyi
from langchain_core.documents import Document

# --- 配置 ---
SOUND_EFFECTS_JSON_PATH = "resources/sound_effect_with_descriptions.json"
os.environ["DASHSCOPE_API_KEY"] = "sk-f1074ca1fbab4ab8aa56885752555525"

# --- 1. 自定义 JSON 加载器和扁平化处理 (递归版本) ---
def flatten_sound_effects_json(json_data: dict, current_path: list = None) -> list[Document]:
    if current_path is None:
        current_path = []
    
    documents = []
    for key, value in json_data.items():
        new_path = current_path + [key]
        
        if isinstance(value, dict) and "url" in value and "label" in value:
            full_url = value["url"]
            labels = value["label"] 
            audio_description = value.get("description")
            if not audio_description:
                audio_description = f"{key} ({' '.join(labels)})"
            
            relative_path = full_url
            try:
                relative_path_start_index = full_url.index("/resources/sound_effects/")
                relative_path = full_url[relative_path_start_index + len("/resources/sound_effects/"):]
            except ValueError:
                relative_path = full_url
            
            doc = Document(
                page_content=audio_description,
                metadata={
                    "full_url": full_url,
                    "relative_path": relative_path,
                    "labels": labels,
                    "file_name": key,
                    "description_raw": value.get("description")
                }
            )
            documents.append(doc)
        elif isinstance(value, dict):
            documents.extend(flatten_sound_effects_json(value, new_path))
            
    return documents

# --- 2. 文档加载与分割 ---
print(f"正在加载音效库文件：{SOUND_EFFECTS_JSON_PATH}...")
try:
    with open(SOUND_EFFECTS_JSON_PATH, 'r', encoding='utf-8') as f:
        raw_json_data = json.load(f)
    documents = flatten_sound_effects_json(raw_json_data)
    print(f"成功加载并扁平化了 {len(documents)} 个音效文档。")
except Exception as e:
    print(f"加载或处理文件时发生错误：{e}")
    exit()

text_splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=50)
splits = text_splitter.split_documents(documents)
print(f"文档被处理成 {len(splits)} 个文本块。")

# --- 3. 创建嵌入模型 ---
print("\n正在加载本地嵌入模型...")
# 从本地 models 目录加载模型
# 获取项目根目录路径
current_dir = os.path.dirname(os.path.abspath(__file__))  # src/rag/
project_root = os.path.dirname(os.path.dirname(current_dir))  # 项目根目录
model_path = os.path.join(project_root, "models", "BAAI", "bge-large-zh-v1.5")

print(f"模型路径: {model_path}")

# 检查模型目录是否存在
if not os.path.exists(model_path):
    print(f"❌ 模型目录不存在: {model_path}")
    print("请先运行 'python download_models.py' 下载模型")
    exit(1)

embeddings = HuggingFaceEmbeddings(
    model_name=model_path,
    model_kwargs={'device': 'cpu'}  # 明确指定使用 CPU
)
print("嵌入模型加载完成。")

# --- 4. 构建向量存储 ---
print("\n正在构建向量存储 (FAISS)...")
retriever = FAISS.from_documents(splits, embeddings).as_retriever(search_kwargs={"k": 1})
print("向量存储构建完成。")

# --- 5. 定义 LLM 和 Prompt ---
llm = ChatTongyi(model_name="qwen-plus", temperature=0.0) 

# 【关键修改】优化后的 Prompt
rag_prompt = ChatPromptTemplate.from_template("""
你是一名专业的音效检索专家。你的任务是根据用户提供的**单个输入音效描述**，并结合以下**提供的音效库上下文**，找到最匹配的音效URL。

请严格遵守以下指令：
1. **输入格式**：我将提供一个JSON数组，但每次只会包含**一个**对象，该对象包含 `"word"` 和 `"audio"`。
2. **上下文（音效库）格式**：上下文将包含一个或多个以清晰格式呈现的音效文档信息。每个文档都明确列出了“描述”、“URL”、“标签”等。
3. **URL匹配与提取逻辑**：
    - 匹配核心：根据输入中 `"audio"` 字段的描述，找到上下文中与该描述最贴合的音效文档。
    - **【强制指令】URL来源：你必须且只能从上下文文档中提取 URL。具体来说，请找到与用户查询最匹配的文档，并使用该文档中明确提供的 `URL` 字段的值。**
    - **【优先级】如果你找到多个匹配文档，请严格使用**第一个**最匹配文档的 `URL`。**
    - **【严禁生成】严禁凭空捏造或生成任何形式的 URL，例如 `example.com` 或任何虚构的链接。这是绝对不允许的。**
    - **【严格返回null】如果上下文中的所有文档都无法提供一个合理的 URL 匹配（即，你认为没有音效匹配，或者匹配的文档没有 `URL` 字段），则返回 `null`。**
    - 请忽略`relative_path`和`file_name`字段，它们仅供参考，不对URL提取有直接影响。
4. **输出格式**：
    - 返回一个JSON数组。
    - 这个数组应该只包含**一个**对象，该对象必须包含输入中的 `"word"` 和 `"audio"` 字段，同时新增每个数组检索到的第一块（`"content""）`）。
    - 严格保持JSON格式的正确性，字段用双引号包裹，对象间用逗号分隔，数组首尾用方括号包裹。
    - **不添加任何额外内容（如解释、注释、代码块标记等），只输出纯粹的JSON数组。**

---
用户输入JSON: {input}

上下文音效库信息 (请只从这里提取URL):
{context}
---
请直接输出匹配结果的JSON数组，不要包含任何额外文字或格式。
""")

# 构建 RAG Chain
# 这里我们直接将 retriever 和 document_chain 组合，LangChain 会自动处理文档的传递
document_chain = create_stuff_documents_chain(llm, rag_prompt)
retrieval_chain = create_retrieval_chain(retriever, document_chain)

print("\nLLM 已定义 (当前使用 Qwen 模型：qwen-plus)。")

# --- 6. 命令行交互循环 (修复了 Document 处理问题) ---
def process_user_input_with_rag(user_inputs: str):
    try:
        if not isinstance(user_inputs, list):
            raise ValueError("输入必须是一个JSON数组。")

        all_results = []

        for item_input in user_inputs:
            single_item_json_str = json.dumps([item_input], ensure_ascii=False)

            print(f"\n--- 正在处理单个查询: {single_item_json_str} ---")

            try:
                # 调用 retrieval_chain.invoke，它会同时执行检索和文档组合
                # 返回结果是一个字典，其中 'answer' 是 LLM 的最终回答，'context' 是检索到的文档
                response = retrieval_chain.invoke({"input": single_item_json_str, "audio": item_input['audio']})
                
                # 打印检索到的上下文，用于调试和分析
                print("\n--- 检索到的上下文 ---")
                retrieved_docs = response.get('context', [])
                if not retrieved_docs:
                    print("未检索到任何上下文。")
                
                for i, doc in enumerate(retrieved_docs):
                    print(f"文档 {i+1} (Page Content): {doc.page_content}")
                    print(f"  URL: {doc.metadata.get('full_url', 'N/A')}")
                    print(f"  原始描述 (description_raw): {doc.metadata.get('description_raw', 'N/A')}") 
                    print(f"  标签 (labels): {doc.metadata.get('labels', 'N/A')}")
                    print(f"  文件名称 (file_name): {doc.metadata.get('file_name', 'N/A')}")
                    print("-" * 20)
                print("------------------------")

                llm_answer = response['answer']

                try:
                    single_item_output = json.loads(llm_answer)
                    if isinstance(single_item_output, list) and len(single_item_output) == 1:
                        all_results.append(single_item_output[0])
                    else:
                        print(f"警告: LLM 对 '{item_input.get('audio')}' 返回格式不符或结果非单项。原始返回: {llm_answer}")
                        all_results.append({
                            "word": item_input.get("word", "unknown"),
                            "audio": item_input.get("audio", "unknown"),
                            "url": None,
                            "error_details": f"LLM 返回格式不符或结果非单项，原始返回: {llm_answer}"
                        })
                except json.JSONDecodeError:
                    print(f"警告: LLM 对 '{item_input.get('audio')}' 返回非有效JSON。原始返回: {llm_answer}")
                    all_results.append({
                        "word": item_input.get("word", "unknown"),
                        "audio": item_input.get("audio", "unknown"),
                        "url": None,
                        "error_details": f"LLM 返回非有效JSON，原始返回: {llm_answer}"
                    })

            except Exception as e:
                print(f"处理查询 '{item_input.get('audio')}' 时发生未知错误: {e}")
                all_results.append({
                    "word": item_input.get("word", "unknown"),
                    "audio": item_input.get("audio", "unknown"),
                    "url": None,
                    "error_details": str(e)
                })

        return json.dumps(all_results, ensure_ascii=False, indent=2)

    except json.JSONDecodeError:
        return json.dumps([{"error": "无效的JSON输入，请确保输入格式正确。" }], ensure_ascii=False, indent=2)
    except ValueError as ve:
        return json.dumps([{"error": str(ve) }], ensure_ascii=False, indent=2)
    except Exception as e:
        return json.dumps([{"error": f"处理请求时发生未知错误: {e}"}], ensure_ascii=False, indent=2)

print("\n--- 音效检索专家（RAG系统）---\n")
print("请输入一个JSON数组，每个对象包含 'word' 和 'audio' 字段。")
print("例如: [{'word': '鹰叫', 'audio': '秃鹰叫声'}]")
print("输入 'exit' 退出。")
print("-" * 40)

while True:
    user_input = input("\n您的输入 (JSON数组): ")
    if user_input.lower() == 'exit':
        print("感谢使用，再见！")
        break
    user_inputs = json.loads(user_input)
    output_json = process_user_input_with_rag(user_inputs)
    print("\n--- 匹配结果 ---\n")
    print(output_json)
    print("\n" + "-" * 40)

print("\nRAG 示例运行完毕。")