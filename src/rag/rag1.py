#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音效匹配专家 - 使用大模型为句子匹配音效
根据输入句子，返回包含音效信息的JSON数组
"""
import json
import os
import time
import dashscope
from dashscope import Generation
from dotenv import load_dotenv
from pathlib import Path
dotenv_path = Path(__file__).parent.parent.parent / '.env'
load_dotenv(dotenv_path=dotenv_path,verbose=True,override=True)



class AudioEffectMatcher:
    def __init__(self):
        self.api_key = os.getenv("DASHSCOPE_API_KEY")
        if not self.api_key:
            raise ValueError("请设置DASHSCOPE_API_KEY环境变量")
        
        dashscope.api_key = self.api_key
        
    def generate_audio_effects(self, sentence, max_retries=3):
        """
        为句子生成音效匹配
        
        Args:
            sentence: 输入句子
            max_retries: 最大重试次数
            
        Returns:
            list: 音效信息列表，格式为 [{"word": "词语", "audio": "音效描述"}]
        """
        
        # 构建提示词
        prompt = f"""请你作为音效匹配专家，根据我提供的句子，返回一个包含音效信息的 JSON 数组。要求如下：

数组中的每个元素是一个对象，仅包含两个键："word"（指定句子中对应的词语）和"audio"（该词语对应的音效描述）。

每个句子只返回 3-5 个重要且不重复的音效，必须贴合场景。

严格按照 JSON 格式输出，不添加任何额外内容，输出为一行（如解释、标点补充等）。

示例：

输入：李承乾一骑冲出城门，身后只跟着几百人马。他不是在逃命，而是在赌一场生死局。

输出：
[{{"word": "冲出","audio": "马蹄急促奔跑声"}},{{"word": "城门","audio": "厚重木门开启声"}},{{"word": "人马","audio": "军队行进声"}},{{"word": "生死局","audio": "紧张心跳声"}}]

现在请处理以下句子：

输入：{sentence}

输出："""

        for attempt in range(max_retries):
            try:
                response = Generation.call(
                    model='qwen-turbo',
                    prompt=prompt,
                    max_tokens=500,
                    temperature=0.3,  # 较低温度确保格式稳定
                    api_key=self.api_key
                )
                
                if response.status_code == 200:
                    result_text = response.output.text.strip()
                    
                    # 清理可能的前缀和后缀
                    result_text = self.clean_response(result_text)
                    
                    # 尝试解析JSON
                    try:
                        audio_effects = json.loads(result_text)
                        
                        # 验证格式
                        if self.validate_audio_effects(audio_effects):
                            print(f"✅ 成功生成 {len(audio_effects)} 个音效匹配")
                            return audio_effects
                        else:
                            raise ValueError("返回格式不符合要求")
                            
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON解析失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                        print(f"原始响应: {result_text}")
                        
                        if attempt < max_retries - 1:
                            time.sleep(1)
                            continue
                        else:
                            return self.generate_fallback_effects(sentence)
                
                else:
                    print(f"❌ API调用失败: {response.status_code}")
                    if attempt < max_retries - 1:
                        time.sleep(2)
                        continue
                    else:
                        return self.generate_fallback_effects(sentence)
                        
            except Exception as e:
                print(f"❌ 生成音效时出错 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)
                    continue
                else:
                    return self.generate_fallback_effects(sentence)
        
        return self.generate_fallback_effects(sentence)
    
    def clean_response(self, text):
        """清理API响应文本"""
        # 移除可能的前缀
        prefixes = ['输出：', '输出:', 'JSON:', 'json:', '结果：', '结果:']
        for prefix in prefixes:
            if text.startswith(prefix):
                text = text[len(prefix):].strip()
        
        # 移除可能的后缀说明
        lines = text.split('\n')
        if lines:
            # 取第一行（应该是JSON）
            text = lines[0].strip()
        
        # 移除可能的markdown代码块标记
        text = text.replace('```json', '').replace('```', '').strip()
        
        return text
    
    def validate_audio_effects(self, audio_effects):
        """验证音效数组格式"""
        if not isinstance(audio_effects, list):
            return False
        
        if len(audio_effects) < 3 or len(audio_effects) > 5:
            print(f"⚠️ 音效数量不符合要求: {len(audio_effects)} (应为3-5个)")
        
        for effect in audio_effects:
            if not isinstance(effect, dict):
                return False
            
            if 'word' not in effect or 'audio' not in effect:
                return False
            
            if not isinstance(effect['word'], str) or not isinstance(effect['audio'], str):
                return False
            
            if len(effect) != 2:  # 只能有word和audio两个键
                return False
        
        return True
    
    def generate_fallback_effects(self, sentence):
        """生成备用音效（当API失败时）"""
        print("🔄 生成备用音效...")
        
        # 简单的关键词匹配备用方案
        fallback_effects = []
        
        # 基础音效映射
        effect_mapping = {
            '马': [{"word": "马蹄", "audio": "马蹄声"}],
            '剑': [{"word": "剑", "audio": "金属碰撞声"}],
            '门': [{"word": "门", "audio": "开门声"}],
            '风': [{"word": "风", "audio": "风声"}],
            '水': [{"word": "水", "audio": "流水声"}],
            '火': [{"word": "火", "audio": "火焰燃烧声"}],
            '雷': [{"word": "雷", "audio": "雷声"}],
            '雨': [{"word": "雨", "audio": "雨声"}],
            '脚步': [{"word": "脚步", "audio": "脚步声"}],
            '心': [{"word": "心跳", "audio": "心跳声"}]
        }
        
        for keyword, effects in effect_mapping.items():
            if keyword in sentence and len(fallback_effects) < 4:
                fallback_effects.extend(effects)
        
        # 如果没有匹配到，提供通用音效
        if not fallback_effects:
            fallback_effects = [
                {"word": "环境", "audio": "环境音效"},
                {"word": "氛围", "audio": "背景音乐"},
                {"word": "情绪", "audio": "情绪音效"}
            ]
        
        return fallback_effects[:4]  # 最多返回4个
    
    def process_sentence(self, sentence):
        """
        处理单个句子

        Args:
            sentence: 输入句子

        Returns:
            list: 包含词语和音效信息的JSON数组
        """
        print(f"🎵 处理句子: {sentence}")

        audio_effects = self.generate_audio_effects(sentence)

        # 直接返回API生成的JSON数组
        return audio_effects
    
    def process_multiple_sentences(self, sentences):
        """
        批量处理多个句子
        
        Args:
            sentences: 句子列表
            
        Returns:
            list: 处理结果列表
        """
        print(f"📝 开始批量处理 {len(sentences)} 个句子...")
        
        results = []
        for i, sentence in enumerate(sentences, 1):
            print(f"\n[{i}/{len(sentences)}]")
            result = self.process_sentence(sentence)
            results.append(result)
            
            # 添加延迟避免API频率限制
            if i < len(sentences):
                time.sleep(0.5)
        
        return results
    
    def save_results(self, results):
        """输出结果"""
        # 为每个句子输出一个JSON数组
        for result in results:
            json_output = json.dumps(result, ensure_ascii=False)
            print(json_output)

def main():
    """主函数 - 演示用法"""
    print("=" * 60)
    print("🎵 音效匹配专家")
    print("=" * 60)
    
    matcher = AudioEffectMatcher()
    
    # 测试句子
    test_sentences = [
        "李承乾一骑冲出城门，身后只跟着几百人马。他不是在逃命，而是在赌一场生死局。",
        "刺鼻的血腥味袭来，林威只感觉胃里翻江倒海，难受的干呕让林威大口的吸气。",
        "夜深人静，月光透过窗棂洒在地上，远处传来阵阵犬吠声。",
        "剑光如电，两人在竹林中激烈交手，竹叶纷飞如雨。"
    ]
    
    try:
        # 处理测试句子
        results = matcher.process_multiple_sentences(test_sentences)
        
        # 显示结果
        print("\n" + "=" * 60)
        print("📊 处理结果:")
        print("=" * 60)
        
        for i, result in enumerate(results, 1):
            print(f"\n{i}. 句子音效数组:")
            for item in result:
                print(f"   - {item['word']}: {item['audio']}")
        
        # 保存结果
        matcher.save_results(results)
        
        print("\n✅ 处理完成！")
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断处理")
    except Exception as e:
        print(f"\n❌ 处理失败: {e}")

def interactive_mode():
    """交互模式"""
    print("🎵 音效匹配专家 - 交互模式")
    print("输入句子，获取音效匹配。输入 'quit' 退出。")
    print("-" * 40)
    
    matcher = AudioEffectMatcher()
    
    while True:
        try:
            sentence = input("\n请输入句子: ").strip()
            
            if sentence.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break
            
            if not sentence:
                print("⚠️ 请输入有效句子")
                continue
            
            result = matcher.process_sentence(sentence)
            
            print("\n🎵 音效匹配结果:")
            print(json.dumps(result, ensure_ascii=False))
            
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 处理出错: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        interactive_mode()
    else:
        main()
