#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
字幕处理模块

负责字幕数据的加载、处理和生成
"""

import json
import re
import requests
import os
from typing import Dict, Any, List, Tuple, Optional

from .config import ClipTemplateConfig, AudioEffectsConfig
from ..sound_effect_retrieval import SoundEffectManager


class SubtitleProcessor:
    """字幕处理类

    负责字幕数据的加载、处理和生成
    支持新的火山引擎字幕格式
    """
    def __init__(self, story_boards_data: List[Dict[str, Any]] = None):
        """初始化字幕处理器

        Args:
            story_boards_data: 分镜数据数组，每个分镜包含short_subtitles字段
        """
        self.story_boards_data = []

        # 直接使用传递的分镜数据
        if story_boards_data and isinstance(story_boards_data, list):
            self.story_boards_data = story_boards_data
            total_subtitles = sum(len(board.get('subtitle', [])) for board in story_boards_data)
          #print(f"✅ 字幕处理器初始化成功，共{len(story_boards_data)}个分镜，{total_subtitles}条字幕")
        else:
          print("⚠️ 未提供有效的分镜数据")

        # 初始化clip模板配置
        self.clip_template_config = ClipTemplateConfig()
        self.audio_effects_config = AudioEffectsConfig()
        self.sound_effect_manager = SoundEffectManager()


    async def create_volcengine_subtitle_clips(self, timeline_offset: float, story_board: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """创建基于火山引擎字幕数据的字幕片段，包含关键词强化效果

        Args:
            timeline_offset: 当前分镜在整个时间轴上的偏移时间（秒）
            story_board: 分镜数据，包含subtitle和keywords字段

        Returns:
            Tuple[字幕剪辑配置列表, 关键词音效列表]
        """
        subtitle_clips = []
        keyword_sound_effects = []
        short_subtitles = story_board.get('subtitle', [])
        keywords = story_board.get('keywords', [])
        sound_effects = []
        if story_board.get('video_url') and len(story_board.get('video_url')) > 0:
            story_board_sentence = story_board.get('story_board')
            sound_words = await self.sound_effect_manager.generate_audio_effects(story_board_sentence)
            for sound_pair in sound_words:
                sound_word = sound_pair['audio']
                retrieved_sound = self.sound_effect_manager.search(sound_word, top_k=1)
                sound_effect = [sound_pair['word'], retrieved_sound[0].get('url')]
                sound_effects.append(sound_effect)

        if not short_subtitles:
            return subtitle_clips, keyword_sound_effects

        for subtitle in short_subtitles:
            # 获取字幕的基本信息
            text = subtitle.get('text', '')
            start_time_s = subtitle.get('start_time_s', 0)
            end_time_s = subtitle.get('end_time_s', start_time_s+0.5)
            words = subtitle.get('words', [])

            if not text.strip():
                continue

            # 计算在整个时间轴上的实际时间
            timeline_in = timeline_offset + start_time_s
            timeline_out = timeline_offset + end_time_s

            # 处理关键词强化效果
            enhanced_text = self.enhance_keywords_in_text(text, keywords, words, timeline_offset, keyword_sound_effects, sound_effect=sound_effects)

            # 使用clip模板创建字幕片段配置
            clip = self.clip_template_config.create_volcengine_subtitle_clip(
                content=enhanced_text,
                start_time=timeline_in,
                end_time=timeline_out
            )
            subtitle_clips.append(clip)
          #print(f"   字幕: {text[:15]}... ({timeline_in:.2f}s - {timeline_out:.2f}s)")

        return subtitle_clips, keyword_sound_effects

    def enhance_keywords_in_text(self, text: str, keywords: List[str], words: List[Dict], timeline_offset: float, keyword_sound_effects: List[Dict], sound_effect: List[str] = []) -> str:
        """在文本中为关键词添加强化效果

        Args:
            text: 原始文本
            keywords: 关键词列表
            words: 字幕中每个字的时间信息
            timeline_offset: 时间轴偏移
            keyword_sound_effects: 关键词音效列表（用于收集音效）

        Returns:
            包含强化效果的文本
        """
        if not keywords or not words:
            return text

        enhanced_text = text

        for word_sound in sound_effect:
            keyword = word_sound[0]
            if keyword in text:
                keyword_time = self.find_keyword_time_in_words(keyword, words, timeline_offset)
                if keyword_time:
                    # 添加关键词音效
                    keyword_sound_effects.append({
                        "keyword": keyword,
                        "timeline_in": keyword_time,
                        "timeline_out": keyword_time + self.audio_effects_config.sound_effect_duration,  # 音效持续时间
                        "sound_effect_url": word_sound[1]
                    })

        # 为每个关键词添加强化效果
        for keyword in keywords:
            if keyword in text:
                # 查找关键词在words中的时间信息
                keyword_time = self.find_keyword_time_in_words(keyword, words, timeline_offset)

                if keyword_time:
                    # 添加关键词音效
                    keyword_sound_effects.append({
                        "keyword": keyword,
                        "timeline_in": keyword_time,
                        "timeline_out": keyword_time + self.audio_effects_config.sound_effect_duration  # 音效持续时间
                    })

                # 使用阿里云IMS字幕局部效果语法强化关键词
                # 设置关键词为黄色、加粗、放大
                enhanced_keyword = self.clip_template_config.enhance_text_style.replace("{keyword}", keyword)
                enhanced_text = enhanced_text.replace(keyword, enhanced_keyword)

              #print(f"   🔥 强化关键词: {keyword} (时间: {keyword_time:.2f}s)")

        return enhanced_text

    def find_keyword_time_in_words(self, keyword: str, words: List[Dict], timeline_offset: float) -> float:
        """在words数组中查找关键词的时间点

        Args:
            keyword: 关键词
            words: 字幕中每个字的时间信息
            timeline_offset: 时间轴偏移

        Returns:
            关键词开始时间（秒），如果未找到返回0
        """
        if not words or not keyword:
            return 0.0

        # 将关键词拆分为字符
        keyword_chars = list(keyword)

        # 在words中查找连续匹配的字符
        for i in range(len(words) - len(keyword_chars) + 1):
            match = True
            for j, char in enumerate(keyword_chars):
                if i + j >= len(words) or words[i + j].get('text', '') != char:
                    match = False
                    break

            if match:
                # 找到匹配，返回第一个字符的开始时间
                start_time_ms = words[i].get('start_time', 0)
                start_time_s = start_time_ms / 1000.0  # 转换为秒
                return timeline_offset + start_time_s

        return 0.0

    async def get_all_subtitle_clips(self, audio_durations: List[float]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """获取所有分镜的字幕片段和关键词音效，考虑时间轴偏移

        Args:
            audio_durations: 每个分镜音频的时长列表

        Returns:
            Tuple[所有字幕剪辑配置列表, 所有关键词音效列表]
        """
        all_subtitle_clips = []
        all_keyword_sound_effects = []
        current_timeline_offset = 0.0

      #print(f"🎬 开始处理 {len(self.story_boards_data)} 个分镜的字幕和关键词")

        for i, story_board in enumerate(self.story_boards_data):
            # 获取当前分镜的音频时长
            if i < len(audio_durations):
                audio_duration = audio_durations[i]
            else:
                # 如果没有对应的音频时长，使用默认值
                audio_duration = 8.0
              #print(f"⚠️ 分镜 {i+1} 没有对应的音频时长，使用默认值 {audio_duration}s")

            # 创建当前分镜的字幕片段和关键词音效
            subtitle_clips, keyword_sound_effects = await self.create_volcengine_subtitle_clips(current_timeline_offset, story_board)
            all_subtitle_clips.extend(subtitle_clips)
            all_keyword_sound_effects.extend(keyword_sound_effects)

            # 更新时间轴偏移
            current_timeline_offset += audio_duration

          #print(f"📊 分镜 {i+1}: 时间轴 {current_timeline_offset-audio_duration:.1f}s - {current_timeline_offset:.1f}s, 字幕数量: {len(subtitle_clips)}, 关键词音效: {len(keyword_sound_effects)}")

      #print(f"✅ 总共生成 {len(all_subtitle_clips)} 个字幕片段，{len(all_keyword_sound_effects)} 个关键词音效")
        return all_subtitle_clips, all_keyword_sound_effects

    def split_subtitle_by_sentences(self, text: str, audio_duration: float) -> Tuple[List[str], float]:
        """将长文本按句子分割，并分配时间
        
        Args:
            text: 字幕文本
            audio_duration: 音频时长(秒)
            
        Returns:
            分割后的句子列表和每句平均时长的元组
        """
        # 按句号、问号、感叹号分割
        sentences = re.split('[。？！]', text)
        sentences = [s.strip() for s in sentences if s.strip()]

        # 如果没有分割出句子，就使用原文本
        if not sentences:
            sentences = [text]

        # 平均分配时间
        if len(sentences) > 0:
            time_per_sentence = audio_duration / len(sentences)
            return sentences, time_per_sentence
        return [text], audio_duration
    
    def get_subtitle_for_segment(self, index: int) -> Optional[str]:
        """获取指定索引的字幕文本
        
        Args:
            index: 字幕片段索引
            
        Returns:
            字幕文本，如果索引无效则返回None
        """
        if 0 <= index < len(self.subtitle_data):
            return self.subtitle_data[index].get("original_segment", "")
        return None