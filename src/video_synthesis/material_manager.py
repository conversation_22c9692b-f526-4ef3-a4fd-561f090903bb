#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
素材管理模块

负责管理视频、音频、图片等素材资源
"""

from typing import Dict, Any, List, Tuple

from .config import MaterialConfig, ClipTemplateConfig


class MaterialManager:
    """素材管理类
    
    负责管理和组织视频、音频、图片等素材资源
    """
    
    def __init__(self, config: MaterialConfig = None):
        """初始化素材管理器

        Args:
            config: 素材配置对象，如果为None则使用默认配置
        """
        self.config = config or MaterialConfig()
        self.clip_template_config = ClipTemplateConfig()
      #print("✅ 素材管理器初始化成功")
    
    def get_video_audio_pairs(self, count: int = 2) -> List[Tuple[str, str]]:
        """获取视频和音频配对列表
        
        Args:
            count: 需要的配对数量
            
        Returns:
            视频URL和音频URL的配对列表
        """
        videos = self.config.get_videos()
        audios = self.config.get_audios()
        
        pairs = []
        for i in range(min(count, len(videos), len(audios))):
            pairs.append((videos[i], audios[i]))
            
        return pairs
    
    def get_image_audio_pairs(self, start_audio_index: int = 2, count: int = 3) -> List[Tuple[str, str]]:
        """获取图片和音频配对列表
        
        Args:
            start_audio_index: 音频起始索引
            count: 需要的配对数量
            
        Returns:
            图片URL和音频URL的配对列表
        """
        images = self.config.get_images()
        audios = self.config.get_audios()
        
        pairs = []
        for i in range(min(count, len(images))):
            audio_index = start_audio_index + i
            if audio_index < len(audios):
                pairs.append((images[i], audios[audio_index]))
                
        return pairs
    
    def create_corner_logo_clip(self, total_duration: float = 60.0) -> Dict[str, Any]:
        """创建角标图片剪辑配置

        Args:
            total_duration: 视频总时长

        Returns:
            角标图片剪辑配置字典
        """
        logo_url = self.config.get_corner_logo()
        if not logo_url:
          #print("⚠️ 未配置角标URL，跳过角标创建")
            return {}

        # 使用clip模板创建角标，并覆盖特定参数
        return self.clip_template_config.create_corner_logo_clip(
            logo_url=logo_url,
            total_duration=total_duration
        )
    
    def create_ai_watermark_clip(self, total_duration: float = 60.0) -> Dict[str, Any]:
        """创建AI水印剪辑配置

        Args:
            total_duration: 视频总时长

        Returns:
            AI水印剪辑配置字典
        """
        watermark_url = self.config.get_ai_watermark()
        if not watermark_url:
          #print("⚠️ 未配置水印URL，跳过水印创建")
            return {}

        # 使用clip模板创建水印，并覆盖特定参数
        return self.clip_template_config.create_watermark_clip(
            watermark_url=watermark_url,
            total_duration=total_duration
        )