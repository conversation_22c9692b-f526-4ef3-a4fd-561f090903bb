#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
视频下载模块

负责从URL下载视频文件并保存到本地
"""

import os
import re
import requests
import logging
from pathlib import Path
from typing import Optional, Tuple
from urllib.parse import urlparse


class VideoDownloader:
    """视频下载器类
    
    负责从URL下载视频文件并保存到本地指定目录
    """
    
    def __init__(self, timeout: int = 300, chunk_size: int = 8192):
        """初始化视频下载器
        
        Args:
            timeout: 下载超时时间（秒）
            chunk_size: 下载块大小（字节）
        """
        self.timeout = timeout
        self.chunk_size = chunk_size
        
        # 配置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
      #print("✅ 视频下载器初始化成功")
    
    def extract_chapter_number(self, chapter_name: str) -> str:
        """从章节名中提取章节号
        
        Args:
            chapter_name: 章节名称，如 "chapter10_誓师"
            
        Returns:
            章节号字符串，如 "10"
        """
        # 匹配章节名中的数字，如 chapter10_誓师 -> 10
        match = re.search(r'chapter(\d+)', chapter_name.lower())
        if match:
            return match.group(1)
        
        # 如果没有找到chapter数字格式，尝试提取其他数字
        numbers = re.findall(r'\d+', chapter_name)
        if numbers:
            return numbers[0]
        
        # 如果没有数字，返回章节名本身
        return chapter_name
    
    def generate_filename(self, novel_name: str, chapter_name: str) -> str:
        """生成视频文件名
        
        Args:
            novel_name: 小说名称
            chapter_name: 章节名称
            
        Returns:
            格式化的文件名，如 "大秦二世公子华-10.mp4"
        """
        chapter_number = self.extract_chapter_number(chapter_name)
        return f"{novel_name}-{chapter_number}.mp4"
    
    def download_video(self, video_url: str, save_path: str, 
                      novel_name: str = None, chapter_name: str = None) -> Tuple[bool, str]:
        """下载视频文件
        
        Args:
            video_url: 视频URL
            save_path: 保存路径（目录）
            novel_name: 小说名称（用于生成文件名）
            chapter_name: 章节名称（用于生成文件名）
            
        Returns:
            (是否成功, 保存的文件路径或错误信息)
        """
        try:
            # 确保保存目录存在
            save_dir = Path(save_path)
            save_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成文件名
            if novel_name and chapter_name:
                filename = self.generate_filename(novel_name, chapter_name)
            else:
                # 从URL中提取文件名
                parsed_url = urlparse(video_url)
                filename = os.path.basename(parsed_url.path)
                if not filename or not filename.endswith('.mp4'):
                    filename = "video.mp4"
            
            # 完整的保存路径
            file_path = save_dir / filename
            
            self.logger.info(f"开始下载视频: {video_url}")
            self.logger.info(f"保存路径: {file_path}")
            
            # 发送请求下载文件
            response = requests.get(video_url, stream=True, timeout=self.timeout)
            response.raise_for_status()
            
            # 获取文件大小
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            # 写入文件
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=self.chunk_size):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        # 显示下载进度
                        if total_size > 0:
                            progress = (downloaded_size / total_size) * 100
                          #print(f"\r📥 下载进度: {progress:.1f}% ({downloaded_size}/{total_size} bytes)", end='')
            
          #print()  # 换行
            self.logger.info(f"✅ 视频下载成功: {file_path}")
            return True, str(file_path)
            
        except requests.exceptions.RequestException as e:
            error_msg = f"网络请求错误: {e}"
            self.logger.error(f"❌ {error_msg}")
            return False, error_msg
        except Exception as e:
            error_msg = f"下载过程中发生错误: {e}"
            self.logger.error(f"❌ {error_msg}")
            return False, error_msg
    
    def download_videos_batch(self, video_info_list: list, base_save_path: str, 
                             novel_name: str) -> dict:
        """批量下载视频
        
        Args:
            video_info_list: 视频信息列表，每个元素包含 {'url': str, 'chapter_name': str}
            base_save_path: 基础保存路径
            novel_name: 小说名称
            
        Returns:
            下载结果字典
        """
        results = {}
        
        # 创建小说视频目录
        novel_video_dir = Path(base_save_path) / "videos"
        novel_video_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"开始批量下载 {len(video_info_list)} 个视频")
        
        for i, video_info in enumerate(video_info_list, 1):
            video_url = video_info.get('url')
            chapter_name = video_info.get('chapter_name')
            
            if not video_url or not chapter_name:
                self.logger.warning(f"跳过无效的视频信息: {video_info}")
                continue
            
            self.logger.info(f"[{i}/{len(video_info_list)}] 下载章节: {chapter_name}")
            
            success, result = self.download_video(
                video_url=video_url,
                save_path=str(novel_video_dir),
                novel_name=novel_name,
                chapter_name=chapter_name
            )
            
            results[chapter_name] = {
                'success': success,
                'file_path': result if success else None,
                'error': result if not success else None
            }
        
        successful_count = sum(1 for r in results.values() if r['success'])
        self.logger.info(f"批量下载完成: {successful_count}/{len(video_info_list)} 成功")
        
        return results


def download_video_from_url(video_url: str, save_path: str, 
                           novel_name: str = None, chapter_name: str = None) -> Tuple[bool, str]:
    """便捷函数：下载单个视频
    
    Args:
        video_url: 视频URL
        save_path: 保存路径
        novel_name: 小说名称
        chapter_name: 章节名称
        
    Returns:
        (是否成功, 保存的文件路径或错误信息)
    """
    downloader = VideoDownloader()
    return downloader.download_video(video_url, save_path, novel_name, chapter_name)


if __name__ == "__main__":
    # 测试代码
    downloader = VideoDownloader()
    
    # 测试单个视频下载
    test_url = "https://example.com/test_video.mp4"
    test_path = "test_downloads"
    
    success, result = downloader.download_video(
        video_url=test_url,
        save_path=test_path,
        novel_name="测试小说",
        chapter_name="chapter1_开始"
    )
    
    if success:
      print(f"下载成功: {result}")
    else:
      print(f"下载失败: {result}")
