#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
任务管理模块

负责视频剪辑任务的提交、监控和管理
"""
import os
import time
import uuid
from typing import Dict, Any, Tuple, Optional

from .aliyun_client import AliCloudClient
from .config import VideoConfig
from .timeline_builder import TimelineBuilder


class JobManager:
    """任务管理类
    
    负责视频剪辑任务的提交、监控和管理
    """
    
    def __init__(self, aliyun_client: AliCloudClient, timeline_builder: TimelineBuilder, 
                video_config: VideoConfig = None, output_dir: str = None):
        """初始化任务管理器
        
        Args:
            aliyun_client: 阿里云客户端
            timeline_builder: 时间轴构建器
            video_config: 视频配置，如果为None则使用默认配置
            output_dir: 输出目录，如果提供则将输出文件保存到该目录
        """
        self.aliyun_client = aliyun_client
        self.timeline_builder = timeline_builder
        self.video_config = video_config or VideoConfig()
        self.output_dir = output_dir

    
    def generate_output_url(self, chapter_name: str = None) -> str:
        """生成输出文件URL
        
        Args:
            chapter_name: 章节名称，用于生成有意义的文件名
            
        Returns:
            输出文件URL
        """
        # 生成唯一标识符
        unique_id = uuid.uuid4().hex[:8]
        
        # 构建文件名
        if chapter_name:
            output_filename = f"{chapter_name}_ai_novel_{unique_id}.mp4"
        else:
            output_filename = f"ai_novel_{unique_id}.mp4"
        
        # 确保使用完整的HTTPS URL
        return f"https://public-audio-xiaolongtuan.oss-cn-beijing.aliyuncs.com/wujiejvzhen/output/{output_filename}"
    
    def submit_job(self, timeline_json) -> Tuple[Optional[str], Optional[str]]:
        """提交剪辑任务
        
        Returns:
            任务ID和输出URL的元组，失败时返回(None, None)
        """
        try:
            # 生成输出URL
            output_url = self.generate_output_url()
            
            # 获取输出配置
            output_config = self.video_config.get_output_config(output_url)
            
            
            # 提交任务
            return self.aliyun_client.submit_job(timeline_json, output_config)
            
        except Exception:
            return None, None
    
    def monitor_job_status(self, job_id: str, check_interval: int = 5) -> bool:
        """监控任务状态
        
        Args:
            job_id: 任务ID
            check_interval: 检查间隔(秒)
            
        Returns:
            任务是否成功完成
        """
        while True:
            status, _, _ = self.aliyun_client.get_job_status(job_id)
            print(f"任务 {job_id} 当前状态: {status}")

            if not status:
                return False

            if status == "Success":
                return True
            elif status == "Failed":
                return False
            else:
                time.sleep(check_interval)