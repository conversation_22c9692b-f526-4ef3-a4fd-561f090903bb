#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
时间轴构建模块

负责构建视频剪辑的时间轴配置
"""

import json
import random
from tkinter import E
from typing import Dict, Any, List

from src.video_synthesis.material_manager import MaterialManager
from src.video_synthesis.subtitle_processor import SubtitleProcessor
from src.video_synthesis.config import AudioEffectsConfig, TimelineConfig, ClipTemplateConfig


class TimelineBuilder:
    """时间轴构建类
    
    负责构建视频剪辑的时间轴配置
    """
    
    def __init__(self, material_manager: MaterialManager, subtitle_processor: SubtitleProcessor):
        """初始化时间轴构建器

        Args:
            material_manager: 素材管理器
            subtitle_processor: 字幕处理器
        """
        self.material_manager = material_manager
        self.subtitle_processor = subtitle_processor
        self.audio_effects_config = AudioEffectsConfig()
        self.timeline_config = TimelineConfig()  # 使用新的Timeline配置
        self.clip_template_config = ClipTemplateConfig()  # 使用新的Clip模板配置

        # 动态获取视频和图片数量
        self.video_count = len(self.material_manager.config.get_videos())
        self.image_count = len(self.material_manager.config.get_images())

        print(f"✅ 时间轴构建器初始化成功 (视频: {self.video_count}, 图片: {self.image_count})")
    def create_base_timeline(self) -> Dict[str, Any]:
        """创建基础时间轴结构

        Returns:
            基础时间轴结构字典
        """
        return self.timeline_config.get_base_timeline()
    
    def get_audio_durations_from_storyboards(self) -> List[float]:
        """从分镜数据中获取实际音频时长

        Returns:
            每个音频片段的时长列表
        """
        durations = []

        # # print("⏱️ 从分镜数据获取实际音频时长...")

        if hasattr(self.subtitle_processor, 'story_boards_data') and self.subtitle_processor.story_boards_data:
            for i, story_board in enumerate(self.subtitle_processor.story_boards_data):
                # 优先使用audio_duration字段
                if 'audio_duration' in story_board and story_board['audio_duration'] > 0:
                    duration = float(story_board['audio_duration'])
                    # # print(f"📊 分镜{i+1}使用实际音频时长: {duration:.2f}秒")
                else:
                    raise Exception(f"分镜{i+1}没有音频时长数据")

                durations.append(duration)
        else:
            raise Exception("分镜数据为空")

        total_duration = sum(durations)
        # # print(f"⏱️ 总音频时长: {total_duration:.2f}秒，共{len(durations)}个分镜")
        return durations
    
    def add_background_music(self, timeline: Dict[str, Any], total_duration: float) -> None:
        """添加背景音乐"""
        try:
            bgm_config = self.audio_effects_config.get_random_background_music()
            if bgm_config:
                bgm_clip = self.clip_template_config.create_background_music_clip(
                    audio_url=bgm_config["url"],
                    total_duration=total_duration
                )

                # 使用配置中的音量设置覆盖默认音量
                for effect in bgm_clip.get("Effects", []):
                    if effect.get("Type") == "Volume":
                        effect["Gain"] = bgm_config["volume"]

                timeline["AudioTracks"][1]["AudioTrackClips"].append(bgm_clip)
                # # print(f"✅ 添加背景音乐完成: {bgm_config['url']} (音量: {bgm_config['volume']})")
        except Exception as e:
          print(f"⚠️ 添加背景音乐失败: {str(e)}")
    
    def add_fixed_sound_effects(self, timeline: Dict[str, Any], audio_durations: List[float]) -> None:
        """添加固定的开头和结尾音效，不添加转场音效"""
        try:
            sound_effect_duration = 2.0  # 音效持续时间

            # 开头添加固定音效
            opening_effects = self.audio_effects_config.get_opening_effects()
            if opening_effects:
                # 使用clip模板创建开场音效，使用配置中的音量
                opening_effect = opening_effects[0]
                effect_clip = self.clip_template_config.create_sound_effect_clip(
                    audio_url=opening_effect["url"],
                    start_time=0,
                    duration=sound_effect_duration,
                    volume_gain=opening_effect["volume"]
                )
                timeline["AudioTracks"][2]["AudioTrackClips"].append(effect_clip)
                # # print(f"🎵 添加开场音效 (音量: {opening_effect['volume']})")

            # 结尾添加固定音效
            total_duration = sum(audio_durations)
            ending_effects = self.audio_effects_config.get_ending_effects()
            if ending_effects:
                # 使用clip模板创建结尾音效，使用配置中的音量
                ending_effect = ending_effects[0]
                effect_clip = self.clip_template_config.create_sound_effect_clip(
                    audio_url=ending_effect["url"],
                    start_time=total_duration - sound_effect_duration,
                    duration=sound_effect_duration,
                    volume_gain=ending_effect["volume"]
                )
                timeline["AudioTracks"][2]["AudioTrackClips"].append(effect_clip)
                # # print(f"🎵 添加结尾音效 (音量: {ending_effect['volume']})")

            # # print(f"✅ 添加固定音效完成")

        except Exception as e:
          print(f"⚠️ 添加固定音效失败: {str(e)}")

    def retrieve_sound_effects(self, story_board) -> None:
        sentence = story_board["sentence"]
        sounds = self.matcher.process_sentence(sentence)
        # sound_effect_urls = process_user_input_with_rag(sounds)

    def _is_time_overlapping(self, time_in1: float, time_out1: float, time_in2: float, time_out2: float) -> bool:
        """检查两个时间段是否重叠

        Args:
            time_in1: 第一个时间段的开始时间
            time_out1: 第一个时间段的结束时间
            time_in2: 第二个时间段的开始时间
            time_out2: 第二个时间段的结束时间

        Returns:
            是否重叠
        """
        # 如果一个时间段的开始时间大于等于另一个的结束时间，则不重叠
        # 或者一个时间段的结束时间小于等于另一个的开始时间，则不重叠
        return not (time_in1 >= time_out2 or time_out1 <= time_in2)

    async def add_keyword_sound_effects(self, timeline: Dict[str, Any], keyword_sound_effects: List[Dict[str, Any]]) -> None:
        """添加关键词音效

        Args:
            timeline: 时间轴字典
            keyword_sound_effects: 关键词音效列表
        """
        try:
            if not keyword_sound_effects:
                # print("📝 没有关键词音效需要添加")
                return

            # 获取关键词音效名称列表
            keyword_effect_names = self.audio_effects_config.get_key_sound_effects_list()
            
            # 记录已添加的音效数量和跳过的音效数量
            added_count = 0
            skipped_count = 0

            for i, keyword_effect in enumerate(keyword_sound_effects):
                # 选择音效配置（循环使用）
                effect_name = keyword_effect_names[i % len(keyword_effect_names)]
                effect_config = self.audio_effects_config.get_sound_effect_by_name(effect_name)
                
                # 新音效的时间范围
                new_time_in = keyword_effect["timeline_in"]
                new_time_out = keyword_effect["timeline_out"]
                
                # 检查是否与已有音效重叠
                has_overlap = False
                latest_end_time = 0.0

                for existing_clip in timeline["AudioTracks"][2]["AudioTrackClips"]:
                    existing_time_in = existing_clip["TimelineIn"]
                    existing_time_out = existing_clip["TimelineOut"]
                    latest_end_time = max(new_time_in, existing_time_out)

                    if self._is_time_overlapping(new_time_in, new_time_out, existing_time_in, existing_time_out):
                        has_overlap = True

                # 如果有重叠且keyword_effect包含sound_effect_url，则后移时间
                if has_overlap and "sound_effect_url" in keyword_effect:
                    # 计算音效持续时间
                    effect_duration = new_time_out - new_time_in
                    # 将音效时间后移至最晚结束时间之后（无缝衔接）
                    new_time_in = latest_end_time
                    new_time_out = new_time_in + effect_duration
                    has_overlap = False  # 重置重叠标志
                    # print(f"🔄 后移关键词音效: {keyword_effect['keyword']} 至 ({new_time_in:.2f}s)")
                elif has_overlap:
                    # print(f"⚠️ 跳过重叠的关键词音效: {keyword_effect['keyword']} ({new_time_in:.2f}s)")
                    skipped_count += 1
                
                # 如果没有重叠，则添加音效
                if not has_overlap and (random.random() < 1/3 or "sound_effect_url" in keyword_effect):
                    # 确定音效URL：优先使用sound_effect_url，否则使用配置中的URL
                    audio_url = keyword_effect.get("sound_effect_url", effect_config["url"])
                    volume_gain = await self.subtitle_processor.sound_effect_manager.get_volume_multiplier_async(audio_url)

                    # 使用clip模板创建关键词音效
                    effect_clip = self.clip_template_config.create_sound_effect_clip(
                        audio_url=audio_url,
                        start_time=new_time_in,
                        duration=new_time_out - new_time_in,
                        volume_gain=volume_gain
                    )

                    timeline["AudioTracks"][2]["AudioTrackClips"].append(effect_clip)
                    # print(f"🔥 添加关键词音效: {keyword_effect['keyword']} ({new_time_in:.2f}s)")
                    added_count += 1

            # print(f"✅ 关键词音效添加完成，共添加 {added_count} 个音效，跳过 {skipped_count} 个重叠音效")

        except Exception as e:
          print(f"⚠️ 添加关键词音效失败: {str(e)}")

    def add_random_video_effects(self, timeline: Dict[str, Any], total_duration: float) -> None:
        """添加随机视频特效轨道

        Args:
            timeline: 时间轴字典
            total_duration: 视频总时长
        """
        try:
            # print("🎬 开始添加随机视频特效...")

            # 创建特效视频轨道
            effect_track = {
                "VideoTrackClips": []
            }

            # 从配置获取特效参数
            effects_config = self.timeline_config.get_video_effects_config()
            effect_interval = effects_config["effect_interval"]
            effect_duration = effects_config["effect_duration"]
            start_delay = effects_config["start_delay"]
            end_buffer = effects_config["end_buffer"]
            opacity = effects_config["opacity"]

            current_time = start_delay
            effect_count = 0

            # 获取故事上下文（用于智能选择特效）
            story_context = self.get_story_context()

            while current_time + effect_duration <= total_duration - end_buffer:
                # 智能选择特效
                effect_config = self.timeline_config.get_smart_video_effect(
                    current_time, total_duration, story_context
                )

                if effect_config:
                    # 使用clip模板创建特效clip对（视频+音频）
                    effect_clips = self.clip_template_config.create_effect_clip_pair(
                        video_url=effect_config["video_url"],
                        audio_url=effect_config["sound_url"],
                        start_time=current_time,
                        duration=effect_duration,
                        video_opacity=opacity,
                        audio_volume=0.4  # 40%音量，不干扰主音频
                    )

                    # 添加视频特效clip
                    effect_track["VideoTrackClips"].append(effect_clips["video_clip"])

                    # 添加音频特效clip到音效轨道
                    if len(timeline["AudioTracks"]) >= 3:
                        timeline["AudioTracks"][2]["AudioTrackClips"].append(effect_clips["audio_clip"])
                        # print(f"   🔊 添加特效音效: {effect_config['name']} ({current_time:.1f}s)")

                    effect_count += 1
                    # print(f"   🎭 添加特效 {effect_count}: {effect_config['name']} ({current_time:.1f}s - {current_time + effect_duration:.1f}s)")

                current_time += effect_interval

            # 添加特效轨道到时间轴
            timeline["VideoTracks"].append(effect_track)

            # print(f"✅ 随机视频特效添加完成，共 {effect_count} 个特效")

        except Exception as e:
          print(f"⚠️ 添加随机视频特效失败: {str(e)}")



    def get_story_context(self) -> str:
        """获取故事上下文，用于智能选择特效

        Returns:
            故事上下文字符串
        """
        try:
            if hasattr(self.subtitle_processor, 'story_boards_data') and self.subtitle_processor.story_boards_data:
                # 收集所有分镜的故事内容
                contexts = []
                for board in self.subtitle_processor.story_boards_data:
                    story_board = board.get('story_board', '')
                    if story_board:
                        contexts.append(story_board)

                return ' '.join(contexts)

            return ""

        except Exception as e:
            # print(f"⚠️ 获取故事上下文失败: {str(e)}")
            return ""

    def add_opening_visual_effects(self, timeline: Dict[str, Any]) -> None:
        """为第一个视频添加开场视觉特效

        Args:
            timeline: 时间轴字典
        """
        # print("🎬 添加开场视觉特效...")

        video_clips = timeline["VideoTracks"][0]["VideoTrackClips"]

        if not video_clips:
            # print("⚠️ 没有视频片段，跳过开场特效")
            return

        # 从配置获取开场特效
        opening_config = self.timeline_config.get_opening_effects_config()
        opening_effects = opening_config["effects"]
        duration = opening_config["duration"]

        # 随机选择一个开场特效
        selected_effect = random.choice(opening_effects)

        first_clip = video_clips[0]
        if "Effects" not in first_clip:
            first_clip["Effects"] = []

        # 移除现有转场效果
        first_clip["Effects"] = [effect for effect in first_clip["Effects"] if effect.get("Type") != "Transition"]

        # 使用clip模板创建开场视觉特效
        effect_params = self.timeline_config.get_opening_effect_params(selected_effect)
        opening_transition = self.clip_template_config.create_opening_visual_effect(
            sub_type=selected_effect,
            duration=duration,
            **effect_params
        )

        first_clip["Effects"].append(opening_transition)
        # print(f"🎬 第一个视频添加开场特效: {selected_effect}")

    def add_aliyun_transitions(self, timeline: Dict[str, Any]) -> None:
        """添加阿里云支持的转场特效

        Args:
            timeline: 时间轴字典
        """
        # print("✨ 添加阿里云转场特效...")

        video_clips = timeline["VideoTracks"][0]["VideoTrackClips"]

        # 从配置获取转场特效类型
        transition_config = self.timeline_config.get_transition_effects_config()
        aliyun_transition_types = transition_config["aliyun_transition_types"]

        for i, clip in enumerate(video_clips):
            if "Effects" not in clip:
                clip["Effects"] = []

            # 移除现有转场效果
            clip["Effects"] = [effect for effect in clip["Effects"] if effect.get("Type") != "Transition"]

            if i == 0:
                # 第一个片段：跳过，因为已经在开场特效中处理
                continue
            elif i == len(video_clips) - 1:
                # 最后一个片段：淡出效果
                ending_duration = transition_config["ending_duration"]
                transition_effect = self.clip_template_config.create_transition_visual_effect(
                    sub_type="fade",
                    duration=ending_duration
                )
                # print(f"✨ 片段{i+1}结尾转场: fade (淡出)")
            else:
                # 中间片段：随机选择阿里云转场类型
                transition_type = random.choice(aliyun_transition_types)
                normal_duration = transition_config["normal_duration"]

                # 从配置获取转场参数
                transition_params = self.timeline_config.get_transition_params(transition_type)
                transition_effect = self.clip_template_config.create_transition_visual_effect(
                    sub_type=transition_type,
                    duration=normal_duration,
                    **transition_params
                )

                # print(f"✨ 片段{i+1}转场特效: {transition_type}")

            if i > 0:  # 跳过第一个片段
                clip["Effects"].append(transition_effect)

    def add_aliyun_effect_tracks(self, timeline: Dict[str, Any], audio_durations: List[float]) -> None:
        """添加阿里云EffectTracks画面特效

        Args:
            timeline: 时间轴字典
            audio_durations: 每个音频片段的实际时长列表
        """
        # print("🎨 添加阿里云EffectTracks画面特效...")

        # 从配置获取VFX特效类型和时间配置
        vfx_config = self.timeline_config.get_vfx_effects_config()
        aliyun_vfx_types = vfx_config["aliyun_vfx_types"]
        timing_config = vfx_config["timing"]

        # 初始化EffectTracks
        if "EffectTracks" not in timeline:
            timeline["EffectTracks"] = []

        # 计算累积时间偏移
        cumulative_time = 0.0
        added_effects_count = 0

        for i, duration in enumerate(audio_durations):
            # 检查分镜时长是否足够添加特效
            if duration < timing_config["min_segment_duration"]:
                # print(f"🎨 分镜{i+1}时长{duration:.1f}s太短，跳过特效添加")
                cumulative_time += duration
                continue

            # 根据概率决定是否添加特效
            if random.random() > vfx_config["probability"]:
                # print(f"🎨 分镜{i+1}随机跳过特效添加")
                cumulative_time += duration
                continue

            # 在每段分镜开始后随机延迟添加特效
            segment_start = cumulative_time
            effect_start_delay = random.uniform(
                timing_config["start_delay_min"],
                timing_config["start_delay_max"]
            )
            effect_start_time = segment_start + effect_start_delay

            # 特效持续时间
            effect_duration = random.uniform(
                timing_config["duration_min"],
                timing_config["duration_max"]
            )
            effect_end_time = effect_start_time + effect_duration

            # 确保特效不超出当前分镜的时间范围
            segment_end = cumulative_time + duration
            if effect_end_time > segment_end:
                effect_end_time = segment_end
                effect_duration = effect_end_time - effect_start_time

            # 如果特效时间太短，跳过
            if effect_duration < 1.0:
                # print(f"🎨 分镜{i+1}特效时间太短，跳过")
                cumulative_time += duration
                continue

            # 随机选择特效类型
            vfx_type = random.choice(aliyun_vfx_types)

            # 创建特效配置
            effect_item = self.clip_template_config.create_effect_track_vfx(
                sub_type=vfx_type,
                start_time=effect_start_time,
                end_time=effect_end_time
            )

            # 创建EffectTrack
            effect_track = {
                "EffectTrackItems": [effect_item]
            }

            timeline["EffectTracks"].append(effect_track)
            added_effects_count += 1

            # print(f"🎨 分镜{i+1}添加画面特效: {vfx_type} ({effect_start_time:.1f}s-{effect_end_time:.1f}s)")

            cumulative_time += duration

        # print(f"✅ 共添加了 {added_effects_count} 个画面特效")
    
    async def add_subtitles(self, timeline: Dict[str, Any], audio_durations: List[float]) -> List[Dict[str, Any]]:
        """添加字幕（基于火山引擎字幕数据和实际音频时长）

        Args:
            timeline: 时间轴字典
            audio_durations: 每个音频片段的实际时长列表

        Returns:
            关键词音效列表
        """

        # print("📝 开始添加火山引擎字幕和关键词强化...")

        # 使用新的字幕处理方法，一次性获取所有字幕片段和关键词音效
        all_subtitle_clips, keyword_sound_effects = await self.subtitle_processor.get_all_subtitle_clips(audio_durations)

        # 将所有字幕片段添加到字幕轨道
        for clip in all_subtitle_clips:
            timeline["SubtitleTracks"][0]["SubtitleTrackClips"].append(clip)

        # print(f"✅ 字幕添加完成，共 {len(all_subtitle_clips)} 个字幕片段，{len(keyword_sound_effects)} 个关键词音效")
        return keyword_sound_effects
    
    async def build_timeline(self) -> str:
        """构建完整的时间轴
        
        Returns:
            时间轴JSON字符串
        """
        print("🔨 构建动态时长Timeline配置...")
        # print("🎯 核心功能: 视觉素材自动对齐音频时长，确保配音完整播放")
        
        # 创建基础时间轴
        timeline = self.create_base_timeline()
        
        # 从分镜数据获取实际音频时长
        audio_durations = self.get_audio_durations_from_storyboards()

        # 添加视频和音频配对（自动对齐）
        self.add_video_audio_pairs(timeline, audio_durations)

        # 添加图片和音频配对（自动对齐）
        self.add_image_audio_pairs(timeline, audio_durations)
        total_duration = sum(audio_durations)
        
        # 📝 添加字幕（使用实际时长）并获取关键词音效
        keyword_sound_effects = await self.add_subtitles(timeline, audio_durations)

        # 🎼 添加背景音乐（全程）
        self.add_background_music(timeline, total_duration)

        # 🎵 添加固定音效（开头和结尾）
        self.add_fixed_sound_effects(timeline, audio_durations)

        # 🔥 添加关键词音效
        await self.add_keyword_sound_effects(timeline, keyword_sound_effects)

        # # 🎬 添加随机视频特效
        # self.add_random_video_effects(timeline, total_duration)

        # 🎬 添加开场视觉特效
        self.add_opening_visual_effects(timeline)

        # ✨ 添加阿里云转场特效
        self.add_aliyun_transitions(timeline)

        # 🎨 添加阿里云EffectTracks画面特效
        self.add_aliyun_effect_tracks(timeline, audio_durations)

        # 添加全局元素
        self.add_global_elements(timeline, total_duration)
        
        # print("✅ 动态时长Timeline构建完成")
        print(f"⏱️ 视频总时长: {total_duration:.1f}秒")
    
        # 保存Timeline用于调试
        try:
            with open("src/video_synthesis/timelines/timeline_debug.json", "w", encoding="utf-8") as f:
                json.dump(timeline, f, indent=4, ensure_ascii=False)
            print("✅ Timeline已保存至 timeline_debug.json")
        except Exception as e:
          print(f"⚠️ 保存Timeline失败: {e}")
        
        return json.dumps(timeline)
    
    def add_global_elements(self, timeline: Dict[str, Any], total_duration: float) -> None:
        """添加全局元素（角标和水印）"""
        # 添加角标
        corner_logo_url = self.material_manager.config.get_corner_logo()
        if corner_logo_url:
            corner_logo_clip = self.clip_template_config.create_corner_logo_clip(
                logo_url=corner_logo_url,
                total_duration=total_duration
            )
            timeline["VideoTracks"][1]["VideoTrackClips"].append(corner_logo_clip)
            # print("📍 添加全局角标: '热门小说'")

        # 添加AI水印
        watermark_url = self.material_manager.config.get_ai_watermark()
        if watermark_url:
            ai_watermark_clip = self.clip_template_config.create_watermark_clip(
                watermark_url=watermark_url,
                total_duration=total_duration
            )
            timeline["VideoTracks"][2]["VideoTrackClips"].append(ai_watermark_clip)
            # print("💧 添加AI生成提醒水印")

    def create_video_clip(self, audio_clip_id: str, video_url: str, is_first: bool) -> Dict[str, Any]:
        """创建视频剪辑配置

        Args:
            audio_clip_id: 音频剪辑ID（用于自动对齐）
            video_url: 视频URL
            is_first: 是否是第一个视频片段

        Returns:
            视频剪辑配置字典
        """
        return self.clip_template_config.create_main_video_clip(
            reference_clip_id=audio_clip_id,
            video_url=video_url,
            is_first=is_first
        )
    
    def create_audio_clip(self, clip_id: str, audio_url: str) -> Dict[str, Any]:
        """创建音频剪辑配置

        Args:
            clip_id: 剪辑ID
            audio_url: 音频URL

        Returns:
            音频剪辑配置字典
        """
        return self.clip_template_config.create_main_audio_clip(clip_id, audio_url)
    
    def create_image_clip(self, audio_clip_id: str, image_url: str, effect_type: int, audio_duration) -> Dict[str, Any]:
        """创建图片剪辑配置

        Args:
            audio_clip_id: 音频剪辑ID（用于自动对齐）
            image_url: 图片URL
            effect_type: 效果类型(0-3)

        Returns:
            图片剪辑配置字典
        """
        return self.clip_template_config.create_kenburns_image_clip(
            reference_clip_id=audio_clip_id,
            image_url=image_url,
            effect_type=effect_type,
            duration=audio_duration
        )
    
    def add_video_audio_pairs(self, timeline: Dict[str, Any], audio_durations: List[float]) -> None:
        """添加视频和音频配对（使用自动对齐）"""
        video_count = min(len(audio_durations), self.video_count) if self.video_count > 0 else len(audio_durations)
        pairs = self.material_manager.get_video_audio_pairs(video_count)
        
        current_time = 0.0
        for i, (video_url, audio_url) in enumerate(pairs):
            audio_clip_id = f"audio_{i+1}"
            audio_duration = audio_durations[i]
            
            # 添加音频片段，设置ClipId和时间轴位置
            audio_clip = self.create_audio_clip(
                audio_clip_id, 
                audio_url,
            )
            timeline["AudioTracks"][0]["AudioTrackClips"].append(audio_clip)
            
            # 添加视频片段，使用ReferenceClipId对齐音频
            video_clip = self.create_video_clip(audio_clip_id, video_url, i == 0)
            timeline["VideoTracks"][0]["VideoTrackClips"].append(video_clip)
            
            current_time += audio_duration
            # print(f"📊 视频片段{i+1}配音时间轴: {current_time-audio_duration:.1f}s - {current_time:.1f}s")

    def add_image_audio_pairs(self, timeline: Dict[str, Any], audio_durations: List[float]) -> None:
        """添加图片和音频配对（使用自动对齐）"""
        # 计算视频占用的分镜数量
        video_count = min(len(audio_durations), self.video_count) if self.video_count > 0 else 0
        start_audio_index = video_count
        image_count = len(audio_durations) - video_count

        if image_count <= 0:
            # print("⚠️ 没有图片分镜需要处理")
            return

        pairs = self.material_manager.get_image_audio_pairs(start_audio_index, image_count)
        
        # 计算图片音频的起始时间（视频音频之后）
        current_time = sum(audio_durations[:self.video_count])
        
        for i, (image_url, audio_url) in enumerate(pairs):
            audio_index = start_audio_index + i
            audio_clip_id = f"audio_{audio_index+1}"
            audio_duration = audio_durations[audio_index]
            
            # 添加音频片段，设置ClipId和时间轴位置
            audio_clip = self.create_audio_clip(
                audio_clip_id, 
                audio_url
            )
            timeline["AudioTracks"][0]["AudioTrackClips"].append(audio_clip)
            
            # 添加图片片段，使用ReferenceClipId对齐音频
            effect_type = i % 4
            image_clip = self.create_image_clip(audio_clip_id, image_url, effect_type, audio_duration)
            timeline["VideoTracks"][0]["VideoTrackClips"].append(image_clip)
            
            current_time += audio_duration
            # print(f"📊 图片片段{i+1}配音时间轴: {current_time-audio_duration:.1f}s - {current_time:.1f}s")