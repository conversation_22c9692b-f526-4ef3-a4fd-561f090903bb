#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
阿里云客户端模块

负责与阿里云API的交互，包括任务提交和状态查询
"""

import json
from typing import Dict, Any, Tuple, Optional

from alibabacloud_ice20201109.client import Client as ICE20201109Client
from alibabacloud_ice20201109 import models as ice_models
from alibabacloud_tea_openapi import models as open_api_models

from .config import AliCloudConfig


class AliCloudClient:
    """阿里云客户端类
    
    负责与阿里云API的交互，包括任务提交和状态查询
    """
    
    def __init__(self, config: AliCloudConfig = None):
        """初始化阿里云客户端
        
        Args:
            config: 阿里云配置对象，如果为None则使用默认配置
        """
        if config is None:
            config = AliCloudConfig()
            
        # 配置阿里云客户端
        ali_config = open_api_models.Config(
            access_key_id=config.access_key_id,
            access_key_secret=config.access_key_secret
        )
        ali_config.endpoint = config.endpoint
        ali_config.protocol = config.protocol
        ali_config.region_id = config.region_id  # 添加区域ID配置
        
        # 创建客户端
        self.client = ICE20201109Client(ali_config)
      #print(f"✅ 阿里云客户端初始化成功，区域: {config.region_id}")
        
    def submit_job(self, timeline_json: str, output_config: Dict[str, Any]) -> Tuple[Optional[str], Optional[str]]:
        """提交媒体制作任务
        
        Args:
            timeline_json: 时间轴JSON字符串
            output_config: 输出配置字典
            
        Returns:
            任务ID和输出URL的元组，失败时返回(None, None)
        """
        try:
            # 准备请求
            request = ice_models.SubmitMediaProducingJobRequest()
            request.timeline = timeline_json
            request.output_media_config = json.dumps(output_config)
            # with open("src/video_synthesis/timelines/timeline.txt", "w") as f:
            #     f.write(timeline_json)
            # with open("src/video_synthesis/timelines/output_media.txt", "w") as f:
            #     f.write(json.dumps(output_config))
            # exit(0)
            # 发送请求
            response = self.client.submit_media_producing_job(request)
            job_id = response.body.job_id
            output_url = output_config.get("MediaURL")
            
          #print(f"🚀 剪辑任务提交成功！")
          #print(f"📋 任务ID: {job_id}")
          #print(f"📁 输出地址: {output_url}")
            
            return job_id, output_url
            
        except Exception as e:
          #print(f"❌ 提交任务失败: {e}")
            return None, None
    
    def get_job_status(self, job_id: str) -> Tuple[Optional[str], Optional[str], Optional[str]]:
        """获取任务状态
        
        Args:
            job_id: 任务ID
            
        Returns:
            (状态, 错误码, 错误信息) 的元组
        """
        try:
            request = ice_models.GetMediaProducingJobRequest()
            request.job_id = job_id
            
            response = self.client.get_media_producing_job(request)
            job_info = response.body.media_producing_job
            status = job_info.status
            
            if status == "Failed":
                return status, job_info.code, job_info.message
            
            return status, None, None
        except Exception as e:
          #print(f"❌ 查询任务状态失败: {e}")
            return None, None, None