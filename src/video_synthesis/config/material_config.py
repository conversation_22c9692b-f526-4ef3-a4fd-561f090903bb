#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
素材配置管理

管理视频、图片、音频等素材的URL配置
"""

import json
from typing import Dict, Any, List, Optional


class MaterialConfig:
    """素材配置类
    
    管理视频、图片、音频等素材的URL配置
    """
    
    def __init__(self, json_file_path: Optional[str] = None, chapter_data: Optional[Dict[str, Any]] = None):
        """初始化素材配置
        
        Args:
            json_file_path: JSON配置文件路径，如果提供则从文件加载配置
            chapter_data: 章节数据字典，直接提供素材数据
        """
        # 默认素材URL配置
        self.materials = {
            'videos': [],
            'images': [],
            'audios': [],
            'corner_logo': 'https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/resources/%E8%A7%92%E6%A0%871%EF%BC%88%E9%BB%91%E5%BA%95%E5%AD%97%EF%BC%89.png',
            'ai_watermark': 'https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/resources/AI%E6%B0%B4%E5%8D%B0.png'
        }
        
        # 如果提供了章节数据，直接使用
        if chapter_data:
            self.load_from_chapter_data(chapter_data)
        # 如果提供了JSON文件路径，则从文件加载配置
        elif json_file_path:
            self.load_from_json(json_file_path)
    
    def load_from_chapter_data(self, chapter_data: Dict[str, Any]) -> None:
        """从章节数据加载素材配置
        
        Args:
            chapter_data: 章节数据字典
        """
        try:
            # 提取视频URL
            if 'video_urls' in chapter_data and isinstance(chapter_data['video_urls'], list):
                self.materials['videos'] = chapter_data['video_urls']
            
            # 提取图片URL
            if 'image_urls' in chapter_data and isinstance(chapter_data['image_urls'], list):
                self.materials['images'] = chapter_data['image_urls']
            
            # 提取音频URL
            if 'audio_urls' in chapter_data and isinstance(chapter_data['audio_urls'], list):
                self.materials['audios'] = chapter_data['audio_urls']

            # 提取角标和水印
            if 'corner_logo' in chapter_data and isinstance(chapter_data['corner_logo'], str):
                self.materials['corner_logo'] = chapter_data['corner_logo']

            if 'ai_watermark' in chapter_data and isinstance(chapter_data['ai_watermark'], str):
                self.materials['ai_watermark'] = chapter_data['ai_watermark']
            
          #print(f"✅ 成功从章节数据加载素材配置")
        except Exception as e:
          print(f"⚠️ 从章节数据加载素材配置失败: {str(e)}")
    
    def load_from_json(self, json_file_path: str) -> None:
        """从JSON文件加载素材配置
        
        Args:
            json_file_path: JSON配置文件路径
        """
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                
            # 更新素材配置
            if isinstance(config_data, dict):
                # 检查并更新各类素材
                if 'videos' in config_data and isinstance(config_data['videos'], list):
                    self.materials['videos'] = config_data['videos']
                
                if 'images' in config_data and isinstance(config_data['images'], list):
                    self.materials['images'] = config_data['images']
                
                if 'audios' in config_data and isinstance(config_data['audios'], list):
                    self.materials['audios'] = config_data['audios']
                
                if 'corner_logo' in config_data and isinstance(config_data['corner_logo'], str):
                    self.materials['corner_logo'] = config_data['corner_logo']
                
                if 'ai_watermark' in config_data and isinstance(config_data['ai_watermark'], str):
                    self.materials['ai_watermark'] = config_data['ai_watermark']
            
          #print(f"✅ 成功从JSON文件加载素材配置: {json_file_path}")
        except FileNotFoundError:
          print(f"⚠️ 配置文件不存在: {json_file_path}")
        except json.JSONDecodeError as e:
          print(f"⚠️ JSON文件格式错误: {str(e)}")
        except Exception as e:
          print(f"⚠️ 加载配置文件失败: {str(e)}")
    
    def get_videos(self) -> List[str]:
        """获取视频URL列表"""
        return self.materials.get('videos', [])
    
    def get_images(self) -> List[str]:
        """获取图片URL列表"""
        return self.materials.get('images', [])
    
    def get_audios(self) -> List[str]:
        """获取音频URL列表"""
        return self.materials.get('audios', [])
    
    def get_corner_logo(self) -> str:
        """获取角标URL"""
        return self.materials['corner_logo']

    def get_ai_watermark(self) -> str:
        """获取AI水印URL"""
        return self.materials['ai_watermark']
    
    def add_video(self, video_url: str) -> None:
        """添加视频URL"""
        if video_url and video_url not in self.materials['videos']:
            self.materials['videos'].append(video_url)
            print(f"📹 添加视频: {video_url}")
    
    def add_image(self, image_url: str) -> None:
        """添加图片URL"""
        if image_url and image_url not in self.materials['images']:
            self.materials['images'].append(image_url)
            print(f"🖼️ 添加图片: {image_url}")
    
    def add_audio(self, audio_url: str) -> None:
        """添加音频URL"""
        if audio_url and audio_url not in self.materials['audios']:
            self.materials['audios'].append(audio_url)
            print(f"🎵 添加音频: {audio_url}")

    def set_corner_logo(self, logo_url: str) -> None:
        """设置角标URL"""
        self.materials['corner_logo'] = logo_url
        if logo_url:
            print(f"🏷️ 设置角标: {logo_url}")
        else:
            print("🏷️ 清空角标URL")

    def set_ai_watermark(self, watermark_url: str) -> None:
        """设置AI水印URL"""
        self.materials['ai_watermark'] = watermark_url
        if watermark_url:
            print(f"💧 设置水印: {watermark_url}")
        else:
            print("💧 清空水印URL")
    

    def get_material_count(self) -> Dict[str, int]:
        """获取各类素材数量统计"""
        return {
            'videos': len(self.materials['videos']),
            'images': len(self.materials['images']),
            'audios': len(self.materials['audios']),
            'has_corner_logo': bool(self.materials['corner_logo']),
            'has_ai_watermark': bool(self.materials['ai_watermark'])
        }
    
    def clear_all(self) -> None:
        """清空所有素材配置"""
        self.materials = {
            'videos': [],
            'images': [],
            'audios': [],
            'corner_logo': '',
            'ai_watermark': ''
        }
        print("🗑️ 已清空所有素材配置")
    
    def save_to_json(self, json_file_path: str) -> None:
        """保存配置到JSON文件
        
        Args:
            json_file_path: JSON文件保存路径
        """
        try:
            with open(json_file_path, 'w', encoding='utf-8') as f:
                json.dump(self.materials, f, ensure_ascii=False, indent=2)
            print(f"✅ 素材配置已保存到: {json_file_path}")
        except Exception as e:
            print(f"⚠️ 保存配置文件失败: {str(e)}")
    
    def validate_materials(self) -> Dict[str, bool]:
        """验证素材配置的有效性
        
        Returns:
            验证结果字典
        """
        result = {
            'has_videos': len(self.materials['videos']) > 0,
            'has_images': len(self.materials['images']) > 0,
            'has_audios': len(self.materials['audios']) > 0,
            'has_corner_logo': bool(self.materials['corner_logo']),
            'has_ai_watermark': bool(self.materials['ai_watermark'])
        }
        
        # 检查是否至少有视频或图片
        result['has_visual_content'] = result['has_videos'] or result['has_images']
        
        return result
