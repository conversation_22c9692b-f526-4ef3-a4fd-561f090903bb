#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
视频配置管理

管理视频输出参数和格式设置
"""

from typing import Dict, Any


class VideoConfig:
    """视频配置类
    
    管理视频输出参数和格式设置
    """
    
    def __init__(self):
        """初始化视频配置参数"""
        self.width = 720
        self.height = 1280
        self.video_bitrate = 1000
        self.fps = 24
      #print("✅ 视频配置初始化完成")
    
    def get_output_config(self, output_url: str) -> Dict[str, Any]:
        """获取输出配置
        
        Args:
            output_url: 输出文件URL
            
        Returns:
            输出配置字典
        """
        return {
            "MediaURL": output_url,
            "Width": self.width,
            "Height": self.height,
            "Bitrate": self.video_bitrate,
            "Video": {
                "Codec": "H.264",
                "Fps": self.fps
            }
        }
    
    def update_resolution(self, width: int, height: int) -> None:
        """更新分辨率设置
        
        Args:
            width: 视频宽度
            height: 视频高度
        """
        self.width = width
        self.height = height
      #print(f"📐 视频分辨率更新为: {width}x{height}")
    
    def update_bitrate(self, video_bitrate: int, audio_bitrate: int = None) -> None:
        """更新比特率设置
        
        Args:
            video_bitrate: 视频比特率
            audio_bitrate: 音频比特率（可选）
        """
        self.video_bitrate = video_bitrate
        if audio_bitrate is not None:
            self.audio_bitrate = audio_bitrate
      #print(f"🎬 比特率更新为: 视频{video_bitrate}kbps, 音频{self.audio_bitrate}kbps")
    
    def get_aspect_ratio(self) -> float:
        """获取宽高比
        
        Returns:
            宽高比值
        """
        return self.width / self.height
    
    def is_portrait(self) -> bool:
        """判断是否为竖屏
        
        Returns:
            True表示竖屏，False表示横屏
        """
        return self.height > self.width
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要
        
        Returns:
            配置摘要字典
        """
        return {
            "resolution": f"{self.width}x{self.height}",
            "aspect_ratio": f"{self.get_aspect_ratio():.2f}",
            "orientation": "portrait" if self.is_portrait() else "landscape",
            "video_bitrate": f"{self.video_bitrate}kbps",
            "audio_bitrate": f"{self.audio_bitrate}kbps",
            "format": self.format,
            "long_short_mode": self.long_short_mode,
            "adj_dar_method": self.adj_dar_method
        }
