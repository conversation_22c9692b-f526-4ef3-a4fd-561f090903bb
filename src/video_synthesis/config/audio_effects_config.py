#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
音频特效配置管理

管理音效、背景音乐等音频资源配置
"""

import random
from typing import Dict, List, Any


class AudioEffectsConfig:
    """音频特效配置类
    
    管理音效、背景音乐等音频资源配置
    """

    def __init__(self):
        """初始化音频特效配置"""
        # 音效配置 - 使用字典结构，包含URL和音量属性
        self.sound_effects = {
            "人群嘈杂声": {
                "url": "https://novel-ai-wujiejvzhen.tos-cn-beijing.volces.com/resources/sound_effects/%E5%B8%B8%E7%94%A8/%E4%BA%BA%E7%BE%A4%E5%98%88%E6%9D%82%E5%A3%B0.MP3",
                "volume": 1.2  # 90%音量
            },
            "人群脚步声": {
                "url": "https://novel-ai-wujiejvzhen.tos-cn-beijing.volces.com/resources/sound_effects/%E5%B8%B8%E7%94%A8/%E4%BA%BA%E7%BE%A4%E8%84%9A%E6%AD%A5%E5%A3%B0.MP3",
                "volume": 1.2  # 90%音量
            },
            "叮咚一声": {
                "url": "https://novel-ai-wujiejvzhen.tos-cn-beijing.volces.com/resources/sound_effects/%E5%B8%B8%E7%94%A8/%E5%8F%AE%E5%92%9A%E4%B8%80%E5%A3%B0-%E6%83%8A%E5%96%9C%E5%87%BA%E7%8E%B0.MP3",
                "volume": 1.2  # 90%音量
            },
            "哐-有回音": {
                "url": "https://novel-ai-wujiejvzhen.tos-cn-beijing.volces.com/resources/sound_effects/%E5%B8%B8%E7%94%A8/%E5%93%90-%E6%9C%89%E5%9B%9E%E9%9F%B3-%E9%9C%87%E6%83%8A%E7%B4%A7%E5%BC%A0%E5%AE%B3%E6%80%95.MP3",
                "volume": 1.2  # 90%音量
            },
            "哼-男声": {
                "url": "https://novel-ai-wujiejvzhen.tos-cn-beijing.volces.com/resources/sound_effects/%E5%B8%B8%E7%94%A8/%E5%93%BC-%E7%94%B7%E5%A3%B0.MP3",
                "volume": 1.2  # 90%音量
            },
            "唰一声": {
                "url": "https://novel-ai-wujiejvzhen.tos-cn-beijing.volces.com/resources/sound_effects/%E5%B8%B8%E7%94%A8/%E5%94%B0%E4%B8%80%E5%A3%B0.MP3",
                "volume": 1.2  # 90%音量
            },
            "嗖嗖一声": {
                "url": "https://novel-ai-wujiejvzhen.tos-cn-beijing.volces.com/resources/sound_effects/%E5%B8%B8%E7%94%A8/%E5%97%96%E5%97%96%E4%B8%80%E5%A3%B0.MP3",
                "volume": 1.2  # 90%音量
            },
            "开场-叮咚": {
                "url": "https://novel-ai-wujiejvzhen.tos-cn-beijing.volces.com/resources/sound_effects/%E5%B8%B8%E7%94%A8/%E5%BC%80%E5%9C%BA-%E5%8F%AE%E5%92%9A.MP3",
                "volume": 1.2  # 90%音量
            },
            "脚步声": {
                "url": "https://novel-ai-wujiejvzhen.tos-cn-beijing.volces.com/resources/sound_effects/%E5%B8%B8%E7%94%A8/%E8%84%9A%E6%AD%A5%E5%A3%B0.MP3",
                "volume": 1.2  # 90%音量
            },
            "叹气-男声": {
                "url": "https://novel-ai-wujiejvzhen.tos-cn-beijing.volces.com/resources/sound_effects/%E5%B8%B8%E7%94%A8/%E5%8F%B9%E6%B0%94-%E7%94%B7%E5%A3%B0.MP3",
                "volume": 1.2

            }
        }

        # 背景音乐配置 - 添加音量属性
        self.background_music = [
            {
                "url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/resources/bgm/%5B%E5%8F%A4%E9%9F%B5%E7%BA%AF%E9%9F%B3%E4%B9%90001%5D.mp3",
                "volume": 0.1  # 30%音量，背景音乐不应过于突出
            },
            {
                "url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/resources/bgm/%5B%E5%8F%A4%E9%9F%B5%E7%BA%AF%E9%9F%B3%E4%B9%90002%5D.mp3",
                "volume": 0.1  # 30%音量
            },
            {
                "url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/resources/bgm/%5B%E5%8F%A4%E9%9F%B5%E7%BA%AF%E9%9F%B3%E4%B9%90003%5D.mp3",
                "volume": 0.1  # 30%音量
            }
        ]

        # 关键音效列表（用于关键词高亮）- 使用音效名称引用
        self.key_sound_effects_list = [
            "叮咚一声",
            "哼-男声",
            "脚步声",
            "开场-叮咚",
            "哐-有回音",
            "人群嘈杂声",
            "人群脚步声",
            "唰一声",
            "嗖嗖一声",
            "叹气-男声"
        ]

        # 开场固定音效（第一个视频开始时使用）- 使用音效名称引用
        self.opening_effects = ["开场-叮咚"]

        # 结尾音效 - 使用音效名称引用
        self.ending_effects = ["开场-叮咚"]

        # 开场和结尾音效的默认音量
        self.opening_effect_volume = 1.2  # 80%音量
        self.ending_effect_volume = 1.2  # 60%音量

        self.sound_effect_duration = 1.8  # 每个音效的默认持续时间（秒）

      #print("✅ 音频特效配置初始化完成")

    def get_key_sound_effects_list(self) -> List[str]:
        """获取关键音效列表"""
        return self.key_sound_effects_list

    def get_sound_effects_list(self) -> List[Dict[str, Any]]:
        """获取所有音效配置列表"""
        return list(self.sound_effects.values())

    def get_sound_effect_by_name(self, name: str) -> Dict[str, Any]:
        """根据名称获取音效配置

        Args:
            name: 音效名称

        Returns:
            音效配置字典，包含url和volume，如果不存在返回空字典
        """
        return self.sound_effects.get(name, {})

    def get_opening_effects(self) -> List[Dict[str, Any]]:
        """获取开场音效配置列表"""
        effects = []
        for name in self.opening_effects:
            if name in self.sound_effects:
                effect_config = self.sound_effects[name].copy()
                # 使用开场音效的默认音量，如果配置中没有指定
                if "volume" not in effect_config:
                    effect_config["volume"] = self.opening_effect_volume
                effects.append(effect_config)
        return effects

    def get_ending_effects(self) -> List[Dict[str, Any]]:
        """获取结尾音效配置列表"""
        effects = []
        for name in self.ending_effects:
            if name in self.sound_effects:
                effect_config = self.sound_effects[name].copy()
                # 使用结尾音效的默认音量，如果配置中没有指定
                if "volume" not in effect_config:
                    effect_config["volume"] = self.ending_effect_volume
                effects.append(effect_config)
        return effects

    def get_background_music(self) -> List[Dict[str, Any]]:
        """获取背景音乐配置列表"""
        return self.background_music

    def get_random_sound_effect(self) -> Dict[str, Any]:
        """随机获取一个音效配置"""
        return random.choice(list(self.sound_effects.values()))

    def get_random_background_music(self) -> Dict[str, Any]:
        """随机获取一个背景音乐配置"""
        return random.choice(self.background_music)

    def get_random_key_sound_effect(self) -> Dict[str, Any]:
        """随机获取一个关键音效配置"""
        effect_name = random.choice(self.key_sound_effects_list)
        return self.sound_effects[effect_name]

    def add_sound_effect(self, name: str, url: str, volume: float = 1.1) -> None:
        """添加新的音效

        Args:
            name: 音效名称
            url: 音效URL
            volume: 音效音量 (0.0-1.0)
        """
        self.sound_effects[name] = {"url": url, "volume": volume}
      #print(f"🔊 添加音效: {name} -> {url} (音量: {volume})")

    def add_background_music(self, url: str, volume: float = 0.2) -> None:
        """添加新的背景音乐

        Args:
            url: 背景音乐URL
            volume: 背景音乐音量 (0.0-1.0)
        """
        music_config = {"url": url, "volume": volume}
        if music_config not in self.background_music:
            self.background_music.append(music_config)
          #print(f"🎵 添加背景音乐: {url} (音量: {volume})")

    def remove_sound_effect(self, name: str) -> bool:
        """移除音效
        
        Args:
            name: 音效名称
            
        Returns:
            是否成功移除
        """
        if name in self.sound_effects:
            del self.sound_effects[name]
          #print(f"🗑️ 移除音效: {name}")
            return True
        return False

    def get_sound_effects_by_category(self, category: str) -> Dict[str, str]:
        """根据类别获取音效
        
        Args:
            category: 类别关键词
            
        Returns:
            匹配的音效字典
        """
        category_lower = category.lower()
        matching_effects = {}

        for name, url in self.sound_effects.items():
            if category_lower in name.lower():
                matching_effects[name] = url

        return matching_effects

    def get_config_summary(self) -> Dict[str, int]:
        """获取配置摘要
        
        Returns:
            配置统计信息
        """
        return {
            "total_sound_effects": len(self.sound_effects),
            "total_background_music": len(self.background_music),
            "key_sound_effects": len(self.key_sound_effects_list),
            "opening_effects": len(self.opening_effects),
            "ending_effects": len(self.ending_effects)
        }
