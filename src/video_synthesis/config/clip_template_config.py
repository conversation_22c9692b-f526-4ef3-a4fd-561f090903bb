#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Clip模板配置管理

统一管理所有类型的clip字典模板
"""

import copy
from typing import Dict, Any, List


class ClipTemplateConfig:
    """Clip模板配置类

    统一管理所有类型的clip字典模板
    """

    def __init__(self):
        """初始化Clip模板配置"""
        # 音频clip模板配置
        self.audio_clip_template = {
            "base": {
                "ClipId": "",  # 将在使用时设置
                "MediaURL": "",  # 将在使用时设置
                "Effects": [{"Type": "Volume", "Gain": 1.15}],  # +15%音量
            },
            "main_track": {
                "ClipId": "",
                "MediaURL": "",
                "Effects": [{"Type": "Volume", "Gain": 1.15}],  # 主音频轨道保持原音量
            },
            "background_music": {
                "MediaURL": "",
                "In":3,
                "TimelineOut": 0,  # 将在使用时设置
                "Effects": [
                    {"Type": "Volume", "Gain": 0.3},
                    {"Type": "AFade", "SubType": "In", "Duration": 1, "Curve": "tri"},
                    {"Type": "AFade", "SubType": "Out", "Duration": 1, "Curve": "tri"},
                ],  # 背景音乐30%音量
                "LoopMode": True,
            },
            "sound_effect": {
                "MediaURL": "",
                "TimelineIn": 0,  # 将在使用时设置
                "TimelineOut": 0,  # 将在使用时设置
                "Effects": [
                    {"Type": "Volume", "Gain": 1},  # 音效80%音量
                    # {  # 淡入淡出
                    #     "Type": "AFade",
                    #     "SubType": "In",
                    #     "Duration": 0.3,
                    #     "Curve": "tri",
                    # },
                    {
                        "Type": "AFade",
                        "SubType": "Out",
                        "Duration": 0.3,
                        "Curve": "tri",
                    },
                ],
            },
            "audio_effect": {
                "MediaURL": "",
                "TimelineIn": 0,  # 将在使用时设置
                "TimelineOut": 0,  # 将在使用时设置
                "Effects": [
                    {"Type": "Volume", "Gain": 1},  # 音效80%音量
                    # {  # 淡入淡出
                    #     "Type": "AFade",
                    #     "SubType": "In",
                    #     "Duration": 0.3,
                    #     "Curve": "tri",
                    # },
                    {
                        "Type": "AFade",
                        "SubType": "Out",
                        "Duration": 0.3,
                        "Curve": "tri",
                    },
                ],
            },
        }
        self.filter_template = { # 降低饱和度
                "Type": "Filter",
                "SubType": "color",
                "ExtParams": "effect=color,saturation=-10"
            }

        # 视频clip模板配置
        self.video_clip_template = {
            "base": {
                "Type": "Video",
                "ReferenceClipId": "",  # 将在使用时设置
                "MediaURL": "",  # 将在使用时设置
                "Effects": [],
            },
            "with_transition": {
                "Type": "Video",
                "ReferenceClipId": "",
                "MediaURL": "",
                "Effects": [
                    {"Type": "Transition", "SubType": "random", "Duration": 1.0}
                ],
            },
            "with_opening_effect": {
                "Type": "Video",
                "ReferenceClipId": "",
                "MediaURL": "",
                "Effects": [{"Type": "VFX", "SubType": "bluropen", "Duration": 1.0}],
            },
            "effect_overlay": {
                "Type": "Video",
                "MediaURL": "",
                "TimelineIn": 0,
                "TimelineOut": 0,
                "X": 0,
                "Y": 0,
                "Width": 1.0,
                "Height": 1.0,
                "Effects": [{"Type": "Opacity", "Value": 0.6}],
            },
            "video_effect": {
                "Type": "Video",
                "MediaURL": "",
                "TimelineIn": 0,
                "TimelineOut": 0,
                "X": 0,
                "Y": 0,
                "Width": 1.0,
                "Height": 1.0,
                "Effects": [{"Type": "Opacity", "Value": 0.6}],
            },
        }

        # 视觉特效clip模板配置
        self.visual_effects_template = {
            "opening_vfx": {
                "Type": "VFX",
                "SubType": "",  # 将在使用时设置
                "Duration": 1.0,  # 默认持续时间
            },
            "transition_vfx": {
                "Type": "Transition",
                "SubType": "",  # 将在使用时设置
                "Duration": 0.8,  # 默认持续时间
            },
            "custom_vfx": {
                "Type": "VFX",
                "SubType": "",
                "Duration": 1.0,
                "Intensity": 0.8,  # 默认强度
            },
            "effect_track_vfx": {
                "Type": "VFX",
                "SubType": "",  # 将在使用时设置
                "TimelineIn": 0,  # 将在使用时设置
                "TimelineOut": 0,  # 将在使用时设置
            },
        }

        # 图片clip模板配置
        self.image_clip_template = {
            "base": {
                "Type": "Image",
                "ReferenceClipId": "",  # 将在使用时设置
                "MediaURL": "",  # 将在使用时设置
                "Effects": [],
            },
            "with_kenburns": {
                "Type": "Image",
                "ReferenceClipId": "",
                "MediaURL": "",
                "Effects": [
                    {"Type": "Transition", "SubType": "random"},
                    {
                        "Type": "Zoom",
                        "StartRate": 1,
                        "EndRate":1.7,
                        "Loop": True
                    },
                ],
            },
            "coordinate_kenburns": {
                "Type": "Image",
                "ReferenceClipId": "",
                "MediaURL": "",
                "Effects": [
                    {"Type": "Transition", "SubType": "random"},
                    {
                        "Type": "KenBurns",
                        "Start": "0,0,0.6,0.6",
                        "End": "0.2,0.2,0.8,0.8",
                        "Loop": True
                    },
                ],
            },
        }

        # 字幕clip模板配置
        self.subtitle_clip_template = {
            "volcengine_style": {
                "Type": "Text",
                "Content": "",  # 将在使用时设置
                "TimelineIn": 0,  # 将在使用时设置
                "TimelineOut": 0,  # 将在使用时设置
                "Y": 800,  # 距离底部1/3处 (1280*2/3 = 853)
                "Font": "SiYuan Heiti",  # 思源黑体
                "FontColor": "#F5FFFA",  # 字体颜色
                "FontSize": 45,  # 字体大小
                "Alignment": "TopCenter",  # 水平居中对齐
                "Outline": 2,  # 描边宽度
                "OutlineColour": "#0e0100",  # 描边颜色
            }
        }

        # 全局元素clip模板配置
        self.global_clip_template = {
            "corner_logo": {
                "Type": "GlobalImage",
                "MediaURL": "",  # 将在使用时设置
                "X": 486,  # 原来的像素位置
                "Y": 0,
                "Width": 234,  # 原来的像素宽度
                "Height": 203,
            },
            "watermark": {
                "Type": "GlobalImage",
                "MediaURL": "",  # 将在使用时设置
                "X": 88,
                "Y": 1200,  # 垂直底部参考点
                "Width": 543,
                "Height": 68,
                "Alignment": "TopCenter",  # 底部居中对齐
            },
        }

        self.enhance_text_style = (
            "{\\\\1c&00FFFF&\\\\b1\\\\fs55}{keyword}{\\\\1c\\\\b0\\\\fs}"
        )

      #print("✅ Clip模板配置初始化完成")

    # 音频clip创建方法
    def create_audio_clip(self, clip_type: str = "base", **kwargs) -> Dict[str, Any]:
        """创建音频clip

        Args:
            clip_type: clip类型 ("base", "main_track", "background_music", "sound_effect")
            **kwargs: 动态参数，用于覆盖模板中的值

        Returns:
            音频clip配置字典
        """
        if clip_type not in self.audio_clip_template:
            raise ValueError(f"不支持的音频clip类型: {clip_type}")

        # 深拷贝模板
        clip = copy.deepcopy(self.audio_clip_template[clip_type])

        # 使用传入的参数覆盖模板值
        for key, value in kwargs.items():
            if key in clip:
                clip[key] = value
            elif key == "volume_gain" and "Effects" in clip:
                # 特殊处理音量参数
                for effect in clip["Effects"]:
                    if effect.get("Type") == "Volume":
                        effect["Gain"] = value

        return clip

    def create_video_clip(self, clip_type: str = "base", **kwargs) -> Dict[str, Any]:
        """创建视频clip

        Args:
            clip_type: clip类型 ("base", "with_transition", "with_opening_effect", "effect_overlay")
            **kwargs: 动态参数，用于覆盖模板中的值

        Returns:
            视频clip配置字典
        """
        if clip_type not in self.video_clip_template:
            raise ValueError(f"不支持的视频clip类型: {clip_type}")

        # 深拷贝模板
        clip = copy.deepcopy(self.video_clip_template[clip_type])
        clip["Effects"].append(copy.deepcopy(self.filter_template))

        # 使用传入的参数覆盖模板值
        for key, value in kwargs.items():
            if key in clip:
                clip[key] = value
            elif key == "transition_duration" and "Effects" in clip:
                # 特殊处理转场时长
                for effect in clip["Effects"]:
                    if effect.get("Type") == "Transition":
                        effect["Duration"] = value
            elif key == "opacity" and "Effects" in clip:
                # 特殊处理透明度
                for effect in clip["Effects"]:
                    if effect.get("Type") == "Opacity":
                        effect["Value"] = value

        return clip

    def create_image_clip(self, clip_type: str = "base", **kwargs) -> Dict[str, Any]:
        """创建图片clip

        Args:
            clip_type: clip类型 ("base", "with_kenburns", "coordinate_kenburns")
            **kwargs: 动态参数，用于覆盖模板中的值

        Returns:
            图片clip配置字典
        """
        if clip_type not in self.image_clip_template:
            raise ValueError(f"不支持的图片clip类型: {clip_type}")

        # 深拷贝模板
        clip = copy.deepcopy(self.image_clip_template[clip_type])

        # 使用传入的参数覆盖模板值
        for key, value in kwargs.items():
            if key in clip:
                clip[key] = value
            elif key == "kenburns_scale" and "Effects" in clip:
                # 特殊处理KenBurns缩放
                for effect in clip["Effects"]:
                    if effect.get("Type") == "KenBurns" and "Scale" in effect:
                        effect["Scale"] = value
            elif key == "transition_duration" and "Effects" in clip:
                # 特殊处理转场时长
                for effect in clip["Effects"]:
                    if effect.get("Type") == "Transition":
                        effect["Duration"] = value
            elif key == "duration" and "Effects" in clip:
                for effect in clip["Effects"]:
                    if effect.get("Type") in ["KenBurns", "Zoom"]:
                        effect["Duration"] = value

        return clip

    def create_subtitle_clip(self, clip_type: str = "base", **kwargs) -> Dict[str, Any]:
        """创建字幕clip

        Args:
            clip_type: clip类型 ("base", "with_highlight", "with_animation")
            **kwargs: 动态参数，用于覆盖模板中的值

        Returns:
            字幕clip配置字典
        """
        if clip_type not in self.subtitle_clip_template:
            raise ValueError(f"不支持的字幕clip类型: {clip_type}")

        # 深拷贝模板
        clip = copy.deepcopy(self.subtitle_clip_template[clip_type])

        # 使用传入的参数覆盖模板值
        for key, value in kwargs.items():
            if key in clip:
                clip[key] = value
            elif key == "highlight_color" and "Effects" in clip:
                # 特殊处理高亮颜色
                for effect in clip["Effects"]:
                    if effect.get("Type") == "TextHighlight":
                        effect["Color"] = value

        return clip

    def create_global_clip(self, clip_type: str, **kwargs) -> Dict[str, Any]:
        """创建全局元素clip

        Args:
            clip_type: clip类型 ("corner_logo", "watermark")
            **kwargs: 动态参数，用于覆盖模板中的值

        Returns:
            全局元素clip配置字典
        """
        if clip_type not in self.global_clip_template:
            raise ValueError(f"不支持的全局clip类型: {clip_type}")

        # 深拷贝模板
        clip = copy.deepcopy(self.global_clip_template[clip_type])

        # 使用传入的参数覆盖模板值
        for key, value in kwargs.items():
            if key in clip:
                clip[key] = value
            elif key == "opacity" and "Effects" in clip:
                # 特殊处理透明度
                for effect in clip["Effects"]:
                    if effect.get("Type") == "Opacity":
                        effect["Value"] = value

        return clip

    def create_visual_effect(
        self, effect_type: str = "opening_vfx", **kwargs
    ) -> Dict[str, Any]:
        """创建视觉特效

        Args:
            effect_type: 特效类型 ("opening_vfx", "transition_vfx", "custom_vfx")
            **kwargs: 动态参数，用于覆盖模板中的值

        Returns:
            视觉特效配置字典
        """
        if effect_type not in self.visual_effects_template:
            raise ValueError(f"不支持的视觉特效类型: {effect_type}")

        # 深拷贝模板
        effect = copy.deepcopy(self.visual_effects_template[effect_type])

        # 使用传入的参数覆盖模板值
        for key, value in kwargs.items():
            effect[key] = value

        return effect

    # 便捷方法 - 常用clip创建
    def create_main_audio_clip(self, clip_id: str, audio_url: str) -> Dict[str, Any]:
        """创建主音频clip（配音）"""
        return self.create_audio_clip(
            clip_type="main_track", ClipId=clip_id, MediaURL=audio_url
        )

    def create_background_music_clip(
        self, audio_url: str, total_duration: float
    ) -> Dict[str, Any]:
        """创建背景音乐clip"""
        return self.create_audio_clip(
            clip_type="background_music", MediaURL=audio_url, TimelineOut=total_duration
        )

    def create_sound_effect_clip(
        self,
        audio_url: str,
        start_time: float,
        duration: float,
        volume_gain: float = 0.8,
    ) -> Dict[str, Any]:
        """创建音效clip"""
        return self.create_audio_clip(
            clip_type="sound_effect",
            MediaURL=audio_url,
            TimelineIn=start_time,
            TimelineOut=start_time + duration,
            volume_gain=volume_gain,
        )

    def create_main_video_clip(
        self, reference_clip_id: str, video_url: str, is_first: bool = False
    ) -> Dict[str, Any]:
        """创建主视频clip"""
        clip_type = "with_opening_effect" if is_first else "with_transition"
        return self.create_video_clip(
            clip_type=clip_type, ReferenceClipId=reference_clip_id, MediaURL=video_url
        )

    def create_effect_video_clip(
        self, video_url: str, start_time: float, duration: float, opacity: float = 0.6
    ) -> Dict[str, Any]:
        """创建特效视频clip"""
        return self.create_video_clip(
            clip_type="effect_overlay",
            MediaURL=video_url,
            TimelineIn=start_time,
            TimelineOut=start_time + duration,
            opacity=opacity,
        )

    def create_kenburns_image_clip(
        self, reference_clip_id: str, image_url: str, effect_type: int = 0, duration: float = 10
    ) -> Dict[str, Any]:
        """创建带KenBurns效果的图片clip"""
        if effect_type in [2, 3]:  # 坐标式效果
            clip_type = "coordinate_kenburns"
        else:
            clip_type = "with_kenburns"

        return self.create_image_clip(
            clip_type=clip_type, ReferenceClipId=reference_clip_id, MediaURL=image_url, duration=duration
        )

    def create_corner_logo_clip(
        self, logo_url: str, total_duration: float
    ) -> Dict[str, Any]:
        """创建角标clip"""
        return self.create_global_clip(
            clip_type="corner_logo", MediaURL=logo_url, TimelineOut=total_duration
        )

    def create_watermark_clip(
        self, watermark_url: str, total_duration: float
    ) -> Dict[str, Any]:
        """创建水印clip"""
        return self.create_global_clip(
            clip_type="watermark", MediaURL=watermark_url, TimelineOut=total_duration
        )

    def create_subtitle_clip_with_timing(
        self,
        text: str,
        start_time: float,
        end_time: float,
        with_highlight: bool = False,
    ) -> Dict[str, Any]:
        """创建带时间的字幕clip"""
        clip_type = "with_highlight" if with_highlight else "base"
        return self.create_subtitle_clip(
            clip_type=clip_type, Text=text, TimelineIn=start_time, TimelineOut=end_time
        )

    def create_volcengine_subtitle_clip(
        self, content: str, start_time: float, end_time: float, **kwargs
    ) -> Dict[str, Any]:
        """创建火山引擎风格的字幕clip

        Args:
            content: 字幕内容
            start_time: 开始时间
            end_time: 结束时间
            **kwargs: 其他参数覆盖

        Returns:
            火山引擎风格的字幕clip配置字典
        """
        return self.create_subtitle_clip(
            clip_type="volcengine_style",
            Content=content,
            TimelineIn=start_time,
            TimelineOut=end_time,
            **kwargs,
        )

    def create_video_effect_clip(
        self, video_url: str, start_time: float, duration: float, opacity: float = 0.6
    ) -> Dict[str, Any]:
        """创建视频特效clip"""
        return self.create_video_clip(
            clip_type="video_effect",
            MediaURL=video_url,
            TimelineIn=start_time,
            TimelineOut=start_time + duration,
            opacity=opacity,
        )

    def create_audio_effect_clip(
        self,
        audio_url: str,
        start_time: float,
        duration: float,
        volume_gain: float = 0.8,
    ) -> Dict[str, Any]:
        """创建音频特效clip"""
        return self.create_audio_clip(
            clip_type="audio_effect",
            MediaURL=audio_url,
            TimelineIn=start_time,
            TimelineOut=start_time + duration,
            volume_gain=volume_gain,
        )

    def create_effect_clip_pair(
        self,
        video_url: str,
        audio_url: str,
        start_time: float,
        duration: float,
        video_opacity: float = 0.6,
        audio_volume: float = 0.8,
    ) -> Dict[str, Any]:
        """创建特效clip对（视频+音频）

        Args:
            video_url: 特效视频URL
            audio_url: 特效音频URL
            start_time: 开始时间
            duration: 持续时间
            video_opacity: 视频透明度
            audio_volume: 音频音量

        Returns:
            包含视频和音频clip的字典
        """
        return {
            "video_clip": self.create_video_effect_clip(
                video_url=video_url,
                start_time=start_time,
                duration=duration,
                opacity=video_opacity,
            ),
            "audio_clip": self.create_audio_effect_clip(
                audio_url=audio_url,
                start_time=start_time,
                duration=duration,
                volume_gain=audio_volume,
            ),
        }

    def create_opening_visual_effect(
        self, sub_type: str, duration: float = 1.0, **kwargs
    ) -> Dict[str, Any]:
        """创建开场视觉特效

        Args:
            sub_type: 特效子类型（如 "bluropen", "withcircleopen" 等）
            duration: 特效持续时间
            **kwargs: 其他特效参数

        Returns:
            开场视觉特效配置字典
        """
        return self.create_visual_effect(
            effect_type="opening_vfx", SubType=sub_type, Duration=duration, **kwargs
        )

    def create_transition_visual_effect(
        self, sub_type: str, duration: float = 0.8, **kwargs
    ) -> Dict[str, Any]:
        """创建转场视觉特效

        Args:
            sub_type: 转场类型（如 "fade", "displacement" 等）
            duration: 转场持续时间
            **kwargs: 其他转场参数

        Returns:
            转场视觉特效配置字典
        """
        return self.create_visual_effect(
            effect_type="transition_vfx", SubType=sub_type, Duration=duration, **kwargs
        )

    def create_custom_visual_effect(
        self, sub_type: str, duration: float = 1.0, intensity: float = 0.8, **kwargs
    ) -> Dict[str, Any]:
        """创建自定义视觉特效

        Args:
            sub_type: 特效子类型
            duration: 特效持续时间
            intensity: 特效强度
            **kwargs: 其他特效参数

        Returns:
            自定义视觉特效配置字典
        """
        return self.create_visual_effect(
            effect_type="custom_vfx",
            SubType=sub_type,
            Duration=duration,
            Intensity=intensity,
            **kwargs,
        )

    def create_effect_track_vfx(
        self, sub_type: str, start_time: float, end_time: float, **kwargs
    ) -> Dict[str, Any]:
        """创建EffectTrack视觉特效

        Args:
            sub_type: 特效子类型（如 "h_blur", "displacement" 等）
            start_time: 特效开始时间
            end_time: 特效结束时间
            **kwargs: 其他特效参数

        Returns:
            EffectTrack视觉特效配置字典
        """
        return self.create_visual_effect(
            effect_type="effect_track_vfx",
            SubType=sub_type,
            TimelineIn=start_time,
            TimelineOut=end_time,
            **kwargs,
        )

    # 获取可用的clip类型
    def get_available_audio_clip_types(self) -> List[str]:
        """获取可用的音频clip类型"""
        return list(self.audio_clip_template.keys())

    def get_available_video_clip_types(self) -> List[str]:
        """获取可用的视频clip类型"""
        return list(self.video_clip_template.keys())

    def get_available_image_clip_types(self) -> List[str]:
        """获取可用的图片clip类型"""
        return list(self.image_clip_template.keys())

    def get_available_subtitle_clip_types(self) -> List[str]:
        """获取可用的字幕clip类型"""
        return list(self.subtitle_clip_template.keys())

    def get_available_global_clip_types(self) -> List[str]:
        """获取可用的全局clip类型"""
        return list(self.global_clip_template.keys())

    def get_available_visual_effect_types(self) -> List[str]:
        """获取可用的视觉特效类型"""
        return list(self.visual_effects_template.keys())

    # 模板管理方法
    def update_audio_template(self, clip_type: str, template: Dict[str, Any]) -> None:
        """更新音频clip模板"""
        self.audio_clip_template[clip_type] = template

    def update_video_template(self, clip_type: str, template: Dict[str, Any]) -> None:
        """更新视频clip模板"""
        self.video_clip_template[clip_type] = template

    def update_image_template(self, clip_type: str, template: Dict[str, Any]) -> None:
        """更新图片clip模板"""
        self.image_clip_template[clip_type] = template

    def add_custom_template(
        self, category: str, clip_type: str, template: Dict[str, Any]
    ) -> None:
        """添加自定义模板

        Args:
            category: 模板类别 ("audio", "video", "image", "subtitle", "global")
            clip_type: clip类型名称
            template: 模板字典
        """
        if category == "audio":
            self.audio_clip_template[clip_type] = template
        elif category == "video":
            self.video_clip_template[clip_type] = template
        elif category == "image":
            self.image_clip_template[clip_type] = template
        elif category == "subtitle":
            self.subtitle_clip_template[clip_type] = template
        elif category == "global":
            self.global_clip_template[clip_type] = template
        else:
            raise ValueError(f"不支持的模板类别: {category}")
