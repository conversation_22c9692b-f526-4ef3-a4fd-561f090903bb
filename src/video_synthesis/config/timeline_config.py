#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Timeline配置管理

统一管理视频时间轴的所有配置模板和参数
"""

import copy
import random
from typing import Dict, Any, List


class TimelineConfig:
    """Timeline配置类
    
    统一管理视频时间轴的所有配置模板和参数
    """
    
    def __init__(self):
        """初始化Timeline配置"""
        # 基础时间轴结构模板
        self.base_timeline_template = {
            "VideoTracks": [
                {"VideoTrackClips": []},  # 主视频轨道
                {"VideoTrackClips": []},  # 角标轨道
                {"VideoTrackClips": []}   # 水印轨道
            ],
            "AudioTracks": [
                {"AudioTrackClips": [], "MainTrack": True},  # 主音频轨道（配音）
                {"AudioTrackClips": []},  # 转场音效轨道
                {"AudioTrackClips": []}   # 背景音乐轨道
            ],
            "SubtitleTracks": [
                {"SubtitleTrackClips": []} # 字幕轨道
            ]
        }
        
        # 视频特效配置
        self.video_effects_config = {
            "effect_interval": 10.0,  # 特效间隔时间（秒）
            "effect_duration": 2.0,   # 每个特效持续时间（秒）
            "start_delay": 3.0,       # 开始延迟，避免与开场冲突
            "end_buffer": 5.0,        # 结尾缓冲，避免与结尾冲突
            "opacity": 0.6            # 特效透明度
        }
        
        # 开场视觉特效配置
        self.opening_effects_config = {
            "effects": ["bluropen"],  # 可用的开场特效类型
            "duration": 1.0,          # 开场特效持续时间
            "effect_params": {
                "withcircleopen": {"Intensity": 0.8},
                "photograph": {"FlashIntensity": 0.9},
                "slightrectshow": {"RectSize": 0.6},
                "slightshow": {"FadeSpeed": 0.5},
                "wipecross": {"CrossDirection": "center"},
                "whitesho": {"WhiteIntensity": 0.8}
            }
        }
        
        # 转场特效配置
        self.transition_effects_config = {
            "aliyun_transition_types": [
                "fade",           # 淡入淡出
                "displacement",   # 漩涡
                "waterdrop",      # 水滴效果
                "colordistance",  # 色彩溶解
                "hexagonalize"    # 蜂巢溶解
            ],
            "first_clip_duration": 1.0,    # 第一个片段转场时长
            "normal_duration": 0.8,        # 普通转场时长
            "ending_duration": 2.0,        # 结尾转场时长
            "effect_params": {
                "displacement": {"Intensity": 0.8},
                "waterdrop": {"Amplitude": 0.6},
                "colordistance": {"Threshold": 0.5},
                "hexagonalize": {"Steps": 20}
            }
        }

        # VFX画面特效配置（用于EffectTracks）
        self.vfx_effects_config = {
            "aliyun_vfx_types": [
                "colorfulradial",     # 彩虹射线
                "colorfulstarry",     # 绚烂星空
                "flyfire",            # 萤火
                "sparklestarfield",   # 星星冲屏
                "spotfall",           # 光斑飘落
                "starexplosion",      # 星光绽放
                "starry",             # 繁星点点
                "flashinglight",      # 闪光灯
                # "shine",              # 闪动
                "colorfulripples",    # 彩色涟漪
                "colorfulbubbles",    # 彩色泡泡
                "starsparkle"         # 星星花火
            ],
            "timing": {
                "start_delay_min": 3.0,    # 分镜开始后最小延迟时间（秒）
                "start_delay_max": 5.0,    # 分镜开始后最大延迟时间（秒）
                "duration_min": 6.0,       # 特效最小持续时间（秒）
                "duration_max": 10.0,       # 特效最大持续时间（秒）
                "min_segment_duration": 10.0  # 分镜最小时长，低于此时长不添加特效
            },
            "probability": 0.8,  # 添加特效的概率（80%）
            "max_effects_per_segment": 1  # 每个分镜最多添加的特效数量
        }

        # 音频配置
        self.audio_config = {
            "default_segment_duration": 8.0,  # 默认片段时长
            "background_music_volume": 0.3,   # 背景音乐音量
            "sound_effect_volume": 0.8,       # 音效音量
            "main_audio_volume": 1.0          # 主音频音量
        }
        
        # 视频特效素材配置（从video_effects_config.py整合）
        self.video_effects_library = [
            {
                "name": "光芒闪烁",
                "video_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/resources/特效/光芒闪烁.mp4",
                "sound_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/resources/音效/魔法闪烁.mp3",
                "category": "光效",
                "description": "柔和的光芒闪烁效果"
            },
            {
                "name": "粒子爆发",
                "video_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/resources/特效/粒子爆发.mp4",
                "sound_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/resources/音效/爆发音效.mp3",
                "category": "粒子",
                "description": "粒子爆发特效"
            },
            {
                "name": "能量波纹",
                "video_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/resources/特效/能量波纹.mp4",
                "sound_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/resources/音效/能量波动.mp3",
                "category": "能量",
                "description": "能量波纹扩散效果"
            },
            {
                "name": "星光点缀",
                "video_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/resources/特效/星光点缀.mp4",
                "sound_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/resources/音效/星光音效.mp3",
                "category": "装饰",
                "description": "星光点缀装饰效果"
            },
            {
                "name": "火花飞溅",
                "video_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/resources/特效/火花飞溅.mp4",
                "sound_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/resources/音效/火花音效.mp3",
                "category": "火效",
                "description": "火花飞溅效果"
            },
            {
                "name": "雷电闪光",
                "video_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/resources/特效/雷电闪光.mp4",
                "sound_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/resources/音效/雷电音效.mp3",
                "category": "雷电",
                "description": "雷电闪光效果"
            },
            {
                "name": "水波涟漪",
                "video_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/resources/特效/水波涟漪.mp4",
                "sound_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/resources/音效/水波音效.mp3",
                "category": "水效",
                "description": "水波涟漪效果"
            },
            {
                "name": "风暴旋涡",
                "video_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/resources/特效/风暴旋涡.mp4",
                "sound_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/resources/音效/风暴音效.mp3",
                "category": "风效",
                "description": "风暴旋涡效果"
            }
        ]

        # 智能特效选择配置
        self.smart_effects_config = {
            "time_based_effects": {
                "opening": ["装饰", "光效"],      # 开头部分使用温和效果
                "middle": "all",                 # 中间部分可使用所有特效
                "ending": ["能量", "雷电"]       # 结尾部分使用强烈效果
            },
            "context_keywords": {
                "battle": {
                    "keywords": ["战斗", "打斗", "厮杀", "激战"],
                    "categories": ["火效", "雷电"]
                },
                "magic": {
                    "keywords": ["魔法", "法术", "咒语", "仙术"],
                    "categories": ["光效", "能量"]
                },
                "water": {
                    "keywords": ["水", "江", "河", "湖", "海"],
                    "categories": ["水效"]
                },
                "storm": {
                    "keywords": ["风", "暴", "雷", "电"],
                    "categories": ["风效", "雷电"]
                }
            },
            "time_thresholds": {
                "opening_duration": 30,  # 开头30秒
                "ending_duration": 30    # 结尾30秒
            }
        }

      #print("✅ Timeline配置初始化完成")
    
    def get_base_timeline(self) -> Dict[str, Any]:
        """获取基础时间轴结构
        
        Returns:
            基础时间轴结构的深拷贝
        """
        return copy.deepcopy(self.base_timeline_template)
    
    def get_video_effects_config(self) -> Dict[str, Any]:
        """获取视频特效配置"""
        return self.video_effects_config.copy()
    
    def get_opening_effects_config(self) -> Dict[str, Any]:
        """获取开场特效配置"""
        return self.opening_effects_config.copy()
    
    def get_transition_effects_config(self) -> Dict[str, Any]:
        """获取转场特效配置"""
        return self.transition_effects_config.copy()
    
    def get_image_effects_config(self) -> Dict[str, Any]:
        """获取图片特效配置"""
        return self.image_effects_config.copy()
    
    def get_audio_config(self) -> Dict[str, Any]:
        """获取音频配置"""
        return self.audio_config.copy()
    
    def get_ken_burns_effect(self, effect_type: int) -> List[Dict[str, Any]]:
        """根据类型获取KenBurns特效
        
        Args:
            effect_type: 特效类型(0-3)
            
        Returns:
            特效配置列表
        """
        effects_list = self.image_effects_config["ken_burns_effects"]
        effect_index = effect_type % len(effects_list)
        return copy.deepcopy(effects_list[effect_index]["effects"])
    
    def get_transition_duration(self, position: str) -> float:
        """获取转场时长
        
        Args:
            position: 位置类型 ("opening", "normal", "ending")
            
        Returns:
            转场时长
        """
        durations = self.image_effects_config["transition_durations"]
        return durations.get(position, durations["normal"])
    
    def get_aliyun_transition_types(self) -> List[str]:
        """获取阿里云支持的转场类型"""
        return self.transition_effects_config["aliyun_transition_types"].copy()
    
    def get_transition_params(self, transition_type: str) -> Dict[str, Any]:
        """获取转场特效参数
        
        Args:
            transition_type: 转场类型
            
        Returns:
            转场参数字典
        """
        params = self.transition_effects_config["effect_params"]
        return params.get(transition_type, {}).copy()

    def get_vfx_effects_config(self) -> Dict[str, Any]:
        """获取VFX画面特效配置

        Returns:
            VFX特效配置字典
        """
        return self.vfx_effects_config.copy()

    def get_aliyun_vfx_types(self) -> List[str]:
        """获取阿里云支持的VFX特效类型"""
        return self.vfx_effects_config["aliyun_vfx_types"].copy()

    def get_vfx_timing_config(self) -> Dict[str, Any]:
        """获取VFX特效时间配置

        Returns:
            VFX特效时间配置字典
        """
        return self.vfx_effects_config["timing"].copy()

    def get_opening_effect_params(self, effect_type: str) -> Dict[str, Any]:
        """获取开场特效参数
        
        Args:
            effect_type: 开场特效类型
            
        Returns:
            特效参数字典
        """
        params = self.opening_effects_config["effect_params"]
        return params.get(effect_type, {}).copy()

    # 视频特效库管理方法
    def get_video_effects_library(self) -> List[Dict[str, Any]]:
        """获取视频特效库"""
        return copy.deepcopy(self.video_effects_library)

    def get_random_video_effect(self) -> Dict[str, Any]:
        """随机获取一个视频特效

        Returns:
            随机特效配置字典
        """
        effect = random.choice(self.video_effects_library)
      #print(f"🎬 随机选择特效: {effect['name']} ({effect['category']})")
        return copy.deepcopy(effect)

    def get_effects_by_category(self, category: str) -> List[Dict[str, Any]]:
        """根据类别获取特效

        Args:
            category: 特效类别

        Returns:
            该类别的特效列表
        """
        effects = [effect for effect in self.video_effects_library if effect["category"] == category]
      #print(f"📂 获取 {category} 类别特效: {len(effects)} 个")
        return copy.deepcopy(effects)

    def get_smart_video_effect(self, current_time: float, total_duration: float, story_context: str = None) -> Dict[str, Any]:
        """智能选择视频特效

        Args:
            current_time: 当前时间点
            total_duration: 视频总时长
            story_context: 故事上下文（可选）

        Returns:
            智能选择的特效配置
        """
        suitable_effects = []
        config = self.smart_effects_config

        # 根据时间点选择合适的特效
        opening_duration = config["time_thresholds"]["opening_duration"]
        ending_duration = config["time_thresholds"]["ending_duration"]

        if current_time < opening_duration:  # 开头部分
            categories = config["time_based_effects"]["opening"]
            for category in categories:
                suitable_effects.extend(self.get_effects_by_category(category))
        elif current_time > total_duration - ending_duration:  # 结尾部分
            categories = config["time_based_effects"]["ending"]
            for category in categories:
                suitable_effects.extend(self.get_effects_by_category(category))
        else:  # 中间部分，可以使用所有特效
            suitable_effects = self.video_effects_library

        # 根据故事内容选择（如果有关键词信息）
        if story_context:
            context_lower = story_context.lower()
            context_keywords = config["context_keywords"]

            for context_type, context_config in context_keywords.items():
                keywords = context_config["keywords"]
                categories = context_config["categories"]

                if any(keyword in context_lower for keyword in keywords):
                    suitable_effects = []
                    for category in categories:
                        suitable_effects.extend(self.get_effects_by_category(category))
                    print(f"🎯 检测到{context_type}场景，选择对应特效")
                    break

        # 如果没有合适的特效，使用所有特效
        if not suitable_effects:
            suitable_effects = self.video_effects_library

        effect = random.choice(suitable_effects)
        print(f"🎯 智能选择特效: {effect['name']} (时间: {current_time:.1f}s)")
        return copy.deepcopy(effect)

    def get_all_effect_categories(self) -> List[str]:
        """获取所有特效类别

        Returns:
            特效类别列表
        """
        categories = list(set(effect["category"] for effect in self.video_effects_library))
        return sorted(categories)

    def get_effect_by_name(self, name: str) -> Dict[str, Any]:
        """根据名称获取特效

        Args:
            name: 特效名称

        Returns:
            特效配置，如果未找到返回随机特效
        """
        for effect in self.video_effects_library:
            if effect["name"] == name:
                return copy.deepcopy(effect)

        print(f"⚠️ 未找到特效 {name}，返回随机特效")
        return self.get_random_video_effect()
