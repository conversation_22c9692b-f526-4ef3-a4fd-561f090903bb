#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
阿里云配置管理

负责管理阿里云API凭证和服务端点配置
"""

import os
from typing import Optional


class AliCloudConfig:
    """阿里云配置类
    
    管理阿里云API访问凭证和服务端点配置
    """
    
    def __init__(self, access_key_id: Optional[str] = None, access_key_secret: Optional[str] = None, region_id: Optional[str] = None):
        """初始化阿里云配置
        
        Args:
            access_key_id: 阿里云访问密钥ID
            access_key_secret: 阿里云访问密钥密码
            region_id: 阿里云区域ID
        """
        self.access_key_id = access_key_id or os.environ.get('ALY_ACCESS_KEY_ID')
      #print(f"🔑 阿里云访问密钥ID: {self.access_key_id}")
        self.access_key_secret = access_key_secret or os.environ.get('ALY_ACCESS_KEY_SECRET')
        self.endpoint = os.environ.get('ALY_ENDPOINT')
        self.region_id = region_id or os.environ.get('ALY_REGION_ID', 'cn-beijing')  # 默认使用北京区域
        self.protocol = 'HTTP'
