#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
URL素材有效性验证器

在提交剪辑任务前验证timeline中所有URL素材的有效性
"""

import json
import requests
import asyncio
import aiohttp
from typing import Dict, Any, List, Tuple
from urllib.parse import urlparse
import time


class URLValidator:
    """URL素材有效性验证器"""
    
    def __init__(self, timeout: int = 10, max_concurrent: int = 20):
        """初始化验证器
        
        Args:
            timeout: 请求超时时间（秒）
            max_concurrent: 最大并发请求数
        """
        self.timeout = timeout
        self.max_concurrent = max_concurrent
        self.session = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        connector = aiohttp.TCPConnector(limit=self.max_concurrent)
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        self.session = aiohttp.ClientSession(connector=connector, timeout=timeout)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    def extract_urls_from_timeline(self, timeline: Dict[str, Any]) -> List[Dict[str, str]]:
        """从timeline中提取所有URL素材
        
        Args:
            timeline: 时间轴配置
            
        Returns:
            URL信息列表，包含url、类型、位置等信息
        """
        urls = []
        
        try:
            # 检查视频轨道
            video_tracks = timeline.get('VideoTracks', [])
            for track_idx, track in enumerate(video_tracks):
                clips = track.get('VideoTrackClips', [])
                for clip_idx, clip in enumerate(clips):
                    media_url = clip.get('MediaURL')
                    if media_url:
                        urls.append({
                            'url': media_url,
                            'type': 'video',
                            'location': f'VideoTracks[{track_idx}].VideoTrackClips[{clip_idx}]',
                            'clip_type': clip.get('Type', 'Unknown')
                        })
            
            # 检查音频轨道
            audio_tracks = timeline.get('AudioTracks', [])
            for track_idx, track in enumerate(audio_tracks):
                clips = track.get('AudioTrackClips', [])
                for clip_idx, clip in enumerate(clips):
                    media_url = clip.get('MediaURL')
                    if media_url:
                        urls.append({
                            'url': media_url,
                            'type': 'audio',
                            'location': f'AudioTracks[{track_idx}].AudioTrackClips[{clip_idx}]',
                            'main_track': track.get('MainTrack', False)
                        })
            
            # 检查字幕轨道（如果有外部字幕文件）
            subtitle_tracks = timeline.get('SubtitleTracks', [])
            for track_idx, track in enumerate(subtitle_tracks):
                clips = track.get('SubtitleTrackClips', [])
                for clip_idx, clip in enumerate(clips):
                    media_url = clip.get('MediaURL')
                    if media_url:
                        urls.append({
                            'url': media_url,
                            'type': 'subtitle',
                            'location': f'SubtitleTracks[{track_idx}].SubtitleTrackClips[{clip_idx}]'
                        })
            
          #print(f"📊 从timeline中提取到 {len(urls)} 个URL素材")
            return urls
            
        except Exception as e:
          #print(f"❌ 提取URL时发生错误: {e}")
            return []
    
    async def check_url_async(self, url_info: Dict[str, str]) -> Dict[str, Any]:
        """异步检查单个URL的有效性
        
        Args:
            url_info: URL信息字典
            
        Returns:
            检查结果字典
        """
        url = url_info['url']
        result = {
            'url': url,
            'type': url_info['type'],
            'location': url_info['location'],
            'valid': False,
            'status_code': None,
            'error': None,
            'response_time': None,
            'content_length': None,
            'content_type': None
        }
        
        try:
            start_time = time.time()
            
            # 发送HEAD请求检查资源是否存在
            async with self.session.head(url, allow_redirects=True) as response:
                result['status_code'] = response.status
                result['response_time'] = time.time() - start_time
                result['content_length'] = response.headers.get('Content-Length')
                result['content_type'] = response.headers.get('Content-Type')
                
                # 2xx状态码表示成功
                if 200 <= response.status < 300:
                    result['valid'] = True
                else:
                    result['error'] = f"HTTP {response.status}"
                    
        except asyncio.TimeoutError:
            result['error'] = f"请求超时 (>{self.timeout}s)"
        except aiohttp.ClientError as e:
            result['error'] = f"网络错误: {str(e)}"
        except Exception as e:
            result['error'] = f"未知错误: {str(e)}"
        
        return result
    
    async def validate_all_urls(self, urls: List[Dict[str, str]]) -> Tuple[List[Dict[str, Any]], Dict[str, int]]:
        """批量验证所有URL
        
        Args:
            urls: URL信息列表
            
        Returns:
            (验证结果列表, 统计信息)
        """
        if not urls:
            return [], {'total': 0, 'valid': 0, 'invalid': 0}
        
      #print(f"🔍 开始验证 {len(urls)} 个URL素材...")
        
        # 创建并发任务
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        async def check_with_semaphore(url_info):
            async with semaphore:
                return await self.check_url_async(url_info)
        
        # 执行并发检查
        tasks = [check_with_semaphore(url_info) for url_info in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        valid_results = []
        for result in results:
            if isinstance(result, Exception):
              print(f"⚠️ 检查过程中发生异常: {result}")
            else:
                valid_results.append(result)
        
        # 统计信息
        stats = {
            'total': len(valid_results),
            'valid': sum(1 for r in valid_results if r['valid']),
            'invalid': sum(1 for r in valid_results if not r['valid'])
        }
        
        return valid_results, stats
    
    def print_validation_report(self, results: List[Dict[str, Any]], stats: Dict[str, int]) -> None:
        """打印验证报告
        
        Args:
            results: 验证结果列表
            stats: 统计信息
        """
      #print(f"\n📋 URL素材验证报告")
      #print(f"=" * 60)
      #print(f"总计: {stats['total']} 个素材")
      #print(f"有效: {stats['valid']} 个 ✅")
      #print(f"无效: {stats['invalid']} 个 ❌")
      #print(f"成功率: {(stats['valid']/stats['total']*100):.1f}%" if stats['total'] > 0 else "0%")
        
        # 按类型分组显示
        by_type = {}
        for result in results:
            type_name = result['type']
            if type_name not in by_type:
                by_type[type_name] = {'valid': 0, 'invalid': 0}
            
            if result['valid']:
                by_type[type_name]['valid'] += 1
            else:
                by_type[type_name]['invalid'] += 1
        
      #print(f"\n📊 按类型统计:")
        for type_name, counts in by_type.items():
            total = counts['valid'] + counts['invalid']
            success_rate = (counts['valid'] / total * 100) if total > 0 else 0
          #print(f"  {type_name}: {counts['valid']}/{total} ({success_rate:.1f}%)")
        
        # 显示无效的URL
        invalid_results = [r for r in results if not r['valid']]
        if invalid_results:
            print(f"\n❌ 无效的URL素材:")
            for result in invalid_results:
                print(f"  类型: {result['type']}")
                print(f"  位置: {result['location']}")
                print(f"  URL: {result['url']}...")
                print(f"  错误: {result['error']}")
                print(f"  状态码: {result['status_code']}")
                print(f"  " + "-" * 50)
    
    async def validate_timeline(self, timeline: Dict[str, Any]) -> bool:
        """验证timeline中的所有URL素材
        
        Args:
            timeline: 时间轴配置
            
        Returns:
            是否所有URL都有效
        """
        # 提取URL
        urls = self.extract_urls_from_timeline(timeline)
        
        if not urls:
            print("✅ timeline中没有找到URL素材")
            return True
        
        # 验证URL
        results, stats = await self.validate_all_urls(urls)
        
        # 打印报告
        self.print_validation_report(results, stats)
        
        # 返回是否全部有效
        return stats['invalid'] == 0


async def validate_timeline_file(timeline_file: str) -> bool:
    """验证timeline文件中的URL素材
    
    Args:
        timeline_file: timeline文件路径
        
    Returns:
        是否所有URL都有效
    """
    try:
        # 读取timeline文件
        with open(timeline_file, 'r', encoding='utf-8') as f:
            timeline = json.load(f)
        
        print(f"📖 读取timeline文件: {timeline_file}")
        
        # 验证URL
        async with URLValidator() as validator:
            return await validator.validate_timeline(timeline)
            
    except FileNotFoundError:
        print(f"❌ 文件不存在: {timeline_file}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ JSON格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
        return False


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) != 2:
        print("使用方法: python url_validator.py <timeline_file>")
        sys.exit(1)
    
    timeline_file = sys.argv[1]
    
    # 运行验证
    result = asyncio.run(validate_timeline_file(timeline_file))
    
    if result:
        print("\n🎉 所有URL素材验证通过！")
        sys.exit(0)
    else:
        print("\n⚠️ 存在无效的URL素材，请检查后重试")
        sys.exit(1)
