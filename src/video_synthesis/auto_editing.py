#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
自动视频剪辑主模块

使用阿里云自动对齐功能的视频剪辑自动化系统
"""
from src.video_synthesis.config import AliCloudConfig, VideoConfig, MaterialConfig
from src.video_synthesis.aliyun_client import AliCloudClient
from src.video_synthesis.material_manager import MaterialManager
from src.video_synthesis.subtitle_processor import SubtitleProcessor
from src.video_synthesis.timeline_builder import TimelineBuilder
from src.video_synthesis.job_manager import JobManager
from src.video_synthesis.url_validator import URLValidator
from typing import Dict, Any, Tuple, Optional
import asyncio
import json


class AutoVideoEditor:
    """自动视频剪辑类
    
    使用阿里云自动对齐功能的视频剪辑自动化系统
    """
    
    def __init__(self, chapter_data: Dict[str, Any] = None, output_dir: str = None, skip_url_validation: bool = True):
        """初始化自动视频剪辑系统

        Args:
            chapter_data: 章节数据，包含素材URL等信息
            output_dir: 输出目录
            skip_url_validation: 是否跳过URL验证
        """
        # 初始化配置
        self.ali_config = AliCloudConfig()
        self.video_config = VideoConfig()

        # 如果提供了章节数据，使用章节数据初始化素材配置
        if chapter_data:
            self.material_config = MaterialConfig(chapter_data=chapter_data)
        else:
            self.material_config = MaterialConfig()

        # 设置输出目录
        self.output_dir = output_dir

        # 设置是否跳过URL验证
        self.skip_url_validation = skip_url_validation
        
        # 初始化组件
        self.aliyun_client = AliCloudClient(self.ali_config)
        self.material_manager = MaterialManager(self.material_config)
        
        # 修改字幕处理器初始化逻辑，支持新的火山引擎字幕格式
        if chapter_data and 'story_boards' in chapter_data:
            # 如果章节数据中包含分镜数据（新格式），使用分镜数据
            self.subtitle_processor = SubtitleProcessor(story_boards_data=chapter_data['story_boards'])
        else:
            raise Exception("章节数据中没有分镜数据，无法初始化字幕处理器")

        self.timeline_builder = TimelineBuilder(self.material_manager, self.subtitle_processor)
        self.job_manager = JobManager(self.aliyun_client, self.timeline_builder, self.video_config, self.output_dir)
        


    async def validate_timeline_urls(self, timeline_json: str) -> bool:
        """验证timeline中的所有URL素材

        Args:
            timeline_json: timeline的JSON字符串

        Returns:
            是否所有URL都有效
        """
        try:
            # 解析timeline
            timeline = json.loads(timeline_json)

            # 使用URL验证器
            async with URLValidator(timeout=15, max_concurrent=10) as validator:
                is_valid = await validator.validate_timeline(timeline)

            return is_valid

        except Exception as e:
            return False

    def run_with_validation(self) -> Tuple[bool, Optional[str]]:
        """运行视频剪辑（可选URL验证）

        Returns:
            (是否成功, 输出文件路径或错误信息)
        """
        try:
            # 1. 构建timeline
            timeline_json = self.timeline_builder.build_timeline()

            # 2. 验证URL素材（如果未跳过验证）
            if not self.skip_url_validation:
              #print("🔍 开始验证URL素材...")
                is_valid = asyncio.run(self.validate_timeline_urls(timeline_json))

                if not is_valid:
                    return False, "URL素材验证失败，请检查无效的素材链接"
                print("✅ URL素材验证通过")

            # 3. 提交剪辑任务
            success, result = self.job_manager.submit_editing_job(timeline_json)

            if success:
                return True, result
            else:
                return False, result

        except Exception as e:
            error_msg = f"视频剪辑过程中发生错误: {str(e)}"
            return False, error_msg

    def run(self) -> Tuple[bool, Optional[str]]:
        """运行自动视频剪辑流程（可选URL验证）

        Returns:
            成功完成的布尔值和输出URL的元组
        """
        # 1. 构建timeline
        timeline_json = self.timeline_builder.build_timeline()

        # 2. 验证URL（如果未跳过验证）
        if not self.skip_url_validation:
          #print("🔍 开始验证URL素材...")
            is_valid = asyncio.run(self.validate_timeline_urls(timeline_json))

            if not is_valid:
                return False, "URL素材验证失败"
          #print("✅ URL素材验证通过")
        else:
          print("⏭️ 跳过URL验证")

        # 3. 提交任务
        job_id, output_url = self.job_manager.submit_job(timeline_json)
        if not job_id:
            return False, None

        # 4. 监控任务
        success = self.job_manager.monitor_job_status(job_id)

        if success:
            return True, output_url
        else:
            return False, None

def main():
    """主函数"""
    editor = AutoVideoEditor()
    success = editor.run()


if __name__ == "__main__":
    main()