#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
并行视频处理模块

负责并行处理多个章节的视频剪辑任务
"""
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import os
import json
import glob
import logging
import concurrent.futures
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from src.video_synthesis.auto_editing import AutoVideoEditor
from src.video_synthesis.video_downloader import VideoDownloader
from dotenv import load_dotenv

# 加载.env文件中的环境变量
dotenv_path = Path(__file__).parent.parent.parent / '.env'
load_dotenv(dotenv_path=dotenv_path,verbose=True,override=True)

class ParallelVideoProcessor:
    """并行视频处理类
    
    负责并行处理多个章节的视频剪辑任务
    """

    def __init__(self, novel_dir: str, output_base_dir: str = "output/video_output", max_workers: int = 3):
        """初始化并行视频处理器
        
        Args:
            novel_dir: 小说目录路径
            output_base_dir: 输出基础目录
            max_workers: 最大并行工作线程数
        """
        self.novel_dir = Path(novel_dir)
        self.novel_name = self.novel_dir.name
        self.output_base_dir = Path(output_base_dir)
        self.novel_output_dir = self.output_base_dir / self.novel_name
        self.max_workers = max_workers

        # 创建输出目录
        os.makedirs(self.novel_output_dir, exist_ok=True)

        # 初始化视频下载器
        self.video_downloader = VideoDownloader()

        # 配置日志
        self.setup_logging()

        logging.info(f"🎬 视频处理器初始化完成，最大并行任务数: {max_workers}")

    def setup_logging(self):
        """配置日志"""
        log_file = self.novel_output_dir / "video_process.log"

        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(levelname)s - %(message)s",
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )

    def _is_chapter_video_completed(self, chapter_name: str) -> bool:
        """检查章节视频是否已经生成完成

        Args:
            chapter_name: 章节名称

        Returns:
            True if 章节视频已完成, False otherwise
        """
        try:
            # 构建视频输出目录路径
            videos_dir = self.novel_output_dir / "videos"

            # 检查视频目录是否存在
            if not videos_dir.exists():
                return False

            # 从章节名提取章节号
            # 例如：chapter1_风起大秦公子华 -> 1
            import re
            chapter_match = re.search(r'chapter(\d+)', chapter_name.lower())
            if not chapter_match:
                return False

            chapter_num = chapter_match.group(1)

            # 查找该章节的mp4视频文件
            # 实际格式：大秦二世公子华_去重-7.mp4 (小说名-章节号.mp4)
            video_patterns = [
                f"{self.novel_name}-{chapter_num}.mp4",
                f"{self.novel_name}_{chapter_num}.mp4",
                f"*-{chapter_num}.mp4",
                f"*_{chapter_num}.mp4",
                # 兼容其他可能的格式
                f"{chapter_name}.mp4",
                f"*{chapter_name}*.mp4",
                f"chapter{chapter_num}_*.mp4",
                f"chapter{chapter_num}.mp4"
            ]

            for pattern in video_patterns:
                video_files = list(videos_dir.glob(pattern))
                if video_files:
                    # 检查文件是否有效（大小大于0）
                    for video_file in video_files:
                        if video_file.stat().st_size > 0:
                            logging.info(f"📋 章节 {chapter_name} 视频已完成，跳过处理 (文件: {video_file.name})")
                            return True

            return False

        except Exception as e:
            logging.warning(f"⚠️ 检查章节 {chapter_name} 视频完成状态时出错: {e}")
            return False

    def find_chapters(self) -> List[Dict[str, Any]]:
        """查找所有章节
        
        Returns:
            章节信息列表
        """
        chapters = []

        # 查找所有章节目录
        chapter_dirs = list(self.novel_dir.glob("*/"))

        for chapter_dir in chapter_dirs:
            # 检查是否是章节目录（包含storyboards.json文件）
            storyboards_path = chapter_dir / "storyboards.json"
            if storyboards_path.exists():
                chapter_info = {
                    "chapter_name": chapter_dir.name,
                    "chapter_dir": str(chapter_dir),
                    "storyboards_path": str(storyboards_path)
                }
                chapters.append(chapter_info)

        # 按章节名中的数字排序
        def extract_chapter_number(chapter_name):
            """从章节名中提取数字用于排序"""
            import re
            # 匹配章节名中的数字，如 chapter10_誓师 -> 10
            match = re.search(r'chapter(\d+)', chapter_name.lower())
            if match:
                return int(match.group(1))
            # 如果没有找到chapter数字格式，尝试提取其他数字
            numbers = re.findall(r'\d+', chapter_name)
            if numbers:
                return int(numbers[0])
            # 如果没有数字，返回0
            return 0
        
        chapters.sort(key=lambda x: extract_chapter_number(x["chapter_name"]))

        logging.info(f"📚 发现 {len(chapters)} 个章节")
        return chapters

    def prepare_chapter_data(self, chapter_info: Dict[str, Any]) -> Dict[str, Any]:
        """准备章节数据
        
        Args:
            chapter_info: 章节信息
            
        Returns:
            章节数据字典
        """
        chapter_data = {}

        try:
            # 加载storyboards.json文件
            with open(chapter_info["storyboards_path"], "r", encoding="utf-8") as f:
                storyboards = json.load(f)

            # 提取视频、图片、音频URL和字幕数据
            video_urls = []
            image_urls = []
            audio_urls = []
            all_subtitles = []  # 收集所有字幕数据

            for item in storyboards:
                if "video_url" in item and item["video_url"]:
                    video_urls.append(item["video_url"])
                elif "image_url" in item and item["image_url"]:
                    image_urls.append(item["image_url"])
                else:
                    raise ValueError("项目缺少视频或图片URL")

                if "audio_url" in item and item["audio_url"]:
                    audio_urls.append(item["audio_url"])
                else:
                    raise ValueError("项目缺少音频URL")

                # 提取字幕数据
                if "subtitle" in item and isinstance(item["subtitle"], list):
                    all_subtitles.extend(item["subtitle"])

            # 构建章节数据
            chapter_data = {
                "chapter_name": chapter_info["chapter_name"],
                "video_urls": video_urls,
                "image_urls": image_urls,
                "audio_urls": audio_urls,
                "subtitles": all_subtitles,  # 直接传递字幕数据而不是文件路径
                "story_boards": storyboards,
                # 可以添加其他需要的数据
            }



        except Exception as e:
            logging.error(f"准备章节 {chapter_info['chapter_name']} 数据失败: {e}")

        return chapter_data

    def _process_chapter_sync(self, chapter_info: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """同步包装函数，用于在线程池中执行异步的 process_chapter"""
        import asyncio
        try:
            # 在新的事件循环中运行异步方法
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self.process_chapter(chapter_info))
            finally:
                loop.close()
        except Exception as e:
            logging.error(f"处理章节 {chapter_info.get('chapter_name', 'unknown')} 时发生错误: {e}")
            return False, None

    async def process_chapter(self, chapter_info: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """处理单个章节
        
        Args:
            chapter_info: 章节信息
            
        Returns:
            处理结果和输出URL的元组
        """
        chapter_name = chapter_info["chapter_name"]
        logging.info(f"🎬 开始剪辑章节: {chapter_name}")

        try:
            # 准备章节数据
            chapter_data = self.prepare_chapter_data(chapter_info)
            if not chapter_data:
                logging.error(f"❌ 章节 {chapter_name} 数据准备失败")
                return False, None

            # 创建章节输出目录
            chapter_output_dir = self.novel_output_dir / chapter_name
            os.makedirs(chapter_output_dir, exist_ok=True)

            # 创建自动视频编辑器（跳过URL验证）
            editor = AutoVideoEditor(
                chapter_data=chapter_data,
                output_dir=str(chapter_output_dir),
                skip_url_validation=True
            )

            # 运行视频剪辑
            success, output_url = await editor.run()

            if success and output_url:
                logging.info(f"✅ 章节 {chapter_name} 剪辑完成")

                # 下载视频到本地
                download_success, download_result = self.download_video_for_chapter(
                    output_url, chapter_name
                )


                # 保存处理结果
                result_file = chapter_output_dir / "process_result.json"
                with open(result_file, "w", encoding="utf-8") as f:
                    json.dump({
                        "chapter_name": chapter_name,
                        "success": True,
                        "output_url": output_url,
                        "download_success": download_success,
                        "local_file_path": download_result if download_success else None,
                        "download_error": download_result if not download_success else None
                    }, f, ensure_ascii=False, indent=2)

                return True, output_url
            else:
                logging.error(f"❌ 章节 {chapter_name} 剪辑失败")
                return False, None

        except Exception as e:
            logging.error(f"处理章节 {chapter_name} 时发生错误: {e}")
            return False, None

    def download_video_for_chapter(self, video_url: str, chapter_name: str) -> Tuple[bool, str]:
        """为单个章节下载视频

        Args:
            video_url: 视频URL
            chapter_name: 章节名称

        Returns:
            (是否成功, 本地文件路径或错误信息)
        """
        try:
            # 创建videos目录
            videos_dir = self.novel_output_dir / "videos"
            videos_dir.mkdir(exist_ok=True)

            # 使用视频下载器下载
            success, result = self.video_downloader.download_video(
                video_url=video_url,
                save_path=str(videos_dir),
                novel_name=self.novel_name,
                chapter_name=chapter_name
            )

            return success, result

        except Exception as e:
            error_msg = f"下载章节 {chapter_name} 视频时发生错误: {e}"
            logging.error(error_msg)
            return False, error_msg

    def summarize_download_results(self, results: dict) -> None:
        """统计并显示下载结果

        Args:
            results: 处理结果字典
        """
        print("\n" + "="*60)
        print("📥 视频下载结果统计")
        print("="*60)

        total_chapters = len(results)
        successful_downloads = 0
        failed_downloads = 0

        videos_dir = self.novel_output_dir / "videos"

        for chapter_name, result in results.items():
            if result.get("success"):
                # 检查是否有下载信息
                download_success = result.get("download_success", False)
                if download_success:
                    successful_downloads += 1
                    local_path = result.get("local_file_path", "未知路径")
                  #print(f"✅ {chapter_name}: 下载成功 -> {local_path}")
                else:
                    failed_downloads += 1
                    download_error = result.get("download_error", "未知错误")
                  #print(f"❌ {chapter_name}: 下载失败 -> {download_error}")
            else:
                failed_downloads += 1
              #print(f"❌ {chapter_name}: 剪辑失败，跳过下载")

        print("\n" + "-"*60)
        print(f"📊 下载统计:")
        print(f"   总章节数: {total_chapters}")
        print(f"   下载成功: {successful_downloads}")
        print(f"   下载失败: {failed_downloads}")
        print(f"   成功率: {(successful_downloads/total_chapters*100):.1f}%")
        print(f"📁 视频保存目录: {videos_dir}")
        print("="*60)

    def get_user_chapter_selection(self, chapters: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """获取用户选择的章节范围

        Args:
            chapters: 所有章节列表

        Returns:
            用户选择的章节列表
        """
        if not chapters:
            print("❌ 未找到任何章节")
            return []

        # 检查章节视频完成状态并分类
        completed_chapters = []
        pending_chapters = []

        print("\n🔍 正在检查章节视频完成状态...")
        for chapter in chapters:
            chapter_name = chapter['chapter_name']
            if self._is_chapter_video_completed(chapter_name):
                completed_chapters.append(chapter)
            else:
                pending_chapters.append(chapter)

      #print("\n" + "="*70)
      #print("📚 章节视频状态总览")
      #print("="*70)

        # 显示已完成的章节
        if completed_chapters:
            print(f"✅ 已完成视频 ({len(completed_chapters)}个):")
            for chapter in completed_chapters:
              print(f"    {chapter['chapter_name']}")

        # 显示待处理的章节
        if pending_chapters:
            print(f"\n📝 待处理章节 ({len(pending_chapters)}个):")
            for i, chapter in enumerate(pending_chapters, 1):
                print(f"{i:3d}. {chapter['chapter_name']}")
        else:
            print("\n🎉 所有章节视频都已完成！")
            return []

        print("\n" + "="*70)
        print("请选择要处理的章节范围（仅显示待处理章节）:")
        print("格式说明:")
        print("  - 单个章节: 5")
        print("  - 连续章节: 1-10")
        print("  - 多个范围: 1-5,8,10-15")
        print("  - 处理全部待处理章节: all")
        print("="*70)
        
        while True:
            try:
                user_input = input("请输入章节范围: ").strip()

                if user_input.lower() == 'all':
                    print(f"✅ 选择处理所有 {len(pending_chapters)} 个待处理章节")
                    return pending_chapters

                selected_indices = []

                # 解析用户输入（基于待处理章节列表）
                parts = user_input.split(',')
                for part in parts:
                    part = part.strip()
                    if '-' in part:
                        # 范围输入
                        start, end = part.split('-')
                        start_idx = int(start.strip()) - 1
                        end_idx = int(end.strip()) - 1
                        if 0 <= start_idx <= end_idx < len(pending_chapters):
                            selected_indices.extend(range(start_idx, end_idx + 1))
                        else:
                            raise ValueError(f"章节范围 {part} 超出有效范围 (1-{len(pending_chapters)})")
                    else:
                        # 单个章节
                        idx = int(part) - 1
                        if 0 <= idx < len(pending_chapters):
                            selected_indices.append(idx)
                        else:
                            raise ValueError(f"章节 {part} 超出有效范围 (1-{len(pending_chapters)})")

                # 去重并排序
                selected_indices = sorted(list(set(selected_indices)))

                if not selected_indices:
                    print("❌ 未选择任何章节，请重新输入")
                    continue

                # 获取选中的章节（从待处理章节列表中）
                selected_chapters = [pending_chapters[idx] for idx in selected_indices]

                # 确认选择
                print(f"\n✅ 已选择 {len(selected_chapters)} 个待处理章节:")
                for idx in selected_indices:
                    print(f"  {idx + 1}. {pending_chapters[idx]['chapter_name']}")

                confirm = input("\n确认处理这些章节吗？(y/n): ").strip().lower()
                if confirm in ['y', 'yes', '是']:
                    return selected_chapters
                else:
                    print("请重新选择章节范围")
                    
            except ValueError as e:
                print(f"❌ 输入格式错误: {e}")
                print("请按照格式重新输入")
            except KeyboardInterrupt:
                print("\n👋 用户取消操作")
                return []
            except Exception as e:
                print(f"❌ 输入处理错误: {e}")
                print("请重新输入")

    def process_selected_chapters(self, selected_chapters: List[Dict[str, Any]]) -> Dict[str, Any]:
        """处理选定的章节
        
        Args:
            selected_chapters: 选定的章节列表
            
        Returns:
            处理结果字典
        """
        if not selected_chapters:
            logging.warning("没有选择任何章节")
            return {"success": False, "message": "没有选择任何章节"}
    
        results = {}
        
        print(f"\n🚀 开始处理 {len(selected_chapters)} 个章节...")
    
        # 使用线程池并行处理章节
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务（使用同步包装函数）
            future_to_chapter = {executor.submit(self._process_chapter_sync, chapter): chapter for chapter in selected_chapters}
    
            # 处理结果
            for future in concurrent.futures.as_completed(future_to_chapter):
                chapter = future_to_chapter[future]
                chapter_name = chapter["chapter_name"]
    
                try:
                    success, output_url = future.result()
                    results[chapter_name] = {
                        "success": success,
                        "output_url": output_url
                    }
                    
                    if success:
                        print(f"✅ 章节 '{chapter_name}' 处理完成")
                    else:
                        print(f"❌ 章节 '{chapter_name}' 处理失败")
                        
                except Exception as e:
                    logging.error(f"获取章节 {chapter_name} 处理结果时发生错误: {e}")
                    results[chapter_name] = {
                        "success": False,
                        "error": str(e)
                    }
                    print(f"❌ 章节 '{chapter_name}' 处理异常: {e}")
    
        # 保存处理结果
        summary_file = self.novel_output_dir / "process_summary.json"
        with open(summary_file, "w", encoding="utf-8") as f:
            json.dump({
                "novel_name": self.novel_name,
                "total_chapters": len(selected_chapters),
                "successful_chapters": sum(1 for result in results.values() if result["success"]),
                "results": results
            }, f, ensure_ascii=False, indent=2)
    
        successful_count = sum(1 for result in results.values() if result["success"])
        logging.info(f"🎉 章节剪辑任务完成，成功: {successful_count}/{len(selected_chapters)}")

        # 统计下载结果
        self.summarize_download_results(results)

        return {
            "success": True,
            "novel_name": self.novel_name,
            "total_chapters": len(selected_chapters),
            "successful_chapters": successful_count,
            "results": results
        }

    def parse_chapter_range(self, chapters: List[Dict[str, Any]], range_str: str) -> List[Dict[str, Any]]:
        """解析章节范围字符串

        Args:
            chapters: 所有章节列表
            range_str: 章节范围字符串

        Returns:
            选择的章节列表
        """
        try:
            if range_str.lower() == 'all':
                print(f"✅ 选择处理所有 {len(chapters)} 个章节")
                return chapters

            selected_indices = []

            # 解析范围字符串
            parts = range_str.split(',')
            for part in parts:
                part = part.strip()
                if '-' in part:
                    # 范围输入
                    start, end = part.split('-')
                    start_idx = int(start.strip()) - 1
                    end_idx = int(end.strip()) - 1
                    if 0 <= start_idx <= end_idx < len(chapters):
                        selected_indices.extend(range(start_idx, end_idx + 1))
                    else:
                        raise ValueError(f"章节范围 {part} 超出有效范围 (1-{len(chapters)})")
                else:
                    # 单个章节
                    idx = int(part) - 1
                    if 0 <= idx < len(chapters):
                        selected_indices.append(idx)
                    else:
                        raise ValueError(f"章节 {part} 超出有效范围 (1-{len(chapters)})")

            # 去重并排序
            selected_indices = sorted(list(set(selected_indices)))

            if not selected_indices:
                print("❌ 未选择任何章节")
                return []

            # 获取选中的章节
            selected_chapters = [chapters[idx] for idx in selected_indices]

            # 显示选择结果
            print(f"✅ 已选择 {len(selected_chapters)} 个章节:")
            for idx in selected_indices:
                print(f"  {idx + 1}. {chapters[idx]['chapter_name']}")

            return selected_chapters

        except Exception as e:
            print(f"❌ 章节范围解析错误: {e}")
            return []


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="并行处理小说章节视频剪辑")
    parser.add_argument("--novel_dir", required=True, help="小说目录路径")
    parser.add_argument("--output_dir", default="output/video_output", help="输出目录")
    parser.add_argument("--max_workers", type=int, default=3, help="最大并行工作线程数")
    parser.add_argument("--auto_all", action="store_true", help="自动处理所有章节，跳过用户交互")
    parser.add_argument("--chapters", type=str, help="直接指定章节范围，格式同交互模式 (如: 1-5,8,10-15 或 all)")

    args = parser.parse_args()

    print("🚀 启动并行视频处理系统...")
    processor = ParallelVideoProcessor(
        novel_dir=args.novel_dir,
        output_base_dir=args.output_dir,
        max_workers=args.max_workers
    )

    # 查找所有章节
    chapters = processor.find_chapters()
    if not chapters:
        print("❌ 未找到任何章节，请检查小说目录路径")
        return

    # 根据参数决定章节选择方式
    if args.auto_all:
        print(f"🤖 自动模式：处理所有 {len(chapters)} 个章节")
        selected_chapters = chapters
    elif args.chapters:
        # 命令行指定章节范围
        print(f"📋 命令行模式：解析章节范围 '{args.chapters}'")
        selected_chapters = processor.parse_chapter_range(chapters, args.chapters)
        if not selected_chapters:
            print("❌ 章节范围解析失败，程序退出")
            return
    else:
        # 交互模式
        selected_chapters = processor.get_user_chapter_selection(chapters)
        if not selected_chapters:
            print("👋 未选择任何章节，程序退出")
            return

    # 处理选定的章节
    result = processor.process_selected_chapters(selected_chapters)

    # 输出最终结果
    print("\n" + "="*50)
    if result["success"]:
        print(f"🎉 章节处理完成！")
        print(f"📊 总章节数: {result['total_chapters']}")
        print(f"✅ 成功章节数: {result['successful_chapters']}")
        print(f"❌ 失败章节数: {result['total_chapters'] - result['successful_chapters']}")
        
        # 显示处理结果详情
        print("\n📋 处理结果详情:")
        for chapter_name, chapter_result in result['results'].items():
            if chapter_result['success']:
                print(f"  ✅ {chapter_name}: {chapter_result.get('output_url', '成功')}")
            else:
                error_msg = chapter_result.get('error', '未知错误')
                print(f"  ❌ {chapter_name}: {error_msg}")
    else:
        print(f"❌ 处理失败: {result['message']}")




if __name__ == "__main__":
    main()

    # python -m src.video_synthesis.parallel_video_processor --novel_dir output/novel_step2/大唐太子：开局硬刚李世民 --output_dir output/novel_step3 --max_workers 1

