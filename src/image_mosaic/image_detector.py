# -*- coding: utf-8 -*-
"""
@Time ： 2025/7/24 16:25
@Auth ： xiaolongtuan
@File ：image_detector.py
"""
import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Union
from openai import OpenAI

logger = logging.getLogger(__name__)


class ImageDetector:
    """图像违规检测器

    使用豆包大模型检测图像中是否存在违规内容
    """

    def __init__(self):
        """初始化图像检测器"""
        self.api_key = os.environ.get('ARK_API_KEY')
        self.base_url = "https://ark.cn-beijing.volces.com/api/v3"
        self.model = "doubao-1-5-thinking-vision-pro-250428"

        if not self.api_key:
            raise ValueError("请设置环境变量 ARK_API_KEY")

        # 初始化OpenAI客户端
        self.client = OpenAI(
            base_url=self.base_url,
            api_key=self.api_key,
        )

        # 加载系统提示词
        self.system_prompt = self._load_system_prompt()

        logger.info("图像违规检测器初始化完成")

    def _load_system_prompt(self) -> str:
        """加载系统提示词"""
        prompt_path = Path(__file__).parent.parent / "prompts" / "image_dectect_system.md"

        if not prompt_path.exists():
            logger.warning(f"提示词文件不存在: {prompt_path}")
            return self._get_default_prompt()

        try:
            with open(prompt_path, 'r', encoding='utf-8') as f:
                content = f.read()
            logger.info(f"成功加载提示词文件: {prompt_path}")
            return content
        except Exception as e:
            logger.error(f"加载提示词文件失败: {e}")
            return self._get_default_prompt()

    def _get_default_prompt(self) -> str:
        """获取默认提示词"""
        return """你是一个专业的图片审核专家，查看图片中主要人物的服装、动作和互动，检查是否存在以下违规内容
# 需要判断的违规内容及序号如下：
1. 服装违规
   a. 服装领口是 v 领或交领
   b. 上衣是领口较低，露出胸口皮肤

按照 json 的格式返回是否存在违规情况，以及违规点的序号，如：
```
{
    "result":true, # true表示有违规
    "violation_points":["1a"]
}
```"""

    async def detect_image_url(self, image_url: str) -> Dict[str, Any]:
        """检测网络图片URL是否违规

        Args:
            image_url: 图片URL地址

        Returns:
            Dict: 检测结果，包含result和violation_points字段
        """
        try:
            # logger.info(f"开始检测图片URL: {image_url}")

            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": self.system_prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": image_url
                                },
                            }
                        ]
                    }
                ],
                temperature=0.5,
                top_p=0.7
            )

            # 提取响应内容
            content = response.choices[0].message.content
            # 解析JSON结果
            result = self._parse_response(content)

            logger.info(f"检测结果: {result}")
            return result

        except Exception as e:
            logger.error(f"检测图片URL失败: {e}")
            return {
                "result": False,
                "violation_points": [],
                "error": str(e)
            }

    async def detect_image_file(self, image_path: Union[str, Path]) -> Dict[str, Any]:
        """检测本地图片文件是否违规

        Args:
            image_path: 本地图片文件路径

        Returns:
            Dict: 检测结果，包含result和violation_points字段
        """
        import base64

        image_path = Path(image_path)

        if not image_path.exists():
            logger.error(f"图片文件不存在: {image_path}")
            return {
                "result": False,
                "violation_points": [],
                "error": "图片文件不存在"
            }

        try:
            logger.info(f"开始检测本地图片: {image_path}")

            # 读取图片并转换为base64
            with open(image_path, 'rb') as f:
                image_data = f.read()

            # 获取图片格式
            image_format = image_path.suffix.lower().lstrip('.')
            if image_format == 'jpg':
                image_format = 'jpeg'

            # 转换为base64
            base64_image = base64.b64encode(image_data).decode('utf-8')
            data_url = f"data:image/{image_format};base64,{base64_image}"

            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": data_url
                                },
                            },
                            {
                                "type": "text",
                                "text": self.system_prompt
                            },
                        ],
                    }
                ],
            )

            # 提取响应内容
            content = response.choices[0].message.content
            logger.info(f"API响应内容: {content}")

            # 解析JSON结果
            result = self._parse_response(content)

            logger.info(f"检测结果: {result}")
            return result

        except Exception as e:
            logger.error(f"检测本地图片失败: {e}")
            return {
                "result": False,
                "violation_points": [],
                "error": str(e)
            }

    def _parse_response(self, content: str) -> Dict[str, Any]:
        """解析API响应内容，提取JSON结果

        Args:
            content: API响应的文本内容

        Returns:
            Dict: 解析后的结果
        """
        try:
            # 尝试直接解析JSON
            if content.strip().startswith('{'):
                return json.loads(content.strip())

            # 查找JSON代码块
            import re

            # 匹配```json...```或```...```格式的代码块
            json_pattern = r'```(?:json)?\s*(\{.*?\})\s*```'
            match = re.search(json_pattern, content, re.DOTALL)

            if match:
                json_str = match.group(1)
                return json.loads(json_str)

            # 查找单独的JSON对象
            json_pattern = r'\{[^{}]*"result"[^{}]*\}'
            match = re.search(json_pattern, content)

            if match:
                json_str = match.group(0)
                return json.loads(json_str)

            # 如果都找不到，返回默认结果
            logger.warning(f"无法解析响应内容为JSON: {content}")
            return {
                "result": False,
                "violation_points": [],
                "error": "无法解析响应内容"
            }

        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            return {
                "result": False,
                "violation_points": [],
                "error": f"JSON解析失败: {e}"
            }
        except Exception as e:
            logger.error(f"解析响应失败: {e}")
            return {
                "result": False,
                "violation_points": [],
                "error": f"解析响应失败: {e}"
            }

    def is_violation(self, image_input: Union[str, Path]) -> bool:
        """简化的违规检测方法

        Args:
            image_input: 图片URL或本地文件路径

        Returns:
            bool: True表示有违规，False表示无违规
        """
        if isinstance(image_input, str) and (image_input.startswith('http://') or image_input.startswith('https://')):
            result = self.detect_image_url(image_input)
        else:
            result = self.detect_image_file(image_input)

        return result.get('result', False)

    async def get_violation_details(self, image_input: Union[str, Path]) -> Dict[str, Any]:
        """获取详细的违规检测结果

        Args:
            image_input: 图片URL或本地文件路径

        Returns:
            Dict: 详细的检测结果
        """
        if isinstance(image_input, str) and (image_input.startswith('http://') or image_input.startswith('https://')):
            return await self.detect_image_url(image_input)
        else:
            return await self.detect_image_file(image_input)
