#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态垂直偏移量功能使用示例

展示如何使用基于肩宽比例的动态垂直偏移功能，精确控制马赛克位置
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.image_mosaic import PersonMosaicProcessor
from src.image_mosaic.config import get_config_by_name, create_custom_config


def example_2_dynamic_with_offset():
    """示例2: 动态尺寸配合垂直偏移"""
    print("\n=== 示例2: 动态尺寸配合垂直偏移 ===")
    
    # 使用预定义的动态配置（已包含垂直偏移）
    config = get_config_by_name('dynamic')
    processor = PersonMosaicProcessor(config=config)
    
    image_names = ['1','2','3']
    for image_name in image_names:
        input_path = f"test_images/{image_name}.jpg"
        output_path = f"test_images/{image_name}_output.jpg"

        if Path(input_path).exists():
            success = processor.process_image_file(input_path, output_path,apply_chest=True)
            if success:
                print(f"✅ 动态尺寸+偏移处理完成: {output_path}")
                print(f"   马赛克大小根据肩宽调整，位置向下偏移肩宽的{config.vertical_offset_ratio*100:.0f}%")
            else:
                print("❌ 处理失败")
        else:
            print(f"⚠️ 输入文件不存在: {input_path}")


def main():
    """主函数"""
    print("📐 动态垂直偏移量功能使用示例")
    print("=" * 50)
    print("💡 请确保有名为 'input_image.jpg' 的测试图片")
    print()
    
    try:
        # 运行所有示例
        example_2_dynamic_with_offset()
        
    except Exception as e:
        print(f"\n❌ 示例运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
