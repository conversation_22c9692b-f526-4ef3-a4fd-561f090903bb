#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片人物打码模块配置文件

定义默认参数和配置选项
"""

from dataclasses import dataclass
from typing import Tuple


@dataclass
class MosaicConfig:
    """图像处理配置类（支持马赛克和高斯模糊）"""

    # 处理区域尺寸（固定模式）
    mosaic_width: int = 200
    mosaic_height: int = 250

    # 动态调整参数
    use_dynamic_sizing: bool = True  # 是否启用动态调整
    shoulder_width_ratio: float = 0.5  # 处理区域宽度相对于双肩宽度的比例
    aspect_ratio: float = 1.0  # 处理区域的宽高比 (width/height)
    min_mosaic_width: int = 80  # 最小处理区域宽度
    max_mosaic_width: int = 400  # 最大处理区域宽度

    # 位置调整参数
    vertical_offset_ratio: float = 0.1  # 垂直偏移量相对于双肩宽度的比例（正值向下，负值向上）

    # 眼部处理参数
    eye_length_ratio: float = 1.2  # 眼部处理区域长度相对于眼部距离的比例
    eye_width_ratio: float = 0.3   # 眼部处理区域宽度相对于长度的比例

    # 效果类型和参数
    effect_type: str = "sticker"  # 效果类型: "mosaic", "gaussian_blur" 或 "sticker"
    mosaic_size: int = 10  # 马赛克块大小（仅当effect_type为"mosaic"时使用）
    blur_kernel_size: int = 51  # 高斯模糊核大小（必须为奇数，仅当effect_type为"gaussian_blur"时使用）
    blur_sigma_x: float = 0  # 高斯模糊X方向标准差（0表示自动计算）
    blur_sigma_y: float = 0  # 高斯模糊Y方向标准差（0表示自动计算）

    # 贴纸效果参数
    sticker_name: str = "claud2"  # 贴纸名称（仅当effect_type为"sticker"时使用）
    sticker_scale: float = 1.0  # 贴纸缩放比例（相对于检测区域大小）
    sticker_opacity: float = 1.0  # 贴纸透明度（0.0-1.0）
    sticker_stretch_mode: str = "uniform"  # 贴纸拉伸模式: "uniform"(等比例), "fill"(填充区域), "horizontal"(横向拉伸), "vertical"(纵向拉伸)
    sticker_stretch_factor_x: float = 1.0  # 横向拉伸系数（仅在stretch_mode为"horizontal"或自定义时使用）
    sticker_stretch_factor_y: float = 1.0  # 纵向拉伸系数（仅在stretch_mode为"vertical"或自定义时使用）

    # MediaPipe检测参数
    min_detection_confidence: float = 0.1
    min_pose_presence_confidence: float = 0.1
    min_tracking_confidence: float = 0.1
    # 设置最大检测人数
    max_people: int = 4

    # 多人检测优化参数
    use_nms: bool = True  # 是否使用非极大值抑制去除重复检测
    nms_threshold: float = 0.3  # NMS阈值，用于去除重叠的检测结果
    min_person_distance: int = 50  # 两个人之间的最小像素距离
    
    def __post_init__(self):
        """参数验证"""
        if self.mosaic_width <= 0 or self.mosaic_height <= 0:
            raise ValueError("处理区域尺寸必须大于0")

        if not 0 <= self.min_detection_confidence <= 1:
            raise ValueError("检测置信度必须在0-1之间")

        # 动态调整参数验证
        if self.shoulder_width_ratio <= 0:
            raise ValueError("肩宽比例必须大于0")

        if self.aspect_ratio <= 0:
            raise ValueError("宽高比必须大于0")

        if self.min_mosaic_width <= 0 or self.max_mosaic_width <= 0:
            raise ValueError("处理区域尺寸限制必须大于0")

        if self.min_mosaic_width >= self.max_mosaic_width:
            raise ValueError("最小处理区域宽度必须小于最大处理区域宽度")

        # 效果类型验证
        if self.effect_type not in ["mosaic", "gaussian_blur", "sticker"]:
            raise ValueError("效果类型必须是 'mosaic', 'gaussian_blur' 或 'sticker'")

        # 马赛克参数验证
        if self.effect_type == "mosaic" and self.mosaic_size <= 0:
            raise ValueError("马赛克块大小必须大于0")

        # 高斯模糊参数验证
        if self.effect_type == "gaussian_blur":
            if self.blur_kernel_size <= 0 or self.blur_kernel_size % 2 == 0:
                raise ValueError("高斯模糊核大小必须是正奇数")
            if self.blur_sigma_x < 0 or self.blur_sigma_y < 0:
                raise ValueError("高斯模糊标准差不能为负数")

        # 贴纸参数验证
        if self.effect_type == "sticker":
            if not self.sticker_name:
                raise ValueError("贴纸名称不能为空")
            if self.sticker_scale <= 0:
                raise ValueError("贴纸缩放比例必须大于0")
            if not 0 <= self.sticker_opacity <= 1:
                raise ValueError("贴纸透明度必须在0-1之间")
            if self.sticker_stretch_mode not in ["uniform", "fill", "horizontal", "vertical"]:
                raise ValueError("贴纸拉伸模式必须是 'uniform', 'fill', 'horizontal' 或 'vertical'")
            if self.sticker_stretch_factor_x <= 0:
                raise ValueError("横向拉伸系数必须大于0")
            if self.sticker_stretch_factor_y <= 0:
                raise ValueError("纵向拉伸系数必须大于0")

        # 位置调整参数验证（比例可以为负数，表示向上偏移）
        # vertical_offset_ratio 可以为任意实数，不需要额外验证

        # 多人检测参数验证
        if hasattr(self, 'min_pose_presence_confidence') and not 0 <= self.min_pose_presence_confidence <= 1:
            raise ValueError("姿态存在性置信度必须在0-1之间")
        if hasattr(self, 'min_tracking_confidence') and not 0 <= self.min_tracking_confidence <= 1:
            raise ValueError("跟踪置信度必须在0-1之间")
        if hasattr(self, 'nms_threshold') and not 0 <= self.nms_threshold <= 1:
            raise ValueError("NMS阈值必须在0-1之间")
        if hasattr(self, 'min_person_distance') and self.min_person_distance < 0:
            raise ValueError("人与人之间的最小距离不能为负数")


# 预定义配置
class PresetConfigs:
    # 动态调整配置（高斯模糊）
    DYNAMIC = MosaicConfig(
        effect_type="mosaic",
        mosaic_size=40,
        use_dynamic_sizing=True,
        shoulder_width_ratio=0.4,
        aspect_ratio=1/1.1,
        min_mosaic_width=70,
        max_mosaic_width=400,
        vertical_offset_ratio=-0.1,
        min_detection_confidence=0.05,  # 提高置信度阈值
        min_pose_presence_confidence=0.6,  # 提高存在性置信度
        min_tracking_confidence=0.6,  # 提高跟踪置信度
        max_people=6,  # 增加最大检测人数
        use_nms=True,  # 启用非极大值抑制
        nms_threshold=0.4,  # NMS阈值
        min_person_distance=80  # 增加人与人之间的最小距离
    )

    # 贴纸配置（云朵贴纸）
    STICKER_CLOUD = MosaicConfig(
        effect_type="sticker",
        sticker_name="cloud",
        sticker_scale=1.0,
        sticker_opacity=1,
        sticker_stretch_mode="fill",
        use_dynamic_sizing=True,
        shoulder_width_ratio=0.6,
        aspect_ratio=1/1.2,
        min_mosaic_width=80,
        max_mosaic_width=400,
        vertical_offset_ratio=-0.1,
        min_detection_confidence=0.05
    )

    # 贴纸配置（填充模式）
    STICKER_FILL = MosaicConfig(
        effect_type="sticker",
        sticker_name="claud2",
        sticker_scale=1.0,
        sticker_opacity=1,
        sticker_stretch_mode="fill",
        sticker_stretch_factor_x=1.0,
        sticker_stretch_factor_y=1.0,
        use_dynamic_sizing=True,
        shoulder_width_ratio=0.8,
        aspect_ratio=1/1.2,
        min_mosaic_width=80,
        max_mosaic_width=400,
        vertical_offset_ratio=-0.1,
        min_detection_confidence=0.5
    )

    # 多人检测优化配置
    MULTI_PERSON_OPTIMIZED = MosaicConfig(
        effect_type="sticker",
        sticker_name="claud2",
        sticker_scale=1.0,
        sticker_opacity=1,
        sticker_stretch_mode="fill",
        use_dynamic_sizing=True,
        shoulder_width_ratio=0.6,
        aspect_ratio=1/1.2,
        min_mosaic_width=80,
        max_mosaic_width=400,
        vertical_offset_ratio=-0.1,
        min_detection_confidence=0.7,  # 提高置信度阈值
        min_pose_presence_confidence=0.6,  # 提高存在性置信度
        min_tracking_confidence=0.6,  # 提高跟踪置信度
        max_people=6,  # 增加最大检测人数
        use_nms=True,  # 启用非极大值抑制
        nms_threshold=0.4,  # NMS阈值
        min_person_distance=80  # 增加人与人之间的最小距离
    )

# 默认配置
DEFAULT_CONFIG = PresetConfigs.STICKER_FILL


def get_config_by_name(name: str) -> MosaicConfig:
    """根据名称获取预定义配置

    Args:
        name: 配置名称 ('fine', 'standard', 'coarse', 'small', 'large', 'dynamic')

    Returns:
        对应的配置对象

    Raises:
        ValueError: 如果配置名称不存在
    """
    name = name.lower()
    config_map = {
        'dynamic': PresetConfigs.DYNAMIC,
        'sticker_cloud': PresetConfigs.STICKER_CLOUD,
        'sticker_fill': PresetConfigs.STICKER_FILL,
        'multi_person': PresetConfigs.MULTI_PERSON_OPTIMIZED
    }

    if name not in config_map:
        available = ', '.join(config_map.keys())
        raise ValueError(f"未知配置名称: {name}. 可用配置: {available}")

    return config_map[name]


def create_custom_config(mosaic_width: int = 200,
                        mosaic_height: int = 200,
                        effect_type: str = "gaussian_blur",
                        mosaic_size: int = 20,
                        blur_kernel_size: int = 51,
                        blur_sigma_x: float = 0,
                        blur_sigma_y: float = 0,
                        sticker_name: str = "claud2",
                        sticker_scale: float = 1.0,
                        sticker_opacity: float = 1.0,
                        sticker_stretch_mode: str = "uniform",
                        sticker_stretch_factor_x: float = 1.0,
                        sticker_stretch_factor_y: float = 1.0,
                        use_dynamic_sizing: bool = False,
                        shoulder_width_ratio: float = 0.5,
                        aspect_ratio: float = 1.0,
                        min_mosaic_width: int = 80,
                        max_mosaic_width: int = 400,
                        vertical_offset_ratio: float = 0.1,
                        eye_length_ratio: float = 1.2,
                        eye_width_ratio: float = 0.3,
                        min_detection_confidence: float = 0.5) -> MosaicConfig:
    """创建自定义配置

    Args:
        mosaic_width: 处理区域宽度（固定模式）
        mosaic_height: 处理区域高度（固定模式）
        effect_type: 效果类型 ("mosaic", "gaussian_blur" 或 "sticker")
        mosaic_size: 马赛克块大小（仅当effect_type为"mosaic"时使用）
        blur_kernel_size: 高斯模糊核大小（必须为奇数，仅当effect_type为"gaussian_blur"时使用）
        blur_sigma_x: 高斯模糊X方向标准差（0表示自动计算）
        blur_sigma_y: 高斯模糊Y方向标准差（0表示自动计算）
        sticker_name: 贴纸名称（仅当effect_type为"sticker"时使用）
        sticker_scale: 贴纸缩放比例（相对于检测区域大小）
        sticker_opacity: 贴纸透明度（0.0-1.0）
        sticker_stretch_mode: 贴纸拉伸模式（uniform/fill/horizontal/vertical）
        sticker_stretch_factor_x: 横向拉伸系数
        sticker_stretch_factor_y: 纵向拉伸系数
        use_dynamic_sizing: 是否启用动态调整
        shoulder_width_ratio: 处理区域宽度相对于双肩宽度的比例
        aspect_ratio: 处理区域的宽高比
        min_mosaic_width: 最小处理区域宽度
        max_mosaic_width: 最大处理区域宽度
        vertical_offset_ratio: 垂直偏移量相对于双肩宽度的比例（正值向下，负值向上）
        eye_length_ratio: 眼部处理区域长度相对于眼部距离的比例
        eye_width_ratio: 眼部处理区域宽度相对于长度的比例
        min_detection_confidence: 最小检测置信度

    Returns:
        自定义配置对象
    """
    return MosaicConfig(
        mosaic_width=mosaic_width,
        mosaic_height=mosaic_height,
        effect_type=effect_type,
        mosaic_size=mosaic_size,
        blur_kernel_size=blur_kernel_size,
        blur_sigma_x=blur_sigma_x,
        blur_sigma_y=blur_sigma_y,
        sticker_name=sticker_name,
        sticker_scale=sticker_scale,
        sticker_opacity=sticker_opacity,
        sticker_stretch_mode=sticker_stretch_mode,
        sticker_stretch_factor_x=sticker_stretch_factor_x,
        sticker_stretch_factor_y=sticker_stretch_factor_y,
        use_dynamic_sizing=use_dynamic_sizing,
        shoulder_width_ratio=shoulder_width_ratio,
        aspect_ratio=aspect_ratio,
        min_mosaic_width=min_mosaic_width,
        max_mosaic_width=max_mosaic_width,
        vertical_offset_ratio=vertical_offset_ratio,
        eye_length_ratio=eye_length_ratio,
        eye_width_ratio=eye_width_ratio,
        min_detection_confidence=min_detection_confidence
    )


# 配置使用示例
if __name__ == "__main__":
    print("图片人物打码模块配置示例")
    print("=" * 40)
    
    # 使用默认配置
    print(f"默认配置: {DEFAULT_CONFIG}")
    
    # 使用预定义配置
    fine_config = get_config_by_name('fine')
    print(f"精细配置: {fine_config}")
    
    # 创建自定义配置
    custom_config = create_custom_config(
        mosaic_width=250,
        mosaic_height=250,
        mosaic_size=15
    )
    print(f"自定义配置: {custom_config}")
    
    # 列出所有预定义配置
    print("\n所有预定义配置:")
    for name in ['fine', 'standard', 'coarse', 'small', 'large', 'dynamic']:
        config = get_config_by_name(name)
        if config.use_dynamic_sizing:
            print(f"  {name}: 动态调整(肩宽比例={config.shoulder_width_ratio}, 宽高比={config.aspect_ratio}, 垂直偏移比例={config.vertical_offset_ratio}), 块大小={config.mosaic_size}")
        else:
            print(f"  {name}: {config.mosaic_width}x{config.mosaic_height}, 垂直偏移比例={config.vertical_offset_ratio}, 块大小={config.mosaic_size}")
