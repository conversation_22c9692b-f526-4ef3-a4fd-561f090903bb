# 图片人物打码模块

## 概述

这个模块提供了对图片中人物进行自动打码的功能。通过使用MediaPipe人体姿态检测技术，自动识别图片中的人物并在胸部区域添加马赛克效果。

## 功能特性

- 🎯 **自动人物检测**: 使用新版MediaPipe API进行精确的人体姿态检测
- 👥 **多人检测支持**: 支持最多3人同时检测和打码
- 📍 **智能区域定位**: 基于肩膀关键点自动计算胸部中心位置
- 🎨 **可定制马赛克**: 支持自定义马赛克区域大小和效果强度
- 📏 **动态尺寸调整**: 根据双肩宽度自动调整马赛克大小，适应不同人物尺寸
- 📐 **位置精确控制**: 支持基于肩宽比例的动态垂直偏移，精确控制马赛克位置
- 🎛️ **灵活配置**: 支持固定尺寸和动态尺寸两种模式
- 🔧 **本地模型**: 使用本地下载的MediaPipe模型文件，确保稳定性
- 🚀 **简单易用**: 提供便捷的API接口，一行代码完成打码
- 📁 **批量处理**: 支持单张图片和批量图片处理
- 🔧 **参数可调**: 灵活的参数配置满足不同需求

## 📦 安装

### 1. 安装依赖

```bash
pip install opencv-python mediapipe numpy
```

### 2. 模型文件

模型文件已包含在项目中，位于 `src/image_mosaic/models/` 目录下：

- `pose_landmarker_heavy.task` - MediaPipe姿态检测模型（约30MB）

如需重新下载：

```bash
cd src/image_mosaic/models
curl -L -o pose_landmarker_heavy.task https://storage.googleapis.com/mediapipe-models/pose_landmarker/pose_landmarker_heavy/float16/1/pose_landmarker_heavy.task
```

## 快速开始

### 基本使用

```python
from src.image_mosaic import apply_mosaic_to_image

# 简单的一行代码打码
success = apply_mosaic_to_image("input.jpg", "output.jpg")
```

### 高级使用

```python
from src.image_mosaic import PersonMosaicProcessor
from src.image_mosaic.config import get_config_by_name

# 使用预定义配置
config = get_config_by_name('dynamic')  # 动态调整配置
processor = PersonMosaicProcessor(config=config)

# 或者创建自定义处理器
processor = PersonMosaicProcessor(
    mosaic_width=200,    # 马赛克区域宽度（固定模式）
    mosaic_height=200,   # 马赛克区域高度（固定模式）
    mosaic_size=20       # 马赛克块大小
)

# 处理图片文件
success = processor.process_image_file("input.jpg", "output.jpg")

# 或者直接处理图片数组
import cv2
image = cv2.imread("input.jpg")
result = processor.process_image(image)
cv2.imwrite("output.jpg", result)
```

### 动态马赛克使用

```python
from src.image_mosaic.config import create_custom_config

# 创建动态调整配置
config = create_custom_config(
    use_dynamic_sizing=True,      # 启用动态调整
    shoulder_width_ratio=0.5,     # 马赛克宽度为肩宽的50%
    aspect_ratio=1.0,             # 正方形马赛克
    min_mosaic_width=80,          # 最小宽度80px
    max_mosaic_width=400,         # 最大宽度400px
    vertical_offset_ratio=0.1     # 向下偏移肩宽的10%
)

processor = PersonMosaicProcessor(config=config)
success = processor.process_image_file("input.jpg", "output.jpg")
```

## API 参考

### PersonMosaicProcessor 类

主要的打码处理器类。

#### 构造函数

```python
PersonMosaicProcessor(mosaic_width=200, mosaic_height=200, mosaic_size=20)
```

**参数:**
- `mosaic_width` (int): 马赛克区域宽度，默认200像素
- `mosaic_height` (int): 马赛克区域高度，默认200像素
- `mosaic_size` (int): 马赛克块大小，默认20像素

#### 主要方法

##### `detect_chest_centers(image)`

检测图片中所有人物的胸部中心点。

**参数:**
- `image` (np.ndarray): 输入图片 (BGR格式)

**返回:**
- `List[Tuple[int, int]]`: 胸部中心点坐标列表

##### `process_image(image)`

处理图片，为所有检测到的人物添加马赛克。

**参数:**
- `image` (np.ndarray): 输入图片 (BGR格式)

**返回:**
- `np.ndarray`: 处理后的图片

##### `process_image_file(input_path, output_path)`

处理图片文件。

**参数:**
- `input_path` (str|Path): 输入图片路径
- `output_path` (str|Path): 输出图片路径

**返回:**
- `bool`: 处理是否成功

### 便捷函数

#### `apply_mosaic_to_image()`

```python
apply_mosaic_to_image(input_path, output_path, mosaic_width=200, mosaic_height=200, mosaic_size=20)
```

便捷的一行代码打码函数。

**参数:**
- `input_path` (str|Path): 输入图片路径
- `output_path` (str|Path): 输出图片路径
- `mosaic_width` (int): 马赛克区域宽度
- `mosaic_height` (int): 马赛克区域高度
- `mosaic_size` (int): 马赛克块大小

**返回:**
- `bool`: 处理是否成功

## 技术原理

### 1. 人体姿态检测

使用新版MediaPipe API进行多人姿态检测：

```python
# 初始化新版MediaPipe姿态检测
model_path = "models/pose_landmarker_heavy.task"
options = mp.tasks.vision.PoseLandmarkerOptions(
    base_options=mp.tasks.BaseOptions(
        model_asset_path=model_path
    ),
    running_mode=mp.tasks.vision.RunningMode.IMAGE,
    num_poses=3,  # 最多检测3个人
    min_pose_detection_confidence=0.5,
    min_pose_presence_confidence=0.5,
    min_tracking_confidence=0.5
)

pose_landmarker = mp.tasks.vision.PoseLandmarker.create_from_options(options)

# 进行多人检测
mp_image = mp.Image(image_format=mp.ImageFormat.SRGB, data=rgb_image)
detection_result = pose_landmarker.detect(mp_image)

# 遍历检测到的每个人
for i, pose_landmarks in enumerate(detection_result.pose_landmarks):
    # 处理每个人的关键点...
```

### 2. 胸部区域定位

通过检测左右肩膀关键点来计算胸部中心：

```python
# 获取左右肩膀的关键点 (索引11和12)
left_shoulder = landmarks[11]   # 左肩
right_shoulder = landmarks[12]  # 右肩

# 计算胸部中心点（两肩膀中点）
chest_x = (left_x + right_x) // 2
chest_y = (left_y + right_y) // 2
```

### 3. 马赛克区域计算

基于胸部中心点定义马赛克区域：

```python
# 计算马赛克区域边界
x_start = center_x - mosaic_width // 2
y_start = center_y - mosaic_height // 2
x_end = center_x + mosaic_width // 2
y_end = center_y + mosaic_height // 2
```

### 4. 动态尺寸计算

根据双肩宽度动态计算马赛克尺寸：

```python
# 计算肩宽（两肩膀之间的距离）
shoulder_width = int(((right_x - left_x) ** 2 + (right_y - left_y) ** 2) ** 0.5)

# 根据肩宽比例计算马赛克宽度
dynamic_width = int(shoulder_width * shoulder_width_ratio)

# 应用尺寸限制
dynamic_width = max(min_mosaic_width, min(max_mosaic_width, dynamic_width))

# 根据宽高比计算高度
dynamic_height = int(dynamic_width / aspect_ratio)
```

### 5. 动态位置偏移调整

支持基于肩宽比例的动态垂直偏移，精确控制马赛克位置：

```python
# 根据肩宽比例计算动态偏移量
dynamic_offset = int(shoulder_width * vertical_offset_ratio)

# 应用垂直偏移量（正值向下，负值向上）
offset_center_y = center_y + dynamic_offset

# 计算马赛克区域边界（基于偏移后的中心点）
x_start = max(0, center_x - mosaic_width // 2)
y_start = max(0, offset_center_y - mosaic_height // 2)
x_end = min(width, center_x + mosaic_width // 2)
y_end = min(height, offset_center_y + mosaic_height // 2)
```

### 6. 马赛克效果实现

通过图片缩放实现马赛克效果：

```python
# 缩小图片
small_roi = cv2.resize(roi, (small_width, small_height))
# 放大回原尺寸，产生马赛克效果
mosaic_roi = cv2.resize(small_roi, (width, height), cv2.INTER_NEAREST)
```

## 测试和调试

### 运行测试脚本

```bash
# 基本测试
python src/image_mosaic/test_mosaic.py

# 交互式测试
python src/image_mosaic/test_mosaic.py --interactive

# 处理指定图片
python src/image_mosaic/test_mosaic.py --image input.jpg --output output.jpg

# 自定义参数
python src/image_mosaic/test_mosaic.py --image input.jpg --width 300 --height 300 --size 25
```

### 测试功能

测试脚本提供以下功能：

1. **基本功能测试**: 测试核心打码功能
2. **便捷函数测试**: 测试一行代码接口
3. **参数设置测试**: 测试不同参数组合的效果
4. **真实图片测试**: 测试实际图片的处理效果
5. **交互式测试**: 提供菜单式的测试界面

## 参数调优指南

### 马赛克模式选择

#### 固定尺寸模式
- **小区域** (100x100): 适合精确打码，影响范围小
- **中等区域** (200x200): 平衡效果，推荐默认值
- **大区域** (300x300): 覆盖范围广，确保完全遮挡

#### 动态尺寸模式
- **肩宽比例** (shoulder_width_ratio): 控制马赛克相对于人物大小的比例
  - 0.3: 较小的马赛克，精确遮挡
  - 0.5: 标准比例，推荐值
  - 0.8: 较大的马赛克，确保完全覆盖
- **宽高比** (aspect_ratio): 控制马赛克形状
  - 0.5: 高矩形，适合竖向遮挡
  - 1.0: 正方形，通用形状
  - 2.0: 宽矩形，适合横向遮挡

### 尺寸限制设置

```python
config = create_custom_config(
    min_mosaic_width=50,   # 防止马赛克过小
    max_mosaic_width=500,  # 防止马赛克过大
)
```

### 动态位置偏移设置

```python
config = create_custom_config(
    vertical_offset_ratio=0.0,   # 无偏移，马赛克中心对准胸部中心
    vertical_offset_ratio=0.1,   # 向下偏移肩宽的10%
    vertical_offset_ratio=0.15,  # 向下偏移肩宽的15%，适合更好的遮挡效果
    vertical_offset_ratio=-0.05, # 向上偏移肩宽的5%
)
```

### 马赛克块大小

- **细腻效果** (size=10): 马赛克块小，效果精细
- **标准效果** (size=20): 平衡可见性和处理效果
- **粗糙效果** (size=30): 马赛克块大，遮挡效果强

### 检测置信度

可以通过修改MediaPipe的`min_detection_confidence`参数来调整检测敏感度：

- **高敏感度** (0.3): 检测更多可能的人物，可能有误检
- **标准敏感度** (0.5): 平衡准确性和召回率
- **高准确度** (0.7): 只检测明确的人物，可能漏检

## 注意事项

1. **图片格式**: 支持常见的图片格式 (JPG, PNG, BMP等)
2. **人物姿态**: 需要能够清晰看到肩膀部位的人物
3. **图片质量**: 图片质量越高，检测效果越好
4. **处理时间**: 图片越大，处理时间越长
5. **内存使用**: 大图片会占用更多内存

## 常见问题

### Q: 为什么有些人物没有被检测到？

A: 可能的原因：
- 人物姿态不完整（肩膀被遮挡）
- 图片质量较低
- 人物过小或过远
- 检测置信度设置过高

### Q: 如何调整马赛克效果？

A: 可以通过以下参数调整：
- 增大`mosaic_size`使效果更粗糙
- 调整`mosaic_width`和`mosaic_height`改变覆盖范围

### Q: 支持批量处理吗？

A: 目前支持单张图片处理，批量处理可以通过循环调用实现：

```python
import glob
from pathlib import Path

processor = PersonMosaicProcessor()
for image_path in glob.glob("input/*.jpg"):
    output_path = Path("output") / Path(image_path).name
    processor.process_image_file(image_path, output_path)
```

## 版本历史

- **v1.0.0**: 初始版本，支持基本的人物打码功能

## 许可证

本模块遵循项目的整体许可证。
