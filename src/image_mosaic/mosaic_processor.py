#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人物图像处理器

使用MediaPipe进行人体姿态检测，自动识别胸部区域并添加马赛克或高斯模糊效果
"""

import cv2
import numpy as np
import mediapipe as mp
from typing import List, Tuple, Optional, Union, Dict
from pathlib import Path
import logging
import os

from src.image_mosaic.config import MosaicConfig, DEFAULT_CONFIG

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PersonMosaicProcessor:
    """人物图像处理器

    使用MediaPipe检测人体姿态，定位胸部区域并添加马赛克或高斯模糊效果
    """

    def __init__(self, config: Optional[MosaicConfig] = None,
                 mosaic_width: Optional[int] = None,
                 mosaic_height: Optional[int] = None,
                 mosaic_size: Optional[int] = None):
        """初始化图像处理器

        Args:
            config: 配置对象，如果提供则优先使用
            mosaic_width: 处理区域宽度，默认200像素
            mosaic_height: 处理区域高度，默认200像素
            mosaic_size: 马赛克块大小，默认20像素（仅当使用马赛克效果时）
        """
        # 使用配置对象或默认配置
        if config is None:
            config = DEFAULT_CONFIG

        # 如果提供了单独的参数，则覆盖配置
        self.mosaic_width = mosaic_width if mosaic_width is not None else config.mosaic_width
        self.mosaic_height = mosaic_height if mosaic_height is not None else config.mosaic_height
        self.mosaic_size = mosaic_size if mosaic_size is not None else config.mosaic_size

        # 保存配置对象以便后续使用
        self.config = config
        # 初始化贴纸缓存
        self._sticker_cache: Dict[str, np.ndarray] = {}
        self._load_stickers()

        # 初始化MediaPipe姿态检测 - 只使用新版API
        # 获取模型文件路径
        model_path = Path(__file__).parent / "models" / "pose_landmarker_heavy.task"

        if not model_path.exists():
            raise FileNotFoundError(f"模型文件不存在: {model_path}")

        # 创建姿态检测器选项
        options = mp.tasks.vision.PoseLandmarkerOptions(
            base_options=mp.tasks.BaseOptions(
                model_asset_path=str(model_path)
            ),
            running_mode=mp.tasks.vision.RunningMode.IMAGE,
            num_poses=config.max_people,
            min_pose_detection_confidence=config.min_detection_confidence,
            min_pose_presence_confidence=getattr(config, 'min_pose_presence_confidence', 0.1),
            min_tracking_confidence=getattr(config, 'min_tracking_confidence', 0.1)
        )
        self.visibility_threshold = 0.1  # 降低阈值
        # 创建姿态检测器
        self.pose_landmarker = mp.tasks.vision.PoseLandmarker.create_from_options(options)

        effect_info = f"效果类型: {self.config.effect_type}"
        if self.config.effect_type == "mosaic":
            effect_info += f", 块大小: {self.mosaic_size}"
        elif self.config.effect_type == "gaussian_blur":
            effect_info += f", 模糊核大小: {self.config.blur_kernel_size}"
        elif self.config.effect_type == "sticker":
            effect_info += f", 贴纸: {self.config.sticker_name}, 缩放: {self.config.sticker_scale}, 透明度: {self.config.sticker_opacity}, 拉伸模式: {self.config.sticker_stretch_mode}"

        logger.info(
            f"人物图像处理器初始化完成 - 处理区域: {self.mosaic_width}x{self.mosaic_height}, {effect_info}, 最多检测: {self.config.max_people}人")

    def _load_stickers(self):
        """加载贴纸资源"""
        stickers_dir = Path(__file__).parent / "stickers"
        if not stickers_dir.exists():
            logger.warning(f"贴纸目录不存在: {stickers_dir}")
            return

        # 加载所有PNG贴纸文件
        for sticker_file in stickers_dir.glob("*.png"):
            sticker_name = sticker_file.stem
            try:
                # 使用IMREAD_UNCHANGED读取包含alpha通道的图片
                sticker_img = cv2.imread(str(sticker_file), cv2.IMREAD_UNCHANGED)
                if sticker_img is not None:
                    self._sticker_cache[sticker_name] = sticker_img
                    # logger.info(f"加载贴纸: {sticker_name} ({sticker_img.shape})")
                else:
                    logger.warning(f"无法加载贴纸: {sticker_file}")
            except Exception as e:
                logger.error(f"加载贴纸 {sticker_file} 时出错: {e}")

        # logger.info(f"共加载 {len(self._sticker_cache)} 个贴纸")

    def _get_sticker(self, sticker_name: str) -> Optional[np.ndarray]:
        """获取贴纸图片

        Args:
            sticker_name: 贴纸名称

        Returns:
            贴纸图片数组，如果不存在则返回None
        """
        return self._sticker_cache.get(sticker_name)

    def detect_pose_landmarks(self, image: np.ndarray) -> List[List]:
        """检测图片中所有人物的姿态关键点

        Args:
            image: 输入图片 (BGR格式)

        Returns:
            所有人物的关键点列表 [pose_landmarks_1, pose_landmarks_2, ...]
        """
        # 转换为RGB格式（MediaPipe需要）
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        pose_landmarks_list = []

        # 使用新版API - 支持多人检测
        try:
            # 创建MediaPipe图像对象
            mp_image = mp.Image(image_format=mp.ImageFormat.SRGB, data=rgb_image)

            # 进行姿态检测
            detection_result = self.pose_landmarker.detect(mp_image)

            if detection_result.pose_landmarks:
                # logger.info(f"MediaPipe检测到 {len(detection_result.pose_landmarks)} 个姿态")

                for i, pose_landmarks in enumerate(detection_result.pose_landmarks):
                    # logger.info(f"处理第 {i + 1} 个检测到的姿态，关键点数量: {len(pose_landmarks)}")
                    pose_landmarks_list.append(pose_landmarks)
            else:
                logger.warning("MediaPipe未检测到任何姿态")

        except Exception as e:
            logger.error(f"姿态检测失败: {e}")

        # logger.info(f"共检测到 {len(pose_landmarks_list)} 个人物的关键点")

        # 如果启用了NMS，过滤重复检测
        if getattr(self.config, 'use_nms', False) and len(pose_landmarks_list) > 1:
            pose_landmarks_list = self._filter_duplicate_poses(pose_landmarks_list, image.shape[:2])

        return pose_landmarks_list

    def _filter_duplicate_poses(self, pose_landmarks_list, image_shape) -> List:
        """过滤重复的姿态检测

        Args:
            pose_landmarks_list: 检测到的姿态关键点列表
            image_shape: 图像尺寸 (height, width)

        Returns:
            过滤后的姿态关键点列表
        """
        if len(pose_landmarks_list) <= 1:
            return pose_landmarks_list

        height, width = image_shape
        min_distance = getattr(self.config, 'min_person_distance', 50)

        # 计算每个姿态的中心点（使用肩膀中点）
        pose_centers = []
        valid_poses = []

        for i, pose_landmarks in enumerate(pose_landmarks_list):
            if len(pose_landmarks) > 12:
                left_shoulder = pose_landmarks[11]  # LEFT_SHOULDER
                right_shoulder = pose_landmarks[12]  # RIGHT_SHOULDER

                # 检查肩膀关键点的可见性
                left_visibility = getattr(left_shoulder, 'visibility', getattr(left_shoulder, 'presence', 1.0))
                right_visibility = getattr(right_shoulder, 'visibility', getattr(right_shoulder, 'presence', 1.0))

                if left_visibility > self.visibility_threshold and right_visibility > self.visibility_threshold:
                    # 计算肩膀中心点
                    center_x = (left_shoulder.x + right_shoulder.x) * width / 2
                    center_y = (left_shoulder.y + right_shoulder.y) * height / 2
                    pose_centers.append((center_x, center_y, i))
                    valid_poses.append(pose_landmarks)

        # 使用简单的距离阈值过滤重复检测
        filtered_poses = []
        filtered_centers = []

        for i, (x1, y1, idx1) in enumerate(pose_centers):
            # 检查是否与已选择的姿态过于接近
            is_duplicate = False

            for x2, y2, _ in filtered_centers:
                distance = ((x1 - x2) ** 2 + (y1 - y2) ** 2) ** 0.5
                if distance < min_distance:
                    is_duplicate = True
                    break

            # 如果不是重复检测，则添加到结果中
            if not is_duplicate:
                filtered_poses.append(valid_poses[i])
                filtered_centers.append((x1, y1, idx1))

        logger.info(f"姿态过滤: 原始检测 {len(pose_landmarks_list)} 个，过滤后 {len(filtered_poses)} 个")
        return filtered_poses

    def detect_chest_centers_with_shoulder_width(self, image: np.ndarray, pose_landmarks_list) -> List[
        Tuple[int, int, int, float]]:
        """检测图片中所有人物的胸部中心点和肩宽

        Args:
            image: 输入图片 (BGR格式)

        Returns:
            胸部中心点和肩宽列表 [(x, y, shoulder_width, shoulder_angle), ...]
        """
        height, width = image.shape[:2]
        chest_data = []

        for i, pose_landmarks in enumerate(pose_landmarks_list):
            # 获取左右肩膀的坐标 (新版API使用索引)
            # 索引11: LEFT_SHOULDER, 索引12: RIGHT_SHOULDER
            if len(pose_landmarks) > 12:
                left_shoulder = pose_landmarks[11]  # LEFT_SHOULDER
                right_shoulder = pose_landmarks[12]  # RIGHT_SHOULDER

                # 获取可见性/存在性分数
                left_visibility = getattr(left_shoulder, 'visibility', getattr(left_shoulder, 'presence', 1.0))
                right_visibility = getattr(right_shoulder, 'visibility', getattr(right_shoulder, 'presence', 1.0))

                # logger.info(f"人物{i + 1} - 左肩可见性: {left_visibility:.3f}, 右肩可见性: {right_visibility:.3f}")

                # 降低可见性阈值，使用更宽松的条件
                left_visible = left_visibility > self.visibility_threshold
                right_visible = right_visibility > self.visibility_threshold

                if left_visible and right_visible:
                    # 转换为像素坐标
                    left_x = int(left_shoulder.x * width)
                    left_y = int(left_shoulder.y * height)
                    right_x = int(right_shoulder.x * width)
                    right_y = int(right_shoulder.y * height)

                    # logger.info(f"人物{i + 1} - 左肩坐标: ({left_x}, {left_y}), 右肩坐标: ({right_x}, {right_y})")

                    # 计算胸部中心点（两肩膀的中点）
                    chest_x = (left_x + right_x) // 2
                    chest_y = (left_y + right_y) // 2

                    # 计算肩宽（两肩膀之间的距离）
                    shoulder_width = int(((right_x - left_x) ** 2 + (right_y - left_y) ** 2) ** 0.5)

                    # 计算肩膀连线的角度（弧度）
                    import math
                    shoulder_angle = math.atan2(right_y - left_y, right_x - left_x)

                    # 验证肩宽是否合理（避免异常值）
                    if shoulder_width > 10:  # 肩宽至少10像素
                        chest_data.append((chest_x, chest_y, shoulder_width, shoulder_angle))
                        angle_degrees = math.degrees(shoulder_angle)
                        # logger.info(
                        #     f"✅ 检测到人物{i + 1} - 胸部中心点: ({chest_x}, {chest_y}), 肩宽: {shoulder_width}px, 肩膀角度: {angle_degrees:.1f}°")
                    else:
                        logger.warning(f"⚠️ 人物{i + 1} 肩宽过小({shoulder_width}px)，跳过")
                else:
                    logger.warning(
                        f"⚠️ 人物{i + 1} 肩膀关键点可见性不足，跳过 (左肩: {left_visibility:.3f}, 右肩: {right_visibility:.3f})")
            else:
                logger.warning(f"⚠️ 人物{i + 1} 关键点数量不足: {len(pose_landmarks)}")

        # logger.info(f"共检测到 {len(chest_data)} 个人物的胸部信息")
        return chest_data

    def detect_eye_centers_with_angle(self, image: np.ndarray, pose_landmarks_list) -> List[
        Tuple[int, int, int, float]]:
        """检测图片中所有人物的眼部中心点和眼部连线信息

        Args:
            image: 输入图片 (BGR格式)

        Returns:
            眼部中心点和连线信息列表 [(center_x, center_y, eye_distance, eye_angle), ...]
        """
        height, width = image.shape[:2]
        eye_data = []

        for i, pose_landmarks in enumerate(pose_landmarks_list):
            # 获取左右眼外角的坐标
            # 索引3: LEFT_EYE_OUTER, 索引6: RIGHT_EYE_OUTER
            if len(pose_landmarks) > 2:
                left_eye_outer = pose_landmarks[3]  # LEFT_EYE_OUTER
                right_eye_outer = pose_landmarks[6]  # RIGHT_EYE_OUTER

                # 获取可见性/存在性分数
                left_visibility = getattr(left_eye_outer, 'visibility', getattr(left_eye_outer, 'presence', 1.0))
                right_visibility = getattr(right_eye_outer, 'visibility', getattr(right_eye_outer, 'presence', 1.0))

                logger.info(
                    f"人物{i + 1} - 左眼外角可见性: {left_visibility:.3f}, 右眼外角可见性: {right_visibility:.3f}")

                # 检查可见性
                left_visible = left_visibility > self.visibility_threshold
                right_visible = right_visibility > self.visibility_threshold

                if left_visible and right_visible:
                    # 转换为像素坐标
                    left_x = int(left_eye_outer.x * width)
                    left_y = int(left_eye_outer.y * height)
                    right_x = int(right_eye_outer.x * width)
                    right_y = int(right_eye_outer.y * height)

                    logger.info(
                        f"人物{i + 1} - 左眼外角坐标: ({left_x}, {left_y}), 右眼外角坐标: ({right_x}, {right_y})")

                    # 计算眼部中心点（两眼外角的中点）
                    eye_center_x = (left_x + right_x) // 2
                    eye_center_y = (left_y + right_y) // 2

                    # 计算眼部距离（两眼外角之间的距离）
                    eye_distance = int(((right_x - left_x) ** 2 + (right_y - left_y) ** 2) ** 0.5)

                    # 计算眼部连线的角度（弧度）
                    import math
                    eye_angle = math.atan2(right_y - left_y, right_x - left_x)

                    # 验证眼部距离是否合理（避免异常值）
                    if eye_distance > 5:  # 眼部距离至少5像素
                        eye_data.append((eye_center_x, eye_center_y, eye_distance, eye_angle))
                        angle_degrees = math.degrees(eye_angle)
                        logger.info(
                            f"✅ 检测到人物{i + 1} - 眼部中心点: ({eye_center_x}, {eye_center_y}), 眼部距离: {eye_distance}px, 眼部角度: {angle_degrees:.1f}°")
                    else:
                        logger.warning(f"⚠️ 人物{i + 1} 眼部距离过小({eye_distance}px)，跳过")
                else:
                    logger.warning(
                        f"⚠️ 人物{i + 1} 眼部关键点可见性不足，跳过 (左眼: {left_visibility:.3f}, 右眼: {right_visibility:.3f})")
            else:
                logger.warning(f"⚠️ 人物{i + 1} 关键点数量不足: {len(pose_landmarks)}")

        logger.info(f"共检测到 {len(eye_data)} 个人物的眼部信息")
        return eye_data

    def calculate_dynamic_mosaic_size(self, shoulder_width: int) -> Optional[Tuple[int, int]]:
        """根据肩宽动态计算马赛克尺寸

        Args:
            shoulder_width: 肩宽（像素）

        Returns:
            (mosaic_width, mosaic_height) 马赛克区域尺寸，如果超出范围则返回None
        """
        if not self.config.use_dynamic_sizing:
            return self.mosaic_width, self.mosaic_height

        # 根据肩宽比例计算马赛克宽度
        dynamic_width = int(shoulder_width * self.config.shoulder_width_ratio)

        # 检查是否在有效范围内，超出范围则过滤掉
        if dynamic_width < self.config.min_mosaic_width or dynamic_width > self.config.max_mosaic_width:
            logger.info(f"过滤异常检测: 计算宽度 {dynamic_width}px 超出范围 [{self.config.min_mosaic_width}, {self.config.max_mosaic_width}]，肩宽: {shoulder_width}px")
            return None

        # 根据宽高比计算高度
        dynamic_height = int(dynamic_width / self.config.aspect_ratio)

        # logger.info(f"动态马赛克尺寸: {dynamic_width}x{dynamic_height} (基于肩宽: {shoulder_width}px)")

        return dynamic_width, dynamic_height

    def calculate_dynamic_vertical_offset(self, shoulder_width: int) -> int:
        """根据肩宽动态计算垂直偏移量

        Args:
            shoulder_width: 肩宽（像素）

        Returns:
            垂直偏移量（像素，正值向下，负值向上）
        """
        # 根据肩宽比例计算偏移量
        dynamic_offset = int(shoulder_width * self.config.vertical_offset_ratio)

        # logger.info(
        #     f"动态垂直偏移: {dynamic_offset}px (基于肩宽: {shoulder_width}px, 比例: {self.config.vertical_offset_ratio})")

        return dynamic_offset

    def apply_mosaic_to_region(self, image: np.ndarray, center_x: int, center_y: int,
                               mosaic_width: Optional[int] = None,
                               mosaic_height: Optional[int] = None,
                               shoulder_width: Optional[int] = None) -> np.ndarray:
        """在指定中心点周围添加图像效果（马赛克或高斯模糊）

        Args:
            image: 输入图片
            center_x: 中心点X坐标
            center_y: 中心点Y坐标
            mosaic_width: 处理区域宽度（可选，用于动态调整）
            mosaic_height: 处理区域高度（可选，用于动态调整）
            shoulder_width: 肩宽（可选，用于动态偏移量计算）

        Returns:
            添加效果后的图片
        """
        height, width = image.shape[:2]

        # 使用提供的尺寸或默认尺寸
        m_width = mosaic_width if mosaic_width is not None else self.mosaic_width
        m_height = mosaic_height if mosaic_height is not None else self.mosaic_height

        # 计算动态垂直偏移量
        if shoulder_width is not None:
            vertical_offset = self.calculate_dynamic_vertical_offset(shoulder_width)
        else:
            # 如果没有肩宽信息，使用默认肩宽100px来计算偏移量
            vertical_offset = self.calculate_dynamic_vertical_offset(100)

        # 应用垂直偏移量
        offset_center_y = center_y + vertical_offset

        # 计算马赛克区域边界
        x_start = max(0, center_x - m_width // 2)
        y_start = max(0, offset_center_y - m_height // 2)
        x_end = min(width, center_x + m_width // 2)
        y_end = min(height, offset_center_y + m_height // 2)

        # 确保区域有效
        if x_end <= x_start or y_end <= y_start:
            logger.warning(f"无效的马赛克区域: ({x_start}, {y_start}) - ({x_end}, {y_end})")
            return image

        # 提取需要打码的区域
        roi = image[y_start:y_end, x_start:x_end]

        # 应用图像效果
        processed_roi = self._create_effect(roi)

        # 将处理后的区域放回原图
        result_image = image.copy()
        result_image[y_start:y_end, x_start:x_end] = processed_roi

        effect_name = {"gaussian_blur": "高斯模糊", "mosaic": "马赛克", "sticker": f"贴纸({self.config.sticker_name})"}.get(self.config.effect_type, "未知效果")
        logger.info(
            f"在区域 ({x_start}, {y_start}) - ({x_end}, {y_end}) 添加{effect_name} (尺寸: {m_width}x{m_height}, 垂直偏移: {vertical_offset}px)")
        return result_image

    def apply_rotated_mosaic_to_region(self, image: np.ndarray, center_x: int, center_y: int,
                                       mosaic_width: Optional[int] = None,
                                       mosaic_height: Optional[int] = None,
                                       shoulder_width: Optional[int] = None,
                                       shoulder_angle: float = 0.0) -> np.ndarray:
        """在指定中心点周围添加旋转的图像效果（马赛克或高斯模糊）

        Args:
            image: 输入图片
            center_x: 中心点X坐标
            center_y: 中心点Y坐标
            mosaic_width: 处理区域宽度（可选，用于动态调整）
            mosaic_height: 处理区域高度（可选，用于动态调整）
            shoulder_width: 肩宽（可选，用于动态偏移量计算）
            shoulder_angle: 肩膀连线角度（弧度）

        Returns:
            添加效果后的图片
        """
        import math

        height, width = image.shape[:2]

        # 使用提供的尺寸或默认尺寸
        m_width = mosaic_width if mosaic_width is not None else self.mosaic_width
        m_height = mosaic_height if mosaic_height is not None else self.mosaic_height

        # 计算动态垂直偏移量
        if shoulder_width is not None:
            vertical_offset = self.calculate_dynamic_vertical_offset(shoulder_width)
        else:
            # 如果没有肩宽信息，使用默认肩宽100px来计算偏移量
            vertical_offset = self.calculate_dynamic_vertical_offset(100)

        # 计算垂直方向的单位向量（与肩膀连线垂直）
        # 肩膀连线角度 + 90度 = 垂直方向
        vertical_angle = shoulder_angle + math.pi / 2

        # 计算偏移后的中心点（沿垂直方向偏移）
        offset_center_x = center_x + int(vertical_offset * math.cos(vertical_angle))
        offset_center_y = center_y + int(vertical_offset * math.sin(vertical_angle))

        # 计算马赛克区域的四个角点（相对于中心点）
        corners = np.array([
            [-m_width // 2, -m_height // 2],
            [m_width // 2, -m_height // 2],
            [m_width // 2, m_height // 2],
            [-m_width // 2, m_height // 2]
        ], dtype=np.float32)

        # 计算旋转后的角点
        # 注意：要让矩形的垂直对称线与肩线垂直，矩形应该旋转肩膀角度
        rotated_corners = []
        cos_angle = math.cos(shoulder_angle)  # 使用肩膀角度，不是垂直角度
        sin_angle = math.sin(shoulder_angle)

        for corner in corners:
            # 应用旋转变换（绕原点旋转）
            rotated_x = corner[0] * cos_angle - corner[1] * sin_angle
            rotated_y = corner[0] * sin_angle + corner[1] * cos_angle

            # 平移到偏移后的中心点
            final_x = int(rotated_x + offset_center_x)
            final_y = int(rotated_y + offset_center_y)

            rotated_corners.append([final_x, final_y])

        rotated_corners = np.array(rotated_corners, dtype=np.int32)

        # 创建掩码
        mask = np.zeros((height, width), dtype=np.uint8)
        cv2.fillPoly(mask, [rotated_corners], 255)

        # 应用马赛克效果到旋转区域
        result_image = image.copy()

        # 获取掩码区域的边界框
        x_coords = rotated_corners[:, 0]
        y_coords = rotated_corners[:, 1]
        x_min, x_max = max(0, np.min(x_coords)), min(width, np.max(x_coords))
        y_min, y_max = max(0, np.min(y_coords)), min(height, np.max(y_coords))

        # 在边界框内应用马赛克
        if x_max > x_min and y_max > y_min:
            roi = result_image[y_min:y_max, x_min:x_max]
            roi_mask = mask[y_min:y_max, x_min:x_max]

            # 应用图像效果
            processed_roi = self._create_effect(roi)

            # 只在掩码区域应用效果
            roi[roi_mask > 0] = processed_roi[roi_mask > 0]
            result_image[y_min:y_max, x_min:x_max] = roi

        angle_degrees = math.degrees(shoulder_angle)
        effect_name = {"gaussian_blur": "高斯模糊", "mosaic": "马赛克", "sticker": f"贴纸({self.config.sticker_name})"}.get(self.config.effect_type, "未知效果")
        # logger.info(f"在旋转区域添加{effect_name} (中心: ({offset_center_x}, {offset_center_y}), 尺寸: {m_width}x{m_height}, 矩形旋转角度: {angle_degrees:.1f}°, 垂直偏移: {vertical_offset}px)")

        return result_image

    def apply_eye_mosaic_to_region(self, image: np.ndarray, center_x: int, center_y: int,
                                   eye_distance: int, eye_angle: float) -> np.ndarray:
        """在指定区域应用眼部图像效果（马赛克或高斯模糊）

        Args:
            image: 输入图片
            center_x: 眼部中心点X坐标
            center_y: 眼部中心点Y坐标
            eye_distance: 两眼外角之间的距离
            eye_angle: 眼部连线的角度（弧度）

        Returns:
            处理后的图片
        """
        import math

        height, width = image.shape[:2]

        # 计算马赛克尺寸
        mosaic_length = int(eye_distance * self.config.eye_length_ratio)
        mosaic_width = int(mosaic_length * self.config.eye_width_ratio)

        # 确保尺寸为偶数，便于计算中心点
        mosaic_length = mosaic_length + (mosaic_length % 2)
        mosaic_width = mosaic_width + (mosaic_width % 2)

        # 计算旋转矩形的四个角点（相对于中心点）
        half_length = mosaic_length // 2
        half_width = mosaic_width // 2

        # 原始矩形的四个角点（以中心为原点）
        corners = [
            (-half_length, -half_width),  # 左上
            (half_length, -half_width),  # 右上
            (half_length, half_width),  # 右下
            (-half_length, half_width)  # 左下
        ]

        # 应用旋转变换
        cos_angle = math.cos(eye_angle)
        sin_angle = math.sin(eye_angle)

        rotated_corners = []
        for x, y in corners:
            # 旋转变换
            rotated_x = x * cos_angle - y * sin_angle
            rotated_y = x * sin_angle + y * cos_angle

            # 转换为图像坐标
            final_x = int(center_x + rotated_x)
            final_y = int(center_y + rotated_y)

            # 确保坐标在图像范围内
            final_x = max(0, min(width - 1, final_x))
            final_y = max(0, min(height - 1, final_y))

            rotated_corners.append((final_x, final_y))

        rotated_corners = np.array(rotated_corners, dtype=np.int32)

        # 创建掩码
        mask = np.zeros((height, width), dtype=np.uint8)
        cv2.fillPoly(mask, [rotated_corners], 255)

        # 应用马赛克效果到旋转区域
        result_image = image.copy()

        # 获取掩码区域的边界框
        x_coords = rotated_corners[:, 0]
        y_coords = rotated_corners[:, 1]
        x_min, x_max = max(0, np.min(x_coords)), min(width, np.max(x_coords))
        y_min, y_max = max(0, np.min(y_coords)), min(height, np.max(y_coords))

        # 在边界框内应用马赛克
        if x_max > x_min and y_max > y_min:
            roi = result_image[y_min:y_max, x_min:x_max]
            roi_mask = mask[y_min:y_max, x_min:x_max]

            # 应用图像效果
            processed_roi = self._create_effect(roi)

            # 只在掩码区域应用效果
            roi[roi_mask > 0] = processed_roi[roi_mask > 0]
            result_image[y_min:y_max, x_min:x_max] = roi

        angle_degrees = math.degrees(eye_angle)
        effect_name = {"gaussian_blur": "高斯模糊", "mosaic": "马赛克", "sticker": f"贴纸({self.config.sticker_name})"}.get(self.config.effect_type, "未知效果")
        logger.info(
            f"在眼部区域添加{effect_name} (中心: ({center_x}, {center_y}), 尺寸: {mosaic_length}x{mosaic_width}, 角度: {angle_degrees:.1f}°)")

        return result_image

    def _create_effect(self, roi: np.ndarray) -> np.ndarray:
        """创建图像效果（马赛克、高斯模糊或贴纸）

        Args:
            roi: 需要处理的区域

        Returns:
            添加效果后的区域
        """
        if roi.size == 0:
            return roi

        if self.config.effect_type == "mosaic":
            return self._create_mosaic_effect(roi)
        elif self.config.effect_type == "gaussian_blur":
            return self._create_gaussian_blur_effect(roi)
        elif self.config.effect_type == "sticker":
            return self._create_sticker_effect(roi)
        else:
            logger.warning(f"未知的效果类型: {self.config.effect_type}，使用高斯模糊")
            return self._create_gaussian_blur_effect(roi)

    def _create_mosaic_effect(self, roi: np.ndarray) -> np.ndarray:
        """创建马赛克效果

        Args:
            roi: 需要打码的区域

        Returns:
            添加马赛克效果的区域
        """
        if roi.size == 0:
            return roi

        height, width = roi.shape[:2]

        # 缩小图片
        small_height = max(1, height // self.mosaic_size)
        small_width = max(1, width // self.mosaic_size)

        # 缩小后再放大，产生马赛克效果
        small_roi = cv2.resize(roi, (small_width, small_height), interpolation=cv2.INTER_LINEAR)
        mosaic_roi = cv2.resize(small_roi, (width, height), interpolation=cv2.INTER_NEAREST)

        return mosaic_roi

    def _create_gaussian_blur_effect(self, roi: np.ndarray) -> np.ndarray:
        """创建高斯模糊效果

        Args:
            roi: 需要模糊的区域

        Returns:
            添加高斯模糊效果的区域
        """
        if roi.size == 0:
            return roi

        # 确保核大小为奇数
        kernel_size = self.config.blur_kernel_size
        if kernel_size % 2 == 0:
            kernel_size += 1

        # 如果sigma为0，让OpenCV自动计算
        sigma_x = self.config.blur_sigma_x if self.config.blur_sigma_x > 0 else 0
        sigma_y = self.config.blur_sigma_y if self.config.blur_sigma_y > 0 else 0

        # 应用高斯模糊
        blurred_roi = cv2.GaussianBlur(roi, (kernel_size, kernel_size), sigma_x, sigmaY=sigma_y)

        return blurred_roi

    def _create_sticker_effect(self, roi: np.ndarray) -> np.ndarray:
        """创建贴纸效果

        Args:
            roi: 需要添加贴纸的区域

        Returns:
            添加贴纸效果的区域
        """
        if roi.size == 0:
            return roi

        # 获取贴纸图片
        sticker = self._get_sticker(self.config.sticker_name)
        if sticker is None:
            logger.warning(f"贴纸 '{self.config.sticker_name}' 不存在，使用高斯模糊代替")
            return self._create_gaussian_blur_effect(roi)

        # 复制原始区域
        result_roi = roi.copy()
        roi_height, roi_width = roi.shape[:2]

        # 根据拉伸模式计算贴纸尺寸
        sticker_width, sticker_height = self._calculate_sticker_size(roi_width, roi_height)

        # 调整贴纸大小
        resized_sticker = cv2.resize(sticker, (sticker_width, sticker_height))

        # 计算贴纸位置（居中）
        start_x = (roi_width - sticker_width) // 2
        start_y = (roi_height - sticker_height) // 2
        end_x = start_x + sticker_width
        end_y = start_y + sticker_height

        # 确保贴纸不超出边界
        if start_x < 0 or start_y < 0 or end_x > roi_width or end_y > roi_height:
            # 如果贴纸太大，调整到区域大小
            sticker_width = min(sticker_width, roi_width)
            sticker_height = min(sticker_height, roi_height)
            resized_sticker = cv2.resize(sticker, (sticker_width, sticker_height))
            start_x = (roi_width - sticker_width) // 2
            start_y = (roi_height - sticker_height) // 2
            end_x = start_x + sticker_width
            end_y = start_y + sticker_height

        # 应用贴纸
        if resized_sticker.shape[2] == 4:  # 有alpha通道
            self._apply_sticker_with_alpha(result_roi, resized_sticker, start_x, start_y)
        else:  # 没有alpha通道
            result_roi[start_y:end_y, start_x:end_x] = resized_sticker[:end_y-start_y, :end_x-start_x]

        return result_roi

    def _calculate_sticker_size(self, roi_width: int, roi_height: int) -> Tuple[int, int]:
        """根据拉伸模式计算贴纸尺寸

        Args:
            roi_width: 区域宽度
            roi_height: 区域高度

        Returns:
            (sticker_width, sticker_height) 贴纸尺寸
        """
        stretch_mode = self.config.sticker_stretch_mode

        if stretch_mode == "fill":
            # 填充整个区域
            sticker_width = int(roi_width * self.config.sticker_scale)
            sticker_height = int(roi_height * self.config.sticker_scale)

        elif stretch_mode == "horizontal":
            # 横向拉伸，纵向保持比例
            sticker_width = int(roi_width * self.config.sticker_scale * self.config.sticker_stretch_factor_x)
            # 保持原始贴纸的宽高比
            aspect_ratio = roi_height / roi_width if roi_width > 0 else 1.0
            sticker_height = int(sticker_width * aspect_ratio)

        elif stretch_mode == "vertical":
            # 纵向拉伸，横向保持比例
            sticker_height = int(roi_height * self.config.sticker_scale * self.config.sticker_stretch_factor_y)
            # 保持原始贴纸的宽高比
            aspect_ratio = roi_width / roi_height if roi_height > 0 else 1.0
            sticker_width = int(sticker_height * aspect_ratio)

        else:  # uniform - 等比例缩放（默认）
            # 保持贴纸原始宽高比，按较小边缩放
            target_size = min(roi_width, roi_height)
            sticker_size = int(target_size * self.config.sticker_scale)
            sticker_width = sticker_height = sticker_size

        # 确保尺寸至少为1像素
        sticker_width = max(1, sticker_width)
        sticker_height = max(1, sticker_height)

        return sticker_width, sticker_height

    def _apply_sticker_with_alpha(self, background: np.ndarray, sticker: np.ndarray,
                                 start_x: int, start_y: int):
        """应用带透明度的贴纸

        Args:
            background: 背景图片
            sticker: 贴纸图片（RGBA格式）
            start_x: 贴纸在背景中的起始X坐标
            start_y: 贴纸在背景中的起始Y坐标
        """
        sticker_height, sticker_width = sticker.shape[:2]
        end_x = start_x + sticker_width
        end_y = start_y + sticker_height

        # 确保不超出背景边界
        bg_height, bg_width = background.shape[:2]
        end_x = min(end_x, bg_width)
        end_y = min(end_y, bg_height)
        actual_width = end_x - start_x
        actual_height = end_y - start_y

        if actual_width <= 0 or actual_height <= 0:
            return

        # 提取贴纸的RGB和Alpha通道
        sticker_rgb = sticker[:actual_height, :actual_width, :3]
        sticker_alpha = sticker[:actual_height, :actual_width, 3:4] / 255.0

        # 应用透明度配置
        sticker_alpha = sticker_alpha * self.config.sticker_opacity

        # 获取背景区域
        bg_region = background[start_y:end_y, start_x:end_x]

        # Alpha混合
        blended = sticker_rgb * sticker_alpha + bg_region * (1 - sticker_alpha)
        background[start_y:end_y, start_x:end_x] = blended.astype(np.uint8)

    def process_image(self, image: np.ndarray, apply_chest: bool = True,
                      apply_eyes: bool = False) -> np.ndarray:
        """处理图片，可选择性地对人物胸部和/或眼部进行图像效果处理

        Args:
            image: 输入图片
            apply_chest: 是否对胸部进行效果处理
            apply_eyes: 是否对眼部进行效果处理

        Returns:
            处理后的图片
        """
        result_image = image.copy()
        pose_landmarks_list = self.detect_pose_landmarks(image)

        if apply_chest:
            # 检测所有人物的胸部中心点和肩宽
            chest_data = self.detect_chest_centers_with_shoulder_width(image, pose_landmarks_list)

            # 为每个人物添加胸部图像效果
            for center_x, center_y, shoulder_width, shoulder_angle in chest_data:
                if self.config.use_dynamic_sizing:
                    # 动态计算处理区域尺寸
                    size_result = self.calculate_dynamic_mosaic_size(shoulder_width)
                    if size_result is None:
                        # 尺寸超出范围，跳过此检测
                        logger.info(f"跳过异常检测: 中心点({center_x}, {center_y}), 肩宽{shoulder_width}px")
                        continue

                    mosaic_width, mosaic_height = size_result
                    result_image = self.apply_rotated_mosaic_to_region(
                        result_image, center_x, center_y, mosaic_width, mosaic_height,
                        shoulder_width, shoulder_angle
                    )
                else:
                    # 使用固定尺寸，但仍然传递肩宽和角度用于动态偏移量计算
                    result_image = self.apply_rotated_mosaic_to_region(
                        result_image, center_x, center_y, None, None,
                        shoulder_width, shoulder_angle
                    )

        if apply_eyes:
            # 检测所有人物的眼部中心点和连线信息
            eye_data = self.detect_eye_centers_with_angle(result_image, pose_landmarks_list)
            # 为每个人物的眼部添加图像效果
            for center_x, center_y, eye_distance, eye_angle in eye_data:
                result_image = self.apply_eye_mosaic_to_region(
                    result_image, center_x, center_y, eye_distance, eye_angle
                )

        return result_image

    def process_image_file(self, input_path: Union[str, Path], output_path: Union[str, Path], apply_chest: bool = True,
                           apply_eyes: bool = False) -> bool:
        """处理图片文件
        
        Args:
            input_path: 输入图片路径
            output_path: 输出图片路径
            
        Returns:
            处理是否成功
        """
        try:
            # 读取图片
            image = cv2.imread(str(input_path))
            if image is None:
                logger.error(f"无法读取图片: {input_path}")
                return False

            # 处理图片
            result_image = self.process_image(image, apply_chest, apply_eyes)

            # 保存结果
            success = cv2.imwrite(str(output_path), result_image)
            if success:
                logger.info(f"处理完成，结果保存到: {output_path}")
                return True
            else:
                logger.error(f"保存图片失败: {output_path}")
                return False

        except Exception as e:
            logger.error(f"处理图片时发生错误: {e}")
            return False

    def process_video(self, input_video_path: Union[str, Path],
                      output_video_path: Union[str, Path], apply_chest: bool = True,
                      apply_eyes: bool = False) -> bool:
        """处理视频文件，对每一帧进行人物图像效果处理

        Args:
            input_video_path: 输入视频文件路径
            output_video_path: 输出视频文件路径
            apply_chest: 是否对胸部进行效果处理
            apply_eyes: 是否对眼部进行效果处理
        Returns:
            bool: 处理是否成功
        """
        input_path = Path(input_video_path)
        output_path = Path(output_video_path)

        if not input_path.exists():
            logger.error(f"输入视频文件不存在: {input_path}")
            return False

        # 确保输出目录存在
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # 打开输入视频
        cap = cv2.VideoCapture(str(input_path))
        if not cap.isOpened():
            logger.error(f"无法打开输入视频: {input_path}")
            return False

        try:
            # 获取视频属性
            fps = int(cap.get(cv2.CAP_PROP_FPS))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

            logger.info(f"视频信息: {width}x{height}, {fps}fps, {total_frames}帧")

            # 设置视频编码器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))

            if not out.isOpened():
                logger.error(f"无法创建输出视频: {output_path}")
                return False

            frame_count = 0
            processed_frames = 0

            logger.info("开始处理视频帧...")

            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                frame_count += 1

                try:
                    # 处理当前帧
                    processed_frame = self.process_image(frame, apply_chest, apply_eyes)
                    out.write(processed_frame)
                    processed_frames += 1

                    # 每100帧输出一次进度
                    if frame_count % 100 == 0:
                        progress = (frame_count / total_frames) * 100
                        logger.info(f"处理进度: {frame_count}/{total_frames} ({progress:.1f}%)")

                except Exception as e:
                    logger.warning(f"处理第{frame_count}帧时出错: {e}")
                    # 如果处理失败，写入原始帧
                    out.write(frame)

            logger.info(f"视频处理完成: 处理了{processed_frames}/{frame_count}帧")
            return True

        except Exception as e:
            logger.error(f"视频处理出错: {e}")
            return False

        finally:
            # 释放资源
            cap.release()
            if 'out' in locals():
                out.release()

    def __del__(self):
        """清理资源"""
        if hasattr(self, 'pose'):
            self.pose.close()
