#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建贴纸资源文件

生成各种形状的贴纸图片，包括云朵、星星、心形等
"""

import cv2
import numpy as np
from pathlib import Path
import math


def create_cloud_sticker(size: int = 200) -> np.ndarray:
    """创建云朵贴纸
    
    Args:
        size: 贴纸尺寸（正方形）
        
    Returns:
        RGBA格式的云朵贴纸图片
    """
    # 创建透明背景
    sticker = np.zeros((size, size, 4), dtype=np.uint8)
    
    # 云朵颜色（白色，完全不透明）
    cloud_color = (255, 255, 255, 255)  # RGBA
    
    # 云朵中心
    center_x, center_y = size // 2, size // 2
    
    # 绘制多个圆形组成云朵
    circles = [
        (center_x, center_y, size // 4),  # 主体
        (center_x - size // 6, center_y - size // 8, size // 5),  # 左上
        (center_x + size // 6, center_y - size // 8, size // 5),  # 右上
        (center_x - size // 4, center_y + size // 8, size // 6),  # 左下
        (center_x + size // 4, center_y + size // 8, size // 6),  # 右下
        (center_x, center_y - size // 4, size // 6),  # 顶部
    ]
    
    for x, y, radius in circles:
        cv2.circle(sticker, (x, y), radius, cloud_color, -1)
    
    return sticker


def create_star_sticker(size: int = 200) -> np.ndarray:
    """创建星星贴纸
    
    Args:
        size: 贴纸尺寸（正方形）
        
    Returns:
        RGBA格式的星星贴纸图片
    """
    # 创建透明背景
    sticker = np.zeros((size, size, 4), dtype=np.uint8)
    
    # 星星颜色（金黄色，完全不透明）
    star_color = (0, 215, 255, 255)  # RGBA (BGR格式)
    
    # 星星中心
    center_x, center_y = size // 2, size // 2
    radius = size // 3
    
    # 五角星的点
    points = []
    for i in range(10):
        angle = i * math.pi / 5 - math.pi / 2
        if i % 2 == 0:
            # 外部点
            x = center_x + int(radius * math.cos(angle))
            y = center_y + int(radius * math.sin(angle))
        else:
            # 内部点
            x = center_x + int(radius * 0.4 * math.cos(angle))
            y = center_y + int(radius * 0.4 * math.sin(angle))
        points.append([x, y])
    
    points = np.array(points, dtype=np.int32)
    cv2.fillPoly(sticker, [points], star_color)
    
    return sticker


def create_heart_sticker(size: int = 200) -> np.ndarray:
    """创建心形贴纸
    
    Args:
        size: 贴纸尺寸（正方形）
        
    Returns:
        RGBA格式的心形贴纸图片
    """
    # 创建透明背景
    sticker = np.zeros((size, size, 4), dtype=np.uint8)
    
    # 心形颜色（粉红色，完全不透明）
    heart_color = (147, 20, 255, 255)  # RGBA (BGR格式)
    
    # 心形中心
    center_x, center_y = size // 2, size // 2 + size // 8
    scale = size // 8
    
    # 绘制心形（使用两个圆和一个三角形）
    # 左半圆
    cv2.circle(sticker, (center_x - scale // 2, center_y - scale // 2), scale, heart_color, -1)
    # 右半圆
    cv2.circle(sticker, (center_x + scale // 2, center_y - scale // 2), scale, heart_color, -1)
    
    # 下半部分三角形
    triangle_points = np.array([
        [center_x - scale, center_y],
        [center_x + scale, center_y],
        [center_x, center_y + int(scale * 1.5)]
    ], dtype=np.int32)
    
    cv2.fillPoly(sticker, [triangle_points], heart_color)
    
    return sticker


def create_flower_sticker(size: int = 200) -> np.ndarray:
    """创建花朵贴纸
    
    Args:
        size: 贴纸尺寸（正方形）
        
    Returns:
        RGBA格式的花朵贴纸图片
    """
    # 创建透明背景
    sticker = np.zeros((size, size, 4), dtype=np.uint8)
    
    # 花瓣颜色（粉色）
    petal_color = (203, 192, 255, 220)  # RGBA (BGR格式)
    center_color = (0, 255, 255, 220)  # 花心颜色（黄色）
    
    # 花朵中心
    center_x, center_y = size // 2, size // 2
    petal_radius = size // 6
    
    # 绘制花瓣（6个椭圆）
    for i in range(6):
        angle = i * math.pi / 3
        petal_x = center_x + int(petal_radius * 0.8 * math.cos(angle))
        petal_y = center_y + int(petal_radius * 0.8 * math.sin(angle))
        
        # 绘制椭圆花瓣
        axes = (petal_radius // 2, petal_radius)
        cv2.ellipse(sticker, (petal_x, petal_y), axes, 
                   math.degrees(angle), 0, 360, petal_color, -1)
    
    # 绘制花心
    cv2.circle(sticker, (center_x, center_y), petal_radius // 3, center_color, -1)
    
    return sticker


def main():
    """生成所有贴纸文件"""
    stickers_dir = Path("src/image_mosaic/stickers")
    stickers_dir.mkdir(exist_ok=True)
    
    # 生成各种贴纸
    stickers = {
        "cloud": create_cloud_sticker,
        "star": create_star_sticker,
        "heart": create_heart_sticker,
        "flower": create_flower_sticker,
    }
    
    for name, create_func in stickers.items():
        print(f"创建 {name} 贴纸...")
        sticker = create_func(200)
        
        # 保存为PNG格式（支持透明度）
        output_path = stickers_dir / f"{name}.png"
        cv2.imwrite(str(output_path), sticker)
        print(f"保存到: {output_path}")
    
    print("所有贴纸创建完成！")


if __name__ == "__main__":
    main()
