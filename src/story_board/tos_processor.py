import os
import tos
import requests
import threading
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Optional, Tuple
import logging

class TOSProcessor:
    """火山引擎TOS对象存储处理器"""
    
    def __init__(self, 
                 access_key: Optional[str] = None,
                 secret_key: Optional[str] = None, 
                 endpoint: Optional[str] = None,
                 region: Optional[str] = None,
                 bucket_name: Optional[str] = None):
        """
        初始化TOS处理器
        
        Args:
            access_key: TOS访问密钥ID，默认从环境变量TOS_ACCESS_KEY获取
            secret_key: TOS访问密钥，默认从环境变量TOS_SECRET_KEY获取
            endpoint: TOS服务端点，默认从环境变量TOS_ENDPOINT获取
            region: TOS区域，默认从环境变量TOS_REGION获取
            bucket_name: 存储桶名称，默认为"ai-novel"
        """
        self.access_key = access_key or os.getenv('TOS_ACCESS_KEY')
        self.secret_key = secret_key or os.getenv('TOS_SECRET_KEY')
        self.endpoint = endpoint or os.getenv('TOS_ENDPOINT')
        self.region = region or os.getenv('TOS_REGION')
        self.bucket_name = bucket_name or os.getenv('TOS_BUCKET') or "ai-novel"
        self._lock = threading.Lock()

        if not all([self.access_key, self.secret_key, self.endpoint, self.region, self.bucket_name]):
            raise ValueError("TOS配置不完整，请检查环境变量TOS_ACCESS_KEY, TOS_SECRET_KEY, TOS_ENDPOINT, TOS_REGION, TOS_BUCKET")
        
        # 初始化TOS客户端
        try:
            self.client = tos.TosClientV2(self.access_key, self.secret_key, self.endpoint, self.region)
            self._ensure_bucket_exists()
            self._create_folders()
        except Exception as e:
            logging.error(f"TOS客户端初始化失败: {e}")
            raise
    
    def _ensure_bucket_exists(self):
        """确保存储桶存在，如果不存在则创建"""
        try:
            # 检查桶是否存在
            self.client.head_bucket(self.bucket_name)
            logging.info(f"存储桶 {self.bucket_name} 已存在")
        except tos.exceptions.TosServerError as e:
            if e.status_code == 404:
                # 桶不存在，创建桶
                try:
                    # 创建公共读权限的桶
                    self.client.create_bucket(self.bucket_name, acl=tos.ACLType.ACL_Public_Read)
                    logging.info(f"成功创建存储桶 {self.bucket_name}")
                except Exception as create_error:
                    logging.error(f"创建存储桶失败: {create_error}")
                    raise
            else:
                logging.error(f"检查存储桶失败: {e}")
                raise
    
    def _create_folders(self):
        """创建images、audios、videos文件夹"""
        folders = ['images/', 'audios/', 'videos/']
        for folder in folders:
            try:
                # 通过上传空对象来创建文件夹
                self.client.put_object(self.bucket_name, folder, content='')
                logging.info(f"创建文件夹: {folder}")
            except Exception as e:
                logging.warning(f"创建文件夹 {folder} 失败: {e}")
    
    def upload_file(self, local_file_path: str, object_key: str) -> str:
        """
        上传本地文件到TOS

        Args:
            local_file_path: 本地文件路径
            object_key: TOS对象键名

        Returns:
            公共访问URL
        """
        import uuid

        try:
            with self._lock:
                # 使用UUID确保文件名唯一性
                unique_id = str(uuid.uuid4())
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                file_ext = os.path.splitext(object_key)[1]
                base_name = os.path.splitext(os.path.basename(object_key))[0]

                # 组合唯一的文件名：基础名_时间戳_UUID.扩展名
                unique_key = f"{os.path.dirname(object_key)}/{base_name}_{timestamp}_{unique_id}{file_ext}"

                # 上传文件
                with open(local_file_path, 'rb') as f:
                    self.client.put_object(self.bucket_name, unique_key, content=f)

                # 生成公共访问URL
                public_url = f"https://{self.bucket_name}.{self.endpoint.replace('https://', '').replace('http://', '')}/{unique_key}"
                logging.info(f"文件上传成功: {public_url}")
                return public_url

        except Exception as e:
            logging.error(f"上传文件失败: {e}")
            raise
    
    def upload_from_url(self, url: str, object_key: str) -> str:
        """
        从URL下载文件并上传到TOS

        Args:
            url: 源文件URL
            object_key: TOS对象键名

        Returns:
            公共访问URL
        """
        import uuid

        try:
            # 下载文件
            response = requests.get(url, timeout=30)
            response.raise_for_status()

            with self._lock:
                # 使用UUID确保文件名唯一性
                unique_id = str(uuid.uuid4())
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                file_ext = os.path.splitext(object_key)[1]
                base_name = os.path.splitext(os.path.basename(object_key))[0]

                # 组合唯一的文件名：基础名_时间戳_UUID.扩展名
                unique_key = f"{os.path.dirname(object_key)}/{base_name}_{timestamp}_{unique_id}{file_ext}"

                # 上传到TOS
                self.client.put_object(self.bucket_name, unique_key, content=response.content)

                # 生成公共访问URL
                public_url = f"https://{self.bucket_name}.{self.endpoint.replace('https://', '').replace('http://', '')}/{unique_key}"
                logging.info(f"从URL上传文件成功: {unique_key}")
                return public_url

        except Exception as e:
            logging.error(f"从URL上传文件失败: {e}")
            raise
    
    def batch_upload(self, upload_tasks: List[Dict], max_workers: int = 5) -> List[str]:
        """
        批量并发上传文件
        
        Args:
            upload_tasks: 上传任务列表，每个任务包含{'type': 'file'/'url', 'source': '源路径/URL', 'key': '对象键名'}
            max_workers: 最大并发数
            
        Returns:
            公共访问URL列表
        """
        results = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_task = {}
            
            for task in upload_tasks:
                if task['type'] == 'file':
                    future = executor.submit(self.upload_file, task['source'], task['key'])
                elif task['type'] == 'url':
                    future = executor.submit(self.upload_from_url, task['source'], task['key'])
                else:
                    logging.warning(f"未知的上传类型: {task['type']}")
                    continue
                    
                future_to_task[future] = task
            
            for future in as_completed(future_to_task):
                task = future_to_task[future]
                try:
                    url = future.result()
                    results.append(url)
                    logging.info(f"批量上传成功: {task['key']}")
                except Exception as e:
                    logging.error(f"批量上传失败 {task['key']}: {e}")
                    results.append(None)
        
        return results
    
    def clean_old_files(self, days: int = 3):
        """
        清理指定天数之前的文件
        
        Args:
            days: 保留天数，默认3天
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # 列出所有对象
            response = self.client.list_objects_v2(self.bucket_name)
            
            deleted_count = 0
            for obj in response.contents:
                # 检查文件修改时间
                if obj.last_modified < cutoff_date:
                    try:
                        self.client.delete_object(self.bucket_name, obj.key)
                        deleted_count += 1
                        logging.info(f"删除过期文件: {obj.key}")
                    except Exception as e:
                        logging.error(f"删除文件失败 {obj.key}: {e}")
            
            logging.info(f"清理完成，共删除 {deleted_count} 个过期文件")
            
        except Exception as e:
            logging.error(f"清理过期文件失败: {e}")
            raise
    
    def get_file_list(self, prefix: str = "") -> List[Dict]:
        """
        获取文件列表
        
        Args:
            prefix: 文件前缀过滤
            
        Returns:
            文件信息列表
        """
        try:
            response = self.client.list_objects_v2(self.bucket_name, prefix=prefix)
            
            files = []
            for obj in response.contents:
                files.append({
                    'key': obj.key,
                    'size': obj.size,
                    'last_modified': obj.last_modified,
                    'etag': obj.etag
                })
            
            return files
            
        except Exception as e:
            logging.error(f"获取文件列表失败: {e}")
            raise
    
    def get_public_url(self, object_key: str) -> str:
        """
        获取文件的公共访问URL
        
        Args:
            object_key: 对象键名
            
        Returns:
            公共访问URL
        """
        return f"https://{self.bucket_name}.{self.endpoint.replace('https://', '').replace('http://', '')}/{object_key}"