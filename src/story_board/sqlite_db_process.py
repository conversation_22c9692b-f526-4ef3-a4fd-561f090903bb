# -*- coding: utf-8 -*-
"""
@Time ： 2025/7/11 22:34
@Auth ： xiaolongtuan
@File ：sqlite_db_process.py
"""

# 对sqlite数据进行操作，包括在指定路径创建数据库，加载数据，读取/更新/删除数据

import sqlite3
import json
import os
import logging
import threading
import queue
import time
from typing import List, Dict, Any, Optional, Union, Tuple
from pathlib import Path
from datetime import datetime
from contextlib import contextmanager


class ConnectionPool:
    """SQLite连接池"""
    
    def __init__(self, db_path: str, max_connections: int = 10, timeout: float = 30.0):
        self.db_path = db_path
        self.max_connections = max_connections
        self.timeout = timeout
        self._pool = queue.Queue(maxsize=max_connections)
        self._created_connections = 0
        self._lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
        
    def _create_connection(self) -> sqlite3.Connection:
        """创建新的数据库连接"""
        conn = sqlite3.connect(self.db_path, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        # 启用WAL模式以支持并发读写
        conn.execute('PRAGMA journal_mode=WAL')
        conn.execute('PRAGMA synchronous=NORMAL')
        conn.execute('PRAGMA cache_size=10000')
        conn.execute('PRAGMA temp_store=MEMORY')
        return conn
    
    def get_connection(self) -> sqlite3.Connection:
        """从连接池获取连接"""
        try:
            # 尝试从池中获取连接
            conn = self._pool.get(timeout=self.timeout)
            # 检查连接是否有效
            try:
                conn.execute('SELECT 1')
                return conn
            except sqlite3.Error:
                # 连接无效，创建新连接
                conn.close()
                return self._create_connection()
        except queue.Empty:
            # 池中没有可用连接，创建新连接
            with self._lock:
                if self._created_connections < self.max_connections:
                    self._created_connections += 1
                    return self._create_connection()
                else:
                    # 达到最大连接数，等待可用连接
                    raise RuntimeError(f"连接池已满，最大连接数: {self.max_connections}")
    
    def return_connection(self, conn: sqlite3.Connection):
        """将连接返回到池中"""
        try:
            # 检查连接是否有效
            conn.execute('SELECT 1')
            self._pool.put_nowait(conn)
        except (sqlite3.Error, queue.Full):
            # 连接无效或池已满，关闭连接
            conn.close()
            with self._lock:
                self._created_connections -= 1
    
    def close_all(self):
        """关闭所有连接"""
        while not self._pool.empty():
            try:
                conn = self._pool.get_nowait()
                conn.close()
            except queue.Empty:
                break
        self._created_connections = 0


class SQLiteDBProcessor:
    def __init__(self, db_path: str, auto_create: bool = True, max_connections: int = 10):
        """
        初始化数据库处理器
        
        Args:
            db_path: 数据库文件路径
            auto_create: 是否自动创建数据库文件和目录
            max_connections: 连接池最大连接数
        """
        self.db_path = Path(db_path)
        self.max_connections = max_connections
        
        if auto_create:
            # 创建目录（如果不存在）
            self.db_path.parent.mkdir(parents=True, exist_ok=True)
            
        # 初始化连接池
        self._pool = ConnectionPool(str(self.db_path), max_connections)
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = self._pool.get_connection()
        try:
            yield conn
        finally:
            self._pool.return_connection(conn)
    
    def __enter__(self):
        """上下文管理器入口（保持向后兼容）"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口（保持向后兼容）"""
        pass
    
    def close(self):
        """关闭连接池"""
        self._pool.close_all()
        self.logger.info("数据库连接池已关闭")
    
    def execute_sql(self, sql: str, params: Optional[Union[Tuple, Dict]] = None, 
                   fetch_result: bool = False) -> Optional[List[sqlite3.Row]]:
        """
        执行SQL语句
        
        Args:
            sql: SQL语句
            params: 参数
            fetch_result: 是否返回查询结果
            
        Returns:
            查询结果（如果fetch_result为True）
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            try:
                if params:
                    cursor.execute(sql, params)
                else:
                    cursor.execute(sql)
                
                if fetch_result:
                    return cursor.fetchall()
                else:
                    conn.commit()
                    return None
                    
            except sqlite3.Error as e:
                conn.rollback()
                self.logger.error(f"执行SQL失败: {sql}, 错误: {e}")
                raise
            finally:
                cursor.close()
    
    def execute_many(self, sql: str, params_list: List[Union[Tuple, Dict]]):
        """
        批量执行SQL语句
        
        Args:
            sql: SQL语句
            params_list: 参数列表
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            try:
                cursor.executemany(sql, params_list)
                conn.commit()
                self.logger.info(f"批量执行SQL成功，影响行数: {cursor.rowcount}")
            except sqlite3.Error as e:
                conn.rollback()
                self.logger.error(f"批量执行SQL失败: {sql}, 错误: {e}")
                raise
            finally:
                cursor.close()

    def create_table(self, table_name: str, columns: Dict[str, str], 
                    if_not_exists: bool = True) -> bool:
        """
        创建数据表
        
        Args:
            table_name: 表名
            columns: 列定义字典，格式为 {列名: 列类型和约束}
            if_not_exists: 是否使用IF NOT EXISTS
            
        Returns:
            是否创建成功
            
        Example:
            columns = {
                'id': 'INTEGER PRIMARY KEY AUTOINCREMENT',
                'name': 'TEXT NOT NULL',
                'age': 'INTEGER',
                'created_at': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
            }
        """
        try:
            columns_sql = ', '.join([f'{col} {definition}' for col, definition in columns.items()])
            if_not_exists_sql = 'IF NOT EXISTS' if if_not_exists else ''
            
            sql = f"CREATE TABLE {if_not_exists_sql} {table_name} ({columns_sql})"
            self.execute_sql(sql)
            
            self.logger.info(f"表 {table_name} 创建成功")
            return True
            
        except sqlite3.Error as e:
            self.logger.error(f"创建表 {table_name} 失败: {e}")
            return False
    
    def drop_table(self, table_name: str, if_exists: bool = True) -> bool:
        """
        删除数据表
        
        Args:
            table_name: 表名
            if_exists: 是否使用IF EXISTS
            
        Returns:
            是否删除成功
        """
        try:
            if_exists_sql = 'IF EXISTS' if if_exists else ''
            sql = f"DROP TABLE {if_exists_sql} {table_name}"
            self.execute_sql(sql)
            
            self.logger.info(f"表 {table_name} 删除成功")
            return True
            
        except sqlite3.Error as e:
            self.logger.error(f"删除表 {table_name} 失败: {e}")
            return False
    
    def table_exists(self, table_name: str) -> bool:
        """
        检查表是否存在
        
        Args:
            table_name: 表名
            
        Returns:
            表是否存在
        """
        sql = "SELECT name FROM sqlite_master WHERE type='table' AND name=?"
        result = self.execute_sql(sql, (table_name,), fetch_result=True)
        return len(result) > 0 if result else False
    
    def get_table_info(self, table_name: str) -> List[Dict[str, Any]]:
        """
        获取表结构信息
        
        Args:
            table_name: 表名
            
        Returns:
            表结构信息列表
        """
        sql = f"PRAGMA table_info({table_name})"
        result = self.execute_sql(sql, fetch_result=True)
        
        if result:
            return [dict(row) for row in result]
        return []
    
    def insert_data(self, table_name: str, data: Union[Dict[str, Any], List[Dict[str, Any]]], 
                   replace: bool = False) -> int:
        """
        插入数据
        
        Args:
            table_name: 表名
            data: 要插入的数据，可以是单条记录（字典）或多条记录（字典列表）
            replace: 是否使用REPLACE INTO（处理主键冲突）
            
        Returns:
            插入的行数
        """
        if not data:
            return 0
        
        # 统一处理为列表格式
        if isinstance(data, dict):
            data = [data]
        
        if not data:
            return 0
        
        # 获取列名
        columns = list(data[0].keys())
        placeholders = ', '.join(['?' for _ in columns])
        columns_sql = ', '.join(columns)
        
        # 构建SQL
        action = 'REPLACE' if replace else 'INSERT'
        sql = f"{action} INTO {table_name} ({columns_sql}) VALUES ({placeholders})"
        
        # 准备参数
        params_list = [tuple(record[col] for col in columns) for record in data]
        
        try:
            if len(params_list) == 1:
                self.execute_sql(sql, params_list[0])
            else:
                self.execute_many(sql, params_list)
            
            self.logger.info(f"向表 {table_name} 插入 {len(params_list)} 条记录")
            return len(params_list)
            
        except sqlite3.Error as e:
            self.logger.error(f"插入数据到表 {table_name} 失败: {e}")
            raise
    
    def select_data(self, table_name: str, columns: Optional[List[str]] = None,
                   where: Optional[str] = None, params: Optional[Tuple] = None,
                   order_by: Optional[str] = None, limit: Optional[int] = None,
                   offset: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        查询数据
        
        Args:
            table_name: 表名
            columns: 要查询的列名列表，None表示查询所有列
            where: WHERE条件
            params: WHERE条件的参数
            order_by: 排序条件
            limit: 限制返回行数
            offset: 偏移量
            
        Returns:
            查询结果列表
        """
        # 构建SQL
        columns_sql = ', '.join(columns) if columns else '*'
        sql = f"SELECT {columns_sql} FROM {table_name}"
        
        if where:
            sql += f" WHERE {where}"
        
        if order_by:
            sql += f" ORDER BY {order_by}"
        
        if limit:
            sql += f" LIMIT {limit}"
        
        if offset:
            sql += f" OFFSET {offset}"
        
        try:
            result = self.execute_sql(sql, params, fetch_result=True)
            return [dict(row) for row in result] if result else []
            
        except sqlite3.Error as e:
            self.logger.error(f"查询表 {table_name} 失败: {e}")
            raise
    
    def update_data(self, table_name: str, data: Dict[str, Any], 
                   where: str, params: Optional[Tuple] = None) -> int:
        """
        更新数据
        
        Args:
            table_name: 表名
            data: 要更新的数据字典
            where: WHERE条件
            params: WHERE条件的参数
            
        Returns:
            更新的行数
        """
        if not data:
            return 0
        
        # 构建SET子句
        set_clause = ', '.join([f'{col} = ?' for col in data.keys()])
        sql = f"UPDATE {table_name} SET {set_clause} WHERE {where}"
        
        # 合并参数
        update_params = list(data.values())
        if params:
            update_params.extend(params)
        
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(sql, update_params)
                conn.commit()

                affected_rows = cursor.rowcount
                cursor.close()

                self.logger.info(f"更新表 {table_name}，影响行数: {affected_rows}")
                return affected_rows

        except sqlite3.Error as e:
            self.logger.error(f"更新表 {table_name} 失败: {e}")
            raise
    
    def delete_data(self, table_name: str, where: str, params: Optional[Tuple] = None) -> int:
        """
        删除数据
        
        Args:
            table_name: 表名
            where: WHERE条件
            params: WHERE条件的参数
            
        Returns:
            删除的行数
        """
        sql = f"DELETE FROM {table_name} WHERE {where}"
        
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                if params:
                    cursor.execute(sql, params)
                else:
                    cursor.execute(sql)
                conn.commit()

                affected_rows = cursor.rowcount
                cursor.close()

                self.logger.info(f"从表 {table_name} 删除 {affected_rows} 行")
                return affected_rows

        except sqlite3.Error as e:
            self.logger.error(f"删除表 {table_name} 数据失败: {e}")
            raise
    
    def count_rows(self, table_name: str, where: Optional[str] = None, 
                  params: Optional[Tuple] = None) -> int:
        """
        统计行数
        
        Args:
            table_name: 表名
            where: WHERE条件
            params: WHERE条件的参数
            
        Returns:
            行数
        """
        sql = f"SELECT COUNT(*) FROM {table_name}"
        if where:
            sql += f" WHERE {where}"
        
        result = self.execute_sql(sql, params, fetch_result=True)
        return result[0][0] if result else 0
    
    def load_json_data(self, table_name: str, json_file: str, 
                      key_mapping: Optional[Dict[str, str]] = None,
                      replace: bool = False) -> int:
        """
        从JSON文件加载数据到数据库
        
        Args:
            table_name: 表名
            json_file: JSON文件路径
            key_mapping: 键名映射字典，用于将JSON键名映射到数据库列名
            replace: 是否使用REPLACE INTO
            
        Returns:
            加载的记录数
        """
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 如果data不是列表，转换为列表
            if not isinstance(data, list):
                data = [data]
            
            # 应用键名映射
            if key_mapping:
                mapped_data = []
                for record in data:
                    mapped_record = {}
                    for json_key, db_key in key_mapping.items():
                        if json_key in record:
                            mapped_record[db_key] = record[json_key]
                    mapped_data.append(mapped_record)
                data = mapped_data
            
            # 插入数据
            return self.insert_data(table_name, data, replace=replace)
            
        except (FileNotFoundError, json.JSONDecodeError) as e:
            self.logger.error(f"加载JSON文件 {json_file} 失败: {e}")
            raise
    
    def export_to_json(self, table_name: str, json_file: str, 
                      where: Optional[str] = None, params: Optional[Tuple] = None) -> int:
        """
        将数据导出到JSON文件
        
        Args:
            table_name: 表名
            json_file: JSON文件路径
            where: WHERE条件
            params: WHERE条件的参数
            
        Returns:
            导出的记录数
        """
        try:
            data = self.select_data(table_name, where=where, params=params)
            
            # 确保目录存在
            Path(json_file).parent.mkdir(parents=True, exist_ok=True)
            
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
            
            self.logger.info(f"导出 {len(data)} 条记录到 {json_file}")
            return len(data)
            
        except Exception as e:
            self.logger.error(f"导出数据到 {json_file} 失败: {e}")
            raise
    
    def backup_database(self, backup_path: str) -> bool:
        """
        备份数据库
        
        Args:
            backup_path: 备份文件路径
            
        Returns:
            是否备份成功
        """
        try:
            # 确保备份目录存在
            Path(backup_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 创建备份连接
            backup_conn = sqlite3.connect(backup_path)
            
            # 执行备份
            conn = self.get_connection()
            conn.backup(backup_conn)
            
            backup_conn.close()
            
            self.logger.info(f"数据库备份成功: {backup_path}")
            return True
            
        except sqlite3.Error as e:
            self.logger.error(f"数据库备份失败: {e}")
            return False
    
    def restore_database(self, backup_path: str) -> bool:
        """
        从备份恢复数据库
        
        Args:
            backup_path: 备份文件路径
            
        Returns:
            是否恢复成功
        """
        try:
            if not os.path.exists(backup_path):
                raise FileNotFoundError(f"备份文件不存在: {backup_path}")
            
            # 关闭当前连接
            self.close()
            
            # 复制备份文件
            import shutil
            shutil.copy2(backup_path, self.db_path)
            
            # 重新连接
            self.connect()
            
            self.logger.info(f"数据库恢复成功: {backup_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"数据库恢复失败: {e}")
            return False
    
    def get_database_info(self) -> Dict[str, Any]:
        """
        获取数据库信息
        
        Returns:
            数据库信息字典
        """
        info = {
            'database_path': str(self.db_path),
            'database_size': os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0,
            'tables': [],
            'total_records': 0
        }
        
        try:
            # 获取所有表
            tables_result = self.execute_sql(
                "SELECT name FROM sqlite_master WHERE type='table'", 
                fetch_result=True
            )
            
            if tables_result:
                for table_row in tables_result:
                    table_name = table_row[0]
                    table_info = {
                        'name': table_name,
                        'columns': self.get_table_info(table_name),
                        'row_count': self.count_rows(table_name)
                    }
                    info['tables'].append(table_info)
                    info['total_records'] += table_info['row_count']
            
            return info
            
        except sqlite3.Error as e:
            self.logger.error(f"获取数据库信息失败: {e}")
            return info
    
    def vacuum(self) -> bool:
        """
        清理数据库，回收空间
        
        Returns:
            是否清理成功
        """
        try:
            self.execute_sql("VACUUM")
            self.logger.info("数据库清理完成")
            return True
        except sqlite3.Error as e:
            self.logger.error(f"数据库清理失败: {e}")
            return False


# 便捷函数
def create_database(db_path: str, tables_config: Dict[str, Dict[str, str]]) -> SQLiteDBProcessor:
    """
    创建数据库和表的便捷函数
    
    Args:
        db_path: 数据库路径
        tables_config: 表配置字典，格式为 {表名: {列名: 列定义}}
        
    Returns:
        数据库处理器实例
        
    Example:
        tables_config = {
            'users': {
                'id': 'INTEGER PRIMARY KEY AUTOINCREMENT',
                'name': 'TEXT NOT NULL',
                'email': 'TEXT UNIQUE'
            },
            'posts': {
                'id': 'INTEGER PRIMARY KEY AUTOINCREMENT',
                'title': 'TEXT NOT NULL',
                'content': 'TEXT',
                'user_id': 'INTEGER REFERENCES users(id)'
            }
        }
    """
    db = SQLiteDBProcessor(db_path)
    
    with db:
        for table_name, columns in tables_config.items():
            db.create_table(table_name, columns)
    
    return db