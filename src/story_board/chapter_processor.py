# -*- coding: utf-8 -*-
"""
@Time ： 2025/7/12 00:31
@Auth ： xiaolongtuan
@File ：chapter_processor.py
"""
import time
import os
import json
import logging
import asyncio
from typing import List, Dict, Any

# 导入现有的图像生成组件
from src.story_board.aigc_component import (
    generate_scene_info, generate_character_info, generate_image_description,
    generate_image_prompt, image_regening, image_regening_for_cover,
    generate_cover_image_description,
    generate_costume_design, gen_video_prompt, generate_volcengine_image,
    load_system_prompt
)
from src.story_board.cost_tracker import CostTracker
from src.story_board.violation_retry_manager import ViolationRetryManager

from src.story_board.aigc_component import call_volcengine_video_api
from src.story_board.sqlite_db_process import SQLiteDBProcessor
from src.story_board.storyboard_data import StoryboardData, Character
from .tos_processor import TOSProcessor
from src.image_mosaic.image_detector import ImageDetector

# 设置日志
logger = logging.getLogger(__name__)


class ChapterProcessor:
    """章节处理器类，负责处理单个章节的分镜生成"""

    def __init__(self, chapter: int, chapter_dir: str, novel_output_dir: str, novel_storyboard_processor):
        self.chapter = chapter
        self.chapter_dir = chapter_dir
        self.chapter_name = os.path.basename(chapter_dir)
        self.source_storyboard_path = os.path.join(chapter_dir, f"chapter{chapter}_story_boards.json")

        self.story_output_dir = os.path.join(novel_output_dir, self.chapter_name)
        os.makedirs(self.story_output_dir, exist_ok=True)

        self.scene_info_path = os.path.join(self.story_output_dir, 'scene_info.json')
        self.character_info_path = os.path.join(self.story_output_dir, 'character_info.json')

        self.output_storyboards_path = os.path.join(self.story_output_dir, 'storyboards.json')
        self.novel_storyboard_processor = novel_storyboard_processor
        # 创建独立的数据库处理器实例以避免连接冲突
        self.db_processor = SQLiteDBProcessor(
            db_path=novel_storyboard_processor.db_processor.db_path,
            max_connections=5  # 每个处理器使用较少的连接
        )
        self.storyboard_data = StoryboardData()
        self.tos_processor: TOSProcessor = novel_storyboard_processor.tos_processor
        self.mosaic_processor = novel_storyboard_processor.mosaic_processor

        # 初始化成本统计器
        novel_name = novel_storyboard_processor.novel_name
        self.cost_tracker = CostTracker(novel_name, novel_storyboard_processor.novel_output_dir)

        # 初始化违规重试管理器
        self.violation_retry_manager = ViolationRetryManager(self.story_output_dir, max_retries=2)

        # 初始化图像违规检测器
        try:
            self.image_detector = ImageDetector()
            logger.info("图像违规检测器初始化成功")
        except Exception as e:
            logger.warning(f"图像违规检测器初始化失败: {e}")
            self.image_detector = None

        # 添加分镜级别的信号量，控制图片下载并发
        self.segment_semaphore = asyncio.Semaphore(2)  # 每个章节最多同时处理2个分镜

    async def check_image_violation(self, image_path_or_url: str, current_prompt:str) -> Dict[str, Any]:
        """检查图像是否违规

        Args:
            image_path_or_url: 图像路径或URL

        Returns:
            Dict: 检测结果，包含result和violation_points字段
        """
        if self.image_detector is None:
            logger.warning("图像违规检测器未初始化，跳过检测")
            return {
                "result": False,
                "violation_points": [],
                "error": "检测器未初始化"
            }

        try:
            result = await self.image_detector.get_violation_details(image_path_or_url, current_prompt)

            if result.get('result', False):
                logger.warning(f"检测到违规图像: {image_path_or_url}")
                logger.warning(f"违规点: {result.get('violation_points', [])}")
            else:
                logger.info(f"图像检测通过: {image_path_or_url}")

            return result

        except Exception as e:
            logger.error(f"图像违规检测失败: {e}")
            return {
                "result": False,
                "violation_points": [],
                "error": str(e)
            }

    def apply_mosaic_to_image_url(self, image_url: str, apply_chest: bool = True,
                                  apply_eyes: bool = False) -> str:
        """对图片URL进行打码处理

        Args:
            image_url: 图片URL

        Returns:
            str: 本地打码后的图片路径
        """
        import tempfile
        import requests
        import cv2

        try:
            # 下载图片到临时文件
            response = requests.get(image_url, timeout=30)
            response.raise_for_status()

            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as temp_file:
                temp_file.write(response.content)
                temp_input_path = temp_file.name

            # 读取图片
            image = cv2.imread(temp_input_path)
            if image is None:
                raise ValueError("无法读取下载的图片")

            # 应用马赛克
            processed_image = self.mosaic_processor.process_image(image, apply_chest=apply_chest, apply_eyes=apply_eyes)

            # 保存处理后的图片
            with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as output_file:
                output_path = output_file.name

            success = cv2.imwrite(output_path, processed_image)
            if not success:
                raise ValueError("保存处理后的图片失败")

            # 清理临时输入文件
            if os.path.exists(temp_input_path):
                os.remove(temp_input_path)

            logger.info(f"图片打码完成: {image_url} -> {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"图片打码失败: {e}")
            raise

    def apply_mosaic_to_video_url(self, video_url: str, apply_chest: bool = True,
                                  apply_eyes: bool = False) -> str:
        """对视频URL进行打码处理

        Args:
            video_url: 视频URL

        Returns:
            str: 本地打码后的视频路径
        """
        import tempfile
        import requests

        try:
            # 下载视频到临时文件
            response = requests.get(video_url, timeout=60)
            response.raise_for_status()

            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_file:
                temp_file.write(response.content)
                temp_input_path = temp_file.name

            # 创建输出文件路径
            with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as output_file:
                output_path = output_file.name

            # 使用马赛克处理器处理视频
            success = self.mosaic_processor.process_video(temp_input_path, output_path, apply_chest=apply_chest,
                                                          apply_eyes=apply_eyes)

            if not success:
                raise ValueError("视频打码处理失败")

            # 清理临时输入文件
            if os.path.exists(temp_input_path):
                os.remove(temp_input_path)

            logger.info(f"视频打码完成: {video_url} -> {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"视频打码失败: {e}")
            raise

    def read_storyboards_list(self, storyboards_json_path: str) -> List[Dict[str, Any]]:
        """读取拆分的小说片段"""
        with open(storyboards_json_path, "r", encoding="utf-8") as f:
            storyboards_list = json.load(f)['story_boards']
            storyboards_list.sort(key=lambda x: int(x["story_board_id"]))
        return storyboards_list

    def collect_novel_content(self, storyboards: List[Dict[str, Any]]) -> str:
        """收集所有小说内容用于生成场景和角色设定"""
        return "。".join([storyboard["story_board"] for storyboard in storyboards])

    def get_character_info_from_db(self) -> List[Character]:
        """从数据库获取角色信息"""
        try:
            # 使用连接池获取连接
            with self.db_processor.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    'SELECT name, gender, age, clothes, hairstyle, figure, identity, face, personality, from_chapter FROM character_info'
                )
                results = cursor.fetchall()

                if results:
                    character_info = []
                    for result in results:
                        character_info.append({
                            'name': result['name'] or '',
                            'gender': result['gender'] or '',
                            'age': result['age'] or '',
                            'clothes': result['clothes'] or '',
                            'hairstyle': result['hairstyle'] or '',
                            'figure': result['figure'] or '',
                            'identity': result['identity'] or '',
                            'face': result['face'] or '',
                            'personality': result['personality'] or '',
                        })
                    logging.info(f"从数据库获取角色信息，共 {len(character_info)} 个角色")
                    return character_info
                else:
                    logging.info("数据库中没有角色信息")
                    return []

        except Exception as e:
            logging.error(f"获取角色信息失败: {e}")
            return []

    async def get_scene_info(self, novel_content):
        """从数据库获取场景信息"""
        if os.path.exists(self.scene_info_path):
            self.get_scene_info_from_json()
        else:
            # 生成场景和角色信息
            scene_info = await generate_scene_info(novel_content, self.cost_tracker, self.chapter)
            character_info = await generate_character_info(novel_content, self.cost_tracker, self.chapter)
            self.storyboard_data.scenes = scene_info['scene_list']
            self.storyboard_data.era = scene_info['era']
            self.storyboard_data.region = scene_info['region']
            self.storyboard_data.draw_style = scene_info['draw_style']

            # 生成服装设计
            costume_design = await generate_costume_design(self.storyboard_data.era, self.storyboard_data.region,
                                                           character_info)
            for character in character_info:
                for costume in costume_design:
                    if character["name"] == costume["name"]:
                        character["clothes"] = costume["costume"]

            # 使用事务处理角色信息，避免并发问题
            self._process_characters_with_transaction(character_info)

            logging.info(f"当前章节共 {len(self.storyboard_data.scenes)} 个场景")
            logging.info(f"当前章节共 {len(character_info)} 个角色")

        return

    def _process_characters_with_transaction(self, character_info):
        """在事务中处理角色信息，确保并发安全"""
        try:
            with self.db_processor.get_connection() as conn:
                # 开始事务
                conn.execute('BEGIN IMMEDIATE')  # 使用IMMEDIATE锁，防止并发写入

                try:
                    cursor = conn.cursor()

                    # 一次性获取所有现有角色名称
                    cursor.execute('SELECT name FROM character_info')
                    existing_characters = {row[0] for row in cursor.fetchall() if row[0]}

                    new_characters_to_insert = []

                    # 定义Character类支持的字段
                    character_fields = {
                        'name', 'gender', 'age', 'clothes', 'hairstyle',
                        'figure', 'identity', 'other', 'face', 'personality', 'from_chapter', 'action', 'expression'
                    }

                    # 处理每个角色
                    for character in character_info:
                        char_name = character['name']

                        if char_name in existing_characters:
                            # 角色已存在，获取完整信息
                            cursor.execute(
                                'SELECT * FROM character_info WHERE name = ?',
                                (char_name,)
                            )
                            result = cursor.fetchone()
                            if result:
                                # 将数据库结果转换为字典并过滤不支持的字段
                                db_character = dict(zip([col[0] for col in cursor.description], result))
                                # 只保留Character类支持的字段
                                filtered_character = {k: v for k, v in db_character.items() if k in character_fields}
                                character.update(filtered_character)
                        else:
                            # 新角色，准备插入
                            new_characters_to_insert.append({
                                'name': char_name,
                                'gender': character.get('gender', ''),
                                'age': character.get('age', ''),
                                'clothes': character.get('clothes', ''),
                                'hairstyle': character.get('hairstyle', ''),
                                'figure': character.get('figure', ''),
                                'identity': character.get('identity', ''),
                                'face': character.get('face', ''),
                                'personality': character.get('personality', ''),
                                'from_chapter': self.chapter
                            })
                            # 添加到已存在集合中，防止重复
                            existing_characters.add(char_name)

                        # 过滤character字典，只保留Character类支持的字段
                        filtered_character_data = {k: v for k, v in character.items() if k in character_fields}

                        # 添加到storyboard_data
                        self.storyboard_data.add_character(Character.from_dict(filtered_character_data))

                    # 批量插入新角色
                    if new_characters_to_insert:
                        for char_data in new_characters_to_insert:
                            cursor.execute(
                                '''INSERT INTO character_info
                                   (name, gender, age, clothes, hairstyle, figure, identity, face, personality, from_chapter)
                                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)''',
                                (char_data['name'], char_data['gender'], char_data['age'],
                                 char_data['clothes'], char_data['hairstyle'], char_data['figure'],
                                 char_data['identity'], char_data['face'], char_data['personality'], char_data['from_chapter'])
                            )

                        logging.info(
                            f"第{self.chapter}章新增角色信息已保存到数据库，共 {len(new_characters_to_insert)} 条记录")
                    else:
                        logging.info(f"第{self.chapter}章没有新的角色需要添加到数据库")

                    # 提交事务
                    conn.commit()

                except Exception as e:
                    # 回滚事务
                    conn.rollback()
                    logging.error(f"处理第{self.chapter}章角色信息时发生错误: {e}")
                    raise

        except Exception as e:
            logging.error(f"获取数据库连接失败: {e}")
            raise

    def get_scene_info_from_json(self):
        """从数据库获取场景信息"""
        try:
            # 判断是否存在self.scene_info_path
            if not os.path.exists(self.scene_info_path):
                logging.info(f"场景信息文件不存在: {self.scene_info_path}")
                return []
            else:
                with open(self.scene_info_path, "r", encoding="utf-8") as f:
                    scene_data = json.load(f)
                    self.storyboard_data = StoryboardData.from_dict(scene_data)
                    logging.info(f"当前章节共 {len(self.storyboard_data.scenes)} 个场景")
                    return
        except Exception as e:
            logging.error(f"获取场景信息失败: {e}")
            return

    def save_scene_info_to_json(self):
        """保存场景信息到数据库"""
        try:
            with open(self.scene_info_path, "w", encoding="utf-8") as f:
                json.dump(self.storyboard_data.to_dict(), f, ensure_ascii=False, indent=4)
                logging.info(f"场景信息已保存到 {self.scene_info_path}")
        except Exception as e:
            logging.error(f"保存场景信息失败: {e}")
            raise

    def save_storyboard_result_to_json(self, results: List[Dict[str, Any]]):
        """保存分镜结果到JSON文件"""
        try:
            # 保存为JSON文件
            with open(self.output_storyboards_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)

            logging.info(f"分镜结果已保存到文件：{self.output_storyboards_path}")
            return

        except Exception as e:
            logging.error(f"保存分镜结果到JSON文件失败: {e}")
            raise Exception("保存分镜结果到JSON文件失败")

    async def process_chapter(self):
        """处理单个章节（异步版本）"""
        logging.info(f"🎬 开始处理第{self.chapter}章")
        # 读取小说片段
        storyboards_list = self.read_storyboards_list(self.source_storyboard_path)
        if not storyboards_list:
            raise ValueError(f"第{self.chapter}章未找到有效的分镜数据")

        # 检查音频URL并提供详细的错误信息
        missing_audio_segments = []
        for i, segment in enumerate(storyboards_list):
            if not segment.get("audio_oss_url"):
                missing_audio_segments.append(i + 1)

        if missing_audio_segments:
            error_msg = f"第{self.chapter}章第{missing_audio_segments}个分镜缺少音频URL，请检查step1阶段的音频生成是否完整"
            logging.error(error_msg)
            logging.error(f"缺少音频的分镜详情: {[storyboards_list[i-1].get('story_board_id', f'segment_{i}') for i in missing_audio_segments]}")
            raise ValueError(error_msg)

        # 收集所有内容用于生成场景和角色设定
        novel_content = self.collect_novel_content(storyboards_list)

        # 生成该章节的场景
        await self.get_scene_info(novel_content)

        # 检查数据库中是否已绘画风格:
        draw_style = self.novel_storyboard_processor.get_novel_draw_style()
        if draw_style:
            self.storyboard_data.draw_style = draw_style
        else:
            self.novel_storyboard_processor.set_novel_draw_style(self.storyboard_data.draw_style)

        self.save_scene_info_to_json()

        # 处理每个片段
        results = []
        pre_image_description = "无"
        for i, segment in enumerate(storyboards_list):
            # 生成图像描述
            if i < 0:  # 开头片段使用封面图像描述
                image_description = await generate_cover_image_description(
                    novel_content, segment["story_board"], self.storyboard_data
                )
                image_info = generate_image_prompt(image_description, self.storyboard_data)
                image_prompt = await image_regening_for_cover(image_info)
            else:
                image_description = await generate_image_description(
                    novel_content, segment["story_board"], self.storyboard_data, pre_image_description,
                    self.cost_tracker, self.chapter, segment.get("story_board_id", f"segment_{i}")
                )
                image_info = generate_image_prompt(image_description, self.storyboard_data)
                image_prompt = await image_regening(image_info)
            pre_image_description = image_info

            # 添加绘图风格
            image_global_prompt = load_system_prompt("src/prompts/image_global_prompt.txt")
            image_global_prompt = image_global_prompt.replace("{image_prompt}", image_prompt)
            image_global_prompt = image_global_prompt.replace("{draw_style}", self.storyboard_data.draw_style)

            logging.info(f"📝 第{self.chapter}章 - 分镜 {i + 1}/{len(storyboards_list)}\n: {image_global_prompt}")

            # 使用违规检测重试机制生成图像
            segment_id = segment.get("story_board_id", f"segment_{i}")
            story_text = segment.get("story_board", "")

            # 创建图像生成函数的包装器
            async def generate_image_wrapper(prompt, **kwargs):
                return await generate_volcengine_image(
                    prompt, cost_tracker=self.cost_tracker,
                    chapter=self.chapter, segment_id=segment_id, **kwargs
                )

            # 使用违规重试管理器
            image_url, passed_check, final_violation_result = await self.violation_retry_manager.retry_image_generation_with_violation_check(
                tos_processor=self.tos_processor,
                generate_image_func=generate_image_wrapper,
                check_violation_func=self.check_image_violation,
                initial_prompt=image_global_prompt,
                chapter=self.chapter,
                segment_id=segment_id,
                story_text=story_text
            )

            if i < 2: # 要生成视频
                # 视频生成：即使违规检测未通过也不打码，直接使用图像
                video_prompt = gen_video_prompt(
                    self.storyboard_data
                )
                video_ur = await call_volcengine_video_api(
                    video_prompt, image_url, cost_tracker=self.cost_tracker,
                    chapter=self.chapter, segment_id=segment_id
                )

                if not passed_check:
                    logging.warning(f"⚠️ 视频生成使用了未通过违规检测的图像，但不进行打码处理")

            else: # 静态图片，根据检测结果决定是否打码
                video_ur = ""

                if passed_check:
                    logging.info(f"✅ 静态图片通过违规检测")
                else:
                    # 未通过检测，需要打码处理
                    violation_points = final_violation_result.get("violation_points", [])
                    logging.warning(f"❌ 静态图片未通过违规检测，违规点: {violation_points}，进行打码处理")

                    # 应用打码技术
                    masked_image_path = self.apply_mosaic_to_image_url(image_url, apply_chest=True)
                    # 将本地图像上传至tos，使用信号量控制并发
                    async with self.segment_semaphore:
                        image_url = self.tos_processor.upload_file(
                            masked_image_path,
                            f"images/masked_image_{int(time.time())}.jpg"
                        )
                    # 清理临时文件
                    if os.path.exists(masked_image_path):
                        os.remove(masked_image_path)
            # 保存结果
            result = {
                "chapter": self.chapter,
                "story_board": segment["story_board"],
                "characters": [char.to_dict() for char in self.storyboard_data.characters],
                "scene": image_description.get("scene", ""),
                "image_prompt": image_global_prompt,
                "image_url": image_url,
                "audio_url": segment["audio_oss_url"],
                "audio_duration": segment['audio_duration'],
                "video_url": video_ur,
                "story_board_id": segment["story_board_id"],
                "subtitle": segment["short_subtitles"],
                "keywords": segment["all_keywords"]
            }
            results.append(result)
        # 保存所有结果到JSON文件
        self.save_storyboard_result_to_json(results)
        # todo 输出一个剪辑视屏素材配置

        # 保存成本记录
        self.cost_tracker.save_records()

        # 打印成本报告
        chapter_cost = self.cost_tracker.get_chapter_cost(self.chapter)
        logging.info(f"💰 第{self.chapter}章API调用成本: {chapter_cost:.4f} 元")

        # 打印违规检测统计报告
        self.violation_retry_manager.print_violation_report()

        logging.info(f"✅ 第{self.chapter}章处理完成，共 {len(results)} 个分镜")
        return results

    def process_audios(self, temp_audio_file):
        """处理单个音频文件"""
        try:
            public_url = self.tos_processor.upload_file(
                temp_audio_file,
                f"audios/audio_{int(time.time())}.mp3"
            )
            return public_url
        except Exception:
            return temp_audio_file  # 失败时使用原路径