# 小说分镜画面生成工程化脚本

这个模块提供了一个工程化的解决方案，用于将小说文本转换为分镜画面，支持数据库存储和批量处理。

## 功能特性

- 🎬 **分镜画面生成**: 将小说文本转换为视觉分镜画面
- 🗄️ **数据库存储**: 使用SQLite数据库存储角色信息和场景信息
- 📄 **JSON输出**: 分镜结果保存为独立的JSON文件，便于处理和集成
- 📚 **批量处理**: 支持处理整部小说的所有章节
- 🎨 **角色一致性**: 为每个角色生成一致的外观描述和服饰设计
- 🏞️ **场景管理**: 智能识别和管理小说中的场景信息
- 📝 **详细日志**: 完整的处理日志记录
- 🔄 **增量处理**: 支持增量处理，避免重复生成

## 目录结构

```
src/story_board/
├── novel_storyboard_processor.py  # 主要的工程化脚本
├── example_usage.py               # 使用示例
├── README_STORYBOARD.md           # 本文档
├── image_generation_component.py  # 图像生成组件（现有）
├── process_split_novel_low_cost.py # 原始处理脚本（现有）
└── ...
```

## 输出目录结构

```
output/story_board_output/小说名/
├── 小说名.db                      # SQLite数据库文件（角色和场景信息）
├── process_log.log                # 处理日志
├── 第1章/
│   ├── images/                    # 分镜图片目录
│   │   ├── chapter_1_sentence_1.jpg
│   │   ├── chapter_1_sentence_2.jpg
│   │   └── ...
│   ├── storyboard_results/        # 分镜结果JSON文件目录
│   │   ├── chapter_1_sentence_1.json
│   │   ├── chapter_1_sentence_2.json
│   │   └── ...
│   └── results.json              # 分镜结果汇总（JSON格式，兼容性）
├── 第2章/
│   ├── images/
│   ├── storyboard_results/
│   └── results.json
└── ...
```

## 数据库结构

### character_info 表（角色信息）
| 字段 | 类型 | 说明 |
|------|------|------|
| id | INTEGER | 主键 |
| chapter | INTEGER | 章节号 |
| name | TEXT | 角色姓名 |
| gender | TEXT | 性别 |
| age | TEXT | 年龄 |
| clothes | TEXT | 服饰描述 |
| hairstyle | TEXT | 发型 |
| figure | TEXT | 体型 |
| identity | TEXT | 身份 |
| other | TEXT | 其他特征 |
| created_at | TIMESTAMP | 创建时间 |

### scene_info 表（场景信息）
| 字段 | 类型 | 说明 |
|------|------|------|
| id | INTEGER | 主键 |
| chapter | INTEGER | 章节号 |
| draw_style | TEXT | 绘画风格 |
| scene_list | TEXT | 场景列表（JSON格式） |
| created_at | TIMESTAMP | 创建时间 |

### 分镜结果文件（JSON格式）
分镜结果不再存储在数据库中，而是保存为独立的JSON文件，每个文本片段对应一个JSON文件：

**文件命名格式**: `chapter_{章节号}_sentence_{句子序号}.json`

**JSON文件结构**:
```json
{
  "chapter": 1,
  "sentence": 1,
  "original_segment": "原始文本片段",
  "characters": ["角色列表"],
  "scene": "场景描述",
  "image_prompt": "图像提示词",
  "image_url": "图像URL",
  "image_path": "本地图像路径",
  "video_path": "音频文件路径"
}
```

## 使用方法

### 1. 基本使用

```bash
# 处理整部小说
python novel_storyboard_processor.py --novel_dir novel/大唐太子

# 处理指定章节
python novel_storyboard_processor.py --novel_dir novel/大唐太子 --chapter 3

# 指定输出目录
python novel_storyboard_processor.py --novel_dir novel/大唐太子 --output_dir custom_output
```

### 2. 编程接口使用

```python
from novel_storyboard_processor import NovelStoryboardProcessor

# 创建处理器
processor = NovelStoryboardProcessor("novel/大唐太子")

# 处理单个章节
processor.process_chapter(
    chapter=3,
    text_dir="novel/大唐太子/第3章军情紧急_text_parts",
    audio_dir="novel/大唐太子/第3章军情紧急_audio_parts"
)

# 处理整部小说
processor.process_novel("novel/大唐太子", "novel/大唐太子")

# 查询数据库
character_info = processor.get_character_info_from_db(3)
scene_info = processor.get_scene_info_from_db(3)
```

### 3. 数据库查询示例

```python
import sqlite3
import json
import os

# 连接数据库
conn = sqlite3.connect("output/story_board_output/大唐太子/大唐太子.db")
cursor = conn.cursor()

# 查询所有角色
cursor.execute("SELECT DISTINCT name, identity FROM character_info")
characters = cursor.fetchall()

# 查询特定章节的场景信息
cursor.execute("SELECT draw_style, scene_list FROM scene_info WHERE chapter = ?", (3,))
scene_info = cursor.fetchone()

conn.close()

# 读取分镜结果JSON文件
storyboard_dir = "output/story_board_output/大唐太子/第3章/storyboard_results"
if os.path.exists(storyboard_dir):
    for filename in sorted(os.listdir(storyboard_dir)):
        if filename.endswith('.json'):
            with open(os.path.join(storyboard_dir, filename), 'r', encoding='utf-8') as f:
                result = json.load(f)
                print(f"第{result['chapter']}章第{result['sentence']}句: {result['original_segment'][:50]}...")
```

## 输入文件要求

### 文本文件格式
小说文本应该按以下格式组织：
```
novel/大唐太子/
├── 第1章标题_text_parts/
│   ├── 第1章标题_part_1.txt
│   ├── 第1章标题_part_2.txt
│   └── ...
├── 第2章标题_text_parts/
│   └── ...
└── ...
```

### 音频文件格式（可选）
```
novel/大唐太子/
├── 第1章标题_audio_parts/
│   ├── 第1章标题_part_1.wav
│   ├── 第1章标题_part_2.wav
│   └── ...
└── ...
```

## 环境变量配置

在使用前，请确保设置了以下环境变量：

```bash
# 豆包API密钥（用于文本生成）
export ARK_API_KEY="your_ark_api_key"

# 火山引擎API密钥（用于图像生成）
export VOLCENGINE_API_KEY="your_volcengine_api_key"
export VOLCENGINE_API_SECRET="your_volcengine_api_secret"
```

## 工作流程

1. **初始化**: 创建输出目录和SQLite数据库
2. **读取文本**: 扫描小说目录，读取文本片段
3. **生成设定**: 为每章生成角色信息和场景信息
4. **服饰设计**: 根据时代背景为角色设计合适的服饰
5. **分镜生成**: 为每个文本片段生成对应的图像描述和提示词
6. **图像生成**: 调用API生成实际图像
7. **数据存储**: 将所有结果存储到数据库和文件系统

## 特性说明

### 增量处理
- 如果数据库中已存在角色或场景信息，将直接使用，避免重复生成
- 支持中断后继续处理

### 角色一致性
- 为每个角色生成详细的外观描述
- 根据小说背景自动设计符合时代的服饰
- 在整部小说中保持角色外观的一致性

### 场景管理
- 自动识别小说中的场景类型
- 生成统一的绘画风格描述
- 支持场景列表管理

### 日志记录
- 详细的处理日志，包括每个步骤的执行情况
- 错误信息和调试信息
- 同时输出到文件和控制台

## 故障排除

### 常见问题

1. **API密钥错误**
   - 检查环境变量是否正确设置
   - 确认API密钥有效且有足够的配额

2. **文件路径问题**
   - 确保小说目录结构符合要求
   - 检查文件名格式是否正确

3. **数据库锁定**
   - 确保没有其他进程在使用数据库
   - 检查数据库文件权限

4. **内存不足**
   - 对于大型小说，考虑分章节处理
   - 调整图像生成的批次大小

### 日志分析
查看 `process_log.log` 文件获取详细的执行信息：
```bash
tail -f output/story_board_output/大唐太子/process_log.log
```

## 扩展开发

### 添加新的图像生成器
```python
class CustomImageGenerator:
    def generate_image(self, prompt: str) -> str:
        # 实现自定义图像生成逻辑
        pass

# 在 NovelStoryboardProcessor 中使用
processor.image_generator = CustomImageGenerator()
```

### 自定义数据库操作
```python
# 继承并扩展处理器
class ExtendedProcessor(NovelStoryboardProcessor):
    def custom_query(self, sql: str, params: tuple):
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute(sql, params)
        result = cursor.fetchall()
        conn.close()
        return result
```

## 性能优化建议

1. **批量处理**: 一次处理多个文本片段
2. **缓存机制**: 缓存重复的API调用结果
3. **并行处理**: 对于独立的章节可以并行处理
4. **图像压缩**: 对生成的图像进行适当压缩

## 版本历史

- v1.0.0: 初始版本，支持基本的分镜画面生成和数据库存储

## 贡献指南

欢迎提交Issue和Pull Request来改进这个工具。在提交代码前，请确保：

1. 代码符合PEP 8规范
2. 添加适当的注释和文档
3. 包含必要的测试用例
4. 更新相关文档

## 许可证

本项目采用MIT许可证，详见LICENSE文件。