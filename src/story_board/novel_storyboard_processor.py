#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小说分镜画面生成工程化脚本

功能：
1. 输入小说文件夹路径
2. 为每一章生成分镜画面
3. 使用SQLite数据库存储角色和场景信息
4. 输出到指定的目录结构

使用方法：
python novel_storyboard_processor.py --novel_dir novel/大唐太子
"""
# 添加项目根目录到Python路径
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import os
import glob
import re
import argparse
import logging
import asyncio
import json
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

from src.story_board.chapter_processor import ChapterProcessor
from src.story_board.sqlite_db_process import SQLiteDBProcessor
from src.story_board.tos_processor import TOSProcessor
from pathlib import Path
from dotenv import load_dotenv
from src.image_mosaic import PersonMosaicProcessor
from src.image_mosaic.config import get_config_by_name

# 加载.env文件中的环境变量
dotenv_path = Path(__file__).parent.parent.parent / '.env'
load_dotenv(dotenv_path=dotenv_path,verbose=True,override=True)

class NovelStoryboardProcessor:
    """小说分镜画面处理器"""

    def __init__(self, novel_dir: str, output_base_dir: str = "output/novel_step2"):
        self.novel_dir = Path(novel_dir)
        self.novel_name = self.novel_dir.name
        self.output_base_dir = Path(output_base_dir)
        self.novel_output_dir = self.output_base_dir / self.novel_name
        self.db_path = self.novel_output_dir / f"novel_setting.db"

        # 创建输出目录
        self.novel_output_dir.mkdir(parents=True, exist_ok=True)
        # 设置日志
        self._setup_logging()
        self.db_processor = None
        self.tos_processor = TOSProcessor()
        # 初始化数据库
        self._init_database()
        self.mosaic_processor = PersonMosaicProcessor(config=get_config_by_name('dynamic'))
        self.chapter_semaphore = asyncio.Semaphore(5)  # 降低并发数，避免网络问题

    def _init_database(self):
        """初始化SQLite数据库,判断是否已经存在数据库以及表（角色信息表、场景信息表）"""
        # 初始化数据库处理器
        self.db_processor = SQLiteDBProcessor(self.db_path)

        try:
            with self.db_processor:
                # 定义角色信息表结构
                character_info_columns = {
                    'id': 'INTEGER PRIMARY KEY AUTOINCREMENT',
                    'name': 'TEXT NOT NULL',
                    'gender': 'TEXT',
                    'age': 'TEXT',
                    'clothes': 'TEXT',
                    'hairstyle': 'TEXT',
                    'figure': 'TEXT',
                    'identity': 'TEXT',
                    'face': 'TEXT',
                    'personality': 'TEXT',
                    'other': 'TEXT',
                    'from_chapter': 'INTEGER NOT NULL',
                    'created_at': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
                    'UNIQUE(name)': ''  # 按姓名唯一约束，支持跨章节复用
                }

                # 定义画风表结构
                draw_style_columns = {
                    'draw_style': 'TEXT'
                }

                # 创建表（如果不存在）
                tables_created = []

                if not self.db_processor.table_exists('character_info'):
                    self.db_processor.create_table('character_info', character_info_columns)
                    tables_created.append('character_info')

                if not self.db_processor.table_exists('draw_style'):
                    self.db_processor.create_table('draw_style', draw_style_columns)
                    tables_created.append('draw_style')

                # 记录创建的表
                if tables_created:
                    logging.info(f"创建了以下数据表: {', '.join(tables_created)}")
                else:
                    logging.info("所有数据表已存在，无需创建")

                # # 获取数据库信息
                # db_info = self.db_processor.get_database_info()
                # logging.info(f"数据库初始化完成: {self.db_path}")
                # logging.info(f"数据库包含 {len(db_info['tables'])} 个表，总记录数: {db_info['total_records']}")

        except Exception as e:
            logging.error(f"数据库初始化失败: {e}")
            raise

    def _setup_logging(self):
        """设置日志配置"""
        log_filename = self.novel_output_dir / "process_log.log"

        # 获取根日志记录器
        logger = logging.getLogger()

        # 清除现有的处理器，避免重复
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        # 配置日志格式
        log_format = '%(asctime)s - %(levelname)s - %(message)s'
        formatter = logging.Formatter(log_format)

        # 添加文件处理器
        file_handler = logging.FileHandler(log_filename, encoding='utf-8')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

        # 添加控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

        # 设置日志级别
        logger.setLevel(logging.INFO)

        logging.info(f"日志文件已创建: {log_filename}")

    def set_novel_draw_style(self, draw_style: str):
        """保存画风信息到数据库"""
        try:
            # 准备数据
            style_data = {
                'draw_style': draw_style,
            }

            # 使用 SQLiteDBProcessor 插入数据
            with self.db_processor:
                # 先清空表中现有数据
                self.db_processor.delete_data('draw_style', '1=1')

                # 插入新的画风数据
                self.db_processor.insert_data('draw_style', [style_data])
                logging.info(f"画风信息已保存到数据库: {draw_style}")

        except Exception as e:
            logging.error(f"保存画风信息失败: {e}")
            raise

    # 读取draw_style值
    def get_novel_draw_style(self):
        """从数据库获取画风信息"""
        try:
            with self.db_processor:
                # 查询画风信息
                results = self.db_processor.select_data(
                    'draw_style',
                    columns=['draw_style'],
                    limit=1
                )
                if results:
                    draw_style = results[0]['draw_style']
                    logging.info(f"从数据库获取画风信息: {draw_style}")
                    return draw_style
                else:
                    logging.info("数据库中未找到画风信息")
                    return None
        except Exception as e:
            logging.error(f"获取画风信息失败: {e}")
            return None

    def _is_chapter_completed(self, chapter_index: int, chapter_dir: str) -> bool:
        """检查章节是否已经处理完成

        Args:
            chapter_index: 章节号
            chapter_dir: 章节目录路径

        Returns:
            True if 章节已完成, False otherwise
        """
        try:
            # 获取章节名称
            chapter_name = os.path.basename(chapter_dir)

            # 构建输出目录路径
            chapter_output_dir = os.path.join(self.novel_output_dir, chapter_name)

            # 构建storyboards.json文件路径
            storyboards_file = os.path.join(chapter_output_dir, 'storyboards.json')

            # 检查输出目录是否存在
            if not os.path.exists(chapter_output_dir):
                return False

            # 检查storyboards.json文件是否存在
            if not os.path.exists(storyboards_file):
                return False

            # 检查文件是否为空或损坏
            try:
                with open(storyboards_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 检查是否包含必要的字段
                    if not isinstance(data, list):
                        return False
                    if len(data) == 0:
                        return False

                    logging.info(f"📋 章节 {chapter_index} 已完成，跳过处理")
                    return True
            except (json.JSONDecodeError, KeyError, TypeError):
                # 文件损坏，需要重新处理
                logging.warning(f"⚠️ 章节 {chapter_index} 的结果文件损坏，将重新处理")
                return False

        except Exception as e:
            logging.warning(f"⚠️ 检查章节 {chapter_index} 完成状态时出错: {e}")
            return False

        return False

    async def _process_single_chapter(self, chapter_info: Tuple[int, str]) -> Tuple[int, bool, str]:
        """处理单个章节的函数，用于异步执行

        Args:
            chapter_info: (章节号, 章节目录路径)

        Returns:
            (章节号, 是否成功, 错误信息)
        """
        index, chapter_dir = chapter_info

        try:
            # 为每个章节创建独立的处理器实例
            chapter_processor = ChapterProcessor(index, chapter_dir, self.novel_output_dir, self)
            await chapter_processor.process_chapter()
            logging.info(f"✅ 章节 {index} 处理完成")
            return index, True, ""
        except Exception as e:
            error_msg = f"❌ 章节 {index} 处理失败: {str(e)}"
            logging.error(error_msg)
            return index, False, error_msg

    def get_user_chapter_selection(self, chapters: List[Tuple[int, str]]) -> List[Tuple[int, str]]:
        """获取用户选择的章节范围

        Args:
            chapters: 所有章节列表 [(章节号, 章节路径), ...]

        Returns:
            用户选择的章节列表
        """
        if not chapters:
            print("❌ 未找到任何章节")
            return []

        # 检查章节完成状态并分类
        completed_chapters = []
        pending_chapters = []

        print("\n🔍 正在检查章节完成状态...")
        for chapter_num, chapter_path in chapters:
            if self._is_chapter_completed(chapter_num, chapter_path):
                completed_chapters.append((chapter_num, chapter_path))
            else:
                pending_chapters.append((chapter_num, chapter_path))

        print("\n" + "="*70)
        print("📚 章节状态总览")
        print("="*70)

        # 显示已完成的章节
        if completed_chapters:
            print(f"✅ 已完成章节 ({len(completed_chapters)}个):")
            for chapter_num, chapter_path in completed_chapters:
                chapter_name = os.path.basename(chapter_path)
                print(f"    第{chapter_num}章 ({chapter_name})")

        # 显示待处理的章节
        if pending_chapters:
            print(f"\n📝 待处理章节 ({len(pending_chapters)}个):")
            for i, (chapter_num, chapter_path) in enumerate(pending_chapters, 1):
                chapter_name = os.path.basename(chapter_path)
                print(f"{i:3d}. 第{chapter_num}章 ({chapter_name})")
        else:
            print("\n🎉 所有章节都已完成！")
            return []

        print("\n" + "="*70)
        print("请选择要处理的章节范围（仅显示待处理章节）:")
        print("格式说明:")
        print("  - 单个章节: 5")
        print("  - 连续章节: 1-10")
        print("  - 多个范围: 1-5,8,10-15")
        print("  - 处理全部待处理章节: all")
        print("="*70)

        while True:
            try:
                user_input = input("请输入章节范围: ").strip()

                if user_input.lower() == 'all':
                    print(f"✅ 选择处理所有 {len(pending_chapters)} 个待处理章节")
                    return pending_chapters

                selected_indices = []

                # 解析用户输入（基于待处理章节列表）
                parts = user_input.split(',')
                for part in parts:
                    part = part.strip()
                    if '-' in part:
                        # 范围输入
                        start, end = part.split('-')
                        start_idx = int(start.strip()) - 1
                        end_idx = int(end.strip()) - 1
                        if 0 <= start_idx <= end_idx < len(pending_chapters):
                            selected_indices.extend(range(start_idx, end_idx + 1))
                        else:
                            raise ValueError(f"章节范围 {part} 超出有效范围 (1-{len(pending_chapters)})")
                    else:
                        # 单个章节
                        idx = int(part) - 1
                        if 0 <= idx < len(pending_chapters):
                            selected_indices.append(idx)
                        else:
                            raise ValueError(f"章节 {part} 超出有效范围 (1-{len(pending_chapters)})")

                # 去重并排序
                selected_indices = sorted(list(set(selected_indices)))

                if not selected_indices:
                    print("❌ 未选择任何章节，请重新输入")
                    continue

                # 获取选中的章节（从待处理章节列表中）
                selected_chapters = [pending_chapters[idx] for idx in selected_indices]

                # 确认选择
                print(f"\n✅ 已选择 {len(selected_chapters)} 个待处理章节:")
                for idx in selected_indices:
                    chapter_num, chapter_path = pending_chapters[idx]
                    chapter_name = os.path.basename(chapter_path)
                    print(f"  {idx + 1}. 第{chapter_num}章 ({chapter_name})")

                confirm = input("\n确认处理这些章节吗？(y/n): ").strip().lower()
                if confirm in ['y', 'yes', '是']:
                    return selected_chapters
                else:
                    print("请重新选择章节范围")

            except ValueError as e:
                print(f"❌ 输入格式错误: {e}")
                print("请按照格式重新输入")
            except KeyboardInterrupt:
                print("\n👋 用户取消操作")
                return []
            except Exception as e:
                print(f"❌ 输入处理错误: {e}")
                print("请重新输入")

    async def process_novel(self, text_base_dir: str, audio_base_dir: Optional[str] = None, interactive: bool = True):
        """处理整部小说的所有章节（异步并发处理）

        Args:
            text_base_dir: 文本基础目录
            audio_base_dir: 音频基础目录
            interactive: 是否启用交互式章节选择
        """
        logging.info(f"开始处理小说: {self.novel_name}")

        # 查找所有章节目录
        chapter_dirs = glob.glob(os.path.join(text_base_dir, "chapter*"))
        chapter_index = []

        for chapter_dir in chapter_dirs:
            # 从目录名中提取章节号
            match = re.search(r'chapter(\d+)_', os.path.basename(chapter_dir))
            if match:
                chapter_index.append((int(match.group(1)), chapter_dir))
        # 按章节号排序
        chapter_index.sort(key=lambda x: x[0])

        if not chapter_index:
            logging.error("未找到任何章节目录")
            return

        # 如果启用交互式选择，让用户选择章节范围
        if interactive:
            selected_chapters = self.get_user_chapter_selection(chapter_index)
            if not selected_chapters:
                logging.info("用户取消操作或未选择章节")
                return
            chapter_index = selected_chapters

        logging.info(f"📚 开始处理 {len(chapter_index)} 个章节（控制并发数）")

        # 使用信号量控制章节并发数，避免图片生成API超限
        # 由于每个章节可能生成多张图片，需要限制同时处理的章节数
        async def process_chapter_with_semaphore(chapter_info):
            async with self.chapter_semaphore:
                return await self._process_single_chapter(chapter_info)

        successful_chapters = []
        failed_chapters = []

        # 创建所有章节处理任务
        tasks = [process_chapter_with_semaphore(chapter_info) for chapter_info in chapter_index]

        # 并发执行所有任务
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        for i, result in enumerate(results):
            chapter_info = chapter_index[i]
            chapter_num = chapter_info[0]

            if isinstance(result, Exception):
                error_msg = f"章节 {chapter_num} 执行异常: {str(result)}"
                logging.error(error_msg)
                failed_chapters.append((chapter_num, error_msg))
            else:
                index, success, error_msg = result
                if success:
                    successful_chapters.append(index)
                else:
                    failed_chapters.append((index, error_msg))

        # 输出处理结果统计
        logging.info(f"📊 章节处理完成统计:")
        if successful_chapters:
            logging.info(f"✅ 处理完成: {len(successful_chapters)} 个章节: {sorted(successful_chapters)}")
        if failed_chapters:
            logging.error(f"❌ 处理失败: {len(failed_chapters)} 个章节:")
            for chapter_num, error_msg in failed_chapters:
                logging.error(f"  章节 {chapter_num}: {error_msg}")

        logging.info(f"🎉 小说 {self.novel_name} 处理完成，成功: {len(successful_chapters)}/{len(chapter_index)}")


async def main():
    """主函数（异步版本）"""
    parser = argparse.ArgumentParser(description="小说分镜画面生成工程化脚本")
    parser.add_argument("--novel_dir", required=True, help="小说文件夹路径，例如: novel/大唐太子")
    parser.add_argument("--output_dir", default="output/novel_step2", help="输出基础目录")
    parser.add_argument("--chapter", type=int, help="指定处理的章节号，如果不指定则进入交互式选择")
    parser.add_argument("--no-interactive", action="store_true", help="禁用交互式章节选择，处理所有章节", default=False)

    args = parser.parse_args()

    # 初始化处理器
    processor = NovelStoryboardProcessor(args.novel_dir, args.output_dir)

    logging.info("=" * 50)
    logging.info("小说分镜画面生成系统启动")
    logging.info(f"小说目录: {args.novel_dir}")
    logging.info(f"输出目录: {processor.novel_output_dir}")
    logging.info(f"数据库路径: {processor.db_path}")
    logging.info("=" * 50)

    try:
        if args.chapter:
            # 处理指定章节
            chapter_dir_pattern = os.path.join(args.novel_dir, f"chapter{args.chapter}_*")
            chapter_dir = glob.glob(chapter_dir_pattern)
            if chapter_dir:
                # 为指定章节创建独立的处理器实例
                chapter_processor = ChapterProcessor(args.chapter, chapter_dir[0], processor.novel_output_dir, processor)
                await chapter_processor.process_chapter()
            else:
                logging.error(f"未找到第{args.chapter}章的文本目录")
        else:
            # 处理章节（支持交互式选择）
            interactive = not args.no_interactive
            await processor.process_novel(args.novel_dir, args.novel_dir, interactive=interactive)

        logging.info("程序执行完成")
    except Exception as e:
        logging.error(f"程序执行出错: {str(e)}")
        raise


if __name__ == "__main__":
    asyncio.run(main())

# python -m src.story_board.novel_storyboard_processor --novel_dir output/novel_step1/大唐太子：开局硬刚李世民