from dataclasses import dataclass, field, asdict
from typing import List, Dict, Any, Optional
import json
import logging
from pathlib import Path
import os

@dataclass
class Character:
    """角色数据类，用于存储角色相关的属性"""
    name: str
    gender: str = ""
    age: str = ""
    clothes: str = ""
    hairstyle: str = ""
    figure: str = ""
    identity: str = ""
    other: str = ""
    from_chapter: int = 0,
    action: str=""
    expression: str=""
    face: str=""
    personality: str=""

    def update(self, new_data: Dict[str, Any]):
        """更新角色属性"""
        for key, value in new_data.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                logging.warning(f"角色 {self.name} 没有属性 {key}")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Character':
        """从字典创建Character对象"""
        return cls(**data)


@dataclass
class Scene:
    """场景数据类，用于存储场景相关的属性"""
    name: str
    description: str = ""
    era: str = ""
    region: str = ""
    time_of_day: str = ""
    weather: str = ""
    atmosphere: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Scene':
        """从字典创建Scene对象"""
        return cls(**data)


@dataclass
class StoryboardData:
    """分镜数据类，用于管理角色和场景"""
    characters: List[Character] = field(default_factory=list)
    scenes: List[str] = field(default_factory=list)
    draw_style: str = ""
    era: str = ""
    region: str = ""

    def add_character(self, character: Character) -> None:
        """添加角色"""
        # 检查是否已存在同名角色
        for i, existing in enumerate(self.characters):
            if existing.name == character.name:
                logging.info(f"更新已存在的角色: {character.name}")
                self.characters[i] = character
                return
        
        logging.info(f"添加新角色: {character.name}")
        self.characters.append(character)

    def get_character_by_name(self, name: str) -> Optional[Character]:
        """根据名称获取角色"""
        for character in self.characters:
            if character.name == name:
                return character
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典，支持JSON序列化"""
        return {
            'characters': [char.to_dict() for char in self.characters],
            'scenes': self.scenes,
            'draw_style': self.draw_style,
            'era': self.era,
            'region': self.region
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StoryboardData':
        """从字典创建StoryboardData对象"""
        characters = [Character.from_dict(char_data) for char_data in data.get('characters', [])]
        return cls(
            characters=characters,
            scenes=data.get('scenes', []),
            draw_style=data.get('draw_style', ''),
            era=data.get('era', ''),
            region=data.get('region', '')
        )
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'StoryboardData':
        """从JSON字符串创建StoryboardData对象"""
        data = json.loads(json_str)
        return cls.from_dict(data)
    