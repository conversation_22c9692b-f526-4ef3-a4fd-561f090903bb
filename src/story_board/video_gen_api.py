# -*- coding: utf-8 -*-
"""
@Time ： 2025/7/13 21:52
@Auth ： xiaolongtuan
@File ：video_gen_api.py
"""
import os
import asyncio
import logging
from typing import Dict, Any

import aiohttp

# 导入QPS控制器
from src.story_board.qps_controller import GlobalQPSManager

# 全局信号量，控制视频生成API的最大并发数
# 现在使用QPS控制器来限制请求频率，而不是简单的并发数限制
VIDEO_GENERATION_SEMAPHORE = asyncio.Semaphore(10)  # 提高并发数，QPS由QPS控制器管理

# 获取视频生成专用的QPS控制器
VIDEO_QPS_CONTROLLER = GlobalQPSManager.get_controller("video_generation", 2.0)


async def create_video_generation_task(api_key: str, text: str, image_url: str) -> str:
    """
    创建图生视频任务

    Args:
        api_key: API密钥
        text: 描述文本
        image_url: 图片URL
        model: 模型名称

    Returns:
        任务ID
    """
    model = os.environ.get("VIDEO_MODEL")
    url = "https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    data = {
        "model": model,
        "content": [
            {
                "type": "text",
                "text": f"--resolution 720p --duration 5 --camerafixed false"
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": image_url
                }
            }
        ]
    }

    async with aiohttp.ClientSession() as session:
        async with session.post(url, headers=headers, json=data) as response:
            if response.status == 200:
                result = await response.json()
                task_id = result["id"]
                logging.info(f"视频生成任务创建成功，任务ID: {task_id}")
                return task_id
            else:
                text = await response.text()
                logging.error(f"创建视频生成任务失败: {response.status} {text}")
                return ""


async def check_task_status(api_key: str, task_id: str) -> Dict[str, Any]:
    """
    检查任务状态

    Args:
        api_key: API密钥
        task_id: 任务ID

    Returns:
        任务状态信息
    """
    url = f"https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks/{task_id}"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }

    async with aiohttp.ClientSession() as session:
        async with session.get(url, headers=headers) as response:
            if response.status == 200:
                return await response.json()
            else:
                text = await response.text()
                logging.error(f"检查任务状态失败: {response.status} {text}")
                return {}


async def gen_video(image_description: str, image_url: str, max_retries: int = 2,
                   cost_tracker=None, chapter=None, segment_id=None) -> str:
    """
    生成视频（异步版本，使用QPS控制和信号量控制并发，支持重试和成本统计）

    Args:
        image_description: 图像描述文本
        image_url: 图片URL
        max_retries: 最大重试次数
        cost_tracker: 成本统计器（可选）
        chapter: 章节号（用于成本统计）
        segment_id: 分镜ID（用于成本统计）

    Returns:
        视频URL
    """
    # 首先通过QPS控制器获取请求许可
    await VIDEO_QPS_CONTROLLER.acquire()

    async with VIDEO_GENERATION_SEMAPHORE:
        api_key = os.environ.get("ARK_API_KEY")
        if not api_key:
            raise ValueError("请设置环境变量ARK_API_KEY")

        # 记录QPS控制器状态
        qps_stats = VIDEO_QPS_CONTROLLER.get_stats()
        logging.info(f"🎬 视频生成API调用 #{qps_stats['request_count']}, QPS控制器: {qps_stats['name']}")

        # 重试机制
        last_exception = None
        for attempt in range(max_retries + 1):
            try:
                task_id = await create_video_generation_task(api_key, image_description, image_url)

                if not task_id:
                    raise ValueError("创建视频生成任务失败")

                # 等待任务完成
                video_url = None
                max_wait_time = 300  # 最大等待时间5分钟
                wait_time = 0

                while wait_time < max_wait_time:
                    status = await check_task_status(api_key, task_id)
                    current_status = status.get("status")

                    if current_status == "succeeded":
                        video_url = status.get("content", {}).get("video_url")
                        if video_url:
                            # 记录视频生成成本
                            if cost_tracker and chapter is not None:
                                model_name = os.environ.get("VIDEO_MODEL", "doubao-seedance")
                                usage = status.get("usage", {})
                                if usage:
                                    cost_tracker.record_video_api_call(
                                        model_name=model_name,
                                        chapter=chapter,
                                        usage=usage,
                                        segment_id=segment_id,
                                        extra_info={
                                            "task_id": task_id,
                                            "resolution": status.get("resolution"),
                                            "duration": status.get("duration"),
                                            "framespersecond": status.get("framespersecond")
                                        }
                                    )

                            logging.info(f"✅ 视频生成API调用成功 #{qps_stats['request_count']}")
                            return video_url
                        else:
                            raise ValueError("任务成功但未返回视频URL")
                    elif current_status in ["failed", "canceled"]:
                        raise ValueError(f"视频生成任务失败，状态: {current_status}")

                    await asyncio.sleep(10)  # 每10秒检查一次
                    wait_time += 10

                # 超时
                raise TimeoutError(f"视频生成任务超时，任务ID: {task_id}")

            except Exception as e:
                last_exception = e
                if attempt < max_retries:
                    logging.warning(f"视频生成失败，第 {attempt + 1} 次重试: {str(e)}")
                    await asyncio.sleep(5)  # 等待5秒后重试
                else:
                    logging.error(f"视频生成失败，已重试 {max_retries} 次: {str(e)}")

        # 如果所有重试都失败，抛出最后一个异常
        if last_exception:
            raise last_exception
        else:
            raise RuntimeError("视频生成失败，未知错误")