#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QPS控制器模块

功能：
1. 控制API请求的QPS（每秒查询数）
2. 使用异步锁和时间间隔确保请求排队
3. 支持不同API的不同QPS限制
"""

import asyncio
import time
import logging
from typing import Optional, Callable, Any
from functools import wraps

logger = logging.getLogger(__name__)


class QPSController:
    """QPS控制器类，用于限制API请求频率"""
    
    def __init__(self, qps_limit: float = 2.0, name: str = "default"):
        """
        初始化QPS控制器
        
        Args:
            qps_limit: QPS限制，默认2.0（每秒2次请求）
            name: 控制器名称，用于日志标识
        """
        self.qps_limit = qps_limit
        self.name = name
        self.min_interval = 1.0 / qps_limit  # 最小请求间隔（秒）
        self.last_request_time = 0.0
        self.lock = asyncio.Lock()
        self.request_count = 0
        
        logger.info(f"QPS控制器 '{name}' 初始化完成，QPS限制: {qps_limit}, 最小间隔: {self.min_interval:.3f}秒")
    
    async def acquire(self) -> None:
        """
        获取请求许可，如果需要等待则会阻塞
        确保请求间隔不小于 min_interval
        """
        async with self.lock:
            current_time = time.time()
            time_since_last = current_time - self.last_request_time
            
            if time_since_last < self.min_interval:
                # 需要等待
                wait_time = self.min_interval - time_since_last
                logger.debug(f"QPS控制器 '{self.name}' 需要等待 {wait_time:.3f}秒")
                await asyncio.sleep(wait_time)
                current_time = time.time()
            
            self.last_request_time = current_time
            self.request_count += 1
            
            logger.debug(f"QPS控制器 '{self.name}' 允许请求 #{self.request_count}")
    
    def get_stats(self) -> dict:
        """获取控制器统计信息"""
        return {
            "name": self.name,
            "qps_limit": self.qps_limit,
            "min_interval": self.min_interval,
            "request_count": self.request_count,
            "last_request_time": self.last_request_time
        }


class GlobalQPSManager:
    """全局QPS管理器，管理不同API的QPS控制器"""
    
    _instance = None
    _controllers = {}
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @classmethod
    def get_controller(cls, name: str, qps_limit: float = 2.0) -> QPSController:
        """
        获取或创建QPS控制器
        
        Args:
            name: 控制器名称
            qps_limit: QPS限制
            
        Returns:
            QPSController实例
        """
        if name not in cls._controllers:
            cls._controllers[name] = QPSController(qps_limit, name)
            logger.info(f"创建新的QPS控制器: {name}, QPS限制: {qps_limit}")
        return cls._controllers[name]
    
    @classmethod
    def get_all_stats(cls) -> dict:
        """获取所有控制器的统计信息"""
        return {name: controller.get_stats() for name, controller in cls._controllers.items()}


def qps_limit(qps: float = 2.0, controller_name: str = "default"):
    """
    QPS限制装饰器
    
    Args:
        qps: QPS限制
        controller_name: 控制器名称
        
    Usage:
        @qps_limit(qps=2.0, controller_name="image_api")
        async def call_image_api():
            # API调用代码
            pass
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            controller = GlobalQPSManager.get_controller(controller_name, qps)
            await controller.acquire()
            return await func(*args, **kwargs)
        return wrapper
    return decorator


# 预定义的图像生成API QPS控制器
IMAGE_QPS_CONTROLLER = GlobalQPSManager.get_controller("image_generation", 2.0)


async def with_qps_control(controller: QPSController, coro_func: Callable, *args, **kwargs) -> Any:
    """
    使用QPS控制执行协程函数
    
    Args:
        controller: QPS控制器
        coro_func: 要执行的协程函数
        *args, **kwargs: 传递给协程函数的参数
        
    Returns:
        协程函数的返回值
    """
    await controller.acquire()
    return await coro_func(*args, **kwargs)


if __name__ == "__main__":
    # 测试代码
    async def test_qps_controller():
        """测试QPS控制器"""
        controller = QPSController(qps_limit=2.0, name="test")
        
        async def mock_api_call(request_id: int):
            """模拟API调用"""
            await controller.acquire()
            current_time = time.time()
            print(f"请求 {request_id} 在 {current_time:.3f} 执行")
            return f"结果 {request_id}"
        
        # 并发发送10个请求
        tasks = [mock_api_call(i) for i in range(10)]
        start_time = time.time()
        
        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        total_time = end_time - start_time
        actual_qps = len(tasks) / total_time
        
        print(f"\n测试结果:")
        print(f"总请求数: {len(tasks)}")
        print(f"总耗时: {total_time:.3f}秒")
        print(f"实际QPS: {actual_qps:.3f}")
        print(f"期望QPS: {controller.qps_limit}")
        print(f"控制器统计: {controller.get_stats()}")
    
    # 运行测试
    asyncio.run(test_qps_controller())
