import os
import json
import asyncio
import logging
from pathlib import Path
from functools import wraps

from dashscope.common.constants import NEGATIVE_PROMPT
import requests
import aiohttp
from typing import List, Dict, Any

from dotenv import load_dotenv

from src.story_board.storyboard_data import StoryboardData
from src.story_board.video_gen_api import gen_video
from src.utils.network_retry import is_network_error

# 加载.env文件中的环境变量
dotenv_path = Path(__file__).parent.parent.parent / '.env'
load_dotenv(dotenv_path=dotenv_path, verbose=True, override=True)

# 环境变量获取API密钥
API_KEY = os.environ.get("ARK_API_KEY")
if not API_KEY:
    raise ValueError("请设置环境变量ARK_API_KEY")

# API端点
TEXT_API_ENDPOINT = os.environ.get("TEXT_API_ENDPOINT")
IMAGE_API_ENDPOINT = os.environ.get("IMAGE_API_ENDPOINT")
VIDEO_API_ENDPOINT = os.environ.get("VIDEO_API_ENDPOINT")

# 智能生图模型
VOLCENGINE_IMAGE_GEN_MODEL = os.environ.get("VOLCENGINE_IMAGE_GEN_MODEL")

# 模型名称
TEXT_MODEL = os.environ.get("TEXT_MODEL")
IMAGE_MODEL = os.environ.get("IMAGE_MODEL")  # 暂时没有使用
VIDEO_MODEL = os.environ.get("VIDEO_MODEL")

# 提示词文件路径
SCENE_INFO_SYSTEM_PATH = "src/prompts/scene_info_system.txt"
RULE_INFO_SYSTEM_PATH = "src/prompts/rule_info_system.txt"
GEN_IMAGE_SYSTEM_PATH = "src/prompts/gen_image_system.txt"
GEN_COVER_IMAGE_SYSTEM_PATH = "src/prompts/gen_cover_image_system.txt"
IMAGE_REGENING_SYSTEM_PATH = "src/prompts/image_system_regening.txt"
COSTUME_DESIGN_SYSTEM_PATH = "src/prompts/costume_design_system.txt"
COVER_IMAGE_REGENING_SYSTEM_PATH = "src/prompts/cover_image_system_regening.txt"
VIDEO_DES_PROMPT_PATH = "src/prompts/video_des_prompt.txt"
AI_VIDEO_PROMPT_PATH = "src/prompts/ai_video_prompt.txt"
NEGATIVE_PROMPT = "src/prompts/neg_prompt.md"

# 输出目录
OUTPUT_DIR = "output"
IMAGES_DIR = os.path.join(OUTPUT_DIR, "images")

# 导入QPS控制器
from src.story_board.qps_controller import IMAGE_QPS_CONTROLLER

# 全局信号量，控制图片生成API的最大并发数
# 注意：现在使用QPS控制器来限制请求频率，而不是简单的并发数限制
# QPS控制器确保请求频率不超过2 QPS，同时允许更高的并发数
IMAGE_GENERATION_SEMAPHORE = asyncio.Semaphore(10)  # 提高并发数，QPS由QPS控制器管理


def load_system_prompt(file_path: str) -> str:
    """加载系统提示词"""
    with open(file_path, "r", encoding="utf-8") as f:
        return f.read()


async def call_text_api(data, cost_tracker=None, chapter=None, segment_id=None, max_retries: int = 3) -> str:
    """调用文本生成API（异步版本，支持成本统计和网络重试）

    Args:
        data: API请求数据
        cost_tracker: 成本统计器（可选）
        chapter: 章节号（用于成本统计）
        segment_id: 分镜ID（用于成本统计）
        max_retries: 最大重试次数，默认3次

    Returns:
        API响应的文本内容
    """
    headers = {"Content-Type": "application/json", "Authorization": f"Bearer {API_KEY}"}

    last_exception = None

    for attempt in range(max_retries + 1):
        try:
            timeout = aiohttp.ClientTimeout(total=60)  # 60秒超时
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(TEXT_API_ENDPOINT, headers=headers, json=data) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise Exception(f"API调用失败: {response.status} - {error_text}")

                    result = await response.json()

                    # 记录成本统计
                    if cost_tracker and chapter is not None:
                        model_name = data.get("model", "unknown")
                        usage = result.get("usage", {})
                        if usage:
                            cost_tracker.record_text_api_call(
                                model_name=model_name,
                                chapter=chapter,
                                usage=usage,
                                segment_id=segment_id,
                                extra_info={"endpoint": TEXT_API_ENDPOINT}
                            )

                    return result["choices"][0]["message"]["content"]

        except Exception as e:
            # 使用网络错误检测函数判断是否为网络错误
            if is_network_error(e):
                last_exception = e
                if attempt < max_retries:
                    wait_time = 2 ** attempt  # 指数退避：2, 4, 8秒
                    logging.warning(f"🔄 文本API调用网络错误，第 {attempt + 1} 次重试 (等待 {wait_time}s): {str(e)}")
                    await asyncio.sleep(wait_time)
                else:
                    logging.error(f"❌ 文本API调用网络失败，已重试 {max_retries} 次: {str(e)}")
            else:
                # 对于非网络错误（如API错误、JSON解析错误等），不重试
                logging.error(f"❌ 文本API调用非网络错误: {str(e)}")
                raise e

    # 如果所有重试都失败，抛出最后一个网络异常
    if last_exception:
        raise last_exception
    else:
        raise RuntimeError("文本API调用失败，未知错误")


async def download_image(url: str, filename: str) -> None:
    """下载图像（异步版本）"""
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            response.raise_for_status()
            content = await response.read()

            with open(filename, "wb") as f:
                f.write(content)


def split_novel(novel: str, max_length: int = 1000) -> List[str]:
    """将小说分割成片段"""
    # 简单按句号分割，然后合并到最大长度
    sentences = novel.split("。")
    segments = []
    current_segment = ""

    for sentence in sentences:
        if not sentence.strip():
            continue

        # 添加句号回来
        sentence = sentence + "。"

        if len(current_segment) + len(sentence) <= max_length:
            current_segment += sentence
        else:
            if current_segment:
                segments.append(current_segment)
            current_segment = sentence

    if current_segment:
        segments.append(current_segment)

    return segments


async def generate_scene_info(novel: str, cost_tracker=None, chapter=None) -> Dict[str, Any]:
    """生成场景设定信息"""
    system_prompt = load_system_prompt(SCENE_INFO_SYSTEM_PATH)
    data = {
        "model": TEXT_MODEL,
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": novel},
        ],
    }

    response = await call_text_api(data, cost_tracker, chapter, "scene_info")
    # 提取JSON部分
    json_response = parse_json_reponse(response)
    return json_response


async def generate_character_info(novel: str, cost_tracker=None, chapter=None) -> List[Dict[str, str]]:
    """生成角色设定信息"""
    system_prompt = load_system_prompt(RULE_INFO_SYSTEM_PATH)
    data = {
        "model": TEXT_MODEL,
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": novel},
        ],
    }

    response = await call_text_api(data, cost_tracker, chapter, "character_info")
    # 提取JSON部分
    try:
        # 查找JSON开始和结束的位置
        start_idx = response.find("[")
        end_idx = response.rfind("]")

        if start_idx != -1 and end_idx != -1:
            json_str = response[start_idx: end_idx + 1]
            json_data = json.loads(json_str)
            return json_data
        else:
            raise ValueError(f"1无法从响应中提取JSON, 原始响应：{response}")
    except json.JSONDecodeError:
        print(f"JSON解析错误，原始响应：{response}")
        raise


async def generate_image_description(
        novel_content: str, segment: str, storyboard_data: StoryboardData, pre_image_description,
        cost_tracker=None, chapter=None, segment_id=None
) -> Dict[str, Any]:
    """生成图像描述信息"""
    system_prompt = load_system_prompt(GEN_IMAGE_SYSTEM_PATH)

    # 准备角色列表和场景列表
    character_list = str([char.name for char in storyboard_data.characters])
    scene_list = str(storyboard_data.scenes)

    # 将角色列表和场景列表填充到系统提示词中
    system_prompt = system_prompt.replace("{character_list}", character_list)
    system_prompt = system_prompt.replace("{scene_list}", scene_list)
    system_prompt = system_prompt.replace("{pre_image_description}", pre_image_description)

    data = {
        "model": TEXT_MODEL,
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": "参考小说原文如下：" + novel_content},
            {"role": "user", "content": "请生成该分镜内容的角色描述" + segment},
        ],
    }
    response = await call_text_api(data, cost_tracker, chapter, segment_id)
    # 提取JSON部分
    json_response = parse_json_reponse(response)
    # 验证数据
    if isinstance(json_response["scene"], list):
        json_response["scene"] = json_response["scene"][0]
    return json_response


async def generate_cover_image_description(
        novel_content: str, segment: str, storyboard_data: StoryboardData
) -> Dict[str, Any]:
    """生成封面描述信息"""
    system_prompt = load_system_prompt(GEN_COVER_IMAGE_SYSTEM_PATH)

    # 准备角色列表和场景列表
    character_list = str([char.name for char in storyboard_data.characters])
    scene_list = str(storyboard_data.scenes)

    # 将角色列表和场景列表填充到系统提示词中
    system_prompt = system_prompt.replace("{character_list}", character_list)
    system_prompt = system_prompt.replace("{scene_list}", scene_list)

    data = {
        "model": TEXT_MODEL,
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": "小说原文如下：" + novel_content},
            {"role": "user", "content": "引子" + segment},
        ],
    }
    response = await call_text_api(data)
    # 提取JSON部分
    json_response = parse_json_reponse(response)
    return json_response


def parse_json_reponse(response):
    try:
        # 查找JSON开始和结束的位置
        start_idx = response.find("{")
        end_idx = response.rfind("}")

        if start_idx != -1 and end_idx != -1:
            json_str = response[start_idx: end_idx + 1]
            json_data = json.loads(json_str)
            return json_data
        else:
            raise ValueError(f"3无法从响应中提取JSON, 原始响应：{response}")
    except json.JSONDecodeError:
        print(f"JSON解析错误，原始响应：{response}")
        raise


async def generate_costume_design(era, region, characters) -> List[Dict[str, Any]]:
    """为角色生成符合时代和地区的服饰设计"""
    system_prompt = load_system_prompt(COSTUME_DESIGN_SYSTEM_PATH)

    # 准备角色列表 - 将Character对象转换为字典
    character_info = [
        {
            "name": char["name"],
            "gender": char["gender"],
            "age": char["age"],
            "clothes": char["clothes"],
            "hairstyle": char["hairstyle"],
            "figure": char["figure"],
            "identity": char["identity"],
            "face": char['face'],
            "personality": char['personality']
        }
        for char in characters
    ]
    character_list = json.dumps(character_info, ensure_ascii=False)

    # 构建用户提示
    user_prompt = f"请根据以下信息为角色设计服饰:\n\n角色信息：{character_list}"

    data = {
        "model": TEXT_MODEL,
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt},
        ],
    }

    response = await call_text_api(data)
    # 提取JSON部分
    try:
        # 查找JSON开始和结束的位置
        start_idx = response.find("[")
        end_idx = response.rfind("]")

        if start_idx != -1 and end_idx != -1:
            json_str = response[start_idx: end_idx + 1]
            json_data = json.loads(json_str)
            return json_data
        else:
            raise ValueError(f"2无法从响应中提取JSON, 原始响应：{response}")
    except json.JSONDecodeError:
        print(f"JSON解析错误，原始响应：{response}")
        raise


def generate_image_prompt(
        image_description: Dict[str, Any], storyboard_data: StoryboardData
) -> str:
    """生成图像提示词"""
    prompt_parts = []

    # 添加角色信息
    if image_description.get("main_characters"):
        character_descriptions = []
        for main_character in image_description["main_characters"][:3]:
            # 查找角色信息
            char = next(
                (
                    c
                    for c in storyboard_data.characters
                    if c.name == main_character["name"]
                ),
                None,
            )
            if char:
                # 基本角色描述
                char_desc = f"{main_character['name']}：{char.gender}，{char.age}，"
                # 使用原有服饰描述
                char_desc += f"穿着{char.clothes}"

                # 添加其他角色特征
                char_desc += f"，{char.hairstyle}， {char.figure}，{char.face}，{char.personality}"
                char.action = main_character["action"]
                char.expression = main_character["expression"]

                char_desc += (
                    f", {main_character['action']}, {main_character['expression']}"
                )
                character_descriptions.append(char_desc)

        if character_descriptions:
            prompt_parts.append("主要人物信息：" + "\n".join(character_descriptions))

    # 添加场景信息
    scene_name = image_description.get("scene")
    if scene_name and scene_name.lower() != "none":
        prompt_parts.append(f"{scene_name}")

    # 添加绘图描述
    if image_description.get("image_description"):
        prompt_parts.append(f"画面：{image_description['image_description']}")

    return "\n".join(prompt_parts)


async def image_regening(origin_ddescription: str) -> str:
    system_prompt = load_system_prompt(IMAGE_REGENING_SYSTEM_PATH)
    data = {
        "model": TEXT_MODEL,
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": "绘图信息：" + origin_ddescription},
        ],
    }
    response = await call_text_api(data)
    # 提取JSON部分
    json_response = parse_json_reponse(response)
    return json_response["prompt"]


async def image_regening_for_cover(origin_ddescription: str) -> str:
    system_prompt = load_system_prompt(COVER_IMAGE_REGENING_SYSTEM_PATH)
    data = {
        "model": TEXT_MODEL,
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": "绘图信息：" + origin_ddescription},
        ],
    }
    response = await call_text_api(data)
    # 提取JSON部分
    json_response = parse_json_reponse(response)
    return json_response["prompt"]


def gen_video_prompt(storyboard_data: StoryboardData) -> str:
    charator_actions = []
    for character in storyboard_data.characters:
        charator_actions.append(
            f"一个 {character.gender} {character.action} {character.expression}"
        )
    video_prompt = load_system_prompt(AI_VIDEO_PROMPT_PATH)
    video_prompt = video_prompt.replace(
        "{image_description}", "\n".join(charator_actions)
    )
    video_prompt = video_prompt.replace("{era}", storyboard_data.era)
    video_prompt = video_prompt.replace("{region}", storyboard_data.region)
    return video_prompt


from volcengine.visual.VisualService import VisualService


async def call_volcengine_image_api(
        prompt: str,
        negative_prompt: str = "",
        seed: int = -1,
        scale: float = 7.5,
        ddim_steps: int = 16,
        width: int = 720,
        height: int = 1280,
        use_sr: bool = True,
        use_pre_llm: bool = False,
        return_url: bool = True,
        add_logo: bool = False,
        use_rephraser: bool = False,
) -> Dict[str, Any]:
    # 首先通过QPS控制器获取请求许可
    await IMAGE_QPS_CONTROLLER.acquire()

    async with IMAGE_GENERATION_SEMAPHORE:
        # 使用提供的API密钥或环境变量
        # 获取环境变量中的API密钥
        key = os.environ.get("VOLCENGINE_ACCESS_KEY")
        secret = os.environ.get("VOLCENGINE_SECRET_KEY")
        if not key:
            raise ValueError("请设置环境变量VOLCENGINE_API_KEY或提供api_key参数")
        if not secret:
            raise ValueError("请设置环境变量VOLCENGINE_API_SECRET或提供api_secret参数")

        # 在异步环境中运行同步的VisualService调用
        def _sync_call():
            visual_service = VisualService()
            visual_service.set_ak(key)
            visual_service.set_sk(secret)
            # print("VOLCENGINE_IMAGE_GEN_MODEL:" + VOLCENGINE_IMAGE_GEN_MODEL)
            form = {
                "req_key": VOLCENGINE_IMAGE_GEN_MODEL,
                "prompt": prompt,
                "negative_prompt": negative_prompt,
                "seed": seed,
                "scale": scale,
                "ddim_steps": ddim_steps,
                "width": width,
                "height": height,
                "use_sr": use_sr,
                "use_pre_llm": use_pre_llm,
                "use_rephraser": use_rephraser,
                "return_url": return_url,
                "logo_info": {"add_logo": add_logo},
            }
            return visual_service.cv_process(form)

        # 直接调用API，不进行重试（重试由上层函数 generate_volcengine_image 处理）
        try:
            # 记录QPS控制器状态
            qps_stats = IMAGE_QPS_CONTROLLER.get_stats()
            logging.info(f"🎨 图像生成API调用 #{qps_stats['request_count']}, QPS控制器: {qps_stats['name']}")

            # 在线程池中运行同步调用
            loop = asyncio.get_event_loop()
            resp = await loop.run_in_executor(None, _sync_call)

            # 检查响应是否有效
            if resp and isinstance(resp, dict):
                logging.info(f"✅ 图像生成API调用成功 #{qps_stats['request_count']}")
                return resp
            else:
                raise ValueError("API返回无效响应")

        except Exception as e:
            logging.error(f"❌ 图片生成API调用失败: {str(e)}")
            raise


def get_image_url_from_response(response: Dict[str, Any]) -> str:
    """
    从API响应中提取图像URL

    Args:
        response: API响应结果

    Returns:
        图像URL
    """
    if response.get("code") != 10000:
        error_msg = response.get("message", "未知错误")
        raise ValueError(f"API调用失败: {error_msg}")

    # 尝试从image_urls获取URL
    if (
            "data" in response
            and "image_urls" in response["data"]
            and response["data"]["image_urls"]
    ):
        return response["data"]["image_urls"][0], response["data"]["request_id"]

    # 如果没有URL，尝试从base64获取
    if (
            "data" in response
            and "binary_data_base64" in response["data"]
            and response["data"]["binary_data_base64"]
    ):
        # 这里只返回提示，实际应用中可能需要将base64转换为图像
        return "图像以base64格式返回，未提供URL", response["data"]["request_id"]

    raise ValueError("响应中未找到图像URL或base64数据")


async def generate_volcengine_image(prompt: str, size: str = None, max_retries: int = 2,
                                    cost_tracker=None, chapter=None, segment_id=None) -> str:
    """
    生成图像并保存到指定路径（异步版本，支持重试和成本统计）

    Args:
        prompt: 图像提示词
        size: 图像尺寸，格式为"宽x高"
        max_retries: 最大重试次数
        cost_tracker: 成本统计器（可选）
        chapter: 章节号（用于成本统计）
        segment_id: 分镜ID（用于成本统计）

    Returns:
        图像URL
    """
    # 调用API
    size = size or os.environ.get("IMAGE_SIZE", "720x1280")
    try:
        width, height = map(int, size.split("x"))
    except ValueError:
        raise ValueError(f"无效的尺寸格式: {size}，应为'宽x高'，例如'512x512'")

    netativate_prompt = load_system_prompt(NEGATIVE_PROMPT)

    # 重试机制 - 注意：call_volcengine_image_api内部不再重试，避免重复重试
    last_exception = None
    for attempt in range(max_retries + 1):
        try:
            response = await call_volcengine_image_api(
                prompt, negative_prompt=netativate_prompt, width=width, height=height
            )
            # 获取图像URL
            image_url, request_id = get_image_url_from_response(response)

            if image_url:
                # 记录图像生成成本
                if cost_tracker and chapter is not None:
                    model_name = os.environ.get("VOLCENGINE_IMAGE_GEN_MODEL", "volcengine-image")
                    cost_tracker.record_image_api_call(
                        model_name=model_name,
                        chapter=chapter,
                        segment_id=segment_id,
                        extra_info={"prompt_length": len(prompt), "size": size, "request_id": request_id,
                                    "image_url": image_url},
                    )
                return image_url
            else:
                raise ValueError("无法从响应中获取图像URL")

        except Exception as e:
            last_exception = e
            if attempt < max_retries:
                logging.warning(f"图像生成失败，第 {attempt + 1} 次重试: {str(e)}")
                await asyncio.sleep(1)  # 增加等待时间到3秒
            else:
                logging.error(f"图像生成失败，已重试 {max_retries} 次: {str(e)}")

    # 如果所有重试都失败，抛出最后一个异常
    if last_exception:
        raise last_exception
    else:
        raise RuntimeError("图像生成失败，未知错误")


async def call_volcengine_video_api(prompt: str, image_url: str, max_retries: int = 2,
                                    cost_tracker=None, chapter=None, segment_id=None) -> str:
    """
    创建图生视频任务（异步版本，支持重试和成本统计）
    Args:
        prompt: 描述文本
        image_url: 图片URL
        max_retries: 最大重试次数
        cost_tracker: 成本统计器（可选）
        chapter: 章节号（用于成本统计）
        segment_id: 分镜ID（用于成本统计）

    Returns:
        视频URL
    """
    return await gen_video(prompt, image_url, max_retries, cost_tracker, chapter, segment_id)
