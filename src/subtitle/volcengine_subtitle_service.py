#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
火山引擎音频字幕生成服务
"""

import os
import time
import json
import logging
import requests
import base64
from typing import List, Dict, Any, Optional
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

logger = logging.getLogger(__name__)


class VolcengineSubtitleService:
    """火山引擎音频字幕生成服务"""
    
    def __init__(self):
        self.base_url = 'https://openspeech.bytedance.com/api/v1/vc'

        # 优先使用字幕服务专用配置
        self.appid = os.getenv('VOLCENGINE_SUBTITLE_APPID')
        self.access_token = os.getenv('VOLCENGINE_SUBTITLE_ACCESS_TOKEN')
        self.secret_key = os.getenv('VOLCENGINE_SUBTITLE_SECRET_KEY')

        if self.appid and self.access_token:
            logger.info("使用字幕服务专用配置")
            logger.info(f"APPID: {self.appid}")
            logger.info(f"Access Token: {self.access_token[:10]}...")
            if self.secret_key:
                logger.info(f"Secret Key: {self.secret_key[:10]}...")
        else:
            logger.warning("未找到字幕服务专用配置，尝试使用其他配置...")

            # 尝试使用ARK_API_KEY（项目中其他火山引擎API使用的密钥）
            self.access_token = os.getenv('ARK_API_KEY')
            # 使用TOS_ACCESS_KEY作为APPID（从现有代码中看到的模式）
            self.appid = os.getenv('TOS_ACCESS_KEY')

            if not self.appid or not self.access_token:
                logger.warning("未找到ARK_API_KEY或TOS_ACCESS_KEY，尝试使用原配置...")
                # 回退到原来的配置
                raw_appid = os.getenv('VOLCENGINE_APPID')
                raw_token = os.getenv('VOLCENGINE_ACCESS_TOKEN')

                if not raw_appid or not raw_token:
                    raise ValueError("请在.env文件中设置VOLCENGINE_SUBTITLE_APPID和VOLCENGINE_SUBTITLE_TOKEN")

                # 解码Base64编码的配置
                try:
                    self.appid = base64.b64decode(raw_appid).decode('utf-8')
                    self.access_token = base64.b64decode(raw_token).decode('utf-8')
                    logger.info("使用解码后的配置")
                except Exception as e:
                    # 如果解码失败，直接使用原值
                    self.appid = raw_appid
                    self.access_token = raw_token
                    logger.info("使用原始配置值")
            else:
                logger.info("使用ARK_API_KEY配置")

        logger.info(f"APPID: {self.appid}")
        logger.info(f"TOKEN: {self.access_token[:10]}...")
    
    def submit_audio_url(self, audio_url: str, language: str = 'zh-CN', 
                        words_per_line: int = 15, max_lines: int = 1,boosting_table_id:str = '3d2374cf-a0d1-4183-b9e0-353fa8d8fb4d') -> str:

        """
        提交音频URL进行字幕识别
        
        Args:
            audio_url: 音频文件URL
            language: 语言类型，默认zh-CN
            words_per_line: 每行最多字数，默认15
            max_lines: 每屏最多行数，默认1
            
        Returns:
            任务ID
        """
        submit_url = f"{self.base_url}/submit"
        
        params = {
            'appid': self.appid,
            'language': language,
            'use_itn': 'True',
            'use_punc': 'False',  # 增加标点
            'max_lines': max_lines,
            'words_per_line': words_per_line,
            'boosting_table_id': boosting_table_id,
            'asr_appid':self.appid,
        }
        
        headers = {
            'content-type': 'application/json',
            'Authorization': f'Bearer; {self.access_token}'  # 火山引擎要求的格式：Bearer; token
        }
        
        data = {
            'url': audio_url
        }
        
        logger.info(f"提交音频字幕识别任务...")
        logger.info(f"   音频URL: {audio_url}")
        logger.info(f"   语言: {language}")

        logger.info(f"   每行字数: {words_per_line}")
        logger.info(f"   每屏行数: {max_lines}")
        logger.info(f"   模型ID: {boosting_table_id}")
        
        response = requests.post(submit_url, params=params, json=data, headers=headers)
        
        if response.status_code != 200:
            raise Exception(f"提交任务失败: {response.status_code} - {response.text}")
        
        result = response.json()
        if result.get('code') != 0:
            raise Exception(f"提交任务失败: {result.get('message')}")
        
        job_id = result.get('id')
        logger.info(f"任务提交成功，任务ID: {job_id}")
        return job_id
    
    def query_result(self, job_id: str, blocking: bool = True) -> dict:
        """
        查询字幕识别结果
        
        Args:
            job_id: 任务ID
            blocking: 是否阻塞查询，默认True
            
        Returns:
            识别结果
        """
        query_url = f"{self.base_url}/query"
        
        params = {
            'appid': self.appid,
            'id': job_id,
            'blocking': 1 if blocking else 0
        }
        
        headers = {
            'Authorization': f'Bearer; {self.access_token}'  # 火山引擎要求的格式：Bearer; token
        }
        
        logger.info(f"查询字幕识别结果...")
        logger.info(f"   任务ID: {job_id}")
        logger.info(f"   阻塞模式: {blocking}")
        
        response = requests.get(query_url, params=params, headers=headers)
        
        if response.status_code != 200:
            raise Exception(f"查询结果失败: {response.status_code} - {response.text}")
        
        result = response.json()
        
        if result.get('code') == 2000:
            logger.info("任务处理中，请稍后...")
            return result
        elif result.get('code') != 0:
            raise Exception(f"查询结果失败: {result.get('message')}")
        
        logger.info("字幕识别完成")
        return result
    
    def process_audio_subtitle(self, audio_url: str, max_retries: int = 30) -> List[Dict[str, Any]]:
        """
        完整处理音频字幕识别流程
        
        Args:
            audio_url: 音频文件URL
            max_retries: 最大重试次数
            
        Returns:
            格式化的字幕列表
        """
        try:
            # 提交任务
            job_id = self.submit_audio_url(audio_url)
            
            # 轮询查询结果
            for i in range(max_retries):
                try:
                    result = self.query_result(job_id, blocking=False)
                    
                    if result.get('code') == 0:
                        # 成功获取结果
                        return self.format_subtitles(result)
                    elif result.get('code') == 2000:
                        # 任务处理中，等待后重试
                        logger.info(f"   等待中... ({i+1}/{max_retries})")
                        time.sleep(2)
                    else:
                        raise Exception(f"任务失败: {result.get('message')}")
                        
                except Exception as e:
                    logger.warning(f"   查询异常: {e}")
                    if i < max_retries - 1:
                        time.sleep(2)
                    else:
                        raise
            
            raise Exception(f"任务超时，超过最大重试次数 {max_retries}")
            
        except Exception as e:
            logger.error(f"火山引擎字幕识别失败: {e}")
            # 返回空列表，让调用方处理
            return []
    
    def format_subtitles(self, result: dict) -> List[Dict[str, Any]]:
        """
        格式化字幕结果
        
        Args:
            result: 原始识别结果
            
        Returns:
            格式化的字幕列表
        """
        utterances = result.get('utterances', [])
        formatted_subtitles = []
        
        logger.info(f"格式化字幕结果，共 {len(utterances)} 个分句")
        
        for i, utterance in enumerate(utterances):
            subtitle = {
                'subtitle_id': i + 1,
                'text': utterance.get('text', ''),
                'start_time': utterance.get('start_time', 0),  # 毫秒
                'end_time': utterance.get('end_time', 0),      # 毫秒
                'duration': (utterance.get('end_time', 0) - utterance.get('start_time', 0)) / 1000.0,  # 秒
                'timestamp': self._format_timestamp(utterance.get('start_time', 0), utterance.get('end_time', 0)),
                'words': utterance.get('words', []),
                'char_count': len(utterance.get('text', '')),
                'semantic_type': "火山引擎识别"
            }
            formatted_subtitles.append(subtitle)
            
            logger.debug(f"   字幕{i+1}: {subtitle['text'][:20]}... ({subtitle['duration']:.2f}秒)")
        
        return formatted_subtitles
    
    def _format_timestamp(self, start_ms: int, end_ms: int) -> str:
        """格式化时间戳为SRT格式"""
        def ms_to_srt_time(ms):
            hours = ms // 3600000
            minutes = (ms % 3600000) // 60000
            seconds = (ms % 60000) // 1000
            milliseconds = ms % 1000
            return f"{hours:02d}:{minutes:02d}:{seconds:02d},{milliseconds:03d}"
        
        return f"{ms_to_srt_time(start_ms)} --> {ms_to_srt_time(end_ms)}"


class MockVolcengineSubtitleService:
    """模拟火山引擎字幕服务，用于开发测试"""
    
    def __init__(self):
        logger.info("使用模拟火山引擎字幕服务")
    
    def process_audio_subtitle(self, audio_url: str, max_retries: int = 30) -> List[Dict[str, Any]]:
        """
        模拟处理音频字幕识别
        
        Args:
            audio_url: 音频文件URL
            max_retries: 最大重试次数（忽略）
            
        Returns:
            模拟的字幕列表
        """
        logger.info(f"模拟处理音频字幕: {audio_url}")
        
        # 模拟处理时间
        time.sleep(1)
        
        # 返回模拟的字幕数据
        mock_subtitles = [
            {
                'subtitle_id': 1,
                'text': '这是模拟的字幕内容第一句',
                'start_time': 0,
                'end_time': 3000,
                'duration': 3.0,
                'timestamp': '00:00:00,000 --> 00:00:03,000',
                'words': [],
                'char_count': 12,
                'semantic_type': "模拟识别"
            },
            {
                'subtitle_id': 2,
                'text': '这是模拟的字幕内容第二句',
                'start_time': 3000,
                'end_time': 6000,
                'duration': 3.0,
                'timestamp': '00:00:03,000 --> 00:00:06,000',
                'words': [],
                'char_count': 12,
                'semantic_type': "模拟识别"
            }
        ]
        
        logger.info(f"模拟字幕识别完成，生成 {len(mock_subtitles)} 个字幕")
        return mock_subtitles


def create_subtitle_service(use_mock: bool = False) -> 'VolcengineSubtitleService':
    """
    创建字幕服务实例
    
    Args:
        use_mock: 是否使用模拟服务
        
    Returns:
        字幕服务实例
    """
    if use_mock:
        return MockVolcengineSubtitleService()
    else:
        return VolcengineSubtitleService()
