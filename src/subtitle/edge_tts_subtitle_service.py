#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Edge TTS字幕服务
使用Edge TTS生成音频和字幕，并通过jieba进行智能分割
"""

import asyncio
import edge_tts
import re
import tempfile
import os
from typing import List, Dict, Any, Optional
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

# 尝试导入配置，如果失败则使用默认值
try:
    from ..new_novel.config.settings import Settings

    _settings = Settings()
    DEFAULT_SPEED_FACTOR = _settings.audio_speed_factor
    logger.info(f"从配置获取倍速因子: {DEFAULT_SPEED_FACTOR}")
except ImportError:
    DEFAULT_SPEED_FACTOR = 1.25
    logger.warning(f"无法导入配置，使用默认倍速因子: {DEFAULT_SPEED_FACTOR}")


class EdgeTTSSubtitleService:
    """Edge TTS字幕服务"""

    def __init__(self, voice: str = "zh-CN-XiaoyiNeural", max_chars: int = 15, min_chars: int = 3,
                 speed_factor: float = None):
        """
        初始化Edge TTS字幕服务

        Args:
            voice: 语音类型
            max_chars: 每条字幕最大字符数
            min_chars: 每条字幕最小字符数
            speed_factor: 倍速因子，用于调整时间戳
        """
        self.voice = voice
        self.max_chars = max_chars
        self.min_chars = min_chars
        # 如果没有指定倍速因子，使用配置中的值
        self.speed_factor = speed_factor if speed_factor is not None else DEFAULT_SPEED_FACTOR
        logger.info(
            f"Edge TTS字幕服务初始化完成，语音: {voice}, 字数限制: {min_chars}-{max_chars}字, 倍速: {self.speed_factor}x")

    def process_audio_subtitle(self, text: str, max_retries: int = 3) -> List[Dict[str, Any]]:
        """
        处理文本生成字幕（同步接口，兼容火山引擎）

        Args:
            text: 输入文本（这里直接使用文本而不是音频URL）
            max_retries: 最大重试次数（保持接口兼容）

        Returns:
            格式化的字幕列表
        """
        try:
            logger.info(f"开始处理Edge TTS字幕生成，文本长度: {len(text)}字")

            # 尝试使用真正的Edge TTS生成字幕
            try:
                logger.info("使用真正的Edge TTS生成字幕和时间戳")
                return self._generate_subtitles_with_edge_tts(text)
            except Exception as e:
                logger.warning(f"Edge TTS异步生成失败，尝试同步方式: {e}")
                # 如果异步失败，尝试同步方式但仍使用真实的Edge TTS
                logger.info("使用Edge TTS同步方式生成字幕（备用方案）")
                return self._generate_subtitles_with_edge_tts_sync(text)

        except Exception as e:
            logger.error(f"字幕生成完全失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return []

    async def _generate_subtitles_async(self, text: str) -> List[Dict[str, Any]]:
        """异步生成字幕"""
        try:
            # 1. 使用Edge TTS生成音频和词汇边界信息
            communicate = edge_tts.Communicate(text, self.voice)
            submaker = edge_tts.SubMaker()

            # 创建临时文件保存音频
            with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_audio:
                temp_audio_path = temp_audio.name

            try:
                # 生成音频和字幕数据
                with open(temp_audio_path, "wb") as audio_file:
                    async for chunk in communicate.stream():
                        if chunk["type"] == "audio":
                            audio_file.write(chunk["data"])
                        elif chunk["type"] == "WordBoundary":
                            submaker.feed(chunk)

                # 2. 获取原始词汇边界数据
                raw_words = self._extract_word_boundaries(submaker)
                logger.info(f"提取到 {len(raw_words)} 个词汇边界")

                # 3. 使用jieba进行智能分割
                subtitle_groups = self._create_subtitle_groups_with_jieba(raw_words)
                logger.info(f"生成 {len(subtitle_groups)} 条字幕")

                # 4. 转换为火山引擎兼容格式
                formatted_subtitles = self._format_to_volcengine_compatible(subtitle_groups)

                return formatted_subtitles

            finally:
                # 清理临时文件
                try:
                    os.unlink(temp_audio_path)
                except:
                    pass

        except Exception as e:
            logger.error(f"异步字幕生成失败: {e}")
            return []

    def _generate_subtitles_with_edge_tts_sync(self, text: str) -> List[Dict[str, Any]]:
        """使用Edge TTS同步方式生成字幕（真实服务的备用方案）"""
        try:
            logger.info("使用Edge TTS同步方式生成字幕")

            # 使用Edge TTS同步生成
            communicate = edge_tts.Communicate(text, self.voice)
            submaker = edge_tts.SubMaker()

            # 同步处理流
            for chunk in communicate.stream_sync():
                if chunk["type"] == "WordBoundary":
                    submaker.feed(chunk)

            # 获取原始词汇边界数据
            raw_words = self._extract_word_boundaries(submaker)
            logger.info(f"同步方式提取到 {len(raw_words)} 个词汇边界")

            # 使用jieba进行智能分割
            subtitle_groups = self._create_subtitle_groups_with_jieba(raw_words)
            logger.info(f"同步方式生成 {len(subtitle_groups)} 条字幕")

            # 转换为火山引擎兼容格式
            formatted_subtitles = self._format_to_volcengine_compatible(subtitle_groups)

            return formatted_subtitles

        except Exception as e:
            logger.error(f"Edge TTS同步字幕生成失败: {e}")
            # 最后的备用方案：使用模拟数据但确保words字段正确
            logger.warning("使用模拟数据作为最后备用方案")
            return self._generate_subtitles_sync(text)

    def _generate_subtitles_with_edge_tts(self, text: str) -> List[Dict[str, Any]]:
        """使用真正的Edge TTS生成字幕和精确时间戳（模仿字幕.py的方式）"""
        try:
            # 创建临时SRT文件
            with tempfile.NamedTemporaryFile(suffix='.srt', delete=False) as subtitle_file:
                subtitle_path = subtitle_file.name

            try:
                logger.info(f"使用Edge TTS生成SRT字幕文件")

                # 按照字幕.py的方式生成SRT
                communicate = edge_tts.Communicate(text, self.voice)
                submaker = edge_tts.SubMaker()

                # 使用同步方式处理流
                for chunk in communicate.stream_sync():
                    if chunk["type"] == "WordBoundary":
                        submaker.feed(chunk)

                # 生成SRT字幕文件
                with open(subtitle_path, "w", encoding="utf-8") as file:
                    file.write(submaker.get_srt())

                logger.info(f"SRT字幕文件生成成功: {subtitle_path}")

                # 解析SRT字幕文件，获取原始时间戳信息
                original_subtitles = self._parse_srt_subtitle_file(subtitle_path)
                logger.info(f"解析SRT文件成功，获得{len(original_subtitles)}条原始字幕")

                # 使用jieba分词逻辑，但匹配Edge TTS的时间戳
                jieba_subtitles = self._create_jieba_subtitles_with_edge_timestamps(text, original_subtitles)

                logger.info(f"jieba分词+Edge TTS时间戳生成成功，共{len(jieba_subtitles)}条字幕")
                return jieba_subtitles

            finally:
                # 清理临时文件
                try:
                    if os.path.exists(subtitle_path):
                        os.unlink(subtitle_path)
                except Exception as e:
                    logger.warning(f"清理临时文件失败: {e}")

        except Exception as e:
            logger.error(f"Edge TTS字幕生成失败: {e}")
            raise

    def _create_jieba_subtitles_with_edge_timestamps(self, text: str, original_subtitles: List[Dict[str, Any]]) -> List[
        Dict[str, Any]]:
        """使用jieba分词逻辑，但匹配Edge TTS的时间戳"""
        try:
            # 1. 使用jieba分词创建字幕组
            jieba_groups = self._create_subtitle_groups_with_jieba_text_only(text)
            logger.info(f"jieba分词生成{len(jieba_groups)}个字幕组")

            # 2. 为每个jieba字幕组匹配Edge TTS时间戳
            matched_subtitles = []
            current_char_position = 0

            for i, jieba_text in enumerate(jieba_groups):
                # 保持原始文本，不在中间过程删除标点
                if not jieba_text.strip():
                    continue

                # 在原始文本中找到这个字幕组的位置
                start_char_pos = current_char_position
                end_char_pos = current_char_position + len(jieba_text)

                # 从Edge TTS字幕中匹配时间戳
                start_time_ms, end_time_ms = self._match_timestamps_for_text_range(
                    original_subtitles, text, start_char_pos, end_char_pos
                )

                # 应用倍速因子调整时间戳
                adjusted_start_ms = start_time_ms / self.speed_factor
                adjusted_end_ms = end_time_ms / self.speed_factor

                # 只在最终输出时去除开头结尾标点
                final_text = self._ensure_no_leading_punctuation(jieba_text)

                # 创建字幕条目
                subtitle = {
                    'subtitle_id': i + 1,
                    'text': final_text,
                    'timestamp': f"{adjusted_start_ms / 1000:.6f}s - {adjusted_end_ms / 1000:.6f}s",
                    'start_time_ms': int(adjusted_start_ms),
                    'end_time_ms': int(adjusted_end_ms),
                    'duration': int(adjusted_end_ms - adjusted_start_ms),
                    'char_count': len(final_text)
                }

                matched_subtitles.append(subtitle)
                current_char_position = end_char_pos

                logger.debug(f"字幕{i + 1}: '{jieba_text}' -> {start_time_ms}ms-{end_time_ms}ms")

            return matched_subtitles

        except Exception as e:
            logger.error(f"jieba分词+时间戳匹配失败: {e}")
            return []

    def _create_subtitle_groups_with_jieba_text_only(self, text: str) -> List[str]:
        """只使用jieba分词创建字幕文本组，不包含时间戳"""
        # 复用原有的jieba分词逻辑
        sentences = self._split_text_into_sentences(text)

        all_groups = []
        for sentence in sentences:
            if len(sentence) <= self.max_chars:
                all_groups.append(sentence)
            else:
                # 长句子需要进一步分割
                sub_groups = self._split_long_sentence_with_jieba(sentence)
                all_groups.extend(sub_groups)

        return all_groups

    def _split_text_into_sentences(self, text: str) -> List[str]:
        """将文本分割成句子"""
        return self._split_sentences_with_regex(text)

    def _match_timestamps_for_text_range(self, original_subtitles: List[Dict[str, Any]],
                                         full_text: str, start_char: int, end_char: int) -> tuple:
        """为指定文本范围匹配Edge TTS时间戳（改进版）"""
        try:
            # 使用改进的时间戳匹配策略
            start_time_ms, end_time_ms = self._smart_timestamp_matching(
                original_subtitles, full_text, start_char, end_char
            )

            return int(start_time_ms), int(end_time_ms)

        except Exception as e:
            logger.error(f"时间戳匹配失败: {e}")
            # 返回默认时间戳
            return start_char * 100, end_char * 100

    def _smart_timestamp_matching(self, original_subtitles: List[Dict[str, Any]],
                                  full_text: str, start_char: int, end_char: int) -> tuple:
        """智能时间戳匹配策略"""
        if not original_subtitles:
            return start_char * 100, end_char * 100

        # 策略1：基于字符比例的精确匹配
        total_duration = original_subtitles[-1]['end_time_ms'] - original_subtitles[0]['start_time_ms']
        total_chars = len(full_text)
        base_start_time = original_subtitles[0]['start_time_ms']

        if total_chars > 0:
            # 按字符比例计算时间戳
            char_ratio_start = start_char / total_chars
            char_ratio_end = end_char / total_chars

            start_time_ms = base_start_time + (char_ratio_start * total_duration)
            end_time_ms = base_start_time + (char_ratio_end * total_duration)

            # 策略2：尝试找到最接近的Edge TTS字幕边界进行微调
            adjusted_start, adjusted_end = self._adjust_with_subtitle_boundaries(
                original_subtitles, start_time_ms, end_time_ms, start_char, end_char, full_text
            )

            logger.debug(f"智能匹配字符{start_char}-{end_char}: {adjusted_start:.0f}ms-{adjusted_end:.0f}ms")
            return adjusted_start, adjusted_end
        else:
            return start_char * 100, end_char * 100

    def _adjust_with_subtitle_boundaries(self, original_subtitles: List[Dict[str, Any]],
                                         estimated_start: float, estimated_end: float,
                                         start_char: int, end_char: int, full_text: str) -> tuple:
        """根据Edge TTS字幕边界微调时间戳"""
        try:
            # 查找最接近的字幕边界
            best_start = estimated_start
            best_end = estimated_end

            # 获取目标文本片段
            target_text = full_text[start_char:end_char]

            # 在Edge TTS字幕中查找相似的文本片段
            for subtitle in original_subtitles:
                subtitle_text = subtitle['text']

                # 检查是否有文本重叠
                if self._has_text_overlap(target_text, subtitle_text):
                    # 根据重叠程度调整时间戳
                    overlap_ratio = self._calculate_overlap_ratio(target_text, subtitle_text)

                    if overlap_ratio > 0.3:  # 如果重叠度超过30%
                        # 使用加权平均调整时间戳
                        weight = min(overlap_ratio, 0.8)  # 最大权重80%

                        best_start = (1 - weight) * estimated_start + weight * subtitle['start_time_ms']
                        best_end = (1 - weight) * estimated_end + weight * subtitle['end_time_ms']

                        logger.debug(f"找到重叠字幕，重叠率{overlap_ratio:.2f}，调整时间戳")
                        break

            return best_start, best_end

        except Exception as e:
            logger.debug(f"边界调整失败: {e}")
            return estimated_start, estimated_end

    def _has_text_overlap(self, text1: str, text2: str) -> bool:
        """检查两个文本是否有重叠"""
        if not text1 or not text2:
            return False

        # 简单检查：是否有共同的字符序列
        min_len = min(len(text1), len(text2))
        if min_len < 2:
            return text1 in text2 or text2 in text1

        # 检查是否有长度>=2的共同子串
        for i in range(len(text1) - 1):
            substr = text1[i:i + 2]
            if substr in text2:
                return True

        return False

    def _calculate_overlap_ratio(self, text1: str, text2: str) -> float:
        """计算两个文本的重叠比例"""
        if not text1 or not text2:
            return 0.0

        # 计算共同字符数
        common_chars = 0
        text2_chars = set(text2)

        for char in text1:
            if char in text2_chars:
                common_chars += 1

        return common_chars / len(text1) if len(text1) > 0 else 0.0

    def _build_char_to_time_mapping(self, original_subtitles: List[Dict[str, Any]], full_text: str) -> Dict[int, Dict]:
        """构建字符位置到时间戳的映射（改进版，更健壮的匹配）"""
        char_to_time = {}

        # 方法1：尝试精确匹配
        success = self._build_mapping_exact_match(original_subtitles, full_text, char_to_time)

        if not success or len(char_to_time) < len(full_text) * 0.5:  # 如果匹配率低于50%
            logger.warning("精确匹配失败，使用模糊匹配")
            char_to_time.clear()
            # 方法2：使用模糊匹配
            self._build_mapping_fuzzy_match(original_subtitles, full_text, char_to_time)

        logger.debug(
            f"构建字符时间映射完成，共{len(char_to_time)}个字符，覆盖率: {len(char_to_time) / len(full_text) * 100:.1f}%")
        return char_to_time

    def _build_mapping_exact_match(self, original_subtitles: List[Dict[str, Any]], full_text: str,
                                   char_to_time: Dict) -> bool:
        """精确匹配方式构建映射"""
        current_pos = 0
        matched_chars = 0

        for subtitle in original_subtitles:
            subtitle_text = subtitle['text']
            start_time = subtitle['start_time_ms']
            end_time = subtitle['end_time_ms']

            # 在完整文本中查找这个字幕文本的位置
            text_start_pos = full_text.find(subtitle_text, current_pos)

            if text_start_pos != -1:
                # 为这个字幕文本的每个字符分配时间戳
                char_duration = (end_time - start_time) / len(subtitle_text) if len(subtitle_text) > 0 else 0

                for i, char in enumerate(subtitle_text):
                    char_pos = text_start_pos + i
                    char_start_time = start_time + (i * char_duration)
                    char_end_time = start_time + ((i + 1) * char_duration)

                    char_to_time[char_pos] = {
                        'start_time': char_start_time,
                        'end_time': char_end_time,
                        'char': char
                    }

                current_pos = text_start_pos + len(subtitle_text)
                matched_chars += len(subtitle_text)
            else:
                logger.debug(f"无法精确匹配字幕文本: '{subtitle_text}'")

        return matched_chars > 0

    def _build_mapping_fuzzy_match(self, original_subtitles: List[Dict[str, Any]], full_text: str, char_to_time: Dict):
        """模糊匹配方式构建映射（按比例分配）"""
        if not original_subtitles:
            return

        # 计算总时长和总字符数
        total_duration = original_subtitles[-1]['end_time_ms'] - original_subtitles[0]['start_time_ms']
        total_chars = len(full_text)

        if total_chars == 0:
            return

        # 按比例为每个字符分配时间戳
        char_duration = total_duration / total_chars
        start_time_offset = original_subtitles[0]['start_time_ms']

        for i, char in enumerate(full_text):
            char_start_time = start_time_offset + (i * char_duration)
            char_end_time = start_time_offset + ((i + 1) * char_duration)

            char_to_time[i] = {
                'start_time': char_start_time,
                'end_time': char_end_time,
                'char': char
            }

        logger.debug(f"使用模糊匹配为{total_chars}个字符分配时间戳")

    async def _generate_edge_tts_files(self, text: str, audio_path: str, subtitle_path: str):
        """异步生成Edge TTS音频和字幕文件"""
        communicate = edge_tts.Communicate(text, self.voice)
        submaker = edge_tts.SubMaker()

        # 生成音频和收集字幕信息
        with open(audio_path, "wb") as audio_file:
            async for chunk in communicate.stream():
                if chunk["type"] == "audio":
                    audio_file.write(chunk["data"])
                elif chunk["type"] == "WordBoundary":
                    # 使用SubMaker处理词边界信息
                    submaker.feed(chunk)

        # 生成SRT字幕文件
        with open(subtitle_path, "w", encoding="utf-8") as subtitle_file:
            subtitle_file.write(submaker.get_srt())

    def _create_vtt_from_word_boundaries(self, word_boundaries: List[Dict], text: str, subtitle_path: str):
        """从词边界信息创建VTT字幕文件"""
        try:
            with open(subtitle_path, 'w', encoding='utf-8') as f:
                f.write("WEBVTT\n\n")

                for i, boundary in enumerate(word_boundaries):
                    if 'AudioOffset' in boundary and 'Duration' in boundary and 'text' in boundary:
                        # 转换时间戳（从100纳秒单位转换为毫秒）
                        start_ms = boundary['AudioOffset'] / 10000  # 100纳秒 -> 毫秒
                        duration_ms = boundary['Duration'] / 10000
                        end_ms = start_ms + duration_ms

                        # 格式化时间戳
                        start_time = self._format_time_for_vtt(start_ms)
                        end_time = self._format_time_for_vtt(end_ms)

                        # 写入字幕
                        f.write(f"{start_time} --> {end_time}\n")
                        f.write(f"{boundary['text']['Text']}\n\n")

            logger.info(f"成功创建VTT字幕文件，共{len(word_boundaries)}条字幕")

        except Exception as e:
            logger.error(f"创建VTT字幕文件失败: {e}")
            # 创建一个简单的备用VTT文件
            with open(subtitle_path, 'w', encoding='utf-8') as f:
                f.write("WEBVTT\n\n")
                f.write("00:00:00.100 --> 00:00:03.000\n")
                f.write(f"{text}\n\n")

    def _format_time_for_vtt(self, ms: float) -> str:
        """将毫秒格式化为VTT时间格式"""
        total_seconds = ms / 1000
        hours = int(total_seconds // 3600)
        minutes = int((total_seconds % 3600) // 60)
        seconds = total_seconds % 60

        return f"{hours:02d}:{minutes:02d}:{seconds:06.3f}"

    def _parse_srt_subtitle_file(self, subtitle_path: str) -> List[Dict[str, Any]]:
        """解析SRT字幕文件，提取精确的时间戳信息"""
        subtitles = []

        try:
            with open(subtitle_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 解析SRT格式
            # SRT格式示例：
            # 1
            # 00:00:00,100 --> 00:00:00,487
            # 刺鼻

            blocks = content.strip().split('\n\n')

            for block in blocks:
                lines = block.strip().split('\n')
                if len(lines) >= 3:
                    # 第一行是序号
                    subtitle_id = lines[0].strip()

                    # 第二行是时间戳
                    time_line = lines[1].strip()
                    if ' --> ' in time_line:
                        time_parts = time_line.split(' --> ')
                        start_time_str = time_parts[0].strip()
                        end_time_str = time_parts[1].strip()

                        # 转换时间戳为毫秒
                        start_time_ms = self._parse_srt_time_to_ms(start_time_str)
                        end_time_ms = self._parse_srt_time_to_ms(end_time_str)

                        # 第三行及以后是字幕文本
                        text = '\n'.join(lines[2:]).strip()

                        if text:  # 确保文本不为空
                            subtitle = {
                                'subtitle_id': int(subtitle_id) if subtitle_id.isdigit() else len(subtitles) + 1,
                                'text': text,
                                'timestamp': f"{start_time_ms / 1000:.6f}s - {end_time_ms / 1000:.6f}s",
                                'start_time_ms': start_time_ms,
                                'end_time_ms': end_time_ms,
                                'duration': end_time_ms - start_time_ms,
                                'char_count': len(text)
                            }
                            subtitles.append(subtitle)

            logger.info(f"解析SRT字幕文件成功，共{len(subtitles)}条字幕")
            return subtitles

        except Exception as e:
            logger.error(f"解析SRT字幕文件失败: {e}")
            return []

    def _parse_srt_time_to_ms(self, time_str: str) -> int:
        """将SRT时间字符串转换为毫秒"""
        # 格式：00:00:00,100
        time_str = time_str.replace(',', '.')

        parts = time_str.split(':')
        if len(parts) == 3:
            hours = int(parts[0])
            minutes = int(parts[1])
            seconds_parts = parts[2].split('.')
            seconds = int(seconds_parts[0])
            milliseconds = int(seconds_parts[1]) if len(seconds_parts) > 1 else 0

            # 如果毫秒部分少于3位，需要补齐
            if len(seconds_parts) > 1:
                ms_str = seconds_parts[1]
                if len(ms_str) == 1:
                    milliseconds = int(ms_str) * 100
                elif len(ms_str) == 2:
                    milliseconds = int(ms_str) * 10
                else:
                    milliseconds = int(ms_str[:3])

            total_ms = (hours * 3600 + minutes * 60 + seconds) * 1000 + milliseconds
            return total_ms

        return 0

    def _parse_vtt_subtitle_file(self, subtitle_path: str) -> List[Dict[str, Any]]:
        """解析VTT字幕文件，提取精确的时间戳信息"""
        subtitles = []

        try:
            with open(subtitle_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 解析VTT格式
            # VTT格式示例：
            # 00:00:00.100 --> 00:00:00.487
            # 刺鼻

            lines = content.strip().split('\n')
            i = 0
            subtitle_id = 1

            while i < len(lines):
                line = lines[i].strip()

                # 跳过空行和WEBVTT标识
                if not line or line.startswith('WEBVTT') or line.startswith('NOTE'):
                    i += 1
                    continue

                # 查找时间戳行
                if '-->' in line:
                    # 解析时间戳
                    time_parts = line.split(' --> ')
                    if len(time_parts) == 2:
                        start_time_str = time_parts[0].strip()
                        end_time_str = time_parts[1].strip()

                        # 转换时间戳为毫秒
                        start_time_ms = self._parse_time_to_ms(start_time_str)
                        end_time_ms = self._parse_time_to_ms(end_time_str)

                        # 获取字幕文本（下一行）
                        i += 1
                        if i < len(lines):
                            text = lines[i].strip()

                            if text:  # 确保文本不为空
                                subtitle = {
                                    'subtitle_id': subtitle_id,
                                    'text': text,
                                    'timestamp': f"{start_time_ms / 1000:.6f}s - {end_time_ms / 1000:.6f}s",
                                    'start_time_ms': start_time_ms,
                                    'end_time_ms': end_time_ms,
                                    'duration': end_time_ms - start_time_ms,
                                    'char_count': len(text)
                                }
                                subtitles.append(subtitle)
                                subtitle_id += 1

                i += 1

            logger.info(f"解析VTT字幕文件成功，共{len(subtitles)}条字幕")
            return subtitles

        except Exception as e:
            logger.error(f"解析VTT字幕文件失败: {e}")
            return []

    def _parse_time_to_ms(self, time_str: str) -> int:
        """将时间字符串转换为毫秒"""
        # 格式：00:00:00.100 或 00:00:00,100
        time_str = time_str.replace(',', '.')

        parts = time_str.split(':')
        if len(parts) == 3:
            hours = int(parts[0])
            minutes = int(parts[1])
            seconds_parts = parts[2].split('.')
            seconds = int(seconds_parts[0])
            milliseconds = int(seconds_parts[1]) if len(seconds_parts) > 1 else 0

            # 如果毫秒部分少于3位，需要补齐
            if len(seconds_parts) > 1:
                ms_str = seconds_parts[1]
                if len(ms_str) == 1:
                    milliseconds = int(ms_str) * 100
                elif len(ms_str) == 2:
                    milliseconds = int(ms_str) * 10
                else:
                    milliseconds = int(ms_str[:3])

            total_ms = (hours * 3600 + minutes * 60 + seconds) * 1000 + milliseconds
            return total_ms

        return 0

    def _combine_subtitles_by_length(self, original_subtitles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """根据字数限制重新组合原始字幕"""
        if not original_subtitles:
            return []

        combined = []
        current_group = []
        current_text = ""
        current_start_time = None
        current_end_time = None

        for subtitle in original_subtitles:
            text = subtitle['text']

            # 去除开头和结尾标点
            cleaned_text = self._remove_trailing_punctuation(text)

            if not cleaned_text:  # 跳过空文本
                continue

            # 检查加入当前字幕是否会超过长度限制
            potential_text = current_text + cleaned_text

            if len(potential_text) <= self.max_chars or not current_text:
                # 可以合并
                current_group.append(subtitle)
                current_text = potential_text

                if current_start_time is None:
                    current_start_time = subtitle['start_time_ms']
                current_end_time = subtitle['end_time_ms']
            else:
                # 超过限制，保存当前组
                if current_group and current_text:
                    combined_subtitle = self._create_combined_subtitle(
                        current_group, current_text, current_start_time, current_end_time, len(combined) + 1
                    )
                    combined.append(combined_subtitle)

                # 开始新组
                current_group = [subtitle]
                current_text = cleaned_text
                current_start_time = subtitle['start_time_ms']
                current_end_time = subtitle['end_time_ms']

        # 处理最后一组
        if current_group and current_text:
            combined_subtitle = self._create_combined_subtitle(
                current_group, current_text, current_start_time, current_end_time, len(combined) + 1
            )
            combined.append(combined_subtitle)

        return combined

    def _create_combined_subtitle(self, group: List[Dict[str, Any]], text: str,
                                  start_time_ms: int, end_time_ms: int, subtitle_id: int) -> Dict[str, Any]:
        """创建合并后的字幕条目"""
        return {
            'subtitle_id': subtitle_id,
            'text': text,
            'timestamp': f"{start_time_ms / 1000:.6f}s - {end_time_ms / 1000:.6f}s",
            'start_time_ms': start_time_ms,
            'end_time_ms': end_time_ms,
            'duration': end_time_ms - start_time_ms,
            'char_count': len(text),
            'original_count': len(group)  # 记录合并了多少条原始字幕
        }

    def _generate_subtitles_sync(self, text: str) -> List[Dict[str, Any]]:
        """同步方式生成字幕（当已经在事件循环中时使用）"""
        try:
            logger.info("使用同步方式生成字幕（基于jieba分词）")

            # 直接使用jieba进行文本分割，不依赖Edge TTS的音频生成
            # 1. 模拟词汇边界数据
            mock_words = self._create_mock_word_boundaries(text)

            # 2. 使用jieba进行智能分割
            subtitle_groups = self._create_subtitle_groups_with_jieba(mock_words)

            # 3. 转换为兼容格式
            formatted_subtitles = self._format_to_volcengine_compatible(subtitle_groups)

            logger.info(f"同步方式生成 {len(formatted_subtitles)} 条字幕")
            return formatted_subtitles

        except Exception as e:
            logger.error(f"同步字幕生成失败: {e}")
            return []

    def _create_mock_word_boundaries(self, text: str) -> List[Dict[str, Any]]:
        """创建模拟的词汇边界数据"""
        words = []
        current_time = 100  # 开始时间100ms

        try:
            # 尝试使用jieba分词创建更合理的词汇边界
            import jieba
            jieba_words = list(jieba.cut(text))
            logger.debug("使用jieba分词创建词汇边界")
        except ImportError:
            # 如果jieba不可用，按字符分割
            jieba_words = list(text)
            logger.warning("jieba不可用，使用字符分割")

        for word in jieba_words:
            if word.strip():  # 跳过空白字符
                # 根据词汇长度和类型估算时长
                base_duration = 200  # 基础时长200ms
                char_duration = len(word) * 150  # 每字150ms
                if word in '。！？；，、：':
                    duration = 300  # 标点符号300ms
                else:
                    duration = max(base_duration, char_duration)

                words.append({
                    'text': word,
                    'start_time': current_time,
                    'end_time': current_time + duration
                })
                current_time += duration

        logger.debug(f"创建了 {len(words)} 个模拟词汇边界")
        return words

    def _extract_word_boundaries(self, submaker: edge_tts.SubMaker) -> List[Dict[str, Any]]:
        """从SubMaker提取词汇边界信息"""
        words = []

        # 获取SRT格式字幕并解析
        srt_content = submaker.get_srt()
        srt_blocks = srt_content.strip().split('\n\n')

        for i, block in enumerate(srt_blocks):
            lines = block.strip().split('\n')
            if len(lines) >= 3:
                # 解析时间戳
                time_line = lines[1]
                text = lines[2]

                # 解析开始和结束时间
                start_str, end_str = time_line.split(' --> ')
                start_ms = self._srt_time_to_ms(start_str)
                end_ms = self._srt_time_to_ms(end_str)

                words.append({
                    'text': text,
                    'start_time': start_ms,
                    'end_time': end_ms
                })

        return words

    def _srt_time_to_ms(self, time_str: str) -> int:
        """将SRT时间格式转换为毫秒"""
        # 格式: 00:00:00,100
        time_part, ms_part = time_str.split(',')
        h, m, s = map(int, time_part.split(':'))
        ms = int(ms_part)

        total_ms = h * 3600000 + m * 60000 + s * 1000 + ms
        return total_ms

    def _create_subtitle_groups_with_jieba(self, edge_tts_words: List[Dict]) -> List[List[Dict]]:
        """按逗号优先策略分割Edge TTS字幕"""

        # 1. 将所有词汇合并成完整文本
        full_text = ''.join([word['text'] for word in edge_tts_words])

        # 2. 使用逗号优先的分割策略
        sentences = self._split_sentences_with_regex(full_text)

        # 3. 将文本句子映射回原始的词汇时间信息
        subtitle_groups = self._map_sentences_to_word_timing(sentences, edge_tts_words)

        return subtitle_groups

    def _split_sentences_with_regex(self, text: str) -> List[str]:
        """直接按逗号分割，每个逗号分割的部分就是一个字幕（不保留逗号）"""
        # 优先按逗号分割，不保留逗号
        comma_parts = re.split(r'，', text)

        # 清理并收集逗号分割的部分
        comma_segments = []
        for part in comma_parts:
            if part.strip():
                comma_segments.append(part.strip())

        # 如果没有逗号，按句号、问号、感叹号、分号分割，不保留标点
        if len(comma_segments) <= 1:
            # 移除句末标点符号
            cleaned_text = re.sub(r'[。！？；]+$', '', text.strip())
            if cleaned_text:
                # 如果超过15字，需要分割
                if len(cleaned_text) > 15:
                    split_parts = self._split_long_sentence_by_middle(cleaned_text)
                    return split_parts
                else:
                    return [cleaned_text]
            else:
                return [text.strip()] if text.strip() else []

        # 有逗号的情况，进一步处理每个逗号分割的部分
        final_segments = []
        for segment in comma_segments:
            # 移除段末的句号、感叹号、问号、分号
            cleaned_segment = re.sub(r'[。！？；]+$', '', segment.strip())
            if cleaned_segment:
                final_segments.append(cleaned_segment)

        # 只对超过15字的片段进行二次分割
        optimized_segments = []
        for segment in final_segments:
            if len(segment) > 15:
                # 超过15字才进行分割
                split_parts = self._split_long_sentence_by_middle(segment)
                optimized_segments.extend(split_parts)
            else:
                # 15字以内直接保留
                optimized_segments.append(segment)

        return optimized_segments

    def _optimize_sentence_lengths(self, sentences: List[str]) -> List[str]:
        """
        优化句子长度：只分割过长的句子（超过15字），不合并短句
        保持逗号分割的独立性
        """
        if not sentences:
            return []

        optimized = []

        for sentence in sentences:
            current_length = len(sentence)

            # 如果句子长度在合理范围内（包括短句），直接添加
            if current_length <= 15:
                optimized.append(sentence)

            # 如果句子过长（超过15字），进行分割
            else:
                split_parts = self._split_long_sentence_by_middle(sentence)
                optimized.extend(split_parts)

        return optimized

    def _split_long_sentence_by_middle(self, sentence: str) -> List[str]:
        """
        将超过15字的句子从中间分割，保持语义完整性
        优先在标点符号处分割，其次在词汇边界分割
        """
        if len(sentence) <= 15:
            return [sentence]

        # 首先尝试在逗号、顿号、冒号等次级标点处分割
        secondary_punctuation = ['，', '、', '：', '；']
        for punct in secondary_punctuation:
            if punct in sentence:
                parts = sentence.split(punct)
                if len(parts) >= 2:
                    # 重新组合，保留标点符号
                    result = []
                    current_part = ""

                    for i, part in enumerate(parts[:-1]):
                        current_part += part + punct
                        if len(current_part) >= 8:  # 确保每部分至少8字
                            result.append(current_part)
                            current_part = ""

                    # 处理最后一部分
                    current_part += parts[-1]
                    if current_part:
                        if result and len(current_part) < 3:
                            # 如果最后一部分太短，合并到前一部分
                            result[-1] += current_part
                        else:
                            result.append(current_part)

                    # 检查分割结果是否合理
                    if all(3 <= len(part) <= 15 for part in result):
                        return result

        # 如果标点分割不理想，按字数从中间分割
        mid_point = len(sentence) // 2

        # 寻找中间点附近的合适分割位置（避免在词汇中间分割）
        best_split = mid_point

        # 在中间点前后5个字符范围内寻找最佳分割点
        for offset in range(6):
            for direction in [-1, 1]:
                split_pos = mid_point + direction * offset
                if 3 <= split_pos <= len(sentence) - 3:
                    # 检查这个位置是否适合分割（不在词汇中间）
                    if self._is_good_split_position(sentence, split_pos):
                        best_split = split_pos
                        break
            if best_split != mid_point:
                break

        # 执行分割
        first_part = sentence[:best_split]
        second_part = sentence[best_split:]

        result = []
        if len(first_part) >= 3:
            result.append(first_part)
        if len(second_part) >= 3:
            result.append(second_part)

        # 如果分割后有部分太短，递归处理
        final_result = []
        for part in result:
            if len(part) > 15:
                final_result.extend(self._split_long_sentence_by_middle(part))
            else:
                final_result.append(part)

        return final_result if final_result else [sentence]

    def _is_good_split_position(self, text: str, position: int) -> bool:
        """
        判断某个位置是否适合作为分割点
        优先选择词汇边界或自然停顿点
        """
        if position <= 0 or position >= len(text):
            return False

        # 检查前后字符
        prev_char = text[position - 1] if position > 0 else ''
        next_char = text[position] if position < len(text) else ''

        # 如果前一个字符是标点符号，这是一个好的分割点
        if prev_char in '，。！？；：、':
            return True

        # 如果下一个字符是标点符号，避免在这里分割
        if next_char in '，。！？；：、':
            return False

        # 其他情况认为是可接受的分割点
        return True

    def _split_long_sentence_with_jieba(self, sentence: str) -> List[str]:
        """智能分割超长句子，使用jieba分词并保持语义完整性"""
        try:
            import jieba
            import jieba.posseg as pseg
        except ImportError:
            logger.warning("jieba库不可用，使用简单分割")
            return self._split_by_punctuation_only(sentence)

        # 如果句子长度不超过15字，直接返回
        if len(sentence) <= 15:
            return [sentence]

        # 优先级分割策略：
        # 1. 先尝试按标点符号分割
        # 2. 如果标点分割后仍然超长，使用jieba分词进行语义分割

        # 第一优先级：按逗号、顿号、冒号分割
        parts = re.split(r'([，、：])', sentence)
        combined_parts = []
        for i in range(0, len(parts) - 1, 2):
            if i + 1 < len(parts):
                part = parts[i] + parts[i + 1]
                if part.strip():
                    combined_parts.append(part.strip())

        if len(parts) % 2 == 1 and parts[-1].strip():
            combined_parts.append(parts[-1].strip())

        # 检查分割后的部分是否都符合长度要求
        result = []
        for part in combined_parts:
            if len(part) <= 15:
                result.append(part)
            else:
                # 如果标点分割后仍然超长，使用jieba进行语义分割
                jieba_result = self._split_with_jieba_semantic(part)
                result.extend(jieba_result)

        return result

    def _split_by_punctuation_only(self, sentence: str) -> List[str]:
        """仅使用标点符号分割句子（jieba不可用时的备用方案）"""
        if len(sentence) <= 15:
            return [sentence]

        # 按逗号、顿号、冒号分割
        parts = re.split(r'([，、：])', sentence)
        combined_parts = []
        for i in range(0, len(parts) - 1, 2):
            if i + 1 < len(parts):
                part = parts[i] + parts[i + 1]
                if part.strip():
                    combined_parts.append(part.strip())

        if len(parts) % 2 == 1 and parts[-1].strip():
            combined_parts.append(parts[-1].strip())

        # 如果分割后仍然有超长的部分，按字符数强制分割
        result = []
        for part in combined_parts:
            if len(part) <= 15:
                result.append(part)
            else:
                # 强制按15字分割
                for i in range(0, len(part), 15):
                    chunk = part[i:i + 15]
                    if chunk.strip():
                        result.append(chunk)

        return result if result else [sentence]

    def _split_with_jieba_semantic(self, text: str) -> List[str]:
        """使用jieba进行语义分割，保持动宾短语完整性"""
        try:
            import jieba
            import jieba.posseg as pseg
        except ImportError:
            logger.warning("jieba库不可用，使用标点分割")
            return self._split_by_punctuation_only(text)

        # 使用jieba进行词性标注
        words = list(pseg.cut(text))

        # 构建语义分组
        groups = []
        current_group = []
        current_length = 0

        i = 0
        while i < len(words):
            word_pair = words[i]
            word = word_pair.word
            flag = word_pair.flag
            word_len = len(word)

            # 检查是否是动宾短语的开始（动词）
            if flag.startswith('v') and i + 1 < len(words):
                # 查找动宾短语的完整结构
                verb_phrase = self._extract_verb_phrase(words, i)
                verb_phrase_text = ''.join([w.word for w in verb_phrase])
                verb_phrase_len = len(verb_phrase_text)

                # 如果当前组加上动宾短语会超过15字
                if current_length + verb_phrase_len > 15:
                    # 如果当前组不为空且长度>=3，先保存当前组
                    if current_group and current_length >= 3:
                        groups.append(''.join([w.word for w in current_group]))
                        current_group = []
                        current_length = 0

                    # 如果动宾短语本身超过15字，单独成组
                    if verb_phrase_len > 15:
                        groups.append(verb_phrase_text)
                        i += len(verb_phrase)
                        continue

                # 将动宾短语加入当前组
                current_group.extend(verb_phrase)
                current_length += verb_phrase_len
                i += len(verb_phrase)
            else:
                # 普通词汇处理
                if current_length + word_len > 15:
                    # 如果当前组不为空且长度>=3，先保存当前组
                    if current_group and current_length >= 3:
                        groups.append(''.join([w.word for w in current_group]))
                        current_group = []
                        current_length = 0

                current_group.append(word_pair)
                current_length += word_len
                i += 1

        # 处理最后一组
        if current_group:
            last_group_text = ''.join([w.word for w in current_group])
            # 如果最后一组太短（<3字），尝试与前一组合并
            if len(last_group_text) < 3 and groups:
                last_saved = groups[-1]
                if len(last_saved) + len(last_group_text) <= 15:
                    groups[-1] = last_saved + last_group_text
                else:
                    groups.append(last_group_text)
            else:
                groups.append(last_group_text)

        # 确保没有空组
        groups = [g for g in groups if g.strip()]

        # 如果分割失败，返回原文本
        if not groups:
            return [text]

        return groups

    def _extract_verb_phrase(self, words: List, start_idx: int) -> List:
        """提取动宾短语"""
        verb_phrase = [words[start_idx]]  # 动词
        i = start_idx + 1

        # 查找动词后的宾语、补语等
        while i < len(words):
            word_pair = words[i]
            word = word_pair.word
            flag = word_pair.flag

            # 宾语（名词、代词）
            if flag.startswith('n') or flag.startswith('r'):
                verb_phrase.append(word_pair)
                i += 1
            # 补语、副词修饰
            elif flag.startswith('d') or flag.startswith('c'):
                verb_phrase.append(word_pair)
                i += 1
            # 助词（的、了、着等）
            elif flag in ['u', 'ul', 'uz', 'ug']:
                verb_phrase.append(word_pair)
                i += 1
            # 量词
            elif flag.startswith('q'):
                verb_phrase.append(word_pair)
                i += 1
            else:
                break

        return verb_phrase

    def _split_by_minor_punctuation(self, text: str) -> List[str]:
        """尝试在更细粒度的标点符号处分割"""
        # 尝试在书名号、引号、括号等处分割
        minor_punctuation_patterns = [
            r'([""''「」『』])',  # 引号
            r'([（）()【】\[\]])',  # 括号
            r'([《》〈〉])',  # 书名号
        ]

        for pattern in minor_punctuation_patterns:
            parts = re.split(pattern, text)
            if len(parts) > 1:  # 确实被分割了
                combined_parts = []
                for i in range(0, len(parts) - 1, 2):
                    if i + 1 < len(parts):
                        part = parts[i] + parts[i + 1]
                        if part.strip():
                            combined_parts.append(part.strip())

                if len(parts) % 2 == 1 and parts[-1].strip():
                    combined_parts.append(parts[-1].strip())

                # 检查是否所有部分都符合长度要求
                if all(len(part) <= self.max_chars for part in combined_parts):
                    return combined_parts

        # 如果所有标点分割都无法满足长度要求，返回空列表
        return []

    def _group_jieba_words_by_length(self, words: List[str]) -> List[str]:
        """
        将jieba分词结果按长度重新组合，确保每组不少于最小字数
        注意：这个方法现在主要用于处理已经在标点处分割的文本片段
        """
        groups = []
        current_group = ""

        for word in words:
            # 检查加入当前词是否会超长
            if len(current_group + word) <= self.max_chars:
                current_group += word
            else:
                # 检查当前组是否达到最小字数要求
                if len(current_group) >= self.min_chars:
                    # 达到最小要求，保存当前组
                    groups.append(current_group)
                    current_group = word
                else:
                    # 未达到最小要求，尝试强制加入当前词
                    if len(current_group + word) <= self.max_chars * 1.3:  # 允许超出30%
                        current_group += word
                    else:
                        # 实在太长，但为了避免在句子中间断开，还是保存当前组
                        if current_group:
                            groups.append(current_group)
                        current_group = word

        # 处理最后一组
        if current_group:
            # 如果最后一组太短，尝试与前一组合并
            if len(current_group) < self.min_chars and groups:
                last_group = groups[-1]
                if len(last_group + current_group) <= self.max_chars * 1.3:
                    groups[-1] = last_group + current_group
                else:
                    groups.append(current_group)
            else:
                groups.append(current_group)

        return groups

    def _map_sentences_to_word_timing(self, sentences: List[str], edge_tts_words: List[Dict]) -> List[List[Dict]]:
        """将分割后的句子映射回原始词汇的时间信息"""
        subtitle_groups = []
        word_index = 0

        for sentence in sentences:
            group = []
            sentence_chars_processed = 0

            while sentence_chars_processed < len(sentence) and word_index < len(edge_tts_words):
                word = edge_tts_words[word_index]
                word_text = word['text']

                # 检查当前词是否属于当前句子
                remaining_sentence = sentence[sentence_chars_processed:]
                if remaining_sentence.startswith(word_text):
                    group.append(word)
                    sentence_chars_processed += len(word_text)
                    word_index += 1
                else:
                    # 可能存在分词差异，跳过
                    word_index += 1

            if group:
                subtitle_groups.append(group)

        return subtitle_groups

    def _format_to_volcengine_compatible(self, subtitle_groups: List[List[Dict]]) -> List[Dict[str, Any]]:
        """转换为火山引擎兼容格式"""
        formatted_subtitles = []

        for i, group in enumerate(subtitle_groups):
            if not group:
                continue

            text = ''.join([w['text'] for w in group])

            # 去除字符串末尾的标点符号，但保留中间的标点
            cleaned_text = self._remove_trailing_punctuation(text)

            # 最终确保去除开头标点（双重保险）
            final_text = self._ensure_no_leading_punctuation(cleaned_text)

            start_time = group[0]['start_time']
            end_time = group[-1]['end_time']
            duration = end_time - start_time

            # 创建words列表（保持兼容性）
            words = []
            for word in group:
                words.append({
                    'attribute': {
                        'event': 'speech'
                    },
                    'text': word['text'],
                    'start_time': word['start_time'],
                    'end_time': word['end_time']
                })

            # 生成SRT格式的时间戳
            start_timestamp = self._milliseconds_to_srt_timestamp(start_time)
            end_timestamp = self._milliseconds_to_srt_timestamp(end_time)
            srt_timestamp = f"{start_timestamp} --> {end_timestamp}"

            subtitle = {
                'subtitle_id': i + 1,
                'text': final_text,
                'start_time': start_time,
                'end_time': end_time,
                'duration': duration,  # 保持毫秒
                'timestamp': srt_timestamp,
                'words': words,
                'char_count': len(final_text),
                'start_time_s': start_time / 1000.0,  # 添加秒格式
                'end_time_s': end_time / 1000.0,  # 添加秒格式
                'semantic_type': "Edge TTS识别"
            }
            formatted_subtitles.append(subtitle)

        return formatted_subtitles

    def _milliseconds_to_srt_timestamp(self, milliseconds: int) -> str:
        """将毫秒转换为SRT格式的时间戳 (HH:MM:SS,mmm)"""
        total_seconds = milliseconds // 1000
        ms = milliseconds % 1000

        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        seconds = total_seconds % 60

        return f"{hours:02d}:{minutes:02d}:{seconds:02d},{ms:03d}"

    def _remove_trailing_punctuation(self, text: str) -> str:
        """去除字符串开头和末尾的标点符号，但保留中间的标点"""
        if not text:
            return text

        # 定义需要去除的标点符号
        punctuation = '。！？；，、：'

        # 从开头去除标点符号
        cleaned_text = text.lstrip(punctuation)

        # 从末尾去除标点符号
        cleaned_text = cleaned_text.rstrip(punctuation)

        return cleaned_text

    def _ensure_no_leading_punctuation(self, text: str) -> str:
        """最终输出时去除开头和结尾标点符号"""
        if not text:
            return text

        # 定义标点符号
        punctuation = '。！？；，、：'

        # 去除开头和结尾标点
        cleaned = text.strip(punctuation)

        # 如果去除后为空，返回原文本
        if not cleaned.strip():
            return text

        return cleaned


class MockEdgeTTSSubtitleService:
    """模拟Edge TTS字幕服务（用于测试）"""

    def __init__(self, max_chars: int = 15, min_chars: int = 3, speed_factor: float = None):
        self.max_chars = max_chars
        self.min_chars = min_chars
        # 如果没有指定倍速因子，使用配置中的值
        self.speed_factor = speed_factor if speed_factor is not None else DEFAULT_SPEED_FACTOR
        logger.info(f"模拟Edge TTS字幕服务初始化完成，字数限制: {min_chars}-{max_chars}字, 倍速: {self.speed_factor}x")

    def process_audio_subtitle(self, text: str, max_retries: int = 3) -> List[Dict[str, Any]]:
        """模拟处理文本生成字幕"""
        logger.info(f"模拟处理Edge TTS字幕，文本: {text[:50]}...")

        # 按句号分割并确保字数合理
        sentences = re.split(r'([。！？；])', text)
        combined_sentences = []
        for i in range(0, len(sentences) - 1, 2):
            if i + 1 < len(sentences):
                sentence = sentences[i] + sentences[i + 1]
                if sentence.strip():
                    combined_sentences.append(sentence.strip())

        if len(sentences) % 2 == 1 and sentences[-1].strip():
            combined_sentences.append(sentences[-1].strip())

        # 合并过短的句子
        combined_sentences = self._merge_short_sentences(combined_sentences)

        # 生成模拟字幕
        mock_subtitles = []
        current_time = 0

        for i, sentence in enumerate(combined_sentences):
            if len(sentence) > self.max_chars:
                # 简单截断
                sentence = sentence[:self.max_chars]

            # 只在最终输出时去除开头结尾标点
            final_text = self._ensure_no_leading_punctuation(sentence)

            duration = len(final_text) * 300  # 每字300ms

            # 应用倍速因子调整时间戳
            adjusted_start_time = current_time / self.speed_factor
            adjusted_end_time = (current_time + duration) / self.speed_factor

            # 为模拟服务生成words字段
            words = []
            char_start_time = adjusted_start_time
            char_duration = (adjusted_end_time - adjusted_start_time) / len(final_text) if final_text else 0

            for j, char in enumerate(final_text):
                char_end_time = char_start_time + char_duration
                words.append({
                    'attribute': {
                        'event': 'speech'
                    },
                    'text': char,
                    'start_time': int(char_start_time),
                    'end_time': int(char_end_time)
                })
                char_start_time = char_end_time

            subtitle = {
                'subtitle_id': i + 1,
                'text': final_text,
                'start_time': int(adjusted_start_time),
                'end_time': int(adjusted_end_time),
                'duration': (adjusted_end_time - adjusted_start_time) / 1000.0,
                'timestamp': f"{adjusted_start_time / 1000:.6f}s - {adjusted_end_time / 1000:.6f}s",
                'words': words,
                'char_count': len(final_text),
                'semantic_type': "模拟Edge TTS识别"
            }
            mock_subtitles.append(subtitle)
            current_time += duration

        logger.info(f"模拟生成 {len(mock_subtitles)} 条字幕")
        return mock_subtitles

    def _remove_trailing_punctuation(self, text: str) -> str:
        """去除字符串开头和末尾的标点符号，但保留中间的标点"""
        if not text:
            return text

        # 定义需要去除的标点符号
        punctuation = '。！？；，、：'

        # 从开头去除标点符号
        cleaned_text = text.lstrip(punctuation)

        # 从末尾去除标点符号
        cleaned_text = cleaned_text.rstrip(punctuation)

        return cleaned_text

    def _ensure_no_leading_punctuation(self, text: str) -> str:
        """最终输出时去除开头和结尾标点符号"""
        if not text:
            return text

        # 定义标点符号
        punctuation = '。！？；，、：'

        # 去除开头和结尾标点
        cleaned = text.strip(punctuation)

        # 如果去除后为空，返回原文本
        if not cleaned.strip():
            return text

        return cleaned

    def _merge_short_sentences(self, sentences: List[str]) -> List[str]:
        """合并过短的句子，确保每条字幕不少于最小字数"""
        if not sentences:
            return sentences

        merged = []
        current_group = ""

        for sentence in sentences:
            # 去除标点后检查长度
            cleaned = self._remove_trailing_punctuation(sentence)

            if not cleaned:  # 空句子跳过
                continue

            # 如果当前组为空，直接加入
            if not current_group:
                current_group = sentence
            else:
                # 检查合并后是否超过最大长度
                potential_merge = current_group + sentence
                cleaned_merge = self._remove_trailing_punctuation(potential_merge)

                if len(cleaned_merge) <= self.max_chars:
                    # 不超过最大长度，合并
                    current_group = potential_merge
                else:
                    # 超过最大长度，检查当前组是否达到最小要求
                    cleaned_current = self._remove_trailing_punctuation(current_group)
                    if len(cleaned_current) >= self.min_chars:
                        # 达到最小要求，保存当前组
                        merged.append(current_group)
                        current_group = sentence
                    else:
                        # 未达到最小要求，强制合并（允许超出一些）
                        current_group = potential_merge

        # 处理最后一组
        if current_group:
            cleaned_current = self._remove_trailing_punctuation(current_group)
            if len(cleaned_current) < self.min_chars and merged:
                # 最后一组太短，尝试与前一组合并
                last_group = merged[-1]
                potential_merge = last_group + current_group
                cleaned_merge = self._remove_trailing_punctuation(potential_merge)

                if len(cleaned_merge) <= self.max_chars * 1.2:  # 允许超出20%
                    merged[-1] = potential_merge
                else:
                    merged.append(current_group)
            else:
                merged.append(current_group)

        return merged


def create_edge_tts_subtitle_service(use_mock: bool = False, voice: str = "zh-CN-XiaoyiNeural", max_chars: int = 15,
                                     min_chars: int = 3, speed_factor: float = None):
    """
    创建Edge TTS字幕服务

    Args:
        use_mock: 是否使用模拟服务
        voice: 语音类型
        max_chars: 每条字幕最大字符数
        min_chars: 每条字幕最小字符数
        speed_factor: 倍速因子，如果为None则使用配置中的值

    Returns:
        字幕服务实例
    """
    if use_mock:
        return MockEdgeTTSSubtitleService(max_chars=max_chars, min_chars=min_chars, speed_factor=speed_factor)
    else:
        return EdgeTTSSubtitleService(voice=voice, max_chars=max_chars, min_chars=min_chars, speed_factor=speed_factor)
