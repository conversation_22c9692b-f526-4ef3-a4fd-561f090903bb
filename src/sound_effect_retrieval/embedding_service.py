"""
音效文本嵌入服务
使用火山引擎的文本嵌入API进行文本向量化
"""

import os
import logging
from typing import List, Union
from volcenginesdkarkruntime import Ark

logger = logging.getLogger(__name__)


class EmbeddingService:
    """文本嵌入服务类"""
    
    def __init__(self, api_key: str = None, base_url: str = None):
        """
        初始化嵌入服务
        
        Args:
            api_key: 火山引擎API密钥，如果为None则从环境变量获取
            base_url: API基础URL，默认使用火山引擎地址
        """
        self.api_key = api_key or os.getenv('ARK_API_KEY')
        self.base_url = base_url or "https://ark.cn-beijing.volces.com/api/v3"
        self.model = "doubao-embedding-text-240715"
        
        if not self.api_key:
            raise ValueError("API密钥未设置，请设置环境变量ARK_API_KEY或传入api_key参数")
        
        # 初始化客户端
        self.client = Ark(
            api_key=self.api_key,
            base_url=self.base_url
        )
        
        logger.info(f"嵌入服务初始化完成，使用模型: {self.model}")
    
    def embed_text(self, text: Union[str, List[str]]) -> List[List[float]]:
        """
        对文本进行嵌入编码
        
        Args:
            text: 单个文本字符串或文本列表
            
        Returns:
            嵌入向量列表，每个向量是一个浮点数列表
        """
        try:
            # 确保输入是列表格式
            if isinstance(text, str):
                input_texts = [text]
            else:
                input_texts = text
            
            logger.debug(f"开始嵌入 {len(input_texts)} 个文本")
            
            # 调用火山引擎嵌入API
            response = self.client.embeddings.create(
                model=self.model,
                input=input_texts
            )
            
            # 提取嵌入向量
            embeddings = []
            for data in response.data:
                embeddings.append(data.embedding)
            
            logger.debug(f"成功获取 {len(embeddings)} 个嵌入向量")
            return embeddings
            
        except Exception as e:
            logger.error(f"文本嵌入失败: {str(e)}")
            raise
    
    def embed_single_text(self, text: str) -> List[float]:
        """
        对单个文本进行嵌入编码
        
        Args:
            text: 文本字符串
            
        Returns:
            嵌入向量（浮点数列表）
        """
        embeddings = self.embed_text(text)
        return embeddings[0] if embeddings else []
    
    def get_embedding_dimension(self) -> int:
        """
        获取嵌入向量的维度
        
        Returns:
            向量维度
        """
        # 使用一个测试文本获取维度
        test_embedding = self.embed_single_text("测试文本")
        return len(test_embedding)
