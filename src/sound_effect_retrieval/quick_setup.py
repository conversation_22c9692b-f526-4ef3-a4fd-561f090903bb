#!/usr/bin/env python3
"""
音效检索系统快速设置脚本
一键创建索引并导入数据
"""

import os
import sys
import logging
from pathlib import Path
from dotenv import load_dotenv
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
# 读取.env文件
dotenv_path = project_root / '.env'
load_dotenv(dotenv_path=dotenv_path,verbose=True,override=True)
from src.sound_effect_retrieval.sound_effect_manager import SoundEffectManager

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def main():
    """主函数"""
    print("🚀 音效检索系统快速设置")
    print("=" * 40)
    
    # 检查环境变量
    required_vars = ['ARK_API_KEY', 'PINECONE_API_KEY', 'PINECONE_INDEX_NAME']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ 缺少环境变量: {missing_vars}")
        print("\n请设置:")
        print("export ARK_API_KEY='your_ark_key'")
        print("export PINECONE_API_KEY='your_pinecone_key'") 
        print("export PINECONE_INDEX_NAME='your_index_name'")
        return
    
    # 检查数据文件
    data_file = "resources/sound_effect_with_descriptions.json"
    if not os.path.exists(data_file):
        print(f"❌ 数据文件不存在: {data_file}")
        return
    
    try:
        print("📦 创建音效管理器...")
        manager = SoundEffectManager.create_from_env()
        
        print("🔨 构建索引并导入数据...")
        manager.build_index_from_json(data_file, batch_size=50)
        
        print("📊 获取统计信息...")
        stats = manager.get_stats()
        
        print(f"\n✅ 设置完成!")
        print(f"   索引: {os.getenv('PINECONE_INDEX_NAME')}")
        print(f"   音效数: {stats.get('total_vectors', 0)}")
        print(f"   维度: {stats.get('dimension', 'N/A')}")
        
        print(f"\n🔍 测试检索...")
        results = manager.search("古琴", top_k=2)
        if results:
            print(f"   找到 {len(results)} 个结果")
            print(f"   最佳匹配: {results[0]['description'][:50]}...")
        
        print(f"\n🎉 系统就绪!")
        
    except Exception as e:
        print(f"❌ 设置失败: {str(e)}")
        logger.exception("设置异常")


if __name__ == "__main__":
    main()
