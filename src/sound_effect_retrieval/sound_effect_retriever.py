"""
音效检索器
提供音效检索接口
"""

import logging
from typing import List, Dict, Any, Optional

from .embedding_service import EmbeddingService
from .vector_database import VectorDatabase

logger = logging.getLogger(__name__)


class SoundEffectRetriever:
    """音效检索器类"""
    
    def __init__(self, embedding_service: EmbeddingService, vector_db: VectorDatabase):
        """
        初始化音效检索器
        
        Args:
            embedding_service: 嵌入服务实例
            vector_db: 向量数据库实例
        """
        self.embedding_service = embedding_service
        self.vector_db = vector_db
        
        # 安全连接到向量数据库（如果不存在则创建）
        try:
            # 获取嵌入向量维度
            dimension = self.embedding_service.get_embedding_dimension()
            self.vector_db.safe_connect_or_create(dimension=dimension)
        except Exception as e:
            logger.warning(f"无法连接到向量数据库: {str(e)}")
            logger.info("检索器初始化完成，但数据库连接失败，需要先构建索引")

        logger.info("音效检索器初始化完成")
    
    def search_sound_effects(self, query: str, top_k: int = 5, 
                           category_filter: List[str] = None,
                           min_score: float = 0.0) -> List[Dict[str, Any]]:
        """
        根据查询文本检索相似音效
        
        Args:
            query: 查询文本
            top_k: 返回最相似的k个结果，默认5个
            category_filter: 类别过滤器，只返回指定类别的音效
            min_score: 最小相似度分数阈值
            
        Returns:
            音效检索结果列表，每个结果包含url、label、description、score等字段
        """
        try:
            # logger.info(f"开始检索音效，查询: '{query}', top_k: {top_k}")
            
            # 1. 对查询文本进行嵌入编码
            query_embedding = self.embedding_service.embed_single_text(query)
            
            # 2. 准备过滤条件
            filter_dict = None
            if category_filter:
                # 构建类别过滤条件
                filter_dict = {
                    "category_path": {"$in": category_filter}
                }
            
            # 3. 在向量数据库中搜索相似音效
            search_results = self.vector_db.search_similar(
                query_vector=query_embedding,
                top_k=top_k,
                filter_dict=filter_dict
            )
            
            # 4. 格式化结果
            formatted_results = []
            for result in search_results:
                # 过滤低分结果
                if result['score'] < min_score:
                    continue
                
                metadata = result['metadata']
                formatted_result = {
                    'url': metadata.get('url', ''),
                    'label': metadata.get('label', []),
                    'description': metadata.get('description', ''),
                    'score': result['score'],
                    'file_name': metadata.get('file_name', ''),
                    'category_path': metadata.get('category_path', [])
                }
                formatted_results.append(formatted_result)
            
            logger.info(f"检索完成：{formatted_results}")
            return formatted_results
            
        except Exception as e:
            logger.error(f"音效检索失败: {str(e)}")
            raise
    
    def search_by_keywords(self, keywords: List[str], top_k: int = 5,
                          combine_mode: str = "any") -> List[Dict[str, Any]]:
        """
        根据关键词列表检索音效
        
        Args:
            keywords: 关键词列表
            top_k: 返回最相似的k个结果
            combine_mode: 关键词组合模式，"any"表示任意匹配，"all"表示全部匹配
            
        Returns:
            音效检索结果列表
        """
        try:
            if combine_mode == "any":
                # 任意匹配模式：将关键词用空格连接
                query = " ".join(keywords)
            elif combine_mode == "all":
                # 全部匹配模式：将关键词用"和"连接
                query = " 和 ".join(keywords)
            else:
                raise ValueError(f"不支持的组合模式: {combine_mode}")
            
            logger.info(f"关键词检索，关键词: {keywords}, 组合模式: {combine_mode}")
            
            return self.search_sound_effects(query, top_k)
            
        except Exception as e:
            logger.error(f"关键词检索失败: {str(e)}")
            raise
    
    def search_by_category(self, category: str, query: str = "", 
                          top_k: int = 10) -> List[Dict[str, Any]]:
        """
        在指定类别中检索音效
        
        Args:
            category: 音效类别
            query: 查询文本，如果为空则返回该类别下的所有音效
            top_k: 返回结果数量
            
        Returns:
            音效检索结果列表
        """
        try:
            logger.info(f"类别检索，类别: '{category}', 查询: '{query}'")
            
            if query:
                # 有查询文本时，在指定类别中搜索
                return self.search_sound_effects(
                    query=query,
                    top_k=top_k,
                    category_filter=[category]
                )
            else:
                # 无查询文本时，返回该类别下的所有音效
                # 使用一个通用查询来获取该类别的音效
                return self.search_sound_effects(
                    query=category,
                    top_k=top_k,
                    category_filter=[category]
                )
                
        except Exception as e:
            logger.error(f"类别检索失败: {str(e)}")
            raise
    
    def get_similar_sound_effects(self, reference_url: str, 
                                 top_k: int = 5) -> List[Dict[str, Any]]:
        """
        根据参考音效URL找到相似的音效
        
        Args:
            reference_url: 参考音效的URL
            top_k: 返回最相似的k个结果
            
        Returns:
            相似音效列表
        """
        try:
            logger.info(f"查找相似音效，参考URL: {reference_url}")
            
            # 首先通过URL查找参考音效的描述
            # 这里需要实现一个根据URL查找音效信息的方法
            # 暂时使用URL作为查询文本
            query = reference_url.split('/')[-1]  # 使用文件名作为查询
            
            return self.search_sound_effects(query, top_k)
            
        except Exception as e:
            logger.error(f"查找相似音效失败: {str(e)}")
            raise
    
    def get_database_stats(self) -> Dict[str, Any]:
        """
        获取音效数据库统计信息
        
        Returns:
            数据库统计信息
        """
        try:
            stats = self.vector_db.get_index_stats()
            
            # 格式化统计信息
            formatted_stats = {
                'total_vectors': stats.get('total_vector_count', 0),
                'dimension': stats.get('dimension', 0),
                'index_fullness': stats.get('index_fullness', 0.0),
                'namespaces': stats.get('namespaces', {})
            }
            
            logger.info(f"数据库统计信息: {formatted_stats}")
            return formatted_stats
            
        except Exception as e:
            logger.error(f"获取数据库统计信息失败: {str(e)}")
            raise
    
    def validate_connection(self) -> bool:
        """
        验证与向量数据库的连接
        
        Returns:
            连接是否正常
        """
        try:
            stats = self.vector_db.get_index_stats()
            logger.info("向量数据库连接正常")
            return True
        except Exception as e:
            logger.error(f"向量数据库连接异常: {str(e)}")
            return False
