"""
音效检索系统使用示例
"""

import os
import logging
import sys
from pathlib import Path
from src.sound_effect_retrieval.sound_effect_manager import SoundEffectManager
# 读取.env文件
from dotenv import load_dotenv
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

dotenv_path = project_root / '.env'
load_dotenv(dotenv_path=dotenv_path,verbose=True,override=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """主函数 - 演示音效检索系统的使用"""
    
    try:
        # 1. 创建音效管理器
        print("=== 初始化音效管理器 ===")
        manager = SoundEffectManager()
        
        # 2. 验证连接
        print("=== 验证连接 ===")
        if manager.validate_connection():
            print("✓ 连接正常")
        else:
            print("✗ 连接异常")
            return
        
        # 4. 演示音效检索
        print("\n=== 音效检索演示 ===")
        
        # 示例查询
        queries = [
            "古琴轻拨",
            "马蹄声",
            "竹笛悠扬",
            "风声",
            "脚步声"
        ]
        
        for query in queries:
            print(f"\n查询: '{query}'")
            results = manager.search(query, top_k=1)
            
            if results:
                for i, result in enumerate(results, 1):
                    print(f"  {i}. {result['description']}")
                    print(f"     URL: {result['url']}")
                    print(f"     相似度: {result['score']:.3f}")
                    print(f"     标签: {result['label']}")
            else:
                print("  未找到相关音效")
        
        # 5. 关键词检索演示
        print("\n=== 关键词检索演示 ===")
        keywords = ["古风", "武侠", "战斗"]
        print(f"关键词: {keywords}")
        
        results = manager.search_by_keywords(keywords, top_k=3)
        for i, result in enumerate(results, 1):
            print(f"  {i}. {result['description']}")
            print(f"     相似度: {result['score']:.3f}")
        
        # 6. 类别检索演示
        print("\n=== 类别检索演示 ===")
        category = "古风音效库整合"
        print(f"类别: '{category}'")
        
        results = manager.search_by_category(category, query="音乐", top_k=3)
        for i, result in enumerate(results, 1):
            print(f"  {i}. {result['description']}")
            print(f"     相似度: {result['score']:.3f}")
        
        # 7. 显示统计信息
        print("\n=== 数据库统计信息 ===")
        stats = manager.get_stats()
        print(f"总音效数量: {stats['total_vectors']}")
        print(f"向量维度: {stats['dimension']}")
        print(f"索引使用率: {stats['index_fullness']:.2%}")
        
        print("\n=== 演示完成 ===")
        
    except Exception as e:
        logger.error(f"演示过程中发生错误: {str(e)}")
        raise


def test_specific_query():
    """测试特定查询"""
    
    try:
        manager = SoundEffectManager.create_from_env()
        
        # 测试查询
        test_queries = [
            "古代战场上的马蹄声",
            "竹林中的风声",
            "古琴弹奏的声音",
            "武侠打斗的音效",
            "古代宫廷的氛围音效"
        ]
        
        print("=== 特定查询测试 ===")
        for query in test_queries:
            print(f"\n查询: '{query}'")
            results = manager.search(query, top_k=5, min_score=0.5)
            
            if results:
                for i, result in enumerate(results, 1):
                    print(f"  {i}. {result['description']}")
                    print(f"     相似度: {result['score']:.3f}")
                    print(f"     文件: {result['file_name']}")
            else:
                print("  未找到相关音效（相似度 > 0.5）")
                
    except Exception as e:
        logger.error(f"测试查询失败: {str(e)}")
        raise


if __name__ == "__main__":
    # 设置环境变量示例（实际使用时应该在环境中设置）
    # os.environ['VOLCENGINE_API_KEY'] = 'your_volcengine_api_key'
    # os.environ['PINECONE_API_KEY'] = 'your_pinecone_api_key'
    # os.environ['PINECONE_ENVIRONMENT'] = 'us-east-1-aws'
    
    # 运行主演示
    main()
    
    # 运行特定查询测试
    # test_specific_query()
