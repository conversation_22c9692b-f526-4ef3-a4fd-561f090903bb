"""
音效索引构建器
负责处理音效数据并构建向量索引
"""

import json
import logging
import hashlib
from typing import Dict, List, Any, Tuple
from pathlib import Path

from .embedding_service import EmbeddingService
from .vector_database import VectorDatabase

logger = logging.getLogger(__name__)


class SoundEffectIndexer:
    """音效索引构建器"""
    
    def __init__(self, embedding_service: EmbeddingService, vector_db: VectorDatabase):
        """
        初始化索引构建器
        
        Args:
            embedding_service: 嵌入服务实例
            vector_db: 向量数据库实例
        """
        self.embedding_service = embedding_service
        self.vector_db = vector_db
        
        logger.info("音效索引构建器初始化完成")
    
    def load_sound_effects_data(self, json_file_path: str) -> Dict[str, Any]:
        """
        加载音效数据文件
        
        Args:
            json_file_path: JSON文件路径
            
        Returns:
            音效数据字典
        """
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            logger.info(f"成功加载音效数据文件: {json_file_path}")
            return data
            
        except Exception as e:
            logger.error(f"加载音效数据文件失败: {str(e)}")
            raise
    
    def extract_sound_effects(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        从嵌套的音效数据中提取所有音效信息
        
        Args:
            data: 音效数据字典
            
        Returns:
            音效信息列表，每个元素包含url、label、description等字段
        """
        sound_effects = []
        
        def extract_recursive(obj, current_path=[]):
            """递归提取音效数据"""
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if isinstance(value, dict):
                        # 检查是否是音效文件条目（包含url、label、description字段）
                        if 'url' in value and 'label' in value and 'description' in value:
                            # 生成唯一ID
                            sound_id = self._generate_sound_id(value['url'])
                            
                            sound_effect = {
                                'id': sound_id,
                                'url': value['url'],
                                'label': value['label'],
                                'description': value['description'],
                                'file_name': key,
                                'category_path': current_path.copy()
                            }
                            sound_effects.append(sound_effect)
                        else:
                            # 继续递归搜索
                            extract_recursive(value, current_path + [key])
        
        extract_recursive(data)
        
        logger.info(f"提取到 {len(sound_effects)} 个音效")
        return sound_effects
    
    def _generate_sound_id(self, url: str) -> str:
        """
        根据URL生成唯一的音效ID
        
        Args:
            url: 音效文件URL
            
        Returns:
            唯一ID字符串
        """
        return hashlib.md5(url.encode('utf-8')).hexdigest()
    
    def build_index(self, json_file_path: str, batch_size: int = 100) -> None:
        """
        构建音效向量索引
        
        Args:
            json_file_path: 音效数据JSON文件路径
            batch_size: 批处理大小
        """
        try:
            # 1. 加载音效数据
            logger.info("开始构建音效向量索引")
            data = self.load_sound_effects_data(json_file_path)
            
            # 2. 提取音效信息
            sound_effects = self.extract_sound_effects(data)
            
            if not sound_effects:
                logger.warning("未找到任何音效数据")
                return
            
            # 3. 获取嵌入向量维度并创建索引
            dimension = self.embedding_service.get_embedding_dimension()
            self.vector_db.create_index(dimension)
            
            # 4. 批量处理音效数据
            total_batches = (len(sound_effects) + batch_size - 1) // batch_size
            
            for i in range(0, len(sound_effects), batch_size):
                batch = sound_effects[i:i + batch_size]
                batch_num = i // batch_size + 1
                
                logger.info(f"处理批次 {batch_num}/{total_batches}，包含 {len(batch)} 个音效")
                
                # 准备文本列表进行嵌入
                texts = []
                for effect in batch:
                    # 组合描述文本和标签信息
                    text = self._prepare_text_for_embedding(effect)
                    texts.append(text)
                
                # 获取嵌入向量
                embeddings = self.embedding_service.embed_text(texts)
                
                # 准备向量数据
                vectors = []
                for j, effect in enumerate(batch):
                    vector_data = (
                        effect['id'],
                        embeddings[j],
                        {
                            'url': effect['url'],
                            'label': effect['label'],
                            'description': effect['description'],
                            'file_name': effect['file_name'],
                            'category_path': effect['category_path']
                        }
                    )
                    vectors.append(vector_data)
                
                # 插入向量数据库
                self.vector_db.upsert_vectors(vectors)
                
                logger.info(f"批次 {batch_num} 处理完成")
            
            # 5. 获取索引统计信息
            stats = self.vector_db.get_index_stats()
            logger.info(f"索引构建完成，统计信息: {stats}")
            
        except Exception as e:
            logger.error(f"构建索引失败: {str(e)}")
            raise
    
    def _prepare_text_for_embedding(self, sound_effect: Dict[str, Any]) -> str:
        """
        准备用于嵌入的文本
        
        Args:
            sound_effect: 音效信息字典
            
        Returns:
            组合后的文本字符串
        """
        # 组合描述、标签和文件名信息
        text_parts = []
        
        # 添加描述
        if sound_effect.get('description'):
            text_parts.append(sound_effect['description'])
        
        # 添加标签信息
        if sound_effect.get('label'):
            labels = sound_effect['label']
            if isinstance(labels, list):
                text_parts.extend(labels)
            else:
                text_parts.append(str(labels))
        
        # 添加文件名（去除扩展名）
        if sound_effect.get('file_name'):
            file_name = Path(sound_effect['file_name']).stem
            text_parts.append(file_name)
        
        return ' '.join(text_parts)
    
    def update_sound_effect(self, sound_effect: Dict[str, Any]) -> None:
        """
        更新单个音效的向量索引
        
        Args:
            sound_effect: 音效信息字典
        """
        try:
            # 准备文本并获取嵌入向量
            text = self._prepare_text_for_embedding(sound_effect)
            embedding = self.embedding_service.embed_single_text(text)
            
            # 生成ID
            sound_id = self._generate_sound_id(sound_effect['url'])
            
            # 准备向量数据
            vector_data = [(
                sound_id,
                embedding,
                {
                    'url': sound_effect['url'],
                    'label': sound_effect['label'],
                    'description': sound_effect['description'],
                    'file_name': sound_effect.get('file_name', ''),
                    'category_path': sound_effect.get('category_path', [])
                }
            )]
            
            # 更新向量数据库
            self.vector_db.upsert_vectors(vector_data)
            
            logger.info(f"音效 {sound_id} 更新成功")
            
        except Exception as e:
            logger.error(f"更新音效失败: {str(e)}")
            raise
