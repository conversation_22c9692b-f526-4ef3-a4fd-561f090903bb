"""
Pinecone向量数据库管理
"""

import os
import logging
from typing import List, Dict, Any, Optional, Tuple
import pinecone
from pinecone import Pinecone, ServerlessSpec
import json

logger = logging.getLogger(__name__)


class VectorDatabase:
    """Pinecone向量数据库管理类"""
    
    def __init__(self, api_key: str = None, environment: str = None, index_name: str = None):
        """
        初始化向量数据库
        
        Args:
            api_key: Pinecone API密钥
            environment: Pinecone环境
            index_name: 索引名称
        """
        self.api_key = api_key or os.getenv('PINECONE_API_KEY')
        self.environment = environment or os.getenv('PINECONE_ENVIRONMENT', 'us-east-1-aws')
        self.index_name = index_name or os.getenv('PINECONE_INDEX_NAME')

        if not self.api_key:
            raise ValueError("Pinecone API密钥未设置，请设置环境变量PINECONE_API_KEY或传入api_key参数")

        if not self.index_name:
            raise ValueError("索引名称未设置，请设置环境变量PINECONE_INDEX_NAME或传入index_name参数")
        
        # 初始化Pinecone客户端
        self.pc = Pinecone(api_key=self.api_key)
        self.index = None
        
        logger.info(f"向量数据库初始化完成，索引名称: {self.index_name}")
    
    def create_index(self, dimension: int, metric: str = "cosine") -> None:
        """
        创建索引
        
        Args:
            dimension: 向量维度
            metric: 相似度度量方式，默认为cosine
        """
        try:
            # 检查索引是否已存在
            existing_indexes = [index.name for index in self.pc.list_indexes()]
            
            if self.index_name in existing_indexes:
                logger.info(f"索引 {self.index_name} 已存在")
            else:
                logger.info(f"创建索引 {self.index_name}，维度: {dimension}")
                
                self.pc.create_index(
                    name=self.index_name,
                    dimension=dimension,
                    metric=metric,
                    spec=ServerlessSpec(
                        cloud='aws',
                        region=self.environment
                    )
                )
                logger.info(f"索引 {self.index_name} 创建成功")
            
            # 连接到索引
            self.index = self.pc.Index(self.index_name)
            
        except Exception as e:
            logger.error(f"创建索引失败: {str(e)}")
            raise
    
    def connect_to_index(self) -> None:
        """连接到现有索引"""
        try:
            self.index = self.pc.Index(self.index_name)
            logger.info(f"成功连接到索引: {self.index_name}")
        except Exception as e:
            logger.error(f"连接索引失败: {str(e)}")
            raise

    def safe_connect_or_create(self, dimension: Optional[int] = None, metric: str = "cosine") -> None:
        """
        安全连接到索引，如果不存在则创建

        Args:
            dimension: 向量维度（如果需要创建索引），如果为None则需要外部提供
            metric: 相似度度量方式
        """
        try:
            # 首先尝试连接到现有索引
            self.index = self.pc.Index(self.index_name)
            logger.info(f"成功连接到现有索引: {self.index_name}")
        except Exception as e:
            logger.warning(f"连接索引失败: {str(e)}")
            logger.info(f"尝试创建新索引: {self.index_name}")

            if dimension is None:
                raise ValueError("创建索引需要指定维度参数")

            try:
                # 创建新索引
                self.create_index(dimension, metric)
                logger.info(f"新索引创建并连接成功: {self.index_name}")
            except Exception as create_error:
                logger.error(f"创建索引失败: {str(create_error)}")
                raise

    def index_exists(self) -> bool:
        """
        检查索引是否存在

        Returns:
            索引是否存在
        """
        try:
            existing_indexes = [index.name for index in self.pc.list_indexes()]
            return self.index_name in existing_indexes
        except Exception as e:
            logger.error(f"检查索引存在性失败: {str(e)}")
            return False
    
    def upsert_vectors(self, vectors: List[Tuple[str, List[float], Dict[str, Any]]]) -> None:
        """
        插入或更新向量
        
        Args:
            vectors: 向量列表，每个元素为(id, vector, metadata)的元组
        """
        try:
            if not self.index:
                raise ValueError("索引未连接，请先调用create_index或connect_to_index")
            
            logger.info(f"开始插入 {len(vectors)} 个向量")
            
            # 批量插入向量
            self.index.upsert(vectors=vectors)
            
            logger.info(f"成功插入 {len(vectors)} 个向量")
            
        except Exception as e:
            logger.error(f"插入向量失败: {str(e)}")
            raise
    
    def search_similar(self, query_vector: List[float], top_k: int = 5, 
                      filter_dict: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        搜索相似向量
        
        Args:
            query_vector: 查询向量
            top_k: 返回最相似的k个结果
            filter_dict: 过滤条件
            
        Returns:
            相似结果列表，每个结果包含id、score和metadata
        """
        try:
            if not self.index:
                raise ValueError("索引未连接，请先调用create_index或connect_to_index")
            
            logger.debug(f"搜索相似向量，top_k: {top_k}")
            
            # 执行相似性搜索
            search_response = self.index.query(
                vector=query_vector,
                top_k=top_k,
                filter=filter_dict,
                include_metadata=True
            )
            
            # 格式化结果
            results = []
            for match in search_response.matches:
                result = {
                    'id': match.id,
                    'score': match.score,
                    'metadata': match.metadata
                }
                results.append(result)
            
            logger.debug(f"找到 {len(results)} 个相似结果")
            return results
            
        except Exception as e:
            logger.error(f"搜索相似向量失败: {str(e)}")
            raise
    
    def delete_vectors(self, ids: List[str]) -> None:
        """
        删除向量
        
        Args:
            ids: 要删除的向量ID列表
        """
        try:
            if not self.index:
                raise ValueError("索引未连接，请先调用create_index或connect_to_index")
            
            logger.info(f"删除 {len(ids)} 个向量")
            self.index.delete(ids=ids)
            logger.info("向量删除成功")
            
        except Exception as e:
            logger.error(f"删除向量失败: {str(e)}")
            raise
    
    def get_index_stats(self) -> Dict[str, Any]:
        """
        获取索引统计信息
        
        Returns:
            索引统计信息
        """
        try:
            if not self.index:
                raise ValueError("索引未连接，请先调用create_index或connect_to_index")
            
            stats = self.index.describe_index_stats()
            return stats
            
        except Exception as e:
            logger.error(f"获取索引统计信息失败: {str(e)}")
            raise
