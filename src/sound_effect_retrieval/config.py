"""
音效检索系统配置
"""

import os
from typing import Dict, Any


class Config:
    """配置类"""
    
    # 火山引擎配置
    VOLCENGINE_API_KEY = os.getenv('ARK_API_KEY')
    VOLCENGINE_BASE_URL = os.getenv('VOLCENGINE_BASE_URL', 'https://ark.cn-beijing.volces.com/api/v3')
    VOLCENGINE_MODEL = os.getenv('VOLCENGINE_MODEL', 'doubao-embedding-text-240715')
    
    # Pinecone配置
    PINECONE_API_KEY = os.getenv('PINECONE_API_KEY')
    PINECONE_ENVIRONMENT = os.getenv('PINECONE_ENVIRONMENT', 'us-east-1-aws')
    PINECONE_INDEX_NAME = os.getenv('PINECONE_INDEX_NAME')
    
    # 检索配置
    DEFAULT_TOP_K = int(os.getenv('DEFAULT_TOP_K', '5'))
    DEFAULT_MIN_SCORE = float(os.getenv('DEFAULT_MIN_SCORE', '0.0'))
    DEFAULT_BATCH_SIZE = int(os.getenv('DEFAULT_BATCH_SIZE', '100'))
    
    # 向量数据库配置
    # 注意：向量维度将通过embedding_service.get_embedding_dimension()动态获取
    SIMILARITY_METRIC = os.getenv('SIMILARITY_METRIC', 'cosine')
    
    # 日志配置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FORMAT = os.getenv('LOG_FORMAT', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # 数据文件路径
    SOUND_EFFECTS_JSON_PATH = os.getenv('SOUND_EFFECTS_JSON_PATH', 'resources/sound_effect_with_descriptions.json')
    
    @classmethod
    def validate(cls) -> Dict[str, Any]:
        """
        验证配置
        
        Returns:
            验证结果字典
        """
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        # 检查必需的API密钥
        if not cls.VOLCENGINE_API_KEY:
            validation_result['valid'] = False
            validation_result['errors'].append('ARK_API_KEY 未设置')
        
        if not cls.PINECONE_API_KEY:
            validation_result['valid'] = False
            validation_result['errors'].append('PINECONE_API_KEY 未设置')
        
        # 检查数据文件
        if not os.path.exists(cls.SOUND_EFFECTS_JSON_PATH):
            validation_result['warnings'].append(f'音效数据文件不存在: {cls.SOUND_EFFECTS_JSON_PATH}')
        
        # 检查数值配置
        if cls.DEFAULT_TOP_K <= 0:
            validation_result['warnings'].append('DEFAULT_TOP_K 应该大于0')
        
        if cls.DEFAULT_MIN_SCORE < 0 or cls.DEFAULT_MIN_SCORE > 1:
            validation_result['warnings'].append('DEFAULT_MIN_SCORE 应该在0-1之间')
        
        return validation_result
    
    @classmethod
    def get_summary(cls) -> Dict[str, Any]:
        """
        获取配置摘要
        
        Returns:
            配置摘要字典
        """
        return {
            'volcengine': {
                'api_key_set': bool(cls.VOLCENGINE_API_KEY),
                'base_url': cls.VOLCENGINE_BASE_URL,
                'model': cls.VOLCENGINE_MODEL
            },
            'pinecone': {
                'api_key_set': bool(cls.PINECONE_API_KEY),
                'environment': cls.PINECONE_ENVIRONMENT,
                'index_name': cls.PINECONE_INDEX_NAME
            },
            'retrieval': {
                'default_top_k': cls.DEFAULT_TOP_K,
                'default_min_score': cls.DEFAULT_MIN_SCORE,
                'default_batch_size': cls.DEFAULT_BATCH_SIZE
            },
            'vector_db': {
                'metric': cls.SIMILARITY_METRIC,
                'dimension_note': '维度将通过embedding服务动态获取'
            },
            'data': {
                'json_path': cls.SOUND_EFFECTS_JSON_PATH,
                'json_exists': os.path.exists(cls.SOUND_EFFECTS_JSON_PATH)
            }
        }


# 开发环境配置
class DevelopmentConfig(Config):
    """开发环境配置"""
    LOG_LEVEL = 'DEBUG'
    DEFAULT_BATCH_SIZE = 50  # 开发时使用较小的批次


# 生产环境配置
class ProductionConfig(Config):
    """生产环境配置"""
    LOG_LEVEL = 'INFO'
    DEFAULT_BATCH_SIZE = 200  # 生产时使用较大的批次


# 测试环境配置
class TestConfig(Config):
    """测试环境配置"""
    LOG_LEVEL = 'DEBUG'
    PINECONE_INDEX_NAME = 'sound-effects-test'
    DEFAULT_BATCH_SIZE = 10  # 测试时使用很小的批次


def get_config(env: str = None) -> Config:
    """
    根据环境获取配置
    
    Args:
        env: 环境名称 ('development', 'production', 'test')
        
    Returns:
        配置类实例
    """
    env = env or os.getenv('ENVIRONMENT', 'development').lower()
    
    config_map = {
        'development': DevelopmentConfig,
        'production': ProductionConfig,
        'test': TestConfig
    }
    
    return config_map.get(env, DevelopmentConfig)
