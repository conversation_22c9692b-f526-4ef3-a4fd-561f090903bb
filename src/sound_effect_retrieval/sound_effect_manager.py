"""
音效管理器
提供统一的音效检索接口，整合所有功能
"""
import json
import os
import logging
import time
from typing import List, Dict, Any, Optional

from src.sound_effect_retrieval.audio_volume_adjuster import AudioVolumeAdjuster
from src.sound_effect_retrieval.embedding_service import EmbeddingService
from src.sound_effect_retrieval.vector_database import VectorDatabase
from src.sound_effect_retrieval.sound_effect_indexer import SoundEffectIndexer
from src.sound_effect_retrieval.sound_effect_retriever import SoundEffectRetriever
from src.story_board.aigc_component import call_text_api

logger = logging.getLogger(__name__)


class SoundEffectManager:
    """音效管理器 - 统一的音效检索接口"""
    
    def __init__(self, 
                 volcengine_api_key: str = None,
                 pinecone_api_key: str = None,
                 pinecone_environment: str = None,
                 index_name: str = None):
        """
        初始化音效管理器
        
        Args:
            volcengine_api_key: 火山引擎API密钥
            pinecone_api_key: Pinecone API密钥
            pinecone_environment: Pinecone环境
            index_name: 向量数据库索引名称
        """
        try:
            # 初始化嵌入服务
            self.embedding_service = EmbeddingService(api_key=volcengine_api_key)
            
            # 初始化向量数据库
            self.vector_db = VectorDatabase(
                api_key=pinecone_api_key,
                environment=pinecone_environment,
                index_name=index_name
            )
            
            # 初始化索引构建器
            self.indexer = SoundEffectIndexer(
                embedding_service=self.embedding_service,
                vector_db=self.vector_db
            )
            
            # 初始化检索器
            self.retriever = SoundEffectRetriever(
                embedding_service=self.embedding_service,
                vector_db=self.vector_db
            )
            
            logger.info("音效管理器初始化完成")
            
        except Exception as e:
            logger.error(f"音效管理器初始化失败: {str(e)}")
            raise

    def process_sentence(self, sentence):
        """
        处理单个句子

        Args:
            sentence: 输入句子

        Returns:
            list: 包含词语和音效信息的JSON数组
        """
        print(f"🎵 处理句子: {sentence}")

        audio_effects = self.generate_audio_effects(sentence)

        # 直接返回API生成的JSON数组
        return audio_effects

    def clean_response(self, text):
        """清理API响应文本"""
        # 移除可能的前缀
        prefixes = ['输出：', '输出:', 'JSON:', 'json:', '结果：', '结果:']
        for prefix in prefixes:
            if text.startswith(prefix):
                text = text[len(prefix):].strip()

        # 移除可能的后缀说明
        lines = text.split('\n')
        if lines:
            # 取第一行（应该是JSON）
            text = lines[0].strip()

        # 移除可能的markdown代码块标记
        text = text.replace('```json', '').replace('```', '').strip()

        return text

    def validate_audio_effects(self, audio_effects):
        """验证音效数组格式"""
        if not isinstance(audio_effects, list):
            return False

        if len(audio_effects) < 3 or len(audio_effects) > 5:
            print(f"⚠️ 音效数量不符合要求: {len(audio_effects)} (应为3-5个)")

        for effect in audio_effects:
            if not isinstance(effect, dict):
                return False

            if 'word' not in effect or 'audio' not in effect:
                return False

            if not isinstance(effect['word'], str) or not isinstance(effect['audio'], str):
                return False

            if len(effect) != 2:  # 只能有word和audio两个键
                return False

        return True

    async def generate_audio_effects(self, sentence, max_retries=3):
        """
        为句子生成音效匹配

        Args:
            sentence: 输入句子
            max_retries: 最大重试次数

        Returns:
            list: 音效信息列表，格式为 [{"word": "词语", "audio": "音效描述"}]
        """

        # 构建提示词
        prompt = open("src/prompts/sound_effects_key_word.md", "r").read()
        prompt = prompt.replace("{sentence}", sentence)

        for attempt in range(max_retries):
            try:

                data = {
                    "model": os.environ.get("TEXT_MODEL"),
                    "messages": [
                        {"role": "user", "content": prompt},
                    ],
                }

                content = await call_text_api(data)
                result_text = content.strip()
                # 清理可能的前缀和后缀
                result_text = self.clean_response(result_text)

                # 尝试解析JSON
                try:
                    audio_effects = json.loads(result_text)

                    # 验证格式
                    if self.validate_audio_effects(audio_effects):
                        print(f"✅ 成功生成 {len(audio_effects)} 个音效匹配")
                        return audio_effects
                    else:
                        raise ValueError("返回格式不符合要求")

                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    print(f"原始响应: {result_text}")
                    if attempt < max_retries - 1:
                        time.sleep(1)
                        continue
                    else:
                        raise ValueError("返回格式不符合要求")

            except Exception as e:
                print(f"❌ 生成音效时出错 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)
                    continue
                else:
                    raise ValueError("生成音效失败")
        raise ValueError("生成音效失败")

    def build_index_from_json(self, json_file_path: str, batch_size: int = 100) -> None:
        """
        从JSON文件构建音效索引
        
        Args:
            json_file_path: 音效数据JSON文件路径
            batch_size: 批处理大小
        """
        try:
            logger.info(f"开始从JSON文件构建索引: {json_file_path}")
            self.indexer.build_index(json_file_path, batch_size)
            logger.info("索引构建完成")
            
        except Exception as e:
            logger.error(f"构建索引失败: {str(e)}")
            raise
    
    def search(self, query: str, top_k: int = 5, 
               category_filter: List[str] = None,
               min_score: float = 0.0) -> List[Dict[str, Any]]:
        """
        检索音效
        
        Args:
            query: 查询文本
            top_k: 返回最相似的k个结果，默认5个
            category_filter: 类别过滤器
            min_score: 最小相似度分数阈值
            
        Returns:
            音效检索结果列表，每个结果包含url、label、description等字段
        """
        try:
            return self.retriever.search_sound_effects(
                query=query,
                top_k=top_k,
                category_filter=category_filter,
                min_score=min_score
            )
        except Exception as e:
            logger.error(f"音效检索失败: {str(e)}")
            raise

    async def get_volume_multiplier_async(self, url: str) -> float:
        """
        异步获取音频的建议音量调整倍数

        Args:
            url: 音频文件的URL

        Returns:
            float: 建议的音量调整倍数

        Raises:
            Exception: 当处理失败时抛出异常
        """
        adjuster = AudioVolumeAdjuster()

        try:
            # 异步下载音频
            downloaded_path = await adjuster.download_audio_async(url)

            # 获取音频音量信息
            volume_info = adjuster.get_audio_volume_info(downloaded_path)

            # 获取音量建议
            recommendation = adjuster.get_volume_recommendation(volume_info)

            # 返回建议的调整倍数
            return recommendation['optimal_multiplier']

        except Exception as e:
            raise Exception(f"获取音量调整倍数失败: {str(e)}")

        finally:
            # 清理临时文件
            adjuster.cleanup()
    def search_by_keywords(self, keywords: List[str], top_k: int = 5) -> List[Dict[str, Any]]:
        """
        根据关键词检索音效
        
        Args:
            keywords: 关键词列表
            top_k: 返回结果数量
            
        Returns:
            音效检索结果列表
        """
        try:
            return self.retriever.search_by_keywords(keywords, top_k)
        except Exception as e:
            logger.error(f"关键词检索失败: {str(e)}")
            raise
    
    def search_by_category(self, category: str, query: str = "", 
                          top_k: int = 10) -> List[Dict[str, Any]]:
        """
        在指定类别中检索音效
        
        Args:
            category: 音效类别
            query: 查询文本
            top_k: 返回结果数量
            
        Returns:
            音效检索结果列表
        """
        try:
            return self.retriever.search_by_category(category, query, top_k)
        except Exception as e:
            logger.error(f"类别检索失败: {str(e)}")
            raise
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取音效数据库统计信息
        
        Returns:
            统计信息字典
        """
        try:
            return self.retriever.get_database_stats()
        except Exception as e:
            logger.error(f"获取统计信息失败: {str(e)}")
            raise
    
    def validate_connection(self) -> bool:
        """
        验证连接状态
        
        Returns:
            连接是否正常
        """
        try:
            return self.retriever.validate_connection()
        except Exception as e:
            logger.error(f"连接验证失败: {str(e)}")
            return False
    
    @classmethod
    def create_from_env(cls) -> 'SoundEffectManager':
        """
        从环境变量创建音效管理器实例
        
        Args:
            index_name: 索引名称
            
        Returns:
            音效管理器实例
        """
        return cls(
            volcengine_api_key=os.getenv('ARK_API_KEY'),
            pinecone_api_key=os.getenv('PINECONE_API_KEY'),
            pinecone_environment=os.getenv('PINECONE_ENVIRONMENT', 'us-east-1'),
            index_name=os.getenv('PINECONE_INDEX_NAME')
        )


# 便捷函数
def create_sound_effect_manager(volcengine_api_key: str = None,
                               pinecone_api_key: str = None,
                               pinecone_environment: str = None,
                               index_name: str = "sound-effects") -> SoundEffectManager:
    """
    创建音效管理器实例的便捷函数
    
    Args:
        volcengine_api_key: 火山引擎API密钥
        pinecone_api_key: Pinecone API密钥
        pinecone_environment: Pinecone环境
        index_name: 索引名称
        
    Returns:
        音效管理器实例
    """
    return SoundEffectManager(
        volcengine_api_key=volcengine_api_key,
        pinecone_api_key=pinecone_api_key,
        pinecone_environment=pinecone_environment,
        index_name=index_name
    )


def search_sound_effects(query: str, 
                        top_k: int = 5,
                        manager: SoundEffectManager = None) -> List[Dict[str, Any]]:
    """
    快速检索音效的便捷函数
    
    Args:
        query: 查询文本
        top_k: 返回结果数量
        manager: 音效管理器实例，如果为None则从环境变量创建
        
    Returns:
        音效检索结果列表
    """
    if manager is None:
        manager = SoundEffectManager.create_from_env()
    
    return manager.search(query, top_k)
