# -*- coding: utf-8 -*-
"""
@Time ： 2025/8/1 17:55
@Auth ： xiaolongtuan
@File ：audio_volume_adjuster.py
"""
import os
import tempfile
import requests
import librosa
import soundfile as sf
import numpy as np
import aiohttp
from urllib.parse import urlparse

class AudioVolumeAdjuster:
    def __init__(self):
        self.temp_dir = tempfile.mkdtemp()

    def get_audio_volume_info(self, audio_path: str) -> dict:
        """
        获取音频的音量信息

        Args:
            audio_path: 音频文件路径

        Returns:
            dict: 包含音量信息的字典
        """
        try:
            # 加载音频文件
            y, sr = librosa.load(audio_path, sr=None)

            # 计算RMS (Root Mean Square) 能量
            rms = librosa.feature.rms(y=y)[0]
            rms_mean = np.mean(rms)
            rms_max = np.max(rms)

            # 计算峰值音量 (Peak Volume)
            peak_volume = np.max(np.abs(y))

            # 转换为分贝 (dB)
            rms_db = 20 * np.log10(rms_mean + 1e-10)  # 避免log(0)
            peak_db = 20 * np.log10(peak_volume + 1e-10)

            volume_info = {
                'rms_mean': float(rms_mean),
                'rms_max': float(rms_max),
                'peak_volume': float(peak_volume),
                'rms_db': float(rms_db),
                'peak_db': float(peak_db),
                'duration': len(y) / sr,
                'sample_rate': sr
            }

            return volume_info

        except Exception as e:
            raise Exception(f"获取音频音量信息失败: {str(e)}")

    def calculate_optimal_volume_multiplier(self, volume_info: dict, target_rms_db: float = -20.0) -> float:
        """
        根据音频的当前音量计算最佳的音量调整倍数

        Args:
            volume_info: 音频音量信息字典
            target_rms_db: 目标RMS分贝值 (默认-20dB，适合大多数应用)

        Returns:
            float: 建议的音量调整倍数
        """
        current_rms_db = volume_info['rms_db']
        current_peak_db = volume_info['peak_db']

        # 计算基于RMS的调整倍数
        db_difference = target_rms_db - current_rms_db
        rms_multiplier = 10 ** (db_difference / 20)

        # 检查调整后是否会导致削波
        # 预测调整后的峰值分贝
        predicted_peak_db = current_peak_db + db_difference

        # 如果预测的峰值超过-1dB（接近削波），则限制倍数
        max_safe_peak_db = -1.0
        if predicted_peak_db > max_safe_peak_db:
            # 基于峰值计算安全的最大倍数
            safe_db_increase = max_safe_peak_db - current_peak_db
            safe_multiplier = 10 ** (safe_db_increase / 20)
            multiplier = min(rms_multiplier, safe_multiplier)
        else:
            multiplier = rms_multiplier

        # 限制调整范围，避免过度调整
        multiplier = max(0.1, min(multiplier, 10.0))

        return multiplier

    def get_volume_recommendation(self, volume_info: dict) -> dict:
        """
        分析音频音量并给出调整建议

        Args:
            volume_info: 音频音量信息字典

        Returns:
            dict: 包含分析结果和建议的字典
        """
        rms_db = volume_info['rms_db']
        peak_db = volume_info['peak_db']

        # 定义音量等级
        if rms_db > -12:
            level = "过大"
            recommendation = "音量过大，建议降低"
            target_rms_db = -18.0
        elif rms_db > -18:
            level = "较大"
            recommendation = "音量较大，可适当降低"
            target_rms_db = -20.0
        elif rms_db > -25:
            level = "适中"
            recommendation = "音量适中，可选择性微调"
            target_rms_db = -20.0
        elif rms_db > -35:
            level = "较小"
            recommendation = "音量较小，建议增大"
            target_rms_db = -20.0
        else:
            level = "过小"
            recommendation = "音量过小，建议显著增大"
            target_rms_db = -18.0

        # 计算建议的调整倍数
        optimal_multiplier = self.calculate_optimal_volume_multiplier(volume_info, target_rms_db)

        return {
            'current_level': level,
            'recommendation': recommendation,
            'current_rms_db': rms_db,
            'current_peak_db': peak_db,
            'target_rms_db': target_rms_db,
            'optimal_multiplier': optimal_multiplier,
            'adjustment_needed': abs(optimal_multiplier - 1.0) > 0.1
        }

    def adjust_volume(self, audio_path: str, volume_multiplier: float, output_path: str = None) -> str:
        """
        调整音频音量

        Args:
            audio_path: 输入音频文件路径
            volume_multiplier: 音量倍数 (如 1.8 表示增大到1.8倍，0.5表示减小到0.5倍)
            output_path: 输出文件路径，如果为None则自动生成

        Returns:
            str: 输出文件路径
        """
        try:
            # 加载音频文件
            y, sr = librosa.load(audio_path, sr=None)

            # 调整音量
            y_adjusted = y * volume_multiplier

            # 防止音频削波 (clipping)
            if np.max(np.abs(y_adjusted)) > 1.0:
                print("警告: 调整后的音频可能会产生削波，正在进行归一化处理...")
                y_adjusted = y_adjusted / np.max(np.abs(y_adjusted)) * 0.95

            # 生成输出文件路径
            if output_path is None:
                base_name = os.path.splitext(os.path.basename(audio_path))[0]
                output_path = os.path.join(
                    os.path.dirname(audio_path),
                    f"{base_name}_volume_{volume_multiplier}x.wav"
                )

            # 保存调整后的音频
            sf.write(output_path, y_adjusted, sr)

            print(f"音量调整完成，输出文件: {output_path}")
            return output_path

        except Exception as e:
            raise Exception(f"调整音频音量失败: {str(e)}")

    async def download_audio_async(self, url: str) -> str:
        """
        异步从URL下载音频文件到临时目录

        Args:
            url: 音频文件的URL

        Returns:
            str: 下载的音频文件路径
        """
        try:
            # print(f"正在异步下载音频: {url}")

            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    response.raise_for_status()

                    # 从URL获取文件名，如果没有则使用默认名称
                    parsed_url = urlparse(url)
                    filename = os.path.basename(parsed_url.path)
                    if not filename or '.' not in filename:
                        filename = "audio.wav"

                    file_path = os.path.join(self.temp_dir, filename)

                    with open(file_path, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            f.write(chunk)

            # print(f"音频异步下载完成: {file_path}")
            return file_path

        except Exception as e:
            raise Exception(f"异步下载音频失败: {str(e)}")

    def cleanup(self):
        """清理临时文件"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
