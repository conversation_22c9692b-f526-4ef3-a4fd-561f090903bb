# 音效检索系统

基于火山引擎文本嵌入API和Pinecone向量数据库的音效检索系统，支持通过自然语言描述检索相似的音效文件。

## 功能特性

- **智能文本嵌入**: 使用火山引擎的`doubao-embedding-text-240715`模型进行文本向量化
- **高效向量检索**: 基于Pinecone向量数据库进行相似度搜索
- **多种检索方式**: 支持文本查询、关键词检索、类别过滤等
- **批量索引构建**: 支持从JSON文件批量构建音效向量索引
- **灵活的配置**: 支持环境变量配置和多环境部署

## 系统架构

```
音效检索系统
├── EmbeddingService      # 文本嵌入服务
├── VectorDatabase        # Pinecone向量数据库管理
├── SoundEffectIndexer    # 音效索引构建器
├── SoundEffectRetriever  # 音效检索器
└── SoundEffectManager    # 统一管理接口
```

## 安装依赖

```bash
pip install volcenginesdkarkruntime pinecone-client
```

## 环境配置

设置以下环境变量：

```bash
# 火山引擎配置
export VOLCENGINE_API_KEY="your_volcengine_api_key"

# Pinecone配置
export PINECONE_API_KEY="your_pinecone_api_key"
export PINECONE_ENVIRONMENT="us-east-1-aws"  # 可选，默认值
export PINECONE_INDEX_NAME="sound-effects"   # 可选，默认值
```

## 快速开始

### 1. 初始化系统

```python
from src.sound_effect_retrieval import SoundEffectManager

# 从环境变量创建管理器
manager = SoundEffectManager.create_from_env()

# 或者直接传入参数
manager = SoundEffectManager(
    volcengine_api_key="your_key",
    pinecone_api_key="your_key",
    pinecone_environment="us-east-1-aws"
)
```

### 2. 构建音效索引

```python
# 从JSON文件构建索引（首次使用时需要）
manager.build_index_from_json("resources/sound_effect_with_descriptions.json")
```

### 3. 检索音效

```python
# 基本文本检索
results = manager.search("古琴轻拨", top_k=5)

# 关键词检索
results = manager.search_by_keywords(["古风", "武侠"], top_k=5)

# 类别检索
results = manager.search_by_category("古风音效库整合", query="竹笛", top_k=5)

# 处理结果
for result in results:
    print(f"描述: {result['description']}")
    print(f"URL: {result['url']}")
    print(f"相似度: {result['score']:.3f}")
    print(f"标签: {result['label']}")
    print("---")
```

## API 参考

### SoundEffectManager

主要的管理接口类，提供所有音效检索功能。

#### 方法

- `search(query, top_k=5, category_filter=None, min_score=0.0)`: 基本文本检索
- `search_by_keywords(keywords, top_k=5)`: 关键词检索
- `search_by_category(category, query="", top_k=10)`: 类别检索
- `build_index_from_json(json_file_path, batch_size=100)`: 构建索引
- `get_stats()`: 获取数据库统计信息
- `validate_connection()`: 验证连接状态

#### 返回格式

检索结果为字典列表，每个结果包含：

```python
{
    'url': 'https://example.com/sound.wav',      # 音效文件URL
    'label': ['古风', '音效'],                    # 标签列表
    'description': '古琴轻拨，余韵悠长...',        # 描述文本
    'score': 0.85,                              # 相似度分数
    'file_name': 'sound.wav',                   # 文件名
    'category_path': ['古风音效库整合']           # 类别路径
}
```

## 配置选项

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `VOLCENGINE_API_KEY` | - | 火山引擎API密钥（必需） |
| `VOLCENGINE_BASE_URL` | `https://ark.cn-beijing.volces.com/api/v3` | API基础URL |
| `VOLCENGINE_MODEL` | `doubao-embedding-text-240715` | 嵌入模型名称 |
| `PINECONE_API_KEY` | - | Pinecone API密钥（必需） |
| `PINECONE_ENVIRONMENT` | `us-east-1-aws` | Pinecone环境 |
| `PINECONE_INDEX_NAME` | `sound-effects` | 索引名称 |
| `DEFAULT_TOP_K` | `5` | 默认返回结果数量 |
| `DEFAULT_MIN_SCORE` | `0.0` | 默认最小相似度阈值 |
| `DEFAULT_BATCH_SIZE` | `100` | 默认批处理大小 |

### 配置验证

```python
from src.sound_effect_retrieval.config import Config

# 验证配置
validation = Config.validate()
if not validation['valid']:
    print("配置错误:", validation['errors'])

# 查看配置摘要
summary = Config.get_summary()
print(summary)
```

## 使用示例

### 完整示例

```python
import os
from src.sound_effect_retrieval import SoundEffectManager

# 设置环境变量
os.environ['VOLCENGINE_API_KEY'] = 'your_volcengine_key'
os.environ['PINECONE_API_KEY'] = 'your_pinecone_key'

# 创建管理器
manager = SoundEffectManager.create_from_env()

# 验证连接
if not manager.validate_connection():
    print("连接失败")
    exit(1)

# 构建索引（首次运行）
manager.build_index_from_json("resources/sound_effect_with_descriptions.json")

# 检索示例
queries = ["古琴轻拨", "马蹄声", "竹笛悠扬"]

for query in queries:
    print(f"\n查询: '{query}'")
    results = manager.search(query, top_k=3)
    
    for i, result in enumerate(results, 1):
        print(f"  {i}. {result['description']}")
        print(f"     相似度: {result['score']:.3f}")
```

## 注意事项

1. **首次使用**: 需要先调用`build_index_from_json()`构建索引
2. **API限制**: 注意火山引擎API的调用频率限制
3. **向量维度**: 系统会自动通过`get_embedding_dimension()`获取嵌入模型的维度
4. **数据格式**: JSON文件需要符合预期的嵌套结构格式
5. **索引创建**: 如果Pinecone索引不存在，系统会自动创建

## 故障排除

### 常见问题

1. **连接失败**: 检查API密钥和网络连接
2. **索引不存在**: 确保已调用`build_index_from_json()`
3. **检索结果为空**: 检查查询文本和相似度阈值设置
4. **维度不匹配**: 确保向量数据库维度与嵌入模型一致

### 日志配置

```python
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
```

## 性能优化

1. **批处理大小**: 根据内存和网络情况调整`batch_size`
2. **缓存策略**: 考虑对频繁查询进行缓存
3. **索引优化**: 定期检查和优化Pinecone索引
4. **并发控制**: 注意API调用的并发限制
