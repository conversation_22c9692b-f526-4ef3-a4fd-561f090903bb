#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络重试工具模块
提供通用的网络错误检测和重试机制
"""

import asyncio
import logging
from functools import wraps
from typing import Any, Callable, TypeVar, Union
import aiohttp

logger = logging.getLogger(__name__)

# 类型变量，用于保持函数签名
F = TypeVar('F', bound=Callable[..., Any])


def is_network_error(exception: Exception) -> bool:
    """判断是否为网络相关错误
    
    Args:
        exception: 异常对象
        
    Returns:
        bool: 是否为网络错误
    """
    # 常见的网络错误类型
    network_error_types = (
        # aiohttp错误
        aiohttp.ClientError,
        aiohttp.ServerTimeoutError,
        aiohttp.ClientConnectorError,
        aiohttp.ClientSSLError,
        aiohttp.ServerConnectionError,
        # 标准库错误
        asyncio.TimeoutError,
        ConnectionError,
        OSError,
        # OpenAI客户端错误（如果可用）
    )
    
    # 尝试导入OpenAI错误类型
    try:
        from openai import APIConnectionError, APITimeoutError
        network_error_types += (APIConnectionError, APITimeoutError)
    except ImportError:
        pass
    
    # 检查异常类型
    if isinstance(exception, network_error_types):
        return True
    
    # 检查异常消息中的网络相关关键词
    error_msg = str(exception).lower()
    network_keywords = [
        'connection', 'timeout', 'network', 'dns', 'ssl', 'certificate',
        'nodename nor servname provided', 'name resolution failed',
        'connection refused', 'connection reset', 'host unreachable',
        'temporary failure in name resolution', 'network is unreachable',
        'no route to host', 'connection timed out'
    ]
    
    return any(keyword in error_msg for keyword in network_keywords)


async def retry_on_network_error(
    func: Callable,
    *args,
    max_retries: int = 3,
    base_delay: float = 1.0,
    exponential_backoff: bool = True,
    **kwargs
) -> Any:
    """对函数调用进行网络错误重试
    
    Args:
        func: 要重试的异步函数
        *args: 函数的位置参数
        max_retries: 最大重试次数
        base_delay: 基础延迟时间（秒）
        exponential_backoff: 是否使用指数退避
        **kwargs: 函数的关键字参数
        
    Returns:
        函数的返回值
        
    Raises:
        最后一次重试的异常
    """
    last_exception = None
    
    for attempt in range(max_retries + 1):
        try:
            if asyncio.iscoroutinefunction(func):
                return await func(*args, **kwargs)
            else:
                return func(*args, **kwargs)
                
        except Exception as e:
            if is_network_error(e):
                last_exception = e
                if attempt < max_retries:
                    if exponential_backoff:
                        wait_time = base_delay * (2 ** attempt)
                    else:
                        wait_time = base_delay
                    
                    logger.warning(
                        f"🔄 网络错误，第 {attempt + 1} 次重试 (等待 {wait_time:.1f}s): {str(e)}"
                    )
                    await asyncio.sleep(wait_time)
                else:
                    logger.error(f"❌ 网络错误，已重试 {max_retries} 次: {str(e)}")
            else:
                # 非网络错误，直接抛出
                logger.error(f"❌ 非网络错误: {str(e)}")
                raise e
    
    # 如果所有重试都失败，抛出最后一个网络异常
    if last_exception:
        raise last_exception
    else:
        raise RuntimeError("网络重试失败，未知错误")


def network_retry(
    max_retries: int = 3,
    base_delay: float = 1.0,
    exponential_backoff: bool = True
):
    """网络重试装饰器
    
    Args:
        max_retries: 最大重试次数
        base_delay: 基础延迟时间（秒）
        exponential_backoff: 是否使用指数退避
        
    Returns:
        装饰器函数
    """
    def decorator(func: F) -> F:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            return await retry_on_network_error(
                func, *args,
                max_retries=max_retries,
                base_delay=base_delay,
                exponential_backoff=exponential_backoff,
                **kwargs
            )
        return wrapper
    return decorator


class NetworkRetryMixin:
    """网络重试混入类
    
    为类提供网络重试功能
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.default_max_retries = 3
        self.default_base_delay = 1.0
        self.default_exponential_backoff = True
    
    async def retry_network_call(
        self,
        func: Callable,
        *args,
        max_retries: int = None,
        base_delay: float = None,
        exponential_backoff: bool = None,
        **kwargs
    ) -> Any:
        """重试网络调用
        
        Args:
            func: 要重试的函数
            *args: 函数的位置参数
            max_retries: 最大重试次数（None使用默认值）
            base_delay: 基础延迟时间（None使用默认值）
            exponential_backoff: 是否使用指数退避（None使用默认值）
            **kwargs: 函数的关键字参数
            
        Returns:
            函数的返回值
        """
        return await retry_on_network_error(
            func, *args,
            max_retries=max_retries or self.default_max_retries,
            base_delay=base_delay or self.default_base_delay,
            exponential_backoff=exponential_backoff if exponential_backoff is not None else self.default_exponential_backoff,
            **kwargs
        )


# 使用示例
if __name__ == "__main__":
    import aiohttp
    
    # 示例1：使用装饰器
    @network_retry(max_retries=3, base_delay=1.0)
    async def example_api_call():
        async with aiohttp.ClientSession() as session:
            async with session.get("https://httpbin.org/delay/1") as response:
                return await response.json()
    
    # 示例2：使用函数
    async def another_api_call():
        async with aiohttp.ClientSession() as session:
            async with session.get("https://httpbin.org/delay/1") as response:
                return await response.json()
    
    async def main():
        # 使用装饰器的函数
        try:
            result1 = await example_api_call()
            print("装饰器示例成功:", result1)
        except Exception as e:
            print("装饰器示例失败:", e)
        
        # 使用重试函数
        try:
            result2 = await retry_on_network_error(another_api_call, max_retries=3)
            print("重试函数示例成功:", result2)
        except Exception as e:
            print("重试函数示例失败:", e)
    
    # 运行示例
    # asyncio.run(main())
