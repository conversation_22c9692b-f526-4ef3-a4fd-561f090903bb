import json
import re
import os
import logging
import copy
from pathlib import Path
from dotenv import load_dotenv
from dashscope import Generation

# --- 日志配置 ---
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)

# 🆕 拼音处理模块
try:
    import pypinyin


    def get_pinyin_without_tone(char):
        """获取字符的拼音（去声调）"""
        try:
            return pypinyin.lazy_pinyin(char, style=pypinyin.NORMAL)[0]
        except:
            return char


    logger.info("✅ pypinyin 模块加载成功")
except ImportError:
    logger.warning("⚠️ pypinyin 未安装，使用内置同音字库")
    # 内置同音字库（常见的语音识别错误）
    HOMOPHONE_MAP = {
        '他': 'ta', '她': 'ta', '它': 'ta',
        '些': 'xie', '谢': 'xie', '写': 'xie',
        '活': 'huo', '火': 'huo', '货': 'huo',
        '了': 'le', '乐': 'le',
        '的': 'de', '得': 'de', '地': 'de',
        '在': 'zai', '再': 'zai',
        '那': 'na', '哪': 'na',
        '这': 'zhe', '着': 'zhe',
        '和': 'he', '合': 'he', '河': 'he',
        '同': 'tong', '通': 'tong', '童': 'tong',
        '来': 'lai', '莱': 'lai', '赖': 'lai',
        '时': 'shi', '是': 'shi', '事': 'shi',
        '候': 'hou', '后': 'hou',
        '过': 'guo', '国': 'guo',
        '声': 'sheng', '生': 'sheng',
        '感': 'gan', '敢': 'gan'
    }


    def get_pinyin_without_tone(char):
        """使用内置同音字库获取拼音"""
        return HOMOPHONE_MAP.get(char, char)

# --- 加载环境变量 ---
dotenv_path = Path(__file__).parent.parent.parent / '.env'
load_dotenv(dotenv_path=dotenv_path, verbose=True, override=True)

DASHSCOPE_API_KEY = "sk-f1074ca1fbab4ab8aa56885752555525"

# 确保API Key已加载
if not DASHSCOPE_API_KEY:
    logger.error("DASHSCOPE_API_KEY not found in environment variables.")
    raise ValueError("DASHSCOPE_API_KEY not found in environment variables.")


# --- 阿里云大模型调用函数 ---
def call_aliyun_llm(prompt, model_name='qwen-max'):
    """
    调用阿里云通义千问大模型API。
    """
    try:
        response = Generation.call(
            model=model_name,
            prompt=prompt,
            api_key=DASHSCOPE_API_KEY
        )
        if response.status_code == 200:
            return response.output.text
        else:
            raise Exception(f"API调用失败，状态码: {response.status_code}, 错误信息: {response.message}")
    except Exception as e:
        raise Exception(f"调用dashscope失败: {e}")


# --- 功能函数 ---
def clean_text_and_map_indices(text):
    """
    清理文本，移除所有非中文字符，并生成一个映射表，
    将清理后文本的索引映射回原始文本的索引。
    """
    cleaned_chars = []
    cleaned_to_original_map = []
    for original_idx, char in enumerate(text):
        if re.match(r'[\u4e00-\u9fa5]', char):
            cleaned_chars.append(char)
            cleaned_to_original_map.append(original_idx)
    return "".join(cleaned_chars), cleaned_to_original_map


def get_combined_subtitles_with_mapping(subtitles):
    """
    提取字幕的纯中文字符并建立映射关系
    """
    combined_clean = ""
    char_mapping = []  # [(subtitle_idx, char_idx), ...]
    for subtitle_idx, subtitle_entry in enumerate(subtitles):
        subtitle_text = subtitle_entry.get("text", "")
        for char_idx, char in enumerate(subtitle_text):
            if re.match(r'[\u4e00-\u9fa5]', char):
                combined_clean += char
                char_mapping.append((subtitle_idx, char_idx))
    return combined_clean, char_mapping


def get_enhanced_subtitles_mapping(subtitles):
    """
    🆕 增强版字幕映射，支持动态规划算法的精确位置定位
    """
    combined_clean = ""
    char_mapping = []  # 每个元素包含详细的位置信息

    for subtitle_idx, subtitle_entry in enumerate(subtitles):
        subtitle_text = subtitle_entry.get("text", "")
        char_idx_in_subtitle = 0

        for char in subtitle_text:
            if re.match(r'[\u4e00-\u9fa5]', char):  # 只保留中文字符
                char_mapping.append({
                    'subtitle_idx': subtitle_idx,
                    'char_idx_in_subtitle': char_idx_in_subtitle,
                    'global_pos': len(combined_clean),
                    'char': char
                })
                combined_clean += char
            char_idx_in_subtitle += 1

    return combined_clean, char_mapping


def double_pointer_correction(data_entry, story_board_clean, subtitles_clean, subtitle_mapping):
    """
    字数相同时的双指针纠正策略
    """
    logger.info("  🎯 开始双指针对比修正...")
    corrections = 0
    subtitles = data_entry.get("short_subtitles", [])

    # 双指针算法
    story_ptr = 0
    subtitle_ptr = 0

    while story_ptr < len(story_board_clean) and subtitle_ptr < len(subtitles_clean):
        story_char = story_board_clean[story_ptr]
        subtitle_char = subtitles_clean[subtitle_ptr]

        if story_char == subtitle_char:
            # 字符匹配，两个指针都前进
            story_ptr += 1
            subtitle_ptr += 1
        else:
            # 字符不匹配，需要修正
            subtitle_idx, char_idx = subtitle_mapping[subtitle_ptr]
            original_subtitle = subtitles[subtitle_idx]
            original_text_chars = list(original_subtitle['text'])
            original_char = original_text_chars[char_idx]

            # 修正字符
            original_text_chars[char_idx] = story_char
            original_subtitle['text'] = ''.join(original_text_chars)
            corrections += 1

            logger.info(
                f"    ✏️ 修正位置{subtitle_ptr}: 字幕{subtitle_idx + 1}[{char_idx}] '{original_char}' → '{story_char}'")

            # 两个指针都前进
            story_ptr += 1
            subtitle_ptr += 1

    logger.info(f"  ✅ 双指针修正完成，共修正 {corrections} 处")
    return corrections


def mark_different_length_cases(data_entry, story_board_clean, subtitles_clean, subtitle_mapping):
    """
    字数不同时的标记策略 - 不处理，只标记
    """
    story_board_id = data_entry.get('story_board_id', 'N/A')
    char_diff = len(story_board_clean) - len(subtitles_clean)

    logger.info("  🏷️ 字数不匹配，仅标记不处理...")
    logger.info(f"  📊 字符差异: 故事板{len(story_board_clean)}字 - 字幕{len(subtitles_clean)}字 = {char_diff}字差异")

    # 在数据中添加标记
    data_entry['correction_status'] = 'MARKED_DIFFERENT_LENGTH'
    data_entry['char_difference'] = char_diff
    data_entry['needs_manual_review'] = True
    data_entry['review_reason'] = f"字数不匹配，差异{abs(char_diff)}字符"

    # 详细分析
    story_board_text = data_entry.get("story_board", "")
    subtitles_text = ''.join([sub.get('text', '') for sub in data_entry.get("short_subtitles", [])])

    logger.info(f"  📋 故事板内容: {story_board_text}")
    logger.info(f"  📋 字幕合并: {subtitles_text}")
    logger.info(f"  🏷️ 已标记为需要人工审核")

    return 0  # 返回0表示没有进行修正


def mark_large_difference_cases(data_entry, story_board_clean, subtitles_clean, subtitle_mapping):
    """
    🆕 标记差异过大的情况，提供详细分析
    """
    story_board_id = data_entry.get('story_board_id', 'N/A')
    char_diff = abs(len(story_board_clean) - len(subtitles_clean))

    # 🆕 详细问题分析
    logger.warning(f"⚠️ 【问题字幕检测】故事板 ID: {story_board_id}")
    logger.warning(f"❌ 字符差异过大: {char_diff}字 (阈值: 15字)")
    logger.warning(f"📊 故事板: {len(story_board_clean)}字 vs 字幕: {len(subtitles_clean)}字")

    # 🆕 问题严重程度分析
    if char_diff > 50:
        severity = "严重"
        recommendation = "强烈建议重新生成字幕"
    elif char_diff > 30:
        severity = "中等"
        recommendation = "建议检查字幕生成质量"
    else:
        severity = "轻微"
        recommendation = "可考虑手动修正"

    logger.warning(f"🚨 问题严重程度: {severity}")
    logger.warning(f"💡 处理建议: {recommendation}")

    # 🆕 具体字幕分析
    subtitles = data_entry.get("short_subtitles", [])
    logger.warning(f"📋 问题字幕详情:")

    for i, subtitle in enumerate(subtitles, 1):
        subtitle_text = subtitle.get('text', '')
        subtitle_length = len(re.findall(r'[\u4e00-\u9fa5]', subtitle_text))

        if subtitle_length < 5:
            status = "❌ 过短"
        elif subtitle_length > 50:
            status = "❌ 过长"
        else:
            status = "⚠️ 可疑"

        logger.warning(f"   第{i:2d}段: {status} ({subtitle_length:2d}字) {subtitle_text}")

    # 🆕 标记数据状态
    data_entry['correction_status'] = 'FAILED_LARGE_DIFFERENCE'
    data_entry['needs_manual_review'] = True
    data_entry['char_difference'] = char_diff
    data_entry['problem_severity'] = severity
    data_entry['recommendation'] = recommendation
    data_entry['review_reason'] = f"字符差异{char_diff}字，超过处理阈值"

    logger.warning(f"🏷️ 已标记为【问题字幕】，不建议使用")

    return -1  # 🆕 返回-1表示问题字幕，区别于成功(>0)和无需处理(0)


def call_aliyun_llm_for_correction(data_entry, story_board_clean, subtitles_clean):
    """
    调用阿里云大模型进行字幕修正，并保留所有字段。
    """
    story_board_text = data_entry.get("story_board", "")
    subtitles_data = data_entry.get("short_subtitles", [])
    logger.info("    🤖 准备调用阿里云大模型...")
    logger.info(f"    📊 故事板内容: {story_board_text}")
    logger.info(f"    📊 当前字幕数: {len(subtitles_data)} 条")
    prompt = generate_aliyun_correction_prompt(data_entry)
    logger.info(f"    📝 提示词长度: {len(prompt)} 字符")
    try:
        corrected_result = call_aliyun_llm(prompt)
        corrected_data_entry = parse_aliyun_response(corrected_result)
        original_total_chars = len(subtitles_clean)
        data_entry.update(corrected_data_entry)
        new_subtitles_clean = []
        for sub in data_entry["short_subtitles"]:
            new_subtitles_clean.extend([c for c in sub.get('text', '') if re.match(r'[\u4e00-\u9fa5]', c)])
        corrections = abs(len(new_subtitles_clean) - original_total_chars)
        logger.info("    ✅ 大模型修正成功:")
        for i, sub in enumerate(data_entry["short_subtitles"]):
            logger.info(f"      字幕{i + 1}: {sub.get('text', '')}")
        return corrections
    except Exception as e:
        logger.error(f"    ❌ 阿里云大模型调用失败: {e}")
        logger.info("    🔄 回退到简单修正策略")
        return simple_fallback_correction(data_entry, story_board_clean)


def generate_aliyun_correction_prompt(data_entry):
    """
    生成阿里云大模型的字幕修正提示词，包含完整JSON对象
    """
    story_board_text = data_entry.get("story_board", "")
    subtitles_data = data_entry.get("short_subtitles", [])
    original_json_str = json.dumps(data_entry, ensure_ascii=False, indent=2)
    prompt = f"""你是一个专业的字幕修正助手。请根据`story_board`内容，修正JSON对象中`short_subtitles`数组里的字幕文本。

**修正要求：**
1. 你的任务是修正`short_subtitles`数组中每个字幕的**`text`字段**。
2. 修正后的所有`text`内容拼接起来，必须与`story_board`的文本完全一致。
3. 除了`short_subtitles`中的`text`字段，**JSON对象中的其他所有字段（包括`subtitle_id`, `timestamp`, `duration`, `words`等）都必须原封不动地保留**。
4. 修正时要处理错别字、漏字、多余的字，并进行适当的断句，但要保持字幕条目数量不变。

**输入JSON对象：**
```json
{original_json_str}
```

**输出格式：**
请直接返回修正后的完整JSON对象，格式与输入完全一致。不要返回任何解释或额外文字，只返回JSON代码。"""

    return prompt


def parse_aliyun_response(response_text):
    """解析阿里云大模型的返回结果，返回整个JSON对象"""

    try:
        json_match = re.search(r'```json\s*(.*?)\s*```', response_text, re.DOTALL)
        if json_match:
            json_str = json_match.group(1)
        else:
            json_str = response_text.strip()
        corrected_data_entry = json.loads(json_str)
        if not isinstance(corrected_data_entry, dict) or 'short_subtitles' not in corrected_data_entry:
            raise ValueError("返回结果不是预期的JSON对象格式")
        return corrected_data_entry
    except Exception as e:
        raise Exception(f"解析大模型返回结果失败: {e}")


def simple_fallback_correction(data_entry, story_board_clean):
    """
    简单的回退修正策略
    """
    subtitles = data_entry.get("short_subtitles", [])
    if subtitles:
        original_text = subtitles[0].get('text', '')
        subtitles[0]['text'] = ''.join(story_board_clean)
        for i in range(1, len(subtitles)):
            subtitles[i]['text'] = ""
        logger.info("    🔄 回退修正: 将故事板内容设置为第一个字幕")
        logger.info(f"      原始: '{original_text}'")
        logger.info(f"      修正: '{subtitles[0]['text']}'")
        return len(story_board_clean)
    return 0


def process_and_correct_json_file_in_place(file_path):
    """
    🆕 加载JSON文件，应用智能修正策略，并提供详细统计报告
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        logger.info(f"成功加载文件：{file_path}")

        # 🆕 详细统计变量
        total_entries = 0
        successful_corrections = 0
        no_correction_needed = 0
        problem_subtitles = 0
        processing_errors = 0
        failed_storyboards = []  # 记录失败的故事板

        if "story_boards" in data and isinstance(data["story_boards"], list):
            logger.info("\n## 检测到 'story_boards' 列表，开始智能修正处理 ##")
            for sb_entry in data["story_boards"]:
                total_entries += 1
                story_board_id = sb_entry.get('story_board_id', f'unknown_{total_entries}')

                try:
                    result = correct_subtitles_by_comparing_combined_text(sb_entry)

                    if result > 0:
                        successful_corrections += 1
                    elif result == 0:
                        no_correction_needed += 1
                    elif result == -1:  # 🆕 问题字幕
                        problem_subtitles += 1
                        failed_storyboards.append({
                            'story_board_id': story_board_id,
                            'reason': sb_entry.get('review_reason', '字符差异过大'),
                            'char_diff': sb_entry.get('char_difference', 'N/A'),
                            'severity': sb_entry.get('problem_severity', 'N/A')
                        })
                    else:
                        processing_errors += 1

                except Exception as e:
                    processing_errors += 1
                    logger.error(f"处理故事板 {story_board_id} 时出错: {e}")

            logger.info("## 所有故事板智能修正处理完成 ##")

        elif "story_board_id" in data and "short_subtitles" in data:
            logger.info("\n## 检测到单个故事板结构，开始智能修正处理 ##")
            total_entries = 1
            story_board_id = data.get('story_board_id', 'single_entry')

            try:
                result = correct_subtitles_by_comparing_combined_text(data)

                if result > 0:
                    successful_corrections += 1
                elif result == 0:
                    no_correction_needed += 1
                elif result == -1:
                    problem_subtitles += 1
                    failed_storyboards.append({
                        'story_board_id': story_board_id,
                        'reason': data.get('review_reason', '字符差异过大'),
                        'char_diff': data.get('char_difference', 'N/A'),
                        'severity': data.get('problem_severity', 'N/A')
                    })
                else:
                    processing_errors += 1

            except Exception as e:
                processing_errors += 1
                logger.error(f"处理单个故事板时出错: {e}")

            logger.info("## 单个故事板智能修正处理完成 ##")
        else:
            logger.warning("\n警告：JSON 文件结构不符合预期。跳过纠正。")
            return 0

        # 🆕 详细统计报告
        logger.info(f"\n{'=' * 60}")
        logger.info(f"📊 处理完成统计报告")
        logger.info(f"{'=' * 60}")
        logger.info(f"📋 总计处理: {total_entries} 个故事板")
        logger.info(f"✅ 成功修正: {successful_corrections} 个")
        logger.info(f"⭕ 无需修正: {no_correction_needed} 个")
        logger.info(f"❌ 问题字幕: {problem_subtitles} 个 (不建议使用)")
        logger.info(f"💥 处理错误: {processing_errors} 个")

        # 🆕 失败详情
        if failed_storyboards:
            logger.warning(f"\n🚨 问题字幕详情:")
            for failed in failed_storyboards:
                logger.warning(f"   ID: {failed['story_board_id']}")
                logger.warning(f"   原因: {failed['reason']}")
                logger.warning(f"   字符差异: {failed['char_diff']}")
                logger.warning(f"   严重程度: {failed['severity']}")
                logger.warning(f"   ---")

        if problem_subtitles > 0:
            logger.warning(f"\n⚠️ 发现 {problem_subtitles} 个问题字幕，建议:")
            logger.warning(f"   1. 检查字幕生成参数")
            logger.warning(f"   2. 重新生成这些字幕")
            logger.warning(f"   3. 或进行人工修正")

        success_rate = (successful_corrections + no_correction_needed) / total_entries * 100 if total_entries > 0 else 0
        logger.info(f"📈 整体成功率: {success_rate:.1f}%")

        # 保存文件
        with open(file_path, 'w', encoding='utf-8') as f_out:
            json.dump(data, f_out, ensure_ascii=False, indent=2)
        logger.info(f"\n💾 修正后的数据已保存回原文件：{file_path}")

        return successful_corrections  # 返回成功修正的数量
    except FileNotFoundError:
        logger.error(f"错误：文件 '{file_path}' 未找到。")
        return 0
    except json.JSONDecodeError:
        logger.error(f"错误：文件 '{file_path}' 不是有效的JSON格式。")
        return 0
    except Exception as e:
        logger.error(f"处理文件时发生未知错误: {e}")
        return 0


def calculate_content_similarity(text1, text2):
    """计算两个文本的内容相似度"""
    # 简单的字符级相似度计算
    if not text1 or not text2:
        return 0.0

    # 转换为字符集合
    chars1 = set(text1)
    chars2 = set(text2)

    # 计算交集和并集
    intersection = len(chars1 & chars2)
    union = len(chars1 | chars2)

    if union == 0:
        return 0.0

    return intersection / union


def correct_subtitles_by_comparing_combined_text(data_entry):
    """🆕 智能双策略字幕修正：字数相同用双指针，字数不同用动态规划"""
    story_board_full_text = data_entry.get("story_board", "")
    subtitles = data_entry.get("short_subtitles", [])
    if not story_board_full_text or not subtitles:
        logger.warning(f"警告：故事板 ID {data_entry.get('story_board_id', 'N/A')} 缺少内容。跳过纠正。")
        return 0

    # 🆕 根据策略选择不同的映射方式
    story_board_clean, _ = clean_text_and_map_indices(story_board_full_text)

    # 先用简单映射判断策略
    subtitles_clean_simple, subtitle_mapping_simple = get_combined_subtitles_with_mapping(subtitles)

    logger.info(f"\n--- 故事板 ID: {data_entry.get('story_board_id', 'N/A')} 的修正详情 ---")
    logger.info(f"📊 中文字符统计: 故事板{len(story_board_clean)}字, 字幕{len(subtitles_clean_simple)}字")

    # 🎯 策略选择：根据字符数差异选择算法
    chinese_chars_same = len(story_board_clean) == len(subtitles_clean_simple)
    char_diff = abs(len(story_board_clean) - len(subtitles_clean_simple))

    # 检查内容相似度
    content_similarity = calculate_content_similarity(story_board_full_text,
                                                      ''.join([sub.get('text', '') for sub in subtitles]))
    logger.info(f"📊 内容相似度: {content_similarity:.2f}")

    # 🆕 智能策略选择
    if chinese_chars_same:
        logger.info("✅ 中文字符数相同，使用双指针修正策略")
        return double_pointer_correction(data_entry, story_board_clean, subtitles_clean_simple, subtitle_mapping_simple)
    else:
        logger.info(f"🧠 中文字符数不同(差异{char_diff}字)")

        if char_diff <= 15:  # 差异不大，使用动态规划
            logger.info("使用动态规划修正策略")
            # 🆕 使用增强映射进行动态规划
            subtitles_clean_enhanced, char_mapping_enhanced = get_enhanced_subtitles_mapping(subtitles)
            return dynamic_programming_correction(data_entry, story_board_clean, subtitles_clean_enhanced,
                                                  char_mapping_enhanced)
        else:  # 差异太大，直接标记为问题字幕
            logger.warning(f"⚠️ 字符差异过大({char_diff}字)，标记为问题字幕")
            return mark_large_difference_cases(data_entry, story_board_clean, subtitles_clean_simple,
                                               subtitle_mapping_simple)


if __name__ == "__main__":
    # --- 配置你的JSON文件路径 ---
    # !!!重要：在运行前请务必备份你的原始JSON文件!!!
    json_file_path = r"E:\wjjz\ai-novel-clip\output\novel_step1\王府继兄宠我如宝，亲哥却后悔了\chapter1_阿兄万福\chapter1_story_boards.json"

    # --- 运行处理和纠错（直接在原文件上修改） ---
    try:
        process_and_correct_json_file_in_place(json_file_path)
    except Exception as e:
        logger.critical(f"程序执行失败: {e}")


# 🆕 ========== 动态规划修正算法 ==========

def calculate_edit_distance_with_pinyin(s1, s2):
    """
    计算带拼音权重的编辑距离矩阵
    """
    m, n = len(s1), len(s2)
    dp = [[float('inf')] * (n + 1) for _ in range(m + 1)]
    operations = [[None] * (n + 1) for _ in range(m + 1)]

    # 初始化边界
    for i in range(m + 1):
        dp[i][0] = i
        operations[i][0] = 'delete' if i > 0 else 'none'
    for j in range(n + 1):
        dp[0][j] = j
        operations[0][j] = 'insert' if j > 0 else 'none'

    # 填充DP表
    for i in range(1, m + 1):
        for j in range(1, n + 1):
            char1, char2 = s1[i - 1], s2[j - 1]

            if char1 == char2:
                cost = 0  # 完全匹配
                op = 'match'
            elif get_pinyin_without_tone(char1) == get_pinyin_without_tone(char2):
                cost = 0.1  # 同音字，很小的代价
                op = 'replace_homophone'
            else:
                cost = 1  # 不同字符
                op = 'replace'

            # 计算三种操作的代价
            delete_cost = dp[i - 1][j] + 1
            insert_cost = dp[i][j - 1] + 1
            replace_cost = dp[i - 1][j - 1] + cost

            # 选择代价最小的操作
            if replace_cost <= delete_cost and replace_cost <= insert_cost:
                dp[i][j] = replace_cost
                operations[i][j] = op
            elif delete_cost <= insert_cost:
                dp[i][j] = delete_cost
                operations[i][j] = 'delete'
            else:
                dp[i][j] = insert_cost
                operations[i][j] = 'insert'

    return dp, operations


def backtrack_corrections_optimized(operations, s1, s2):
    """
    优化的回溯算法，生成更精确的修正操作序列
    """
    corrections = []
    i, j = len(s1), len(s2)

    while i > 0 or j > 0:
        if i == 0:
            # 删除字幕中多余的字符
            corrections.append({
                'action': 'delete',
                'subtitle_pos': j - 1,  # 在字幕中的位置
                'char': s2[j - 1]
            })
            j -= 1
        elif j == 0:
            # 插入故事板中缺失的字符
            corrections.append({
                'action': 'insert',
                'subtitle_pos': j,  # 插入到字幕的位置
                'story_pos': i - 1,  # 在故事板中的位置
                'char': s1[i - 1]
            })
            i -= 1
        else:
            op = operations[i][j]

            if op == 'match':
                # 匹配，不需要操作
                i -= 1
                j -= 1
            elif op in ['replace', 'replace_homophone']:
                # 替换操作
                corrections.append({
                    'action': 'replace',
                    'subtitle_pos': j - 1,
                    'story_pos': i - 1,
                    'old_char': s2[j - 1],
                    'new_char': s1[i - 1],
                    'is_homophone': op == 'replace_homophone'
                })
                i -= 1
                j -= 1
            elif op == 'delete':
                # 插入故事板中的字符到字幕
                corrections.append({
                    'action': 'insert',
                    'subtitle_pos': j,
                    'story_pos': i - 1,
                    'char': s1[i - 1]
                })
                i -= 1
            elif op == 'insert':
                # 删除字幕中多余的字符
                corrections.append({
                    'action': 'delete',
                    'subtitle_pos': j - 1,
                    'char': s2[j - 1]
                })
                j -= 1

    # 反转操作序列
    corrections.reverse()
    return corrections


def find_optimal_insert_position(subtitle_pos, char_mapping, insert_char):
    """
    找到最优的插入位置
    """
    # 如果插入位置超出范围，插入到最后
    if subtitle_pos >= len(char_mapping):
        if char_mapping:
            last_mapping = char_mapping[-1]
            return {
                'subtitle_idx': last_mapping['subtitle_idx'],
                'char_idx': last_mapping['char_idx_in_subtitle'] + 1,
                'insert_type': 'append'
            }
        return None

    # 如果插入位置为0，插入到第一个字符前
    if subtitle_pos == 0:
        if char_mapping:
            first_mapping = char_mapping[0]
            return {
                'subtitle_idx': first_mapping['subtitle_idx'],
                'char_idx': 0,
                'insert_type': 'prepend'
            }
        return None

    # 正常情况：插入到指定位置前一个字符后面
    prev_mapping = char_mapping[subtitle_pos - 1]

    # 🆕 智能位置优化：根据插入字符的类型选择最佳位置
    if insert_char in ['儿', '子', '的', '了', '着']:
        # 后缀字符，应该紧跟在相关词语后面
        return {
            'subtitle_idx': prev_mapping['subtitle_idx'],
            'char_idx': prev_mapping['char_idx_in_subtitle'] + 1,
            'insert_type': 'after_word'
        }
    else:
        # 其他字符，正常插入
        return {
            'subtitle_idx': prev_mapping['subtitle_idx'],
            'char_idx': prev_mapping['char_idx_in_subtitle'] + 1,
            'insert_type': 'normal'
        }


def apply_corrections_optimized(subtitles, corrections, char_mapping):
    """
    优化的修正应用算法
    """
    subtitles_copy = copy.deepcopy(subtitles)
    applied_corrections = 0

    # 🆕 按操作类型分组处理
    replace_ops = [c for c in corrections if c['action'] == 'replace']
    delete_ops = [c for c in corrections if c['action'] == 'delete']
    insert_ops = [c for c in corrections if c['action'] == 'insert']

    # 1. 先处理替换操作（不影响位置）
    for correction in replace_ops:
        subtitle_pos = correction['subtitle_pos']
        if subtitle_pos < len(char_mapping):
            mapping = char_mapping[subtitle_pos]
            subtitle_idx = mapping['subtitle_idx']
            char_idx = mapping['char_idx_in_subtitle']

            subtitle_text = list(subtitles_copy[subtitle_idx]['text'])
            if char_idx < len(subtitle_text):
                subtitle_text[char_idx] = correction['new_char']
                subtitles_copy[subtitle_idx]['text'] = ''.join(subtitle_text)
                applied_corrections += 1

    # 2. 处理删除操作（按位置倒序，避免位置偏移）
    delete_ops.sort(key=lambda x: x['subtitle_pos'], reverse=True)
    for correction in delete_ops:
        subtitle_pos = correction['subtitle_pos']
        if subtitle_pos < len(char_mapping):
            mapping = char_mapping[subtitle_pos]
            subtitle_idx = mapping['subtitle_idx']
            char_idx = mapping['char_idx_in_subtitle']

            subtitle_text = list(subtitles_copy[subtitle_idx]['text'])
            if char_idx < len(subtitle_text):
                subtitle_text.pop(char_idx)
                subtitles_copy[subtitle_idx]['text'] = ''.join(subtitle_text)
                applied_corrections += 1

    # 3. 处理插入操作（按字幕索引分组，避免位置冲突）
    insert_by_subtitle = {}
    for correction in insert_ops:
        subtitle_pos = correction['subtitle_pos']
        insert_char = correction['char']

        # 找到最优插入位置
        insert_info = find_optimal_insert_position(
            subtitle_pos, char_mapping, insert_char
        )

        if insert_info:
            subtitle_idx = insert_info['subtitle_idx']
            char_idx = insert_info['char_idx']

            if subtitle_idx not in insert_by_subtitle:
                insert_by_subtitle[subtitle_idx] = []

            insert_by_subtitle[subtitle_idx].append({
                'char_idx': char_idx,
                'char': insert_char,
                'insert_type': insert_info['insert_type']
            })

    # 对每个字幕的插入操作按位置倒序排序
    for subtitle_idx, inserts in insert_by_subtitle.items():
        inserts.sort(key=lambda x: x['char_idx'], reverse=True)

        subtitle_text = subtitles_copy[subtitle_idx]['text']
        for insert_info in inserts:
            char_idx = insert_info['char_idx']
            insert_char = insert_info['char']

            # 执行插入
            new_text = subtitle_text[:char_idx] + insert_char + subtitle_text[char_idx:]
            subtitle_text = new_text
            applied_corrections += 1

        subtitles_copy[subtitle_idx]['text'] = subtitle_text

    return subtitles_copy, applied_corrections


def dynamic_programming_correction(data_entry, story_board_clean, subtitles_clean, char_mapping):
    """
    🆕 字符数不同时的动态规划修正策略
    """
    logger.info("  🧠 开始动态规划 + 拼音权重修正...")

    # 1. 计算带拼音权重的编辑距离
    dp, operations = calculate_edit_distance_with_pinyin(story_board_clean, subtitles_clean)

    # 2. 回溯生成修正操作序列
    corrections = backtrack_corrections_optimized(operations, story_board_clean, subtitles_clean)

    edit_distance = dp[-1][-1]
    logger.info(f"  📊 编辑距离: {edit_distance:.1f}, 需要修正: {len(corrections)} 处")

    if not corrections:
        logger.info("  ℹ️ 动态规划未发现需要修正的内容")
        return 0

    # 3. 显示修正操作详情
    logger.info(f"  🔧 修正操作详情:")
    for i, correction in enumerate(corrections, 1):
        action = correction['action']
        if action == 'replace':
            homophone = " (同音字)" if correction.get('is_homophone') else ""
            logger.info(
                f"    {i:2d}. 替换: 位置{correction['subtitle_pos']:2d} '{correction['old_char']}' → '{correction['new_char']}'{homophone}")
        elif action == 'insert':
            logger.info(f"    {i:2d}. 插入: 位置{correction['subtitle_pos']:2d} 插入 '{correction['char']}'")
        elif action == 'delete':
            logger.info(f"    {i:2d}. 删除: 位置{correction['subtitle_pos']:2d} 删除 '{correction['char']}'")

    # 4. 应用修正到字幕
    subtitles = data_entry.get("short_subtitles", [])
    corrected_subtitles, applied_corrections = apply_corrections_optimized(
        subtitles, corrections, char_mapping
    )

    # 5. 更新数据
    data_entry["short_subtitles"] = corrected_subtitles

    logger.info(f"  ✅ 动态规划修正完成，成功应用 {applied_corrections} 处修正")
    return applied_corrections