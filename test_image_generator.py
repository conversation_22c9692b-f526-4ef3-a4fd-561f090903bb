#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像生成器测试脚本
"""

import os
import asyncio
import logging
from simple_image_generator import SimpleImageGenerator

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_environment():
    """测试环境配置"""
    print("🔍 检查环境配置...")
    
    required_vars = [
        'VOLCENGINE_ACCESS_KEY',
        'VOLCENGINE_SECRET_KEY'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.environ.get(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ 缺少环境变量: {', '.join(missing_vars)}")
        print("请在 .env 文件中设置这些变量")
        return False
    
    print("✅ 环境变量配置正确")
    
    # 显示模型信息
    model = os.environ.get('VOLCENGINE_IMAGE_GEN_MODEL', 'high_aes_general_v20_L')
    print(f"📋 使用模型: {model}")
    
    return True


def test_generator_init():
    """测试生成器初始化"""
    print("\n🔧 测试生成器初始化...")
    
    try:
        generator = SimpleImageGenerator()
        print("✅ 生成器初始化成功")
        return generator
    except Exception as e:
        print(f"❌ 生成器初始化失败: {str(e)}")
        return None


def test_simple_generation(generator):
    """测试简单图像生成"""
    print("\n🎨 测试图像生成...")
    
    test_prompt = "一朵美丽的红玫瑰，简单背景，高清细节"
    
    try:
        print(f"提示词: {test_prompt}")
        
        response = generator.generate_image_sync(
            prompt=test_prompt,
            width=512,
            height=512,
            seed=12345,
            scale=7.5,
            ddim_steps=16
        )
        
        print("✅ API调用成功")
        
        # 检查响应
        if response.get("code") == 10000:
            print("✅ 图像生成成功")
            
            # 尝试获取图像URL
            image_url = generator.get_image_url_from_response(response)
            
            if image_url == "base64_data_available":
                print("📷 图像以base64格式返回")
                # 尝试保存
                if generator.save_base64_image(response, "test_image.jpg"):
                    print("✅ 图像已保存为 test_image.jpg")
                else:
                    print("❌ 保存图像失败")
            elif image_url:
                print(f"🔗 图像URL: {image_url}")
            else:
                print("⚠️ 未能获取图像URL")
                
            return True
        else:
            error_msg = response.get("message", "未知错误")
            print(f"❌ 图像生成失败: {error_msg}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False


async def test_async_generation(generator):
    """测试异步图像生成"""
    print("\n⚡ 测试异步图像生成...")
    
    test_prompt = "可爱的小猫咪，卡通风格"
    
    try:
        print(f"提示词: {test_prompt}")
        
        response = await generator.generate_image_async(
            prompt=test_prompt,
            width=512,
            height=512,
            seed=54321
        )
        
        print("✅ 异步API调用成功")
        
        if response.get("code") == 10000:
            print("✅ 异步图像生成成功")
            return True
        else:
            error_msg = response.get("message", "未知错误")
            print(f"❌ 异步图像生成失败: {error_msg}")
            return False
            
    except Exception as e:
        print(f"❌ 异步测试失败: {str(e)}")
        return False


def test_parameter_variations(generator):
    """测试不同参数组合"""
    print("\n🔧 测试参数变化...")
    
    test_cases = [
        {
            "name": "小尺寸图像",
            "params": {"width": 256, "height": 256, "ddim_steps": 8}
        },
        {
            "name": "高引导比例",
            "params": {"scale": 15.0, "ddim_steps": 20}
        },
        {
            "name": "固定种子",
            "params": {"seed": 999, "width": 512, "height": 512}
        }
    ]
    
    base_prompt = "简单的几何图形，抽象艺术"
    
    for i, test_case in enumerate(test_cases):
        print(f"\n  测试 {i+1}: {test_case['name']}")
        
        try:
            response = generator.generate_image_sync(
                prompt=base_prompt,
                **test_case['params']
            )
            
            if response.get("code") == 10000:
                print(f"  ✅ {test_case['name']} - 成功")
            else:
                print(f"  ❌ {test_case['name']} - 失败")
                
        except Exception as e:
            print(f"  ❌ {test_case['name']} - 错误: {str(e)}")


def main():
    """主测试函数"""
    print("🚀 开始图像生成器测试\n")
    
    # 1. 测试环境
    if not test_environment():
        return
    
    # 2. 测试初始化
    generator = test_generator_init()
    if not generator:
        return
    
    # 3. 测试基本生成
    if not test_simple_generation(generator):
        print("\n❌ 基本测试失败，跳过后续测试")
        return
    
    # 4. 测试异步生成
    try:
        asyncio.run(test_async_generation(generator))
    except Exception as e:
        print(f"❌ 异步测试出错: {str(e)}")
    
    # 5. 测试参数变化
    test_parameter_variations(generator)
    
    print("\n🎉 测试完成!")
    print("\n📝 测试总结:")
    print("- 如果所有测试都通过，说明配置正确，可以正常使用")
    print("- 如果有测试失败，请检查错误信息并调整配置")
    print("- 生成的测试图像保存为 test_image.jpg")


if __name__ == "__main__":
    main()
