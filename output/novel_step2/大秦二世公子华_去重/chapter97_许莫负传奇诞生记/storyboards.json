[{"chapter": 97, "story_board": "夜色沉沉，江油驿站灯火通明。公子华一进屋，就闻到一股熟悉的香味，那是肥肠的味道。他眼神一亮，立刻让随从去叫店家。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "走进驿站屋内，眼睛看向周围，随后招手让随从去叫店家", "expression": "兴奋"}, {"name": "秦王政", "gender": "男", "age": "青年", "clothes": "高领圆领黑色帝王冕服，主色调黑色，搭配金色，有龙纹，头戴冕旒，腰间系玉带，配玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "秦国大王", "other": "眼神如刀，威严十足", "from_chapter": 67, "action": "", "expression": ""}, {"name": "许莫负", "gender": "女", "age": "少年（前期）、青年（后期）", "clothes": "前期：高领圆领淡蓝色秦代女童服饰，主色调淡蓝色，搭配白色，有简单花卉纹样，头上扎双马尾，佩戴红色发带；后期：高领圆领淡粉色秦代少女长裙，主色调淡粉色，搭配浅黄色，裙摆绣有蝴蝶花纹，发髻上插着银质发簪圆形衣领，圆形衣领", "hairstyle": "前期为双马尾，黑色；后期为发髻，黑色", "figure": "苗条", "identity": "温城县具有神秘色彩的女孩", "other": "出生时手握带八卦图案的玉块", "from_chapter": [0], "action": "", "expression": ""}, {"name": "王刚", "gender": "男", "age": "中年", "clothes": "高领圆领黑色秦代武将铠甲，主色调黑色，搭配红色，铠甲上有虎纹图案，头戴黑色头盔，腰间束黑色皮带，配有佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "秦国武将，邛都管理者", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "许父", "gender": "男", "age": "中年", "clothes": "高领圆领褐色秦代普通男子服饰，主色调褐色，搭配土黄色，衣服有简单条纹，腰间系麻绳腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "许莫负的父亲", "other": "", "from_chapter": [0], "action": "", "expression": ""}], "scene": "驿站", "image_prompt": "中景，兴奋地走进驿站屋内、眼睛看向周围随后招手示意的青年男子，穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发；驿站屋内的背景；画面风格为动漫分镜插图, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753194595_20250722_222955.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_b88ab5b5-7246-4991-a8b0-d47721efb1a8_1.25x_20250722_203051.wav", "audio_duration": 10.581333, "video_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/videos/video_1753195043_20250722_223724.mp4", "story_board_id": 1, "subtitle": [{"subtitle_id": 1, "text": "夜色沉沉，", "timestamp": "00:00:00,240 --> 00:00:01,120", "duration": 0.88, "char_count": 5, "start_time_s": 0.24, "end_time_s": 1.12, "words": [{"attribute": {"event": "speech"}, "end_time": 360, "start_time": 240, "text": "夜"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 360, "text": "色"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 480, "text": "沉"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 520, "text": "沉"}], "keyword": "夜色"}, {"subtitle_id": 2, "text": "江油驿站灯火通明。", "timestamp": "00:00:01,120 --> 00:00:02,980", "duration": 1.86, "char_count": 9, "start_time_s": 1.12, "end_time_s": 2.98, "words": [{"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1120, "text": "江"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1320, "text": "油"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1480, "text": "驿"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1600, "text": "站"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1840, "text": "灯"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2080, "text": "火"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2280, "text": "通"}, {"attribute": {"event": "speech"}, "end_time": 2980, "start_time": 2440, "text": "明"}], "keyword": "江油驿站"}, {"subtitle_id": 3, "text": "公子华一进屋", "timestamp": "00:00:03,080 --> 00:00:04,320", "duration": 1.24, "char_count": 6, "start_time_s": 3.08, "end_time_s": 4.32, "words": [{"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3080, "text": "公"}, {"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3280, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3440, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3600, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3760, "text": "进"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 3920, "text": "屋"}], "keyword": "公子华"}, {"subtitle_id": 4, "text": "就闻到一股熟悉的香味，", "timestamp": "00:00:04,320 --> 00:00:06,000", "duration": 1.68, "char_count": 11, "start_time_s": 4.32, "end_time_s": 6.0, "words": [{"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4320, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4480, "text": "闻"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4640, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4760, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4880, "text": "股"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5080, "text": "熟"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5280, "text": "悉"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5400, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5520, "text": "香"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5720, "text": "味"}], "keyword": "香味"}, {"subtitle_id": 5, "text": "那是肥肠的味道。", "timestamp": "00:00:06,000 --> 00:00:07,500", "duration": 1.5, "char_count": 8, "start_time_s": 6.0, "end_time_s": 7.5, "words": [{"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6000, "text": "那"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6200, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6360, "text": "肥"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6600, "text": "肠"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6800, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6920, "text": "味"}, {"attribute": {"event": "speech"}, "end_time": 7500, "start_time": 7080, "text": "道"}], "keyword": "肥肠"}, {"subtitle_id": 6, "text": "他眼神一亮，", "timestamp": "00:00:07,600 --> 00:00:08,480", "duration": 0.88, "char_count": 6, "start_time_s": 7.6, "end_time_s": 8.48, "words": [{"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7600, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7800, "text": "眼"}, {"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7920, "text": "神"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8080, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8200, "text": "亮"}], "keyword": "眼神"}, {"subtitle_id": 7, "text": "立刻让随从去叫店家。", "timestamp": "00:00:08,480 --> 00:00:10,260", "duration": 1.78, "char_count": 10, "start_time_s": 8.48, "end_time_s": 10.26, "words": [{"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8480, "text": "立"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8680, "text": "刻"}, {"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8800, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 9160, "start_time": 8960, "text": "随"}, {"attribute": {"event": "speech"}, "end_time": 9320, "start_time": 9160, "text": "从"}, {"attribute": {"event": "speech"}, "end_time": 9520, "start_time": 9360, "text": "去"}, {"attribute": {"event": "speech"}, "end_time": 9640, "start_time": 9520, "text": "叫"}, {"attribute": {"event": "speech"}, "end_time": 9760, "start_time": 9640, "text": "店"}, {"attribute": {"event": "speech"}, "end_time": 10260, "start_time": 9800, "text": "家"}], "keyword": "店家"}], "keywords": ["夜色", "江油驿站", "公子华", "香味", "肥肠", "眼神", "店家"]}, {"chapter": 97, "story_board": "这一顿饭吃得热热闹闹，大家边吃边聊，气氛轻松了不少。可谁也没想到，这趟旅程才刚刚开始，真正的风暴正在暗处酝酿。在咸阳宫内，秦王政正盯着一份奏章，眉头紧锁。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "坐在饭桌旁吃饭聊天", "expression": "轻松"}, {"name": "秦王政", "gender": "男", "age": "青年", "clothes": "高领圆领黑色帝王冕服，主色调黑色，搭配金色，有龙纹，头戴冕旒，腰间系玉带，配玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "秦国大王", "other": "眼神如刀，威严十足", "from_chapter": 67, "action": "站在桌前盯着一份奏章", "expression": "眉头紧锁"}, {"name": "许莫负", "gender": "女", "age": "少年（前期）、青年（后期）", "clothes": "前期：高领圆领淡蓝色秦代女童服饰，主色调淡蓝色，搭配白色，有简单花卉纹样，头上扎双马尾，佩戴红色发带；后期：高领圆领淡粉色秦代少女长裙，主色调淡粉色，搭配浅黄色，裙摆绣有蝴蝶花纹，发髻上插着银质发簪圆形衣领，圆形衣领", "hairstyle": "前期为双马尾，黑色；后期为发髻，黑色", "figure": "苗条", "identity": "温城县具有神秘色彩的女孩", "other": "出生时手握带八卦图案的玉块", "from_chapter": [0], "action": "", "expression": ""}, {"name": "王刚", "gender": "男", "age": "中年", "clothes": "高领圆领黑色秦代武将铠甲，主色调黑色，搭配红色，铠甲上有虎纹图案，头戴黑色头盔，腰间束黑色皮带，配有佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "秦国武将，邛都管理者", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "许父", "gender": "男", "age": "中年", "clothes": "高领圆领褐色秦代普通男子服饰，主色调褐色，搭配土黄色，衣服有简单条纹，腰间系麻绳腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "许莫负的父亲", "other": "", "from_chapter": [0], "action": "", "expression": ""}], "scene": "驿站", "image_prompt": "中景，轻松地坐在饭桌旁吃饭的青年男子，穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发；眉头紧锁、眼神如刀、威严十足地站在桌前盯着一份奏章的青年男子，穿着高领圆领黑色帝王冕服，主色调黑色，搭配金色，有龙纹，头戴冕旒，腰间系玉带，配玉佩，圆形衣领，黑色束发，画面背景是驿站, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753195063_20250722_223743.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_81a33d39-749e-4f5e-8d80-2a7596d2c2c7_1.25x_20250722_203025.wav", "audio_duration": 13.077333, "video_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/videos/video_1753196630_20250722_230351.mp4", "story_board_id": 2, "subtitle": [{"subtitle_id": 1, "text": "这一顿饭吃的热热闹闹，", "timestamp": "00:00:00,200 --> 00:00:01,680", "duration": 1.48, "char_count": 11, "start_time_s": 0.2, "end_time_s": 1.68, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 400, "text": "顿"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 520, "text": "饭"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 720, "text": "吃"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 880, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 1000, "text": "热"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1040, "text": "热"}, {"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 1240, "text": "闹"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1280, "text": "闹"}], "keyword": "饭"}, {"subtitle_id": 2, "text": "大家边吃边聊，", "timestamp": "00:00:01,680 --> 00:00:02,760", "duration": 1.08, "char_count": 7, "start_time_s": 1.68, "end_time_s": 2.76, "words": [{"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1680, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1840, "text": "家"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1960, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2160, "text": "吃"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2280, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2400, "text": "聊"}], "keyword": "吃聊"}, {"subtitle_id": 3, "text": "气氛轻松了不少。", "timestamp": "00:00:02,760 --> 00:00:04,100", "duration": 1.34, "char_count": 8, "start_time_s": 2.76, "end_time_s": 4.1, "words": [{"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2760, "text": "气"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2920, "text": "氛"}, {"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3120, "text": "轻"}, {"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3280, "text": "松"}, {"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3440, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3520, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 4100, "start_time": 3680, "text": "少"}], "keyword": "气氛"}, {"subtitle_id": 4, "text": "可谁也没想到，", "timestamp": "00:00:04,200 --> 00:00:05,280", "duration": 1.08, "char_count": 7, "start_time_s": 4.2, "end_time_s": 5.28, "words": [{"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4200, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4400, "text": "谁"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4520, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4640, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4760, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 4920, "text": "到"}], "keyword": "谁"}, {"subtitle_id": 5, "text": "这趟旅程才刚刚开始，", "timestamp": "00:00:05,280 --> 00:00:07,000", "duration": 1.72, "char_count": 10, "start_time_s": 5.28, "end_time_s": 7.0, "words": [{"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5280, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5480, "text": "趟"}, {"attribute": {"event": "speech"}, "end_time": 5760, "start_time": 5640, "text": "旅"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5760, "text": "程"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5960, "text": "才"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6120, "text": "刚"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6160, "text": "刚"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6440, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6640, "text": "始"}], "keyword": "旅程"}, {"subtitle_id": 6, "text": "真正的风暴正在暗处酝酿。", "timestamp": "00:00:07,000 --> 00:00:09,180", "duration": 2.18, "char_count": 12, "start_time_s": 7.0, "end_time_s": 9.18, "words": [{"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 7000, "text": "真"}, {"attribute": {"event": "speech"}, "end_time": 7360, "start_time": 7200, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7360, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7480, "text": "风"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7600, "text": "暴"}, {"attribute": {"event": "speech"}, "end_time": 8000, "start_time": 7840, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 8000, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 8280, "start_time": 8120, "text": "暗"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8360, "text": "处"}, {"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8560, "text": "酝"}, {"attribute": {"event": "speech"}, "end_time": 9180, "start_time": 8720, "text": "酿"}], "keyword": "风暴"}, {"subtitle_id": 7, "text": "在咸阳宫内，", "timestamp": "00:00:09,280 --> 00:00:10,240", "duration": 0.96, "char_count": 6, "start_time_s": 9.28, "end_time_s": 10.24, "words": [{"attribute": {"event": "speech"}, "end_time": 9480, "start_time": 9280, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 9640, "start_time": 9480, "text": "咸"}, {"attribute": {"event": "speech"}, "end_time": 9760, "start_time": 9640, "text": "阳"}, {"attribute": {"event": "speech"}, "end_time": 9920, "start_time": 9760, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 10240, "start_time": 9920, "text": "内"}], "keyword": "咸阳宫"}, {"subtitle_id": 8, "text": "秦王正正盯着一份奏章，", "timestamp": "00:00:10,240 --> 00:00:11,880", "duration": 1.64, "char_count": 11, "start_time_s": 10.24, "end_time_s": 11.88, "words": [{"attribute": {"event": "speech"}, "end_time": 10400, "start_time": 10240, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 10560, "start_time": 10400, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 10600, "start_time": 10560, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 10920, "start_time": 10600, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 11040, "start_time": 10920, "text": "盯"}, {"attribute": {"event": "speech"}, "end_time": 11160, "start_time": 11040, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 11280, "start_time": 11160, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 11400, "start_time": 11280, "text": "份"}, {"attribute": {"event": "speech"}, "end_time": 11560, "start_time": 11400, "text": "奏"}, {"attribute": {"event": "speech"}, "end_time": 11880, "start_time": 11600, "text": "章"}], "keyword": "秦王"}, {"subtitle_id": 9, "text": "眉头紧锁。", "timestamp": "00:00:11,880 --> 00:00:12,980", "duration": 1.1, "char_count": 5, "start_time_s": 11.88, "end_time_s": 12.98, "words": [{"attribute": {"event": "speech"}, "end_time": 12120, "start_time": 11880, "text": "眉"}, {"attribute": {"event": "speech"}, "end_time": 12320, "start_time": 12120, "text": "头"}, {"attribute": {"event": "speech"}, "end_time": 12440, "start_time": 12320, "text": "紧"}, {"attribute": {"event": "speech"}, "end_time": 12980, "start_time": 12520, "text": "锁"}], "keyword": "奏章"}], "keywords": ["饭", "吃聊", "气氛", "谁", "旅程", "风暴", "咸阳宫", "秦王", "奏章"]}, {"chapter": 97, "story_board": "他的目光落在一个名字上——许莫负。这个女孩的出生，据说手握玉块，还带着神秘的八卦图案。这事儿传得沸沸扬扬，连他都忍不住亲自过问。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "坐在饭桌旁吃饭聊天", "expression": "轻松"}, {"name": "秦王政", "gender": "男", "age": "青年", "clothes": "高领圆领黑色帝王冕服，主色调黑色，搭配金色，有龙纹，头戴冕旒，腰间系玉带，配玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "秦国大王", "other": "眼神如刀，威严十足", "from_chapter": 67, "action": "盯着奏章，目光落在奏章上的名字处", "expression": "严肃、思索"}, {"name": "许莫负", "gender": "女", "age": "少年（前期）、青年（后期）", "clothes": "前期：高领圆领淡蓝色秦代女童服饰，主色调淡蓝色，搭配白色，有简单花卉纹样，头上扎双马尾，佩戴红色发带；后期：高领圆领淡粉色秦代少女长裙，主色调淡粉色，搭配浅黄色，裙摆绣有蝴蝶花纹，发髻上插着银质发簪圆形衣领，圆形衣领", "hairstyle": "前期为双马尾，黑色；后期为发髻，黑色", "figure": "苗条", "identity": "温城县具有神秘色彩的女孩", "other": "出生时手握带八卦图案的玉块", "from_chapter": [0], "action": "", "expression": ""}, {"name": "王刚", "gender": "男", "age": "中年", "clothes": "高领圆领黑色秦代武将铠甲，主色调黑色，搭配红色，铠甲上有虎纹图案，头戴黑色头盔，腰间束黑色皮带，配有佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "秦国武将，邛都管理者", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "许父", "gender": "男", "age": "中年", "clothes": "高领圆领褐色秦代普通男子服饰，主色调褐色，搭配土黄色，衣服有简单条纹，腰间系麻绳腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "许莫负的父亲", "other": "", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，一个青年男子站在皇宫大殿中，眼神如刀、威严十足且神情严肃地盯着奏章，目光落在奏章上的名字处，似乎在思索着什么。他穿着高领圆领的黑色帝王冕服，主色调为黑色，搭配金色，有龙纹，头戴冕旒，腰间系着玉带，配着玉佩，有着圆形衣领，黑色束发，身姿挺拔, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753196925_20250722_230846.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_39769229-17d0-4e00-b8e5-8568db246d2d_1.25x_20250722_203055.wav", "audio_duration": 10.717333, "video_url": "", "story_board_id": 3, "subtitle": [{"subtitle_id": 1, "text": "他的目光落在一个名字上许墨赋。", "timestamp": "00:00:00,200 --> 00:00:02,540", "duration": 2.34, "char_count": 15, "start_time_s": 0.2, "end_time_s": 2.54, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 360, "start_time": 280, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 360, "text": "目"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 520, "text": "光"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 680, "text": "落"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 840, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 960, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 1080, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1160, "text": "名"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1360, "text": "字"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1520, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1720, "text": "许"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1880, "text": "墨"}, {"attribute": {"event": "speech"}, "end_time": 2540, "start_time": 2040, "text": "赋"}], "keyword": "许墨赋"}, {"subtitle_id": 2, "text": "这个女孩的出生，", "timestamp": "00:00:02,640 --> 00:00:03,880", "duration": 1.24, "char_count": 8, "start_time_s": 2.64, "end_time_s": 3.88, "words": [{"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2640, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2800, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2920, "text": "女"}, {"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 3040, "text": "孩"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3160, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3240, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3400, "text": "生"}], "keyword": "女孩"}, {"subtitle_id": 3, "text": "据说手握玉块，", "timestamp": "00:00:03,880 --> 00:00:05,040", "duration": 1.16, "char_count": 7, "start_time_s": 3.88, "end_time_s": 5.04, "words": [{"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3880, "text": "据"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4040, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4200, "text": "手"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4360, "text": "握"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4480, "text": "玉"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4640, "text": "块"}], "keyword": "玉块"}, {"subtitle_id": 4, "text": "还带着神秘的八卦图案。", "timestamp": "00:00:05,040 --> 00:00:06,980", "duration": 1.94, "char_count": 11, "start_time_s": 5.04, "end_time_s": 6.98, "words": [{"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5040, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5240, "text": "带"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5400, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5560, "text": "神"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5720, "text": "秘"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5840, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6000, "text": "八"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6200, "text": "卦"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6360, "text": "图"}, {"attribute": {"event": "speech"}, "end_time": 6980, "start_time": 6520, "text": "案"}], "keyword": "八卦"}, {"subtitle_id": 5, "text": "这事传得沸沸扬扬，", "timestamp": "00:00:07,080 --> 00:00:08,520", "duration": 1.44, "char_count": 9, "start_time_s": 7.08, "end_time_s": 8.52, "words": [{"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7080, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7320, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7440, "text": "传"}, {"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7600, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7720, "text": "沸"}, {"attribute": {"event": "speech"}, "end_time": 8000, "start_time": 7760, "text": "沸"}, {"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 8040, "text": "扬"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8080, "text": "扬"}], "keyword": "事情"}, {"subtitle_id": 6, "text": "连他都忍不住亲自过问。", "timestamp": "00:00:08,520 --> 00:00:10,420", "duration": 1.9, "char_count": 11, "start_time_s": 8.52, "end_time_s": 10.42, "words": [{"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8520, "text": "连"}, {"attribute": {"event": "speech"}, "end_time": 8880, "start_time": 8680, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8880, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 9120, "start_time": 9000, "text": "忍"}, {"attribute": {"event": "speech"}, "end_time": 9280, "start_time": 9120, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 9400, "start_time": 9280, "text": "住"}, {"attribute": {"event": "speech"}, "end_time": 9640, "start_time": 9440, "text": "亲"}, {"attribute": {"event": "speech"}, "end_time": 9800, "start_time": 9640, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 9960, "start_time": 9800, "text": "过"}, {"attribute": {"event": "speech"}, "end_time": 10420, "start_time": 9960, "text": "问"}], "keyword": "他"}], "keywords": ["许墨赋", "女孩", "玉块", "八卦", "事情", "他"]}, {"chapter": 97, "story_board": "最终，他下令赏赐黄金百两，给那户人家。消息一出，整个温城县炸开了锅，所有人都在议论这个女孩的不凡。与此同时，蜀郡的公子华和王刚正在议事。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站着或坐着，身体微微前倾，神情专注地说话", "expression": "自信"}, {"name": "秦王政", "gender": "男", "age": "青年", "clothes": "高领圆领黑色帝王冕服，主色调黑色，搭配金色，有龙纹，头戴冕旒，腰间系玉带，配玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "秦国大王", "other": "眼神如刀，威严十足", "from_chapter": 67, "action": "坐在龙椅上，手指轻敲桌案后挥手下令", "expression": "严肃"}, {"name": "许莫负", "gender": "女", "age": "少年（前期）、青年（后期）", "clothes": "前期：高领圆领淡蓝色秦代女童服饰，主色调淡蓝色，搭配白色，有简单花卉纹样，头上扎双马尾，佩戴红色发带；后期：高领圆领淡粉色秦代少女长裙，主色调淡粉色，搭配浅黄色，裙摆绣有蝴蝶花纹，发髻上插着银质发簪圆形衣领，圆形衣领", "hairstyle": "前期为双马尾，黑色；后期为发髻，黑色", "figure": "苗条", "identity": "温城县具有神秘色彩的女孩", "other": "出生时手握带八卦图案的玉块", "from_chapter": [0], "action": "", "expression": ""}, {"name": "王刚", "gender": "男", "age": "中年", "clothes": "高领圆领黑色秦代武将铠甲，主色调黑色，搭配红色，铠甲上有虎纹图案，头戴黑色头盔，腰间束黑色皮带，配有佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "秦国武将，邛都管理者", "other": "", "from_chapter": [0], "action": "站着或坐着，身体坐直，认真倾听", "expression": "忐忑"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "许父", "gender": "男", "age": "中年", "clothes": "高领圆领褐色秦代普通男子服饰，主色调褐色，搭配土黄色，衣服有简单条纹，腰间系麻绳腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "许莫负的父亲", "other": "", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿中，一位青年男子坐在龙椅上，手指轻敲桌案后挥手下令，神情严肃，眼神如刀，威严十足；旁边一位青年男子身体微微前倾，神情专注，显得自信；还有一位中年男子身体坐直，认真倾听，神情忐忑。坐在龙椅上的青年男子穿着高领圆领黑色帝王冕服，主色调黑色，搭配金色，有龙纹，头戴冕旒，腰间系玉带，配玉佩，圆形衣领，黑色束发，身姿挺拔；身体微微前倾的青年男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发，身姿挺拔；身体坐直的中年男子穿着高领圆领黑色秦代武将铠甲，主色调黑色，搭配红色，铠甲上有虎纹图案，头戴黑色头盔，腰间束黑色皮带，配有佩剑，圆形衣领，黑色束发，身材壮实 ，动漫分镜风格, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753197473_20250722_231754.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_33ceb428-512a-4910-9651-aaef2dde85ed_1.25x_20250722_203034.wav", "audio_duration": 11.986667, "video_url": "", "story_board_id": 4, "subtitle": [{"subtitle_id": 1, "text": "最终，", "timestamp": "00:00:00,160 --> 00:00:00,600", "duration": 0.44, "char_count": 3, "start_time_s": 0.16, "end_time_s": 0.6, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "最"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 280, "text": "终"}], "keyword": "最终"}, {"subtitle_id": 2, "text": "他下令赏赐黄金百两给那户人家。", "timestamp": "00:00:00,600 --> 00:00:03,340", "duration": 2.74, "char_count": 15, "start_time_s": 0.6, "end_time_s": 3.34, "words": [{"attribute": {"event": "speech"}, "end_time": 760, "start_time": 600, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 760, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 920, "text": "令"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1080, "text": "赏"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1240, "text": "赐"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1360, "text": "黄"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1560, "text": "金"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1680, "text": "百"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1880, "text": "两"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2160, "text": "给"}, {"attribute": {"event": "speech"}, "end_time": 2520, "start_time": 2360, "text": "那"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2520, "text": "户"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2680, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 3340, "start_time": 2840, "text": "家"}], "keyword": "黄金百两"}, {"subtitle_id": 3, "text": "消息一出，", "timestamp": "00:00:03,480 --> 00:00:04,280", "duration": 0.8, "char_count": 5, "start_time_s": 3.48, "end_time_s": 4.28, "words": [{"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3480, "text": "消"}, {"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3680, "text": "息"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3800, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 3880, "text": "出"}], "keyword": "消息"}, {"subtitle_id": 4, "text": "整个温城县炸开了锅，", "timestamp": "00:00:04,280 --> 00:00:05,920", "duration": 1.64, "char_count": 10, "start_time_s": 4.28, "end_time_s": 5.92, "words": [{"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4280, "text": "整"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4480, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4640, "text": "温"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4800, "text": "城"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 4960, "text": "县"}, {"attribute": {"event": "speech"}, "end_time": 5320, "start_time": 5160, "text": "炸"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5320, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5480, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5560, "text": "锅"}], "keyword": "温城县"}, {"subtitle_id": 5, "text": "所有人都在议论这个女孩的不凡。", "timestamp": "00:00:05,920 --> 00:00:08,220", "duration": 2.3, "char_count": 15, "start_time_s": 5.92, "end_time_s": 8.22, "words": [{"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5920, "text": "所"}, {"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 6120, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6240, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6400, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6560, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6720, "text": "议"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6880, "text": "论"}, {"attribute": {"event": "speech"}, "end_time": 7120, "start_time": 7000, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7120, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7240, "text": "女"}, {"attribute": {"event": "speech"}, "end_time": 7520, "start_time": 7400, "text": "孩"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7520, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7600, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 8220, "start_time": 7760, "text": "凡"}], "keyword": "女孩"}, {"subtitle_id": 6, "text": "与此同时，", "timestamp": "00:00:08,360 --> 00:00:09,240", "duration": 0.88, "char_count": 5, "start_time_s": 8.36, "end_time_s": 9.24, "words": [{"attribute": {"event": "speech"}, "end_time": 8560, "start_time": 8360, "text": "与"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8560, "text": "此"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8640, "text": "同"}, {"attribute": {"event": "speech"}, "end_time": 9240, "start_time": 8800, "text": "时"}], "keyword": "与此同时"}, {"subtitle_id": 7, "text": "蜀郡的龚子华和王刚正在议事。", "timestamp": "00:00:09,240 --> 00:00:11,700", "duration": 2.46, "char_count": 14, "start_time_s": 9.24, "end_time_s": 11.7, "words": [{"attribute": {"event": "speech"}, "end_time": 9440, "start_time": 9240, "text": "蜀"}, {"attribute": {"event": "speech"}, "end_time": 9560, "start_time": 9480, "text": "郡"}, {"attribute": {"event": "speech"}, "end_time": 9680, "start_time": 9560, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 9800, "start_time": 9680, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 9920, "start_time": 9800, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 10080, "start_time": 9920, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 10240, "start_time": 10080, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 10400, "start_time": 10240, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 10560, "start_time": 10400, "text": "刚"}, {"attribute": {"event": "speech"}, "end_time": 10840, "start_time": 10680, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 11040, "start_time": 10880, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 11200, "start_time": 11040, "text": "议"}, {"attribute": {"event": "speech"}, "end_time": 11700, "start_time": 11200, "text": "事"}], "keyword": "蜀郡"}], "keywords": ["最终", "黄金百两", "消息", "温城县", "女孩", "与此同时", "蜀郡"]}, {"chapter": 97, "story_board": "王刚刚被派往邛都，心中还有些忐忑。毕竟他是个武将，对政务并不熟悉。公子华却信心满满，告诉他只要管理好工厂和船只，再打通官道就行。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "拍着王刚的肩膀", "expression": "信心满满"}, {"name": "秦王政", "gender": "男", "age": "青年", "clothes": "高领圆领黑色帝王冕服，主色调黑色，搭配金色，有龙纹，头戴冕旒，腰间系玉带，配玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "秦国大王", "other": "眼神如刀，威严十足", "from_chapter": 67, "action": "坐在龙椅上，手指轻敲桌案后挥手下令", "expression": "严肃"}, {"name": "许莫负", "gender": "女", "age": "少年（前期）、青年（后期）", "clothes": "前期：高领圆领淡蓝色秦代女童服饰，主色调淡蓝色，搭配白色，有简单花卉纹样，头上扎双马尾，佩戴红色发带；后期：高领圆领淡粉色秦代少女长裙，主色调淡粉色，搭配浅黄色，裙摆绣有蝴蝶花纹，发髻上插着银质发簪圆形衣领，圆形衣领", "hairstyle": "前期为双马尾，黑色；后期为发髻，黑色", "figure": "苗条", "identity": "温城县具有神秘色彩的女孩", "other": "出生时手握带八卦图案的玉块", "from_chapter": [0], "action": "", "expression": ""}, {"name": "王刚", "gender": "男", "age": "中年", "clothes": "高领圆领黑色秦代武将铠甲，主色调黑色，搭配红色，铠甲上有虎纹图案，头戴黑色头盔，腰间束黑色皮带，配有佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "秦国武将，邛都管理者", "other": "", "from_chapter": [0], "action": "身体微微紧绷，双手不自觉地握拳", "expression": "忐忑"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "许父", "gender": "男", "age": "中年", "clothes": "高领圆领褐色秦代普通男子服饰，主色调褐色，搭配土黄色，衣服有简单条纹，腰间系麻绳腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "许莫负的父亲", "other": "", "from_chapter": [0], "action": "", "expression": ""}], "scene": "古代工厂", "image_prompt": "中景，在古代工厂里，一个信心满满的青年男子拍着身旁中年男子的肩膀，中年男子身体微微紧绷，双手不自觉地握拳，神情忐忑。青年男子挺拔，穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发；中年男子壮实，穿着高领圆领黑色秦代武将铠甲，主色调黑色，搭配红色，铠甲上有虎纹图案，头戴黑色头盔，腰间束黑色皮带，配有佩剑，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753197895_20250722_232455.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_253a00c3-b3de-48b4-82d8-b8a0af3a43ba_1.25x_20250722_203043.wav", "audio_duration": 11.274667, "video_url": "", "story_board_id": 5, "subtitle": [{"subtitle_id": 1, "text": "王刚刚被派往琼兜，", "timestamp": "00:00:00,160 --> 00:00:01,560", "duration": 1.4, "char_count": 9, "start_time_s": 0.16, "end_time_s": 1.56, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 320, "start_time": 280, "text": "刚"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 320, "text": "刚"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 560, "text": "被"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 720, "text": "派"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 880, "text": "往"}, {"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 1000, "text": "琼"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1200, "text": "兜"}], "keyword": "王刚"}, {"subtitle_id": 2, "text": "心中还有些忐忑，", "timestamp": "00:00:01,560 --> 00:00:02,940", "duration": 1.38, "char_count": 8, "start_time_s": 1.56, "end_time_s": 2.94, "words": [{"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1560, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1720, "text": "中"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1840, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 2000, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2120, "text": "些"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2240, "text": "忐"}, {"attribute": {"event": "speech"}, "end_time": 2940, "start_time": 2480, "text": "忑"}], "keyword": "忐忑"}, {"subtitle_id": 3, "text": "毕竟他是个武将，", "timestamp": "00:00:03,080 --> 00:00:04,160", "duration": 1.08, "char_count": 8, "start_time_s": 3.08, "end_time_s": 4.16, "words": [{"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3080, "text": "毕"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3240, "text": "竟"}, {"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3400, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3520, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3640, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3760, "text": "武"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 3880, "text": "将"}], "keyword": "武将"}, {"subtitle_id": 4, "text": "对正武并不熟悉。", "timestamp": "00:00:04,160 --> 00:00:05,660", "duration": 1.5, "char_count": 8, "start_time_s": 4.16, "end_time_s": 5.66, "words": [{"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4160, "text": "对"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4320, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4520, "text": "武"}, {"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4680, "text": "并"}, {"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4840, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5000, "text": "熟"}, {"attribute": {"event": "speech"}, "end_time": 5660, "start_time": 5200, "text": "悉"}], "keyword": "正武"}, {"subtitle_id": 5, "text": "龚子华却信心满满，", "timestamp": "00:00:05,760 --> 00:00:07,160", "duration": 1.4, "char_count": 9, "start_time_s": 5.76, "end_time_s": 7.16, "words": [{"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5760, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5920, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6040, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6240, "text": "却"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6400, "text": "信"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6520, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6680, "text": "满"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 6720, "text": "满"}], "keyword": "龚子华"}, {"subtitle_id": 6, "text": "告诉他只要管理好工厂和船只，", "timestamp": "00:00:07,160 --> 00:00:09,440", "duration": 2.28, "char_count": 14, "start_time_s": 7.16, "end_time_s": 9.44, "words": [{"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7160, "text": "告"}, {"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7320, "text": "诉"}, {"attribute": {"event": "speech"}, "end_time": 7640, "start_time": 7440, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 7880, "start_time": 7720, "text": "只"}, {"attribute": {"event": "speech"}, "end_time": 8000, "start_time": 7880, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 8000, "text": "管"}, {"attribute": {"event": "speech"}, "end_time": 8240, "start_time": 8120, "text": "理"}, {"attribute": {"event": "speech"}, "end_time": 8440, "start_time": 8240, "text": "好"}, {"attribute": {"event": "speech"}, "end_time": 8560, "start_time": 8440, "text": "工"}, {"attribute": {"event": "speech"}, "end_time": 8760, "start_time": 8600, "text": "厂"}, {"attribute": {"event": "speech"}, "end_time": 8920, "start_time": 8760, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 9080, "start_time": 8920, "text": "船"}, {"attribute": {"event": "speech"}, "end_time": 9440, "start_time": 9120, "text": "只"}], "keyword": "工厂"}, {"subtitle_id": 7, "text": "再打通官道就行。", "timestamp": "00:00:09,440 --> 00:00:10,940", "duration": 1.5, "char_count": 8, "start_time_s": 9.44, "end_time_s": 10.94, "words": [{"attribute": {"event": "speech"}, "end_time": 9600, "start_time": 9440, "text": "再"}, {"attribute": {"event": "speech"}, "end_time": 9760, "start_time": 9600, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 9920, "start_time": 9760, "text": "通"}, {"attribute": {"event": "speech"}, "end_time": 10120, "start_time": 9920, "text": "官"}, {"attribute": {"event": "speech"}, "end_time": 10320, "start_time": 10120, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 10440, "start_time": 10320, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 10940, "start_time": 10440, "text": "行"}], "keyword": "官道"}], "keywords": ["王刚", "忐忑", "武将", "正武", "龚子华", "工厂", "官道"]}, {"chapter": 97, "story_board": "章邯在一旁打趣说，邛都比关中暖和多了，自己都想过去住。这话让王刚也动了心，但他还是点头答应，准备启程。几天后，公子华一行人离开成都，一路向西赶路。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "带着一行人离开成都向西赶路", "expression": "沉稳自信"}, {"name": "秦王政", "gender": "男", "age": "青年", "clothes": "高领圆领黑色帝王冕服，主色调黑色，搭配金色，有龙纹，头戴冕旒，腰间系玉带，配玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "秦国大王", "other": "眼神如刀，威严十足", "from_chapter": 67, "action": "坐在龙椅上，手指轻敲桌案后挥手下令", "expression": "严肃"}, {"name": "许莫负", "gender": "女", "age": "少年（前期）、青年（后期）", "clothes": "前期：高领圆领淡蓝色秦代女童服饰，主色调淡蓝色，搭配白色，有简单花卉纹样，头上扎双马尾，佩戴红色发带；后期：高领圆领淡粉色秦代少女长裙，主色调淡粉色，搭配浅黄色，裙摆绣有蝴蝶花纹，发髻上插着银质发簪圆形衣领，圆形衣领", "hairstyle": "前期为双马尾，黑色；后期为发髻，黑色", "figure": "苗条", "identity": "温城县具有神秘色彩的女孩", "other": "出生时手握带八卦图案的玉块", "from_chapter": [0], "action": "", "expression": ""}, {"name": "王刚", "gender": "男", "age": "中年", "clothes": "高领圆领黑色秦代武将铠甲，主色调黑色，搭配红色，铠甲上有虎纹图案，头戴黑色头盔，腰间束黑色皮带，配有佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "秦国武将，邛都管理者", "other": "", "from_chapter": [0], "action": "先是被打动，然后点头", "expression": "犹豫后坚定"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "站在一旁说话", "expression": "轻松、打趣"}, {"name": "许父", "gender": "男", "age": "中年", "clothes": "高领圆领褐色秦代普通男子服饰，主色调褐色，搭配土黄色，衣服有简单条纹，腰间系麻绳腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "许莫负的父亲", "other": "", "from_chapter": [0], "action": "", "expression": ""}], "scene": "古代县城", "image_prompt": "中景，画面中，一个魁梧的青年男子站在一旁，神情轻松、带着打趣的表情，他身着高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾；旁边一个壮实的中年男子先是呈现被打动的神情，随后点头，表情犹豫后变得坚定，他穿着高领圆领黑色秦代武将铠甲，主色调黑色，搭配红色，铠甲上有虎纹图案，头戴黑色头盔，腰间束黑色皮带，配有佩剑；不远处，一个挺拔的青年男子带着一行人离开古代县城向西赶路，神情沉稳自信，他身着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩。画面以古代县城为背景。, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753198279_20250722_233119.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_7c1f9c16-0783-4b52-96d2-a6a0e34fab7c_1.25x_20250722_203021.wav", "audio_duration": 12.426667, "video_url": "", "story_board_id": 6, "subtitle": [{"subtitle_id": 1, "text": "张晗在一旁打趣说", "timestamp": "00:00:00,160 --> 00:00:01,520", "duration": 1.36, "char_count": 8, "start_time_s": 0.16, "end_time_s": 1.52, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "张"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 320, "text": "晗"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 480, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 600, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 680, "text": "旁"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 840, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 1000, "text": "趣"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1120, "text": "说"}], "keyword": "张晗"}, {"subtitle_id": 2, "text": "琼都比关中暖和多了，", "timestamp": "00:00:01,520 --> 00:00:02,960", "duration": 1.44, "char_count": 10, "start_time_s": 1.52, "end_time_s": 2.96, "words": [{"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1520, "text": "琼"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1720, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1840, "text": "比"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1960, "text": "关"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2120, "text": "中"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2280, "text": "暖"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2440, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2560, "text": "多"}, {"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2680, "text": "了"}], "keyword": "琼都"}, {"subtitle_id": 3, "text": "自己都想过去住。", "timestamp": "00:00:02,960 --> 00:00:04,300", "duration": 1.34, "char_count": 8, "start_time_s": 2.96, "end_time_s": 4.3, "words": [{"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 2960, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3160, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3320, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3480, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3640, "text": "过"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3720, "text": "去"}, {"attribute": {"event": "speech"}, "end_time": 4300, "start_time": 3840, "text": "住"}], "keyword": "住"}, {"subtitle_id": 4, "text": "这话让王刚也动了心，", "timestamp": "00:00:04,400 --> 00:00:06,040", "duration": 1.64, "char_count": 10, "start_time_s": 4.4, "end_time_s": 6.04, "words": [{"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4400, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4600, "text": "话"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4800, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 4960, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5120, "text": "刚"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5280, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5400, "text": "动"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5520, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5640, "text": "心"}], "keyword": "王刚"}, {"subtitle_id": 5, "text": "但他还是点头答应，", "timestamp": "00:00:06,040 --> 00:00:07,480", "duration": 1.44, "char_count": 9, "start_time_s": 6.04, "end_time_s": 7.48, "words": [{"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6040, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6320, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6560, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6720, "text": "点"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6880, "text": "头"}, {"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 7040, "text": "答"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7200, "text": "应"}], "keyword": "答应"}, {"subtitle_id": 6, "text": "准备启程。", "timestamp": "00:00:07,480 --> 00:00:08,420", "duration": 0.94, "char_count": 5, "start_time_s": 7.48, "end_time_s": 8.42, "words": [{"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7480, "text": "准"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7600, "text": "备"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7760, "text": "启"}, {"attribute": {"event": "speech"}, "end_time": 8420, "start_time": 7920, "text": "程"}], "keyword": "启程"}, {"subtitle_id": 7, "text": "几天后，龚子华一行人离开成都，", "timestamp": "00:00:08,600 --> 00:00:10,960", "duration": 2.36, "char_count": 15, "start_time_s": 8.6, "end_time_s": 10.96, "words": [{"attribute": {"event": "speech"}, "end_time": 8720, "start_time": 8600, "text": "几"}, {"attribute": {"event": "speech"}, "end_time": 8880, "start_time": 8720, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 9080, "start_time": 8880, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 9440, "start_time": 9280, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 9600, "start_time": 9440, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 9720, "start_time": 9600, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 9840, "start_time": 9720, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 9960, "start_time": 9840, "text": "行"}, {"attribute": {"event": "speech"}, "end_time": 10120, "start_time": 9960, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 10280, "start_time": 10120, "text": "离"}, {"attribute": {"event": "speech"}, "end_time": 10440, "start_time": 10280, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 10600, "start_time": 10440, "text": "成"}, {"attribute": {"event": "speech"}, "end_time": 10960, "start_time": 10640, "text": "都"}], "keyword": "龚子华"}, {"subtitle_id": 8, "text": "一路向西赶路。", "timestamp": "00:00:10,960 --> 00:00:12,340", "duration": 1.38, "char_count": 7, "start_time_s": 10.96, "end_time_s": 12.34, "words": [{"attribute": {"event": "speech"}, "end_time": 11200, "start_time": 10960, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 11320, "start_time": 11200, "text": "路"}, {"attribute": {"event": "speech"}, "end_time": 11560, "start_time": 11360, "text": "向"}, {"attribute": {"event": "speech"}, "end_time": 11720, "start_time": 11560, "text": "西"}, {"attribute": {"event": "speech"}, "end_time": 11840, "start_time": 11720, "text": "赶"}, {"attribute": {"event": "speech"}, "end_time": 12340, "start_time": 11880, "text": "路"}], "keyword": "成都"}], "keywords": ["张晗", "琼都", "住", "王刚", "答应", "启程", "龚子华", "成都"]}, {"chapter": 97, "story_board": "他们来到江油，这里的一切都让他感到熟悉。他想起前世曾多次来此，最爱的就是这里的肥肠。一顿饭后，大家放松下来，各自休息。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站在江油驿站内环顾四周，之后坐在饭桌前吃饭，饭后回房休息", "expression": "怀念、满足"}, {"name": "秦王政", "gender": "男", "age": "青年", "clothes": "高领圆领黑色帝王冕服，主色调黑色，搭配金色，有龙纹，头戴冕旒，腰间系玉带，配玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "秦国大王", "other": "眼神如刀，威严十足", "from_chapter": 67, "action": "坐在龙椅上，手指轻敲桌案后挥手下令", "expression": "严肃"}, {"name": "许莫负", "gender": "女", "age": "少年（前期）、青年（后期）", "clothes": "前期：高领圆领淡蓝色秦代女童服饰，主色调淡蓝色，搭配白色，有简单花卉纹样，头上扎双马尾，佩戴红色发带；后期：高领圆领淡粉色秦代少女长裙，主色调淡粉色，搭配浅黄色，裙摆绣有蝴蝶花纹，发髻上插着银质发簪圆形衣领，圆形衣领", "hairstyle": "前期为双马尾，黑色；后期为发髻，黑色", "figure": "苗条", "identity": "温城县具有神秘色彩的女孩", "other": "出生时手握带八卦图案的玉块", "from_chapter": [0], "action": "", "expression": ""}, {"name": "王刚", "gender": "男", "age": "中年", "clothes": "高领圆领黑色秦代武将铠甲，主色调黑色，搭配红色，铠甲上有虎纹图案，头戴黑色头盔，腰间束黑色皮带，配有佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "秦国武将，邛都管理者", "other": "", "from_chapter": [0], "action": "先是被打动，然后点头", "expression": "犹豫后坚定"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "站在一旁说话", "expression": "轻松、打趣"}, {"name": "许父", "gender": "男", "age": "中年", "clothes": "高领圆领褐色秦代普通男子服饰，主色调褐色，搭配土黄色，衣服有简单条纹，腰间系麻绳腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "许莫负的父亲", "other": "", "from_chapter": [0], "action": "", "expression": ""}], "scene": "驿站", "image_prompt": "中景，一位怀念且满足的青年男子先站在江油驿站内环顾四周，之后坐在饭桌前吃饭，饭后回房休息。该男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，有着圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753198686_20250722_233806.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_dee3d338-7328-497d-bd05-662ecda3f46b_1.25x_20250722_203059.wav", "audio_duration": 10.216, "video_url": "", "story_board_id": 7, "subtitle": [{"subtitle_id": 1, "text": "他们来到江油，", "timestamp": "00:00:00,200 --> 00:00:01,120", "duration": 0.92, "char_count": 7, "start_time_s": 0.2, "end_time_s": 1.12, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 360, "start_time": 280, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 360, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 480, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 600, "text": "江"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 760, "text": "油"}], "keyword": "江油"}, {"subtitle_id": 2, "text": "这里的一切都让他感到熟悉，", "timestamp": "00:00:01,120 --> 00:00:03,420", "duration": 2.3, "char_count": 13, "start_time_s": 1.12, "end_time_s": 3.42, "words": [{"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1120, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1320, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1440, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1560, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1720, "text": "切"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1920, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2120, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2280, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2440, "text": "感"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2600, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2760, "text": "熟"}, {"attribute": {"event": "speech"}, "end_time": 3420, "start_time": 2960, "text": "悉"}], "keyword": "一切"}, {"subtitle_id": 3, "text": "他想起前世曾多次来此，", "timestamp": "00:00:03,520 --> 00:00:05,400", "duration": 1.88, "char_count": 11, "start_time_s": 3.52, "end_time_s": 5.4, "words": [{"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3520, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3680, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3840, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4080, "text": "前"}, {"attribute": {"event": "speech"}, "end_time": 4440, "start_time": 4320, "text": "世"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4480, "text": "曾"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4600, "text": "多"}, {"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4760, "text": "次"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4880, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5040, "text": "此"}], "keyword": "前世"}, {"subtitle_id": 4, "text": "最爱的就是这里的肥肠。", "timestamp": "00:00:05,400 --> 00:00:07,060", "duration": 1.66, "char_count": 11, "start_time_s": 5.4, "end_time_s": 7.06, "words": [{"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5400, "text": "最"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5560, "text": "爱"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5720, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5840, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 6080, "start_time": 5960, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 6080, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6240, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6320, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6400, "text": "肥"}, {"attribute": {"event": "speech"}, "end_time": 7060, "start_time": 6560, "text": "肠"}], "keyword": "肥肠"}, {"subtitle_id": 5, "text": "一顿饭后，", "timestamp": "00:00:07,120 --> 00:00:07,920", "duration": 0.8, "char_count": 5, "start_time_s": 7.12, "end_time_s": 7.92, "words": [{"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7120, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7320, "text": "顿"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7440, "text": "饭"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7600, "text": "后"}], "keyword": "饭"}, {"subtitle_id": 6, "text": "大家放松下来，", "timestamp": "00:00:07,920 --> 00:00:08,920", "duration": 1.0, "char_count": 7, "start_time_s": 7.92, "end_time_s": 8.92, "words": [{"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7920, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8080, "text": "家"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8200, "text": "放"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8360, "text": "松"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8520, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 8920, "start_time": 8640, "text": "来"}], "keyword": "大家"}, {"subtitle_id": 7, "text": "各自休息。", "timestamp": "00:00:08,920 --> 00:00:09,900", "duration": 0.98, "char_count": 5, "start_time_s": 8.92, "end_time_s": 9.9, "words": [{"attribute": {"event": "speech"}, "end_time": 9120, "start_time": 8920, "text": "各"}, {"attribute": {"event": "speech"}, "end_time": 9280, "start_time": 9120, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 9480, "start_time": 9320, "text": "休"}, {"attribute": {"event": "speech"}, "end_time": 9900, "start_time": 9480, "text": "息"}], "keyword": "休息"}], "keywords": ["江油", "一切", "前世", "肥肠", "饭", "大家", "休息"]}, {"chapter": 97, "story_board": "然而，就在他们入睡时，远处的燕国正密谋着一场针对秦王政的行动。原来，燕国一直在寻找机会，想要削弱秦国的力量。而许莫负的出现，让他们看到了希望。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站在江油驿站内环顾四周，之后坐在饭桌前吃饭，饭后回房休息", "expression": "怀念、满足"}, {"name": "秦王政", "gender": "男", "age": "青年", "clothes": "高领圆领黑色帝王冕服，主色调黑色，搭配金色，有龙纹，头戴冕旒，腰间系玉带，配玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "秦国大王", "other": "眼神如刀，威严十足", "from_chapter": 67, "action": "坐在宫殿书桌前处理政务", "expression": "专注"}, {"name": "许莫负", "gender": "女", "age": "少年（前期）、青年（后期）", "clothes": "前期：高领圆领淡蓝色秦代女童服饰，主色调淡蓝色，搭配白色，有简单花卉纹样，头上扎双马尾，佩戴红色发带；后期：高领圆领淡粉色秦代少女长裙，主色调淡粉色，搭配浅黄色，裙摆绣有蝴蝶花纹，发髻上插着银质发簪圆形衣领，圆形衣领", "hairstyle": "前期为双马尾，黑色；后期为发髻，黑色", "figure": "苗条", "identity": "温城县具有神秘色彩的女孩", "other": "出生时手握带八卦图案的玉块", "from_chapter": [0], "action": "无", "expression": "无"}, {"name": "王刚", "gender": "男", "age": "中年", "clothes": "高领圆领黑色秦代武将铠甲，主色调黑色，搭配红色，铠甲上有虎纹图案，头戴黑色头盔，腰间束黑色皮带，配有佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "秦国武将，邛都管理者", "other": "", "from_chapter": [0], "action": "先是被打动，然后点头", "expression": "犹豫后坚定"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "站在一旁说话", "expression": "轻松、打趣"}, {"name": "许父", "gender": "男", "age": "中年", "clothes": "高领圆领褐色秦代普通男子服饰，主色调褐色，搭配土黄色，衣服有简单条纹，腰间系麻绳腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "许莫负的父亲", "other": "", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，专注地坐在宫殿书桌前处理政务、眼神如刀、威严十足的青年男子，旁边站着一位少女。青年男子穿着高领圆领黑色帝王冕服，主色调黑色，搭配金色，有龙纹，头戴冕旒，腰间系玉带，配玉佩，圆形衣领，黑色束发；少女穿着高领圆领淡粉色秦代少女长裙，主色调淡粉色，搭配浅黄色，裙摆绣有蝴蝶花纹，发髻上插着银质发簪，圆形衣领，黑色发髻，苗条，手中握着带八卦图案的玉块。背景是皇宫大殿, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753199115_20250722_234516.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_01203272-6e95-4fdd-b940-6033f8f4c250_1.25x_20250722_203046.wav", "audio_duration": 12.866667, "video_url": "", "story_board_id": 8, "subtitle": [{"subtitle_id": 1, "text": "然而就在他们入睡时，", "timestamp": "00:00:00,160 --> 00:00:01,880", "duration": 1.72, "char_count": 10, "start_time_s": 0.16, "end_time_s": 1.88, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 600, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 800, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 920, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 1040, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 1120, "text": "入"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1320, "text": "睡"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1520, "text": "时"}], "keyword": "燕国"}, {"subtitle_id": 2, "text": "远处的燕国", "timestamp": "00:00:01,880 --> 00:00:02,800", "duration": 0.92, "char_count": 5, "start_time_s": 1.88, "end_time_s": 2.8, "words": [{"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1880, "text": "远"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2080, "text": "处"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2240, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2360, "text": "燕"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2520, "text": "国"}], "keyword": "燕国"}, {"subtitle_id": 3, "text": "正密谋着一场针对秦王政的行动。", "timestamp": "00:00:02,800 --> 00:00:05,620", "duration": 2.82, "char_count": 15, "start_time_s": 2.8, "end_time_s": 5.62, "words": [{"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2800, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 3000, "text": "密"}, {"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3160, "text": "谋"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3320, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3400, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3560, "text": "场"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3840, "text": "针"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4040, "text": "对"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4240, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4440, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4640, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4800, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 4960, "text": "行"}, {"attribute": {"event": "speech"}, "end_time": 5620, "start_time": 5120, "text": "动"}], "keyword": "行动"}, {"subtitle_id": 4, "text": "原来，燕国一直在寻找机会，", "timestamp": "00:00:05,680 --> 00:00:07,880", "duration": 2.2, "char_count": 13, "start_time_s": 5.68, "end_time_s": 7.88, "words": [{"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5680, "text": "原"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5840, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6240, "text": "燕"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6440, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6600, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6760, "text": "直"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6920, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 7080, "text": "寻"}, {"attribute": {"event": "speech"}, "end_time": 7360, "start_time": 7240, "text": "找"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7400, "text": "机"}, {"attribute": {"event": "speech"}, "end_time": 7880, "start_time": 7560, "text": "会"}], "keyword": "燕国"}, {"subtitle_id": 5, "text": "想要削弱秦国的力量，", "timestamp": "00:00:07,880 --> 00:00:09,620", "duration": 1.74, "char_count": 10, "start_time_s": 7.88, "end_time_s": 9.62, "words": [{"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7880, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 8160, "start_time": 8040, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8160, "text": "削"}, {"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8320, "text": "弱"}, {"attribute": {"event": "speech"}, "end_time": 8720, "start_time": 8560, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 8920, "start_time": 8760, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 9040, "start_time": 8920, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 9160, "start_time": 9040, "text": "力"}, {"attribute": {"event": "speech"}, "end_time": 9620, "start_time": 9160, "text": "量"}], "keyword": "削弱"}, {"subtitle_id": 6, "text": "而许莫夫的出现让他们看到了希望。", "timestamp": "00:00:09,680 --> 00:00:12,540", "duration": 2.86, "char_count": 16, "start_time_s": 9.68, "end_time_s": 12.54, "words": [{"attribute": {"event": "speech"}, "end_time": 9960, "start_time": 9680, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 10120, "start_time": 9960, "text": "许"}, {"attribute": {"event": "speech"}, "end_time": 10280, "start_time": 10120, "text": "莫"}, {"attribute": {"event": "speech"}, "end_time": 10400, "start_time": 10280, "text": "夫"}, {"attribute": {"event": "speech"}, "end_time": 10520, "start_time": 10400, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 10680, "start_time": 10520, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 10880, "start_time": 10680, "text": "现"}, {"attribute": {"event": "speech"}, "end_time": 11200, "start_time": 11000, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 11360, "start_time": 11200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 11440, "start_time": 11360, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 11640, "start_time": 11440, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 11840, "start_time": 11680, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 11920, "start_time": 11840, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 12080, "start_time": 11960, "text": "希"}, {"attribute": {"event": "speech"}, "end_time": 12540, "start_time": 12080, "text": "望"}], "keyword": "许莫夫"}], "keywords": ["燕国", "燕国", "行动", "燕国", "削弱", "许莫夫"]}, {"chapter": 97, "story_board": "有人认为，这个女孩可能是天命所归，将来或许能成为改变局势的关键。于是，燕国的秘密组织开始策划，打算利用她来制造混乱。而在咸阳，秦王政并没有察觉到危险。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站在江油驿站内环顾四周，之后坐在饭桌前吃饭，饭后回房休息", "expression": "怀念、满足"}, {"name": "秦王政", "gender": "男", "age": "青年", "clothes": "高领圆领黑色帝王冕服，主色调黑色，搭配金色，有龙纹，头戴冕旒，腰间系玉带，配玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "秦国大王", "other": "眼神如刀，威严十足", "from_chapter": 67, "action": "坐在宫殿书桌前处理政务", "expression": "专注"}, {"name": "许莫负", "gender": "女", "age": "少年（前期）、青年（后期）", "clothes": "前期：高领圆领淡蓝色秦代女童服饰，主色调淡蓝色，搭配白色，有简单花卉纹样，头上扎双马尾，佩戴红色发带；后期：高领圆领淡粉色秦代少女长裙，主色调淡粉色，搭配浅黄色，裙摆绣有蝴蝶花纹，发髻上插着银质发簪圆形衣领，圆形衣领", "hairstyle": "前期为双马尾，黑色；后期为发髻，黑色", "figure": "苗条", "identity": "温城县具有神秘色彩的女孩", "other": "出生时手握带八卦图案的玉块", "from_chapter": [0], "action": "无", "expression": "无"}, {"name": "王刚", "gender": "男", "age": "中年", "clothes": "高领圆领黑色秦代武将铠甲，主色调黑色，搭配红色，铠甲上有虎纹图案，头戴黑色头盔，腰间束黑色皮带，配有佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "秦国武将，邛都管理者", "other": "", "from_chapter": [0], "action": "先是被打动，然后点头", "expression": "犹豫后坚定"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "站在一旁说话", "expression": "轻松、打趣"}, {"name": "许父", "gender": "男", "age": "中年", "clothes": "高领圆领褐色秦代普通男子服饰，主色调褐色，搭配土黄色，衣服有简单条纹，腰间系麻绳腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "许莫负的父亲", "other": "", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，一位眼神如刀、威严十足且专注的青年男子坐在宫殿书桌前处理政务，他挺拔地坐着。该男子穿着高领圆领黑色帝王冕服，主色调为黑色，搭配金色，有龙纹，头戴冕旒，腰间系玉带，配玉佩，有着圆形衣领，黑色束发，背景是皇宫大殿, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753199510_20250722_235151.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_4a11585b-5acd-49c7-b1df-403c58d44e8f_1.25x_20250722_203030.wav", "audio_duration": 12.408, "video_url": "", "story_board_id": 9, "subtitle": [{"subtitle_id": 1, "text": "有人认为，", "timestamp": "00:00:00,160 --> 00:00:00,880", "duration": 0.72, "char_count": 5, "start_time_s": 0.16, "end_time_s": 0.88, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 360, "start_time": 280, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 360, "text": "认"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 480, "text": "为"}], "keyword": "有人"}, {"subtitle_id": 2, "text": "这个女孩可能是天命所归，", "timestamp": "00:00:00,880 --> 00:00:02,720", "duration": 1.84, "char_count": 12, "start_time_s": 0.88, "end_time_s": 2.72, "words": [{"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 880, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 1040, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1120, "text": "女"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1240, "text": "孩"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1400, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1520, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1640, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1800, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1960, "text": "命"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2120, "text": "所"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2280, "text": "归"}], "keyword": "女孩"}, {"subtitle_id": 3, "text": "将来或许能成为改变局势的关键。", "timestamp": "00:00:02,720 --> 00:00:05,020", "duration": 2.3, "char_count": 15, "start_time_s": 2.72, "end_time_s": 5.02, "words": [{"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2720, "text": "将"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2880, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 3000, "text": "或"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3160, "text": "许"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3240, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3360, "text": "成"}, {"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3480, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3600, "text": "改"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3840, "text": "变"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4000, "text": "局"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4200, "text": "势"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4280, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4400, "text": "关"}, {"attribute": {"event": "speech"}, "end_time": 5020, "start_time": 4560, "text": "键"}], "keyword": "关键"}, {"subtitle_id": 4, "text": "于是，燕国的秘密组织开始策划，", "timestamp": "00:00:05,120 --> 00:00:07,480", "duration": 2.36, "char_count": 15, "start_time_s": 5.12, "end_time_s": 7.48, "words": [{"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5120, "text": "于"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5280, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5680, "text": "燕"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5880, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 6040, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6120, "text": "秘"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6200, "text": "密"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6400, "text": "组"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6560, "text": "织"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6680, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6840, "text": "始"}, {"attribute": {"event": "speech"}, "end_time": 7120, "start_time": 6960, "text": "策"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7120, "text": "划"}], "keyword": "燕国"}, {"subtitle_id": 5, "text": "打算利用她来制造混乱。", "timestamp": "00:00:07,480 --> 00:00:09,300", "duration": 1.82, "char_count": 11, "start_time_s": 7.48, "end_time_s": 9.3, "words": [{"attribute": {"event": "speech"}, "end_time": 7640, "start_time": 7480, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7640, "text": "算"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7800, "text": "利"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7920, "text": "用"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8040, "text": "她"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8200, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8360, "text": "制"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8520, "text": "造"}, {"attribute": {"event": "speech"}, "end_time": 8840, "start_time": 8680, "text": "混"}, {"attribute": {"event": "speech"}, "end_time": 9300, "start_time": 8840, "text": "乱"}], "keyword": "利用"}, {"subtitle_id": 6, "text": "而在咸阳，", "timestamp": "00:00:09,400 --> 00:00:10,280", "duration": 0.88, "char_count": 5, "start_time_s": 9.4, "end_time_s": 10.28, "words": [{"attribute": {"event": "speech"}, "end_time": 9600, "start_time": 9400, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 9720, "start_time": 9600, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 9880, "start_time": 9720, "text": "咸"}, {"attribute": {"event": "speech"}, "end_time": 10280, "start_time": 9880, "text": "阳"}], "keyword": "咸阳"}, {"subtitle_id": 7, "text": "秦王正并没有察觉到危险。", "timestamp": "00:00:10,280 --> 00:00:12,340", "duration": 2.06, "char_count": 12, "start_time_s": 10.28, "end_time_s": 12.34, "words": [{"attribute": {"event": "speech"}, "end_time": 10440, "start_time": 10280, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 10560, "start_time": 10440, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 10760, "start_time": 10600, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 10960, "start_time": 10800, "text": "并"}, {"attribute": {"event": "speech"}, "end_time": 11080, "start_time": 10960, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 11200, "start_time": 11080, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 11400, "start_time": 11200, "text": "察"}, {"attribute": {"event": "speech"}, "end_time": 11560, "start_time": 11440, "text": "觉"}, {"attribute": {"event": "speech"}, "end_time": 11680, "start_time": 11560, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 11880, "start_time": 11680, "text": "危"}, {"attribute": {"event": "speech"}, "end_time": 12340, "start_time": 11880, "text": "险"}], "keyword": "秦王"}], "keywords": ["有人", "女孩", "关键", "燕国", "利用", "咸阳", "秦王"]}, {"chapter": 97, "story_board": "他忙着处理朝政，同时关注着许莫负的成长。他知道，这个女孩的未来可能关系到国家的命运。于是，他下令加强边境防御，并派人秘密调查许莫负的背景。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站在江油驿站内环顾四周，之后坐在饭桌前吃饭，饭后回房休息", "expression": "怀念、满足"}, {"name": "秦王政", "gender": "男", "age": "青年", "clothes": "高领圆领黑色帝王冕服，主色调黑色，搭配金色，有龙纹，头戴冕旒，腰间系玉带，配玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "秦国大王", "other": "眼神如刀，威严十足", "from_chapter": 67, "action": "坐在宫殿中处理朝政，随后下令加强边境防御、派人秘密调查许莫负背景", "expression": "严肃、专注"}, {"name": "许莫负", "gender": "女", "age": "少年（前期）、青年（后期）", "clothes": "前期：高领圆领淡蓝色秦代女童服饰，主色调淡蓝色，搭配白色，有简单花卉纹样，头上扎双马尾，佩戴红色发带；后期：高领圆领淡粉色秦代少女长裙，主色调淡粉色，搭配浅黄色，裙摆绣有蝴蝶花纹，发髻上插着银质发簪圆形衣领，圆形衣领", "hairstyle": "前期为双马尾，黑色；后期为发髻，黑色", "figure": "苗条", "identity": "温城县具有神秘色彩的女孩", "other": "出生时手握带八卦图案的玉块", "from_chapter": [0], "action": "无", "expression": "无"}, {"name": "王刚", "gender": "男", "age": "中年", "clothes": "高领圆领黑色秦代武将铠甲，主色调黑色，搭配红色，铠甲上有虎纹图案，头戴黑色头盔，腰间束黑色皮带，配有佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "秦国武将，邛都管理者", "other": "", "from_chapter": [0], "action": "先是被打动，然后点头", "expression": "犹豫后坚定"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "站在一旁说话", "expression": "轻松、打趣"}, {"name": "许父", "gender": "男", "age": "中年", "clothes": "高领圆领褐色秦代普通男子服饰，主色调褐色，搭配土黄色，衣服有简单条纹，腰间系麻绳腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "许莫负的父亲", "other": "", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，一个严肃专注、眼神如刀、威严十足的青年男子坐在皇宫大殿中处理朝政，随后做出下令的动作。他身着高领圆领黑色帝王冕服，主色调为黑色，搭配金色，有龙纹，头戴冕旒，腰间系着玉带，配有玉佩，圆形衣领，黑色束发，身姿挺拔, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753199923_20250722_235844.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_58ef8f32-f8f5-4e04-a772-1959bb17067b_1.25x_20250722_203214.wav", "audio_duration": 11.869333, "video_url": "", "story_board_id": 10, "subtitle": [{"subtitle_id": 1, "text": "他忙着处理朝政，", "timestamp": "00:00:00,200 --> 00:00:01,400", "duration": 1.2, "char_count": 8, "start_time_s": 0.2, "end_time_s": 1.4, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "忙"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 440, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 560, "text": "处"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 720, "text": "理"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 880, "text": "朝"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1080, "text": "政"}], "keyword": "他"}, {"subtitle_id": 2, "text": "同时关注着许墨赋的成长。", "timestamp": "00:00:01,400 --> 00:00:03,460", "duration": 2.06, "char_count": 12, "start_time_s": 1.4, "end_time_s": 3.46, "words": [{"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1400, "text": "同"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1640, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1800, "text": "关"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1960, "text": "注"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2120, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2240, "text": "许"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2400, "text": "墨"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2560, "text": "赋"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2680, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2800, "text": "成"}, {"attribute": {"event": "speech"}, "end_time": 3460, "start_time": 3000, "text": "长"}], "keyword": "许墨赋"}, {"subtitle_id": 3, "text": "他知道这个女孩的未来", "timestamp": "00:00:03,560 --> 00:00:05,360", "duration": 1.8, "char_count": 10, "start_time_s": 3.56, "end_time_s": 5.36, "words": [{"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3560, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3760, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3920, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 4440, "start_time": 4280, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4440, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4560, "text": "女"}, {"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4720, "text": "孩"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4840, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 4920, "text": "未"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5080, "text": "来"}], "keyword": "女孩"}, {"subtitle_id": 4, "text": "可能关系到国家的命运，", "timestamp": "00:00:05,360 --> 00:00:07,220", "duration": 1.86, "char_count": 11, "start_time_s": 5.36, "end_time_s": 7.22, "words": [{"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5360, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5520, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5640, "text": "关"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5800, "text": "系"}, {"attribute": {"event": "speech"}, "end_time": 6080, "start_time": 5960, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6120, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 6480, "start_time": 6320, "text": "家"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6480, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6600, "text": "命"}, {"attribute": {"event": "speech"}, "end_time": 7220, "start_time": 6720, "text": "运"}], "keyword": "国家"}, {"subtitle_id": 5, "text": "于是他下令加强边境防御，", "timestamp": "00:00:07,320 --> 00:00:09,480", "duration": 2.16, "char_count": 12, "start_time_s": 7.32, "end_time_s": 9.48, "words": [{"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7320, "text": "于"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7480, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 8000, "start_time": 7840, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 8160, "start_time": 8000, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 8280, "start_time": 8160, "text": "令"}, {"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8280, "text": "加"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8480, "text": "强"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8640, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8840, "text": "境"}, {"attribute": {"event": "speech"}, "end_time": 9160, "start_time": 9000, "text": "防"}, {"attribute": {"event": "speech"}, "end_time": 9480, "start_time": 9200, "text": "御"}], "keyword": "边境"}, {"subtitle_id": 6, "text": "并派人秘密调查许墨赋的背景。", "timestamp": "00:00:09,480 --> 00:00:11,780", "duration": 2.3, "char_count": 14, "start_time_s": 9.48, "end_time_s": 11.78, "words": [{"attribute": {"event": "speech"}, "end_time": 9680, "start_time": 9480, "text": "并"}, {"attribute": {"event": "speech"}, "end_time": 9840, "start_time": 9680, "text": "派"}, {"attribute": {"event": "speech"}, "end_time": 10000, "start_time": 9840, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 10080, "start_time": 10000, "text": "秘"}, {"attribute": {"event": "speech"}, "end_time": 10240, "start_time": 10080, "text": "密"}, {"attribute": {"event": "speech"}, "end_time": 10440, "start_time": 10280, "text": "调"}, {"attribute": {"event": "speech"}, "end_time": 10640, "start_time": 10440, "text": "查"}, {"attribute": {"event": "speech"}, "end_time": 10800, "start_time": 10680, "text": "许"}, {"attribute": {"event": "speech"}, "end_time": 10960, "start_time": 10800, "text": "墨"}, {"attribute": {"event": "speech"}, "end_time": 11080, "start_time": 10960, "text": "赋"}, {"attribute": {"event": "speech"}, "end_time": 11160, "start_time": 11080, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 11320, "start_time": 11160, "text": "背"}, {"attribute": {"event": "speech"}, "end_time": 11780, "start_time": 11360, "text": "景"}], "keyword": "许墨赋"}], "keywords": ["他", "许墨赋", "女孩", "国家", "边境", "许墨赋"]}, {"chapter": 97, "story_board": "与此同时，公子华在江油的停留，也让他的行程变得复杂起来。他原本计划尽快赶到咸阳，但因为美食和休息，多留了一晚。这一决定看似随意，却意外地让他避过了某个危险的时刻。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "坐在驿站房间里休息，脸上带着满足的神情", "expression": "惬意"}, {"name": "秦王政", "gender": "男", "age": "青年", "clothes": "高领圆领黑色帝王冕服，主色调黑色，搭配金色，有龙纹，头戴冕旒，腰间系玉带，配玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "秦国大王", "other": "眼神如刀，威严十足", "from_chapter": 67, "action": "坐在宫殿中处理朝政，随后下令加强边境防御、派人秘密调查许莫负背景", "expression": "严肃、专注"}, {"name": "许莫负", "gender": "女", "age": "少年（前期）、青年（后期）", "clothes": "前期：高领圆领淡蓝色秦代女童服饰，主色调淡蓝色，搭配白色，有简单花卉纹样，头上扎双马尾，佩戴红色发带；后期：高领圆领淡粉色秦代少女长裙，主色调淡粉色，搭配浅黄色，裙摆绣有蝴蝶花纹，发髻上插着银质发簪圆形衣领，圆形衣领", "hairstyle": "前期为双马尾，黑色；后期为发髻，黑色", "figure": "苗条", "identity": "温城县具有神秘色彩的女孩", "other": "出生时手握带八卦图案的玉块", "from_chapter": [0], "action": "无", "expression": "无"}, {"name": "王刚", "gender": "男", "age": "中年", "clothes": "高领圆领黑色秦代武将铠甲，主色调黑色，搭配红色，铠甲上有虎纹图案，头戴黑色头盔，腰间束黑色皮带，配有佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "秦国武将，邛都管理者", "other": "", "from_chapter": [0], "action": "先是被打动，然后点头", "expression": "犹豫后坚定"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "站在一旁说话", "expression": "轻松、打趣"}, {"name": "许父", "gender": "男", "age": "中年", "clothes": "高领圆领褐色秦代普通男子服饰，主色调褐色，搭配土黄色，衣服有简单条纹，腰间系麻绳腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "许莫负的父亲", "other": "", "from_chapter": [0], "action": "", "expression": ""}], "scene": "驿站", "image_prompt": "中景，一个脸上带着满足神情、惬意地坐在驿站房间里休息的青年男子，挺拔身姿，言辞犀利，敢言善辩。该男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发，驿站作为背景, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753200405_20250723_000645.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_aadd12e3-9fd3-4b1d-8b86-6454aa3983f8_1.25x_20250722_203155.wav", "audio_duration": 13.864, "video_url": "", "story_board_id": 11, "subtitle": [{"subtitle_id": 1, "text": "与此同时，", "timestamp": "00:00:00,200 --> 00:00:00,920", "duration": 0.72, "char_count": 5, "start_time_s": 0.2, "end_time_s": 0.92, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "与"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "此"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 400, "text": "同"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 520, "text": "时"}], "keyword": "同时"}, {"subtitle_id": 2, "text": "龚子华在江油的停留", "timestamp": "00:00:00,920 --> 00:00:02,480", "duration": 1.56, "char_count": 9, "start_time_s": 0.92, "end_time_s": 2.48, "words": [{"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 920, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1080, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1240, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1440, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1560, "text": "江"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1720, "text": "油"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1880, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2000, "text": "停"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2200, "text": "留"}], "keyword": "龚子华"}, {"subtitle_id": 3, "text": "也让他的行程变得复杂起来。", "timestamp": "00:00:02,480 --> 00:00:04,860", "duration": 2.38, "char_count": 13, "start_time_s": 2.48, "end_time_s": 4.86, "words": [{"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2480, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2640, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2840, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2960, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3080, "text": "行"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3280, "text": "程"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3560, "text": "变"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3760, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3880, "text": "复"}, {"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4120, "text": "杂"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4280, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 4860, "start_time": 4400, "text": "来"}], "keyword": "行程"}, {"subtitle_id": 4, "text": "他原本计划尽快赶到咸阳，", "timestamp": "00:00:04,880 --> 00:00:06,920", "duration": 2.04, "char_count": 12, "start_time_s": 4.88, "end_time_s": 6.92, "words": [{"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4880, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5040, "text": "原"}, {"attribute": {"event": "speech"}, "end_time": 5320, "start_time": 5200, "text": "本"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5360, "text": "计"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5480, "text": "划"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5720, "text": "尽"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5880, "text": "快"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6040, "text": "赶"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6160, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6320, "text": "咸"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6440, "text": "阳"}], "keyword": "咸阳"}, {"subtitle_id": 5, "text": "但因为美食和休息多留了一晚，", "timestamp": "00:00:06,920 --> 00:00:09,180", "duration": 2.26, "char_count": 14, "start_time_s": 6.92, "end_time_s": 9.18, "words": [{"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6920, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 7040, "text": "因"}, {"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 7160, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7280, "text": "美"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7480, "text": "食"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7600, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7760, "text": "休"}, {"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7920, "text": "息"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8160, "text": "多"}, {"attribute": {"event": "speech"}, "end_time": 8560, "start_time": 8400, "text": "留"}, {"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8560, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 8760, "start_time": 8680, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 9180, "start_time": 8760, "text": "晚"}], "keyword": "美食"}, {"subtitle_id": 6, "text": "这一决定看似随意，", "timestamp": "00:00:09,320 --> 00:00:10,760", "duration": 1.44, "char_count": 9, "start_time_s": 9.32, "end_time_s": 10.76, "words": [{"attribute": {"event": "speech"}, "end_time": 9520, "start_time": 9320, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 9600, "start_time": 9520, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 9760, "start_time": 9600, "text": "决"}, {"attribute": {"event": "speech"}, "end_time": 9880, "start_time": 9760, "text": "定"}, {"attribute": {"event": "speech"}, "end_time": 10080, "start_time": 9880, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 10240, "start_time": 10080, "text": "似"}, {"attribute": {"event": "speech"}, "end_time": 10400, "start_time": 10240, "text": "随"}, {"attribute": {"event": "speech"}, "end_time": 10760, "start_time": 10400, "text": "意"}], "keyword": "决定"}, {"subtitle_id": 7, "text": "却意外的", "timestamp": "00:00:10,760 --> 00:00:11,440", "duration": 0.68, "char_count": 4, "start_time_s": 10.76, "end_time_s": 11.44, "words": [{"attribute": {"event": "speech"}, "end_time": 10920, "start_time": 10760, "text": "却"}, {"attribute": {"event": "speech"}, "end_time": 11080, "start_time": 10920, "text": "意"}, {"attribute": {"event": "speech"}, "end_time": 11240, "start_time": 11080, "text": "外"}, {"attribute": {"event": "speech"}, "end_time": 11440, "start_time": 11240, "text": "的"}], "keyword": "意外"}, {"subtitle_id": 8, "text": "让他避过了某个危险的时刻。", "timestamp": "00:00:11,440 --> 00:00:13,540", "duration": 2.1, "char_count": 13, "start_time_s": 11.44, "end_time_s": 13.54, "words": [{"attribute": {"event": "speech"}, "end_time": 11600, "start_time": 11440, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 11760, "start_time": 11600, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 11920, "start_time": 11760, "text": "避"}, {"attribute": {"event": "speech"}, "end_time": 12080, "start_time": 11960, "text": "过"}, {"attribute": {"event": "speech"}, "end_time": 12200, "start_time": 12080, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 12360, "start_time": 12200, "text": "某"}, {"attribute": {"event": "speech"}, "end_time": 12480, "start_time": 12360, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 12680, "start_time": 12480, "text": "危"}, {"attribute": {"event": "speech"}, "end_time": 12840, "start_time": 12680, "text": "险"}, {"attribute": {"event": "speech"}, "end_time": 12960, "start_time": 12840, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 13080, "start_time": 12960, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 13540, "start_time": 13080, "text": "刻"}], "keyword": "危险"}], "keywords": ["同时", "龚子华", "行程", "咸阳", "美食", "决定", "意外", "危险"]}, {"chapter": 97, "story_board": "后来才知道，有人在他们经过的路上设下了陷阱，若不是多留一晚，后果不堪设想。回到蜀郡，王刚正式接任邛都的职务。他按照公子华的指示，开始着手工厂和官道的建设。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "无", "expression": "无"}, {"name": "秦王政", "gender": "男", "age": "青年", "clothes": "高领圆领黑色帝王冕服，主色调黑色，搭配金色，有龙纹，头戴冕旒，腰间系玉带，配玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "秦国大王", "other": "眼神如刀，威严十足", "from_chapter": 67, "action": "坐在宫殿中处理朝政，随后下令加强边境防御、派人秘密调查许莫负背景", "expression": "严肃、专注"}, {"name": "许莫负", "gender": "女", "age": "少年（前期）、青年（后期）", "clothes": "前期：高领圆领淡蓝色秦代女童服饰，主色调淡蓝色，搭配白色，有简单花卉纹样，头上扎双马尾，佩戴红色发带；后期：高领圆领淡粉色秦代少女长裙，主色调淡粉色，搭配浅黄色，裙摆绣有蝴蝶花纹，发髻上插着银质发簪圆形衣领，圆形衣领", "hairstyle": "前期为双马尾，黑色；后期为发髻，黑色", "figure": "苗条", "identity": "温城县具有神秘色彩的女孩", "other": "出生时手握带八卦图案的玉块", "from_chapter": [0], "action": "无", "expression": "无"}, {"name": "王刚", "gender": "男", "age": "中年", "clothes": "高领圆领黑色秦代武将铠甲，主色调黑色，搭配红色，铠甲上有虎纹图案，头戴黑色头盔，腰间束黑色皮带，配有佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "秦国武将，邛都管理者", "other": "", "from_chapter": [0], "action": "开始着手工厂和官道的建设", "expression": "坚定"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "站在一旁说话", "expression": "轻松、打趣"}, {"name": "许父", "gender": "男", "age": "中年", "clothes": "高领圆领褐色秦代普通男子服饰，主色调褐色，搭配土黄色，衣服有简单条纹，腰间系麻绳腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "许莫负的父亲", "other": "", "from_chapter": [0], "action": "", "expression": ""}], "scene": "古代工厂", "image_prompt": "中景，一个言辞犀利、挺拔的青年站着，旁边是一个表情坚定、壮实的中年着手进行事务安排，背景是古代工厂。青年穿着高领圆领紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发；中年穿着高领圆领黑色秦代武将铠甲，主色调黑色，搭配红色，铠甲上有虎纹图案，头戴黑色头盔，腰间束黑色皮带，配有佩剑，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753200733_20250723_001214.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_763720bf-1861-4ab8-8e34-66eb9f86b431_1.25x_20250722_203150.wav", "audio_duration": 13.696, "video_url": "", "story_board_id": 12, "subtitle": [{"subtitle_id": 1, "text": "后来才知道，", "timestamp": "00:00:00,200 --> 00:00:01,040", "duration": 0.84, "char_count": 6, "start_time_s": 0.2, "end_time_s": 1.04, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 400, "text": "才"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 560, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 680, "text": "道"}], "keyword": "后来"}, {"subtitle_id": 2, "text": "有人在他们经过的路上设下了陷阱，", "timestamp": "00:00:01,040 --> 00:00:03,720", "duration": 2.68, "char_count": 16, "start_time_s": 1.04, "end_time_s": 3.72, "words": [{"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1040, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1200, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1320, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1440, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1600, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1840, "text": "经"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2040, "text": "过"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2160, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2280, "text": "路"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2440, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2720, "text": "设"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2880, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 3040, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3160, "text": "陷"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3360, "text": "阱"}], "keyword": "陷阱"}, {"subtitle_id": 3, "text": "若不是多留一晚，", "timestamp": "00:00:03,720 --> 00:00:04,920", "duration": 1.2, "char_count": 8, "start_time_s": 3.72, "end_time_s": 4.92, "words": [{"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3720, "text": "若"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3880, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4040, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4160, "text": "多"}, {"attribute": {"event": "speech"}, "end_time": 4440, "start_time": 4320, "text": "留"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4440, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4600, "text": "晚"}], "keyword": "留一晚"}, {"subtitle_id": 4, "text": "后果不堪设想。", "timestamp": "00:00:04,920 --> 00:00:06,300", "duration": 1.38, "char_count": 7, "start_time_s": 4.92, "end_time_s": 6.3, "words": [{"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 4920, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 5320, "start_time": 5160, "text": "果"}, {"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5320, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5440, "text": "堪"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5640, "text": "设"}, {"attribute": {"event": "speech"}, "end_time": 6300, "start_time": 5800, "text": "想"}], "keyword": "后果"}, {"subtitle_id": 5, "text": "回到蜀郡，", "timestamp": "00:00:06,400 --> 00:00:07,360", "duration": 0.96, "char_count": 5, "start_time_s": 6.4, "end_time_s": 7.36, "words": [{"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6400, "text": "回"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6600, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6800, "text": "蜀"}, {"attribute": {"event": "speech"}, "end_time": 7360, "start_time": 7000, "text": "郡"}], "keyword": "蜀郡"}, {"subtitle_id": 6, "text": "王刚正式接任琼都的职务，", "timestamp": "00:00:07,360 --> 00:00:09,460", "duration": 2.1, "char_count": 12, "start_time_s": 7.36, "end_time_s": 9.46, "words": [{"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7360, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7560, "text": "刚"}, {"attribute": {"event": "speech"}, "end_time": 7880, "start_time": 7720, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7880, "text": "式"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8040, "text": "接"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8200, "text": "任"}, {"attribute": {"event": "speech"}, "end_time": 8560, "start_time": 8360, "text": "琼"}, {"attribute": {"event": "speech"}, "end_time": 8760, "start_time": 8600, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 8840, "start_time": 8760, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8840, "text": "职"}, {"attribute": {"event": "speech"}, "end_time": 9460, "start_time": 9000, "text": "务"}], "keyword": "王刚"}, {"subtitle_id": 7, "text": "他按照龚子华的指示，", "timestamp": "00:00:09,520 --> 00:00:11,000", "duration": 1.48, "char_count": 10, "start_time_s": 9.52, "end_time_s": 11.0, "words": [{"attribute": {"event": "speech"}, "end_time": 9720, "start_time": 9520, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 9760, "start_time": 9720, "text": "按"}, {"attribute": {"event": "speech"}, "end_time": 9920, "start_time": 9800, "text": "照"}, {"attribute": {"event": "speech"}, "end_time": 10080, "start_time": 9920, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 10240, "start_time": 10080, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 10400, "start_time": 10240, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 10520, "start_time": 10400, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 10640, "start_time": 10520, "text": "指"}, {"attribute": {"event": "speech"}, "end_time": 11000, "start_time": 10640, "text": "示"}], "keyword": "龚子华"}, {"subtitle_id": 8, "text": "开始这守工厂和官道的建设。", "timestamp": "00:00:11,000 --> 00:00:13,340", "duration": 2.34, "char_count": 13, "start_time_s": 11.0, "end_time_s": 13.34, "words": [{"attribute": {"event": "speech"}, "end_time": 11200, "start_time": 11000, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 11360, "start_time": 11200, "text": "始"}, {"attribute": {"event": "speech"}, "end_time": 11480, "start_time": 11360, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 11680, "start_time": 11480, "text": "守"}, {"attribute": {"event": "speech"}, "end_time": 11800, "start_time": 11680, "text": "工"}, {"attribute": {"event": "speech"}, "end_time": 12040, "start_time": 11840, "text": "厂"}, {"attribute": {"event": "speech"}, "end_time": 12240, "start_time": 12040, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 12400, "start_time": 12240, "text": "官"}, {"attribute": {"event": "speech"}, "end_time": 12640, "start_time": 12440, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 12720, "start_time": 12640, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 12920, "start_time": 12720, "text": "建"}, {"attribute": {"event": "speech"}, "end_time": 13340, "start_time": 12920, "text": "设"}], "keyword": "建设"}], "keywords": ["后来", "陷阱", "留一晚", "后果", "蜀郡", "王刚", "龚子华", "建设"]}, {"chapter": 97, "story_board": "虽然一开始有些不适应，但他很快找到了节奏。他发现，邛都的环境远比想象中要好，不仅气候温暖，而且资源丰富。这让他的信心逐渐增强。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "无", "expression": "无"}, {"name": "秦王政", "gender": "男", "age": "青年", "clothes": "高领圆领黑色帝王冕服，主色调黑色，搭配金色，有龙纹，头戴冕旒，腰间系玉带，配玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "秦国大王", "other": "眼神如刀，威严十足", "from_chapter": 67, "action": "坐在宫殿中处理朝政，随后下令加强边境防御、派人秘密调查许莫负背景", "expression": "严肃、专注"}, {"name": "许莫负", "gender": "女", "age": "少年（前期）、青年（后期）", "clothes": "前期：高领圆领淡蓝色秦代女童服饰，主色调淡蓝色，搭配白色，有简单花卉纹样，头上扎双马尾，佩戴红色发带；后期：高领圆领淡粉色秦代少女长裙，主色调淡粉色，搭配浅黄色，裙摆绣有蝴蝶花纹，发髻上插着银质发簪圆形衣领，圆形衣领", "hairstyle": "前期为双马尾，黑色；后期为发髻，黑色", "figure": "苗条", "identity": "温城县具有神秘色彩的女孩", "other": "出生时手握带八卦图案的玉块", "from_chapter": [0], "action": "无", "expression": "无"}, {"name": "王刚", "gender": "男", "age": "中年", "clothes": "高领圆领黑色秦代武将铠甲，主色调黑色，搭配红色，铠甲上有虎纹图案，头戴黑色头盔，腰间束黑色皮带，配有佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "秦国武将，邛都管理者", "other": "", "from_chapter": [0], "action": "在邛都四处查看，脸上露出欣慰的神情", "expression": "自信"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "站在一旁说话", "expression": "轻松、打趣"}, {"name": "许父", "gender": "男", "age": "中年", "clothes": "高领圆领褐色秦代普通男子服饰，主色调褐色，搭配土黄色，衣服有简单条纹，腰间系麻绳腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "许莫负的父亲", "other": "", "from_chapter": [0], "action": "", "expression": ""}], "scene": "古代工厂", "image_prompt": "中景，一个自信且脸上露出欣慰神情、正在古代工厂四处查看的中年男子，穿着高领圆领黑色秦代武将铠甲，主色调黑色，搭配红色，铠甲上有虎纹图案，头戴黑色头盔，腰间束黑色皮带，配有佩剑，圆形衣领，黑色束发，身材壮实, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753201101_20250723_001821.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_b87a8462-e5e7-4724-b680-c0f0910af891_1.25x_20250722_203159.wav", "audio_duration": 11.349333, "video_url": "", "story_board_id": 13, "subtitle": [{"subtitle_id": 1, "text": "虽然一开始有些不适应，", "timestamp": "00:00:00,200 --> 00:00:01,840", "duration": 1.64, "char_count": 11, "start_time_s": 0.2, "end_time_s": 1.84, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 200, "text": "虽"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 320, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 520, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 720, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 880, "text": "始"}, {"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 1000, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 1160, "text": "些"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1280, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1400, "text": "适"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1560, "text": "应"}], "keyword": "不适应"}, {"subtitle_id": 2, "text": "但他很快找到了节奏。", "timestamp": "00:00:01,840 --> 00:00:03,500", "duration": 1.66, "char_count": 10, "start_time_s": 1.84, "end_time_s": 3.5, "words": [{"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1840, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 2000, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2120, "text": "很"}, {"attribute": {"event": "speech"}, "end_time": 2520, "start_time": 2320, "text": "快"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2520, "text": "找"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2680, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2840, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2920, "text": "节"}, {"attribute": {"event": "speech"}, "end_time": 3500, "start_time": 3080, "text": "奏"}], "keyword": "找到节奏"}, {"subtitle_id": 3, "text": "他发现琼都的环境远比想象中要好，", "timestamp": "00:00:03,600 --> 00:00:06,520", "duration": 2.92, "char_count": 16, "start_time_s": 3.6, "end_time_s": 6.52, "words": [{"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3600, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3800, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3920, "text": "现"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4400, "text": "琼"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4560, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4720, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4800, "text": "环"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5000, "text": "境"}, {"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5240, "text": "远"}, {"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5440, "text": "比"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5600, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5800, "text": "象"}, {"attribute": {"event": "speech"}, "end_time": 6080, "start_time": 5920, "text": "中"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6080, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6200, "text": "好"}], "keyword": "琼都"}, {"subtitle_id": 4, "text": "不仅气候温暖，", "timestamp": "00:00:06,520 --> 00:00:07,600", "duration": 1.08, "char_count": 7, "start_time_s": 6.52, "end_time_s": 7.6, "words": [{"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6520, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6720, "text": "仅"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6840, "text": "气"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 7040, "text": "候"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7160, "text": "温"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7320, "text": "暖"}], "keyword": "气候"}, {"subtitle_id": 5, "text": "而且资源丰富，", "timestamp": "00:00:07,600 --> 00:00:08,900", "duration": 1.3, "char_count": 7, "start_time_s": 7.6, "end_time_s": 8.9, "words": [{"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7600, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7800, "text": "且"}, {"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7960, "text": "资"}, {"attribute": {"event": "speech"}, "end_time": 8240, "start_time": 8120, "text": "源"}, {"attribute": {"event": "speech"}, "end_time": 8440, "start_time": 8240, "text": "丰"}, {"attribute": {"event": "speech"}, "end_time": 8900, "start_time": 8440, "text": "富"}], "keyword": "资源"}, {"subtitle_id": 6, "text": "这让他的信心逐渐增强。", "timestamp": "00:00:09,000 --> 00:00:11,020", "duration": 2.02, "char_count": 11, "start_time_s": 9.0, "end_time_s": 11.02, "words": [{"attribute": {"event": "speech"}, "end_time": 9200, "start_time": 9000, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 9320, "start_time": 9200, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 9440, "start_time": 9320, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 9560, "start_time": 9440, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 9720, "start_time": 9560, "text": "信"}, {"attribute": {"event": "speech"}, "end_time": 9880, "start_time": 9720, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 10120, "start_time": 9960, "text": "逐"}, {"attribute": {"event": "speech"}, "end_time": 10320, "start_time": 10160, "text": "渐"}, {"attribute": {"event": "speech"}, "end_time": 10480, "start_time": 10320, "text": "增"}, {"attribute": {"event": "speech"}, "end_time": 11020, "start_time": 10520, "text": "强"}], "keyword": "信心"}], "keywords": ["不适应", "找到节奏", "琼都", "气候", "资源", "信心"]}, {"chapter": 97, "story_board": "而许莫负在温城县长大，她的聪明才智很快显露出来。许父对她寄予厚望，不仅请了最好的老师，还让她接触各种知识。她对天文、地理、历史都有浓厚兴趣，常常提出一些让人惊讶的问题。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "无", "expression": "无"}, {"name": "秦王政", "gender": "男", "age": "青年", "clothes": "高领圆领黑色帝王冕服，主色调黑色，搭配金色，有龙纹，头戴冕旒，腰间系玉带，配玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "秦国大王", "other": "眼神如刀，威严十足", "from_chapter": 67, "action": "坐在宫殿中处理朝政，随后下令加强边境防御、派人秘密调查许莫负背景", "expression": "严肃、专注"}, {"name": "许莫负", "gender": "女", "age": "少年（前期）、青年（后期）", "clothes": "前期：高领圆领淡蓝色秦代女童服饰，主色调淡蓝色，搭配白色，有简单花卉纹样，头上扎双马尾，佩戴红色发带；后期：高领圆领淡粉色秦代少女长裙，主色调淡粉色，搭配浅黄色，裙摆绣有蝴蝶花纹，发髻上插着银质发簪圆形衣领，圆形衣领", "hairstyle": "前期为双马尾，黑色；后期为发髻，黑色", "figure": "苗条", "identity": "温城县具有神秘色彩的女孩", "other": "出生时手握带八卦图案的玉块", "from_chapter": [0], "action": "认真学习各类知识，提出令人惊讶的问题", "expression": "好奇求知"}, {"name": "王刚", "gender": "男", "age": "中年", "clothes": "高领圆领黑色秦代武将铠甲，主色调黑色，搭配红色，铠甲上有虎纹图案，头戴黑色头盔，腰间束黑色皮带，配有佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "秦国武将，邛都管理者", "other": "", "from_chapter": [0], "action": "在邛都四处查看，脸上露出欣慰的神情", "expression": "自信"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "站在一旁说话", "expression": "轻松、打趣"}, {"name": "许父", "gender": "男", "age": "中年", "clothes": "高领圆领褐色秦代普通男子服饰，主色调褐色，搭配土黄色，衣服有简单条纹，腰间系麻绳腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "许莫负的父亲", "other": "", "from_chapter": [0], "action": "为许莫负请老师，安排她接触各种知识", "expression": "满怀期待"}], "scene": "中国古代建筑", "image_prompt": "中景，满怀期待的中年男子站在一旁，旁边是好奇求知、认真学习各类知识的少年女孩。背景是中国古代建筑。站着的中年男子中等身材，穿着高领圆领褐色秦代普通男子服饰，主色调褐色，搭配土黄色，衣服有简单条纹，腰间系麻绳腰带，黑色束发；学习的少年女孩苗条，头上扎着黑色双马尾并佩戴红色发带，手握带八卦图案的玉块，穿着高领圆领淡蓝色秦代女童服饰，主色调淡蓝色，搭配白色，有简单花卉纹样, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753201407_20250723_002328.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_41d3aa44-8246-4bf4-84e1-a764e5c997df_1.25x_20250722_203124.wav", "audio_duration": 14.154667, "video_url": "", "story_board_id": 14, "subtitle": [{"subtitle_id": 1, "text": "而许默父在温城县长大，", "timestamp": "00:00:00,160 --> 00:00:02,080", "duration": 1.92, "char_count": 11, "start_time_s": 0.16, "end_time_s": 2.08, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 160, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "许"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 480, "text": "默"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 600, "text": "父"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 720, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 960, "text": "温"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1200, "text": "城"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1360, "text": "县"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1520, "text": "长"}, {"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1680, "text": "大"}], "keyword": "许默父"}, {"subtitle_id": 2, "text": "他的聪明才智很快显露出来。", "timestamp": "00:00:02,080 --> 00:00:04,460", "duration": 2.38, "char_count": 13, "start_time_s": 2.08, "end_time_s": 4.46, "words": [{"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2080, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2240, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2360, "text": "聪"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2480, "text": "明"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2640, "text": "才"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2880, "text": "智"}, {"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3120, "text": "很"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3360, "text": "快"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3560, "text": "显"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3760, "text": "露"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3880, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 4460, "start_time": 4000, "text": "来"}], "keyword": "聪明才智"}, {"subtitle_id": 3, "text": "许父对他寄予厚望，", "timestamp": "00:00:04,600 --> 00:00:05,880", "duration": 1.28, "char_count": 9, "start_time_s": 4.6, "end_time_s": 5.88, "words": [{"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4600, "text": "许"}, {"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4760, "text": "父"}, {"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4880, "text": "对"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5000, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 5320, "start_time": 5200, "text": "寄"}, {"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5320, "text": "予"}, {"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5440, "text": "厚"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5560, "text": "望"}], "keyword": "许父"}, {"subtitle_id": 4, "text": "不仅请了最好的老师，", "timestamp": "00:00:05,880 --> 00:00:07,400", "duration": 1.52, "char_count": 10, "start_time_s": 5.88, "end_time_s": 7.4, "words": [{"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5880, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6040, "text": "仅"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6160, "text": "请"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6320, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6440, "text": "最"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6600, "text": "好"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6800, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6880, "text": "老"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7040, "text": "师"}], "keyword": "最好老师"}, {"subtitle_id": 5, "text": "还让他接触各种知识。", "timestamp": "00:00:07,400 --> 00:00:09,180", "duration": 1.78, "char_count": 10, "start_time_s": 7.4, "end_time_s": 9.18, "words": [{"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7400, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7560, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7680, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 8000, "start_time": 7840, "text": "接"}, {"attribute": {"event": "speech"}, "end_time": 8160, "start_time": 8000, "text": "触"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8160, "text": "各"}, {"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8360, "text": "种"}, {"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8520, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 9180, "start_time": 8680, "text": "识"}], "keyword": "各种知识"}, {"subtitle_id": 6, "text": "他对天文、地理、历史都有浓厚兴趣，", "timestamp": "00:00:09,240 --> 00:00:11,640", "duration": 2.4, "char_count": 17, "start_time_s": 9.24, "end_time_s": 11.64, "words": [{"attribute": {"event": "speech"}, "end_time": 9440, "start_time": 9240, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 9560, "start_time": 9440, "text": "对"}, {"attribute": {"event": "speech"}, "end_time": 9720, "start_time": 9560, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 9840, "start_time": 9720, "text": "文"}, {"attribute": {"event": "speech"}, "end_time": 9960, "start_time": 9840, "text": "地"}, {"attribute": {"event": "speech"}, "end_time": 10120, "start_time": 9960, "text": "理"}, {"attribute": {"event": "speech"}, "end_time": 10280, "start_time": 10120, "text": "历"}, {"attribute": {"event": "speech"}, "end_time": 10480, "start_time": 10280, "text": "史"}, {"attribute": {"event": "speech"}, "end_time": 10680, "start_time": 10520, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 10840, "start_time": 10680, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 10960, "start_time": 10840, "text": "浓"}, {"attribute": {"event": "speech"}, "end_time": 11160, "start_time": 11000, "text": "厚"}, {"attribute": {"event": "speech"}, "end_time": 11320, "start_time": 11160, "text": "兴"}, {"attribute": {"event": "speech"}, "end_time": 11640, "start_time": 11320, "text": "趣"}], "keyword": "天文地理"}, {"subtitle_id": 7, "text": "常常提出一些让人惊讶的问题。", "timestamp": "00:00:11,640 --> 00:00:14,100", "duration": 2.46, "char_count": 14, "start_time_s": 11.64, "end_time_s": 14.1, "words": [{"attribute": {"event": "speech"}, "end_time": 11680, "start_time": 11640, "text": "常"}, {"attribute": {"event": "speech"}, "end_time": 12000, "start_time": 11680, "text": "常"}, {"attribute": {"event": "speech"}, "end_time": 12160, "start_time": 12000, "text": "提"}, {"attribute": {"event": "speech"}, "end_time": 12320, "start_time": 12160, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 12440, "start_time": 12320, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 12600, "start_time": 12440, "text": "些"}, {"attribute": {"event": "speech"}, "end_time": 12840, "start_time": 12680, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 13000, "start_time": 12840, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 13160, "start_time": 13000, "text": "惊"}, {"attribute": {"event": "speech"}, "end_time": 13360, "start_time": 13200, "text": "讶"}, {"attribute": {"event": "speech"}, "end_time": 13480, "start_time": 13360, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 13600, "start_time": 13480, "text": "问"}, {"attribute": {"event": "speech"}, "end_time": 14100, "start_time": 13600, "text": "题"}], "keyword": "惊讶问题"}], "keywords": ["许默父", "聪明才智", "许父", "最好老师", "各种知识", "天文地理", "惊讶问题"]}, {"chapter": 97, "story_board": "人们都说，这个女孩将来一定不平凡。时间飞逝，转眼间，许莫负已经长大成人。她的名字传遍了整个秦国，甚至引起了朝廷的关注。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "无", "expression": "无"}, {"name": "秦王政", "gender": "男", "age": "青年", "clothes": "高领圆领黑色帝王冕服，主色调黑色，搭配金色，有龙纹，头戴冕旒，腰间系玉带，配玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "秦国大王", "other": "眼神如刀，威严十足", "from_chapter": 67, "action": "坐在宫殿中处理朝政，随后下令加强边境防御、派人秘密调查许莫负背景", "expression": "严肃、专注"}, {"name": "许莫负", "gender": "女", "age": "少年（前期）、青年（后期）", "clothes": "前期：高领圆领淡蓝色秦代女童服饰，主色调淡蓝色，搭配白色，有简单花卉纹样，头上扎双马尾，佩戴红色发带；后期：高领圆领淡粉色秦代少女长裙，主色调淡粉色，搭配浅黄色，裙摆绣有蝴蝶花纹，发髻上插着银质发簪圆形衣领，圆形衣领", "hairstyle": "前期为双马尾，黑色；后期为发髻，黑色", "figure": "苗条", "identity": "温城县具有神秘色彩的女孩", "other": "出生时手握带八卦图案的玉块", "from_chapter": [0], "action": "站着或行走", "expression": "自信从容"}, {"name": "王刚", "gender": "男", "age": "中年", "clothes": "高领圆领黑色秦代武将铠甲，主色调黑色，搭配红色，铠甲上有虎纹图案，头戴黑色头盔，腰间束黑色皮带，配有佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "秦国武将，邛都管理者", "other": "", "from_chapter": [0], "action": "在邛都四处查看，脸上露出欣慰的神情", "expression": "自信"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "站在一旁说话", "expression": "轻松、打趣"}, {"name": "许父", "gender": "男", "age": "中年", "clothes": "高领圆领褐色秦代普通男子服饰，主色调褐色，搭配土黄色，衣服有简单条纹，腰间系麻绳腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "许莫负的父亲", "other": "", "from_chapter": [0], "action": "为许莫负请老师，安排她接触各种知识", "expression": "满怀期待"}], "scene": "古代县城", "image_prompt": "中景，自信从容地站着的少女，手握带八卦图案的玉块，身处于古代县城中。少女身形苗条，梳着黑色发髻，发髻上插着银质发簪，身穿高领圆领淡粉色秦代少女长裙，主色调淡粉色，搭配浅黄色，裙摆绣有蝴蝶花纹, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753201694_20250723_002814.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_e4b87133-0ddf-4ec3-868a-f8a74f508891_1.25x_20250722_203137.wav", "audio_duration": 10.16, "video_url": "", "story_board_id": 15, "subtitle": [{"subtitle_id": 1, "text": "人们都说这个女孩将来一定不平凡。", "timestamp": "00:00:00,160 --> 00:00:02,700", "duration": 2.54, "char_count": 16, "start_time_s": 0.16, "end_time_s": 2.7, "words": [{"attribute": {"event": "speech"}, "end_time": 240, "start_time": 160, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 320, "start_time": 240, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 440, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 880, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 1000, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 1040, "text": "女"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1160, "text": "孩"}, {"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1320, "text": "将"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1440, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1560, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1760, "text": "定"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1880, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2040, "text": "平"}, {"attribute": {"event": "speech"}, "end_time": 2700, "start_time": 2240, "text": "凡"}], "keyword": "许墨馥"}, {"subtitle_id": 2, "text": "时间飞逝，", "timestamp": "00:00:02,920 --> 00:00:03,760", "duration": 0.84, "char_count": 5, "start_time_s": 2.92, "end_time_s": 3.76, "words": [{"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2920, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3080, "text": "间"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3200, "text": "飞"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3360, "text": "逝"}], "keyword": "时间"}, {"subtitle_id": 3, "text": "转眼间许墨馥已经长大成人，", "timestamp": "00:00:03,760 --> 00:00:06,100", "duration": 2.34, "char_count": 13, "start_time_s": 3.76, "end_time_s": 6.1, "words": [{"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3760, "text": "转"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3920, "text": "眼"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4120, "text": "间"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4440, "text": "许"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4560, "text": "墨"}, {"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4720, "text": "馥"}, {"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4880, "text": "已"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 5000, "text": "经"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5120, "text": "长"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5280, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5480, "text": "成"}, {"attribute": {"event": "speech"}, "end_time": 6100, "start_time": 5640, "text": "人"}], "keyword": "许墨馥"}, {"subtitle_id": 4, "text": "她的名字传遍了整个秦国，", "timestamp": "00:00:06,160 --> 00:00:08,240", "duration": 2.08, "char_count": 12, "start_time_s": 6.16, "end_time_s": 8.24, "words": [{"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6160, "text": "她"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6360, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6440, "text": "名"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6600, "text": "字"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6880, "text": "传"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7080, "text": "遍"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7240, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 7520, "start_time": 7320, "text": "整"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7520, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 7840, "start_time": 7680, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 8240, "start_time": 7880, "text": "国"}], "keyword": "秦国"}, {"subtitle_id": 5, "text": "甚至引起了朝廷的关注。", "timestamp": "00:00:08,240 --> 00:00:10,100", "duration": 1.86, "char_count": 11, "start_time_s": 8.24, "end_time_s": 10.1, "words": [{"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8240, "text": "甚"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8400, "text": "至"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8520, "text": "引"}, {"attribute": {"event": "speech"}, "end_time": 8840, "start_time": 8680, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8840, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 9160, "start_time": 8960, "text": "朝"}, {"attribute": {"event": "speech"}, "end_time": 9360, "start_time": 9200, "text": "廷"}, {"attribute": {"event": "speech"}, "end_time": 9480, "start_time": 9360, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 9640, "start_time": 9480, "text": "关"}, {"attribute": {"event": "speech"}, "end_time": 10100, "start_time": 9640, "text": "注"}], "keyword": "朝廷"}], "keywords": ["许墨馥", "时间", "许墨馥", "秦国", "朝廷"]}, {"chapter": 97, "story_board": "秦王政亲自召见了她，想看看这个传说中的女孩到底有何特别之处。而许莫负也没有让众人失望，她的智慧和见识令人刮目相看。与此同时，燕国的阴谋仍在继续。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "无", "expression": "无"}, {"name": "秦王政", "gender": "男", "age": "青年", "clothes": "高领圆领黑色帝王冕服，主色调黑色，搭配金色，有龙纹，头戴冕旒，腰间系玉带，配玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "秦国大王", "other": "眼神如刀，威严十足", "from_chapter": 67, "action": "坐在宫殿主位，目光审视地看着许莫负", "expression": "严肃、好奇"}, {"name": "许莫负", "gender": "女", "age": "少年（前期）、青年（后期）", "clothes": "前期：高领圆领淡蓝色秦代女童服饰，主色调淡蓝色，搭配白色，有简单花卉纹样，头上扎双马尾，佩戴红色发带；后期：高领圆领淡粉色秦代少女长裙，主色调淡粉色，搭配浅黄色，裙摆绣有蝴蝶花纹，发髻上插着银质发簪圆形衣领，圆形衣领", "hairstyle": "前期为双马尾，黑色；后期为发髻，黑色", "figure": "苗条", "identity": "温城县具有神秘色彩的女孩", "other": "出生时手握带八卦图案的玉块", "from_chapter": [0], "action": "站在大殿中，从容不迫地回答问题", "expression": "自信、沉稳"}, {"name": "王刚", "gender": "男", "age": "中年", "clothes": "高领圆领黑色秦代武将铠甲，主色调黑色，搭配红色，铠甲上有虎纹图案，头戴黑色头盔，腰间束黑色皮带，配有佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "秦国武将，邛都管理者", "other": "", "from_chapter": [0], "action": "在邛都四处查看，脸上露出欣慰的神情", "expression": "自信"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "站在一旁说话", "expression": "轻松、打趣"}, {"name": "许父", "gender": "男", "age": "中年", "clothes": "高领圆领褐色秦代普通男子服饰，主色调褐色，搭配土黄色，衣服有简单条纹，腰间系麻绳腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "许莫负的父亲", "other": "", "from_chapter": [0], "action": "为许莫负请老师，安排她接触各种知识", "expression": "满怀期待"}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿里，坐在宫殿主位上，目光审视，表情严肃又好奇的青年男子，眼神如刀，威严十足，挺拔的身姿展现出强大气场；旁边站着一位从容自信、沉稳的少年或青年女子。青年男子穿着高领圆领黑色帝王冕服，主色调黑色，搭配金色，有龙纹，头戴冕旒，腰间系玉带，配玉佩，圆形衣领，黑色束发；少年女子穿着高领圆领淡蓝色秦代女童服饰，主色调淡蓝色，搭配白色，有简单花卉纹样，头上扎着双马尾，佩戴红色发带；青年女子穿着高领圆领淡粉色秦代少女长裙，主色调淡粉色，搭配浅黄色，裙摆绣有蝴蝶花纹，发髻上插着银质发簪，圆形衣领，黑色头发前期为双马尾后期为发髻, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753201954_20250723_003235.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_31702d95-b8c7-4f26-906d-e86bb5f3d639_1.25x_20250722_203145.wav", "audio_duration": 12.618667, "video_url": "", "story_board_id": 16, "subtitle": [{"subtitle_id": 1, "text": "秦王政亲自召见了他，", "timestamp": "00:00:00,240 --> 00:00:01,880", "duration": 1.64, "char_count": 10, "start_time_s": 0.24, "end_time_s": 1.88, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 240, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 480, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 760, "text": "亲"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 960, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 1080, "text": "召"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1320, "text": "见"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1400, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1480, "text": "他"}], "keyword": "秦王政"}, {"subtitle_id": 2, "text": "想看看这个传说中的女孩", "timestamp": "00:00:01,880 --> 00:00:03,560", "duration": 1.68, "char_count": 11, "start_time_s": 1.88, "end_time_s": 3.56, "words": [{"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1880, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 2080, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2120, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2440, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2560, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2680, "text": "传"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2880, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 3000, "text": "中"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3120, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3200, "text": "女"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3360, "text": "孩"}], "keyword": "女孩"}, {"subtitle_id": 3, "text": "到底有何特别之处。", "timestamp": "00:00:03,560 --> 00:00:05,180", "duration": 1.62, "char_count": 9, "start_time_s": 3.56, "end_time_s": 5.18, "words": [{"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3560, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3800, "text": "底"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3880, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4040, "text": "何"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4160, "text": "特"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4400, "text": "别"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4560, "text": "之"}, {"attribute": {"event": "speech"}, "end_time": 5180, "start_time": 4720, "text": "处"}], "keyword": "特别之处"}, {"subtitle_id": 4, "text": "而许墨复也没有让众人失望，", "timestamp": "00:00:05,280 --> 00:00:07,400", "duration": 2.12, "char_count": 13, "start_time_s": 5.28, "end_time_s": 7.4, "words": [{"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5280, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5480, "text": "许"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5640, "text": "墨"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5800, "text": "复"}, {"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 6080, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6240, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6400, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6520, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6680, "text": "众"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6800, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6960, "text": "失"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7080, "text": "望"}], "keyword": "许墨复"}, {"subtitle_id": 5, "text": "他的智慧和见识令人刮目相看。", "timestamp": "00:00:07,400 --> 00:00:09,860", "duration": 2.46, "char_count": 14, "start_time_s": 7.4, "end_time_s": 9.86, "words": [{"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7400, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7560, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7880, "start_time": 7680, "text": "智"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7880, "text": "慧"}, {"attribute": {"event": "speech"}, "end_time": 8160, "start_time": 8040, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8160, "text": "见"}, {"attribute": {"event": "speech"}, "end_time": 8560, "start_time": 8360, "text": "识"}, {"attribute": {"event": "speech"}, "end_time": 8760, "start_time": 8640, "text": "令"}, {"attribute": {"event": "speech"}, "end_time": 8920, "start_time": 8760, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 9080, "start_time": 8920, "text": "刮"}, {"attribute": {"event": "speech"}, "end_time": 9200, "start_time": 9080, "text": "目"}, {"attribute": {"event": "speech"}, "end_time": 9360, "start_time": 9240, "text": "相"}, {"attribute": {"event": "speech"}, "end_time": 9860, "start_time": 9360, "text": "看"}], "keyword": "智慧"}, {"subtitle_id": 6, "text": "与此同时，", "timestamp": "00:00:09,960 --> 00:00:10,760", "duration": 0.8, "char_count": 5, "start_time_s": 9.96, "end_time_s": 10.76, "words": [{"attribute": {"event": "speech"}, "end_time": 10120, "start_time": 9960, "text": "与"}, {"attribute": {"event": "speech"}, "end_time": 10240, "start_time": 10120, "text": "此"}, {"attribute": {"event": "speech"}, "end_time": 10400, "start_time": 10240, "text": "同"}, {"attribute": {"event": "speech"}, "end_time": 10760, "start_time": 10400, "text": "时"}], "keyword": "同时"}, {"subtitle_id": 7, "text": "燕国的阴谋仍在继续。", "timestamp": "00:00:10,760 --> 00:00:12,540", "duration": 1.78, "char_count": 10, "start_time_s": 10.76, "end_time_s": 12.54, "words": [{"attribute": {"event": "speech"}, "end_time": 10920, "start_time": 10760, "text": "燕"}, {"attribute": {"event": "speech"}, "end_time": 11120, "start_time": 10960, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 11240, "start_time": 11120, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 11320, "start_time": 11240, "text": "阴"}, {"attribute": {"event": "speech"}, "end_time": 11520, "start_time": 11360, "text": "谋"}, {"attribute": {"event": "speech"}, "end_time": 11720, "start_time": 11560, "text": "仍"}, {"attribute": {"event": "speech"}, "end_time": 11920, "start_time": 11760, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 12080, "start_time": 11920, "text": "继"}, {"attribute": {"event": "speech"}, "end_time": 12540, "start_time": 12080, "text": "续"}], "keyword": "燕国"}], "keywords": ["秦王政", "女孩", "特别之处", "许墨复", "智慧", "同时", "燕国"]}, {"chapter": 97, "story_board": "他们试图接近许莫负，希望能利用她达到自己的目的。但许莫负并非易与之人，她聪明机警，很快就识破了对方的意图。她选择远离是非，专心研究学问，为未来的挑战做准备。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "无", "expression": "无"}, {"name": "秦王政", "gender": "男", "age": "青年", "clothes": "高领圆领黑色帝王冕服，主色调黑色，搭配金色，有龙纹，头戴冕旒，腰间系玉带，配玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "秦国大王", "other": "眼神如刀，威严十足", "from_chapter": 67, "action": "坐在宫殿主位，目光审视地看着许莫负", "expression": "严肃、好奇"}, {"name": "许莫负", "gender": "女", "age": "少年（前期）、青年（后期）", "clothes": "前期：高领圆领淡蓝色秦代女童服饰，主色调淡蓝色，搭配白色，有简单花卉纹样，头上扎双马尾，佩戴红色发带；后期：高领圆领淡粉色秦代少女长裙，主色调淡粉色，搭配浅黄色，裙摆绣有蝴蝶花纹，发髻上插着银质发簪圆形衣领，圆形衣领", "hairstyle": "前期为双马尾，黑色；后期为发髻，黑色", "figure": "苗条", "identity": "温城县具有神秘色彩的女孩", "other": "出生时手握带八卦图案的玉块", "from_chapter": [0], "action": "皱着眉头转身离开，回到书桌前坐下翻开书籍阅读", "expression": "警惕"}, {"name": "王刚", "gender": "男", "age": "中年", "clothes": "高领圆领黑色秦代武将铠甲，主色调黑色，搭配红色，铠甲上有虎纹图案，头戴黑色头盔，腰间束黑色皮带，配有佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "秦国武将，邛都管理者", "other": "", "from_chapter": [0], "action": "在邛都四处查看，脸上露出欣慰的神情", "expression": "自信"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "站在一旁说话", "expression": "轻松、打趣"}, {"name": "许父", "gender": "男", "age": "中年", "clothes": "高领圆领褐色秦代普通男子服饰，主色调褐色，搭配土黄色，衣服有简单条纹，腰间系麻绳腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "许莫负的父亲", "other": "", "from_chapter": [0], "action": "为许莫负请老师，安排她接触各种知识", "expression": "满怀期待"}], "scene": "古代县城", "image_prompt": "中景，一位神情警惕、皱着眉头的青年女子转身离开后，回到古代县城房间的书桌前坐下翻开书籍阅读。她身材苗条，梳着黑色发髻，发髻上插着银质发簪，身着高领圆领淡粉色秦代少女长裙，主色调淡粉色，搭配浅黄色，裙摆绣有蝴蝶花纹。背景是古代县城的房间, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753202327_20250723_003847.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_7e44fcc7-7770-4b4a-a517-49780690fe00_1.25x_20250722_203141.wav", "audio_duration": 12.618667, "video_url": "", "story_board_id": 17, "subtitle": [{"subtitle_id": 1, "text": "他们试图接近许莫富，", "timestamp": "00:00:00,200 --> 00:00:01,560", "duration": 1.36, "char_count": 10, "start_time_s": 0.2, "end_time_s": 1.56, "words": [{"attribute": {"event": "speech"}, "end_time": 240, "start_time": 200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 360, "start_time": 240, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 360, "text": "试"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 440, "text": "图"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 640, "text": "接"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 800, "text": "近"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 920, "text": "许"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1080, "text": "莫"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1200, "text": "富"}], "keyword": "许莫富"}, {"subtitle_id": 2, "text": "希望能利用他达到自己的目的。", "timestamp": "00:00:01,560 --> 00:00:03,580", "duration": 2.02, "char_count": 14, "start_time_s": 1.56, "end_time_s": 3.58, "words": [{"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1560, "text": "希"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1720, "text": "望"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1800, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1920, "text": "利"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2040, "text": "用"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2280, "text": "达"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2480, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2600, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2760, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2920, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 3000, "text": "目"}, {"attribute": {"event": "speech"}, "end_time": 3580, "start_time": 3160, "text": "的"}], "keyword": "利用"}, {"subtitle_id": 3, "text": "但许莫富并非一语之人，", "timestamp": "00:00:03,680 --> 00:00:05,520", "duration": 1.84, "char_count": 11, "start_time_s": 3.68, "end_time_s": 5.52, "words": [{"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3680, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3840, "text": "许"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 4000, "text": "莫"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4120, "text": "富"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4320, "text": "并"}, {"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4520, "text": "非"}, {"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4680, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4840, "text": "语"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5040, "text": "之"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5160, "text": "人"}], "keyword": "许莫富"}, {"subtitle_id": 4, "text": "他聪明机警，", "timestamp": "00:00:05,520 --> 00:00:06,440", "duration": 0.92, "char_count": 6, "start_time_s": 5.52, "end_time_s": 6.44, "words": [{"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5520, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5680, "text": "聪"}, {"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5840, "text": "明"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5960, "text": "机"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6120, "text": "警"}], "keyword": "聪明"}, {"subtitle_id": 5, "text": "很快就识破了对方的意图。", "timestamp": "00:00:06,440 --> 00:00:08,340", "duration": 1.9, "char_count": 12, "start_time_s": 6.44, "end_time_s": 8.34, "words": [{"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6440, "text": "很"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6680, "text": "快"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6880, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 7000, "text": "识"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7160, "text": "破"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7320, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 7520, "start_time": 7400, "text": "对"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7520, "text": "方"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7680, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7880, "start_time": 7760, "text": "意"}, {"attribute": {"event": "speech"}, "end_time": 8340, "start_time": 7880, "text": "图"}], "keyword": "识破"}, {"subtitle_id": 6, "text": "他选择远离是非，", "timestamp": "00:00:08,480 --> 00:00:09,680", "duration": 1.2, "char_count": 8, "start_time_s": 8.48, "end_time_s": 9.68, "words": [{"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8480, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8640, "text": "选"}, {"attribute": {"event": "speech"}, "end_time": 8920, "start_time": 8800, "text": "择"}, {"attribute": {"event": "speech"}, "end_time": 9040, "start_time": 8920, "text": "远"}, {"attribute": {"event": "speech"}, "end_time": 9200, "start_time": 9080, "text": "离"}, {"attribute": {"event": "speech"}, "end_time": 9360, "start_time": 9200, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 9680, "start_time": 9360, "text": "非"}], "keyword": "是非"}, {"subtitle_id": 7, "text": "专心研究学问，", "timestamp": "00:00:09,680 --> 00:00:10,760", "duration": 1.08, "char_count": 7, "start_time_s": 9.68, "end_time_s": 10.76, "words": [{"attribute": {"event": "speech"}, "end_time": 9840, "start_time": 9680, "text": "专"}, {"attribute": {"event": "speech"}, "end_time": 10040, "start_time": 9880, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 10160, "start_time": 10040, "text": "研"}, {"attribute": {"event": "speech"}, "end_time": 10320, "start_time": 10160, "text": "究"}, {"attribute": {"event": "speech"}, "end_time": 10480, "start_time": 10320, "text": "学"}, {"attribute": {"event": "speech"}, "end_time": 10760, "start_time": 10480, "text": "问"}], "keyword": "学问"}, {"subtitle_id": 8, "text": "为未来的挑战做准备。", "timestamp": "00:00:10,760 --> 00:00:12,540", "duration": 1.78, "char_count": 10, "start_time_s": 10.76, "end_time_s": 12.54, "words": [{"attribute": {"event": "speech"}, "end_time": 11080, "start_time": 10760, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 11160, "start_time": 11080, "text": "未"}, {"attribute": {"event": "speech"}, "end_time": 11280, "start_time": 11160, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 11360, "start_time": 11280, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 11560, "start_time": 11360, "text": "挑"}, {"attribute": {"event": "speech"}, "end_time": 11720, "start_time": 11560, "text": "战"}, {"attribute": {"event": "speech"}, "end_time": 11880, "start_time": 11720, "text": "做"}, {"attribute": {"event": "speech"}, "end_time": 12040, "start_time": 11880, "text": "准"}, {"attribute": {"event": "speech"}, "end_time": 12540, "start_time": 12040, "text": "备"}], "keyword": "挑战"}], "keywords": ["许莫富", "利用", "许莫富", "聪明", "识破", "是非", "学问", "挑战"]}, {"chapter": 97, "story_board": "公子华则在蜀郡继续推动改革，他深知，只有国家强大，才能抵御外敌。他和王刚密切合作，确保邛都的发展顺利进行。两人虽然性格不同，但目标一致，配合得十分默契。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "和王刚一起在蜀郡的办公场所交流、探讨改革事宜，时而在文件上写写画画", "expression": "坚定、沉稳"}, {"name": "秦王政", "gender": "男", "age": "青年", "clothes": "高领圆领黑色帝王冕服，主色调黑色，搭配金色，有龙纹，头戴冕旒，腰间系玉带，配玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "秦国大王", "other": "眼神如刀，威严十足", "from_chapter": 67, "action": "坐在宫殿主位，目光审视地看着许莫负", "expression": "严肃、好奇"}, {"name": "许莫负", "gender": "女", "age": "少年（前期）、青年（后期）", "clothes": "前期：高领圆领淡蓝色秦代女童服饰，主色调淡蓝色，搭配白色，有简单花卉纹样，头上扎双马尾，佩戴红色发带；后期：高领圆领淡粉色秦代少女长裙，主色调淡粉色，搭配浅黄色，裙摆绣有蝴蝶花纹，发髻上插着银质发簪圆形衣领，圆形衣领", "hairstyle": "前期为双马尾，黑色；后期为发髻，黑色", "figure": "苗条", "identity": "温城县具有神秘色彩的女孩", "other": "出生时手握带八卦图案的玉块", "from_chapter": [0], "action": "皱着眉头转身离开，回到书桌前坐下翻开书籍阅读", "expression": "警惕"}, {"name": "王刚", "gender": "男", "age": "中年", "clothes": "高领圆领黑色秦代武将铠甲，主色调黑色，搭配红色，铠甲上有虎纹图案，头戴黑色头盔，腰间束黑色皮带，配有佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "秦国武将，邛都管理者", "other": "", "from_chapter": [0], "action": "认真倾听公子华的意见，不时点头，偶尔提出自己的看法", "expression": "专注、诚恳"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "站在一旁说话", "expression": "轻松、打趣"}, {"name": "许父", "gender": "男", "age": "中年", "clothes": "高领圆领褐色秦代普通男子服饰，主色调褐色，搭配土黄色，衣服有简单条纹，腰间系麻绳腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "许莫负的父亲", "other": "", "from_chapter": [0], "action": "为许莫负请老师，安排她接触各种知识", "expression": "满怀期待"}], "scene": "古代工厂", "image_prompt": "中景，在古代工厂模样的蜀郡办公场所内，一位青年男子挺拔地站着，表情坚定、沉稳，时而在文件上写写画画；旁边一位中年男子壮实地站着，表情专注、诚恳，认真倾听，不时点头。青年男子穿着高领圆领的紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发；中年男子穿着高领圆领的黑色秦代武将铠甲，主色调黑色，搭配红色，铠甲上有虎纹图案，头戴黑色头盔，腰间束黑色皮带，配有佩剑，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://p9-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/20250723004139A8DC6B362DED5E873D1E-5406-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1753288905&x-signature=vy0mA%2Bxkq1Slg5qlp7Zw1b%2FLlX8%3D", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_5ef45795-464c-4af4-b288-4afc56227b2e_1.25x_20250722_203128.wav", "audio_duration": 13.077333, "video_url": "", "story_board_id": 18, "subtitle": [{"subtitle_id": 1, "text": "龚子华则在蜀郡继续推动改革，", "timestamp": "00:00:00,160 --> 00:00:02,440", "duration": 2.28, "char_count": 14, "start_time_s": 0.16, "end_time_s": 2.44, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 400, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 600, "text": "则"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 760, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 920, "text": "蜀"}, {"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 1120, "text": "郡"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1360, "text": "继"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1520, "text": "续"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1640, "text": "推"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1800, "text": "动"}, {"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1920, "text": "改"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2080, "text": "革"}], "keyword": "龚子华"}, {"subtitle_id": 2, "text": "他深知只有国家强大，", "timestamp": "00:00:02,440 --> 00:00:04,160", "duration": 1.72, "char_count": 10, "start_time_s": 2.44, "end_time_s": 4.16, "words": [{"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2440, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2600, "text": "深"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2760, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3080, "text": "只"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3240, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3360, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3480, "text": "家"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3640, "text": "强"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 3880, "text": "大"}], "keyword": "国家"}, {"subtitle_id": 3, "text": "才能抵御外敌。", "timestamp": "00:00:04,160 --> 00:00:05,380", "duration": 1.22, "char_count": 7, "start_time_s": 4.16, "end_time_s": 5.38, "words": [{"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4160, "text": "才"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4360, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4480, "text": "抵"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4640, "text": "御"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4760, "text": "外"}, {"attribute": {"event": "speech"}, "end_time": 5380, "start_time": 4920, "text": "敌"}], "keyword": "外敌"}, {"subtitle_id": 4, "text": "他和王刚密切合作，", "timestamp": "00:00:05,480 --> 00:00:06,920", "duration": 1.44, "char_count": 9, "start_time_s": 5.48, "end_time_s": 6.92, "words": [{"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5480, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5680, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5800, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 6080, "start_time": 5960, "text": "刚"}, {"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 6120, "text": "密"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6240, "text": "切"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6360, "text": "合"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6520, "text": "作"}], "keyword": "王刚"}, {"subtitle_id": 5, "text": "确保琼斗的发展顺利进行。", "timestamp": "00:00:06,920 --> 00:00:09,060", "duration": 2.14, "char_count": 12, "start_time_s": 6.92, "end_time_s": 9.06, "words": [{"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6920, "text": "确"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7080, "text": "保"}, {"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7280, "text": "琼"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7440, "text": "斗"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7560, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7840, "start_time": 7680, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 8000, "start_time": 7840, "text": "展"}, {"attribute": {"event": "speech"}, "end_time": 8280, "start_time": 8120, "text": "顺"}, {"attribute": {"event": "speech"}, "end_time": 8440, "start_time": 8280, "text": "利"}, {"attribute": {"event": "speech"}, "end_time": 8560, "start_time": 8440, "text": "进"}, {"attribute": {"event": "speech"}, "end_time": 9060, "start_time": 8560, "text": "行"}], "keyword": "琼斗"}, {"subtitle_id": 6, "text": "两人虽然性格不同，", "timestamp": "00:00:09,160 --> 00:00:10,560", "duration": 1.4, "char_count": 9, "start_time_s": 9.16, "end_time_s": 10.56, "words": [{"attribute": {"event": "speech"}, "end_time": 9360, "start_time": 9160, "text": "两"}, {"attribute": {"event": "speech"}, "end_time": 9480, "start_time": 9360, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 9600, "start_time": 9480, "text": "虽"}, {"attribute": {"event": "speech"}, "end_time": 9720, "start_time": 9600, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 9960, "start_time": 9760, "text": "性"}, {"attribute": {"event": "speech"}, "end_time": 10080, "start_time": 9960, "text": "格"}, {"attribute": {"event": "speech"}, "end_time": 10200, "start_time": 10080, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 10560, "start_time": 10200, "text": "同"}], "keyword": "性格"}, {"subtitle_id": 7, "text": "但目标一致，", "timestamp": "00:00:10,560 --> 00:00:11,480", "duration": 0.92, "char_count": 6, "start_time_s": 10.56, "end_time_s": 11.48, "words": [{"attribute": {"event": "speech"}, "end_time": 10680, "start_time": 10560, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 10840, "start_time": 10680, "text": "目"}, {"attribute": {"event": "speech"}, "end_time": 11000, "start_time": 10840, "text": "标"}, {"attribute": {"event": "speech"}, "end_time": 11200, "start_time": 11000, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 11480, "start_time": 11200, "text": "致"}], "keyword": "目标"}, {"subtitle_id": 8, "text": "配合得十分默契。", "timestamp": "00:00:11,480 --> 00:00:13,020", "duration": 1.54, "char_count": 8, "start_time_s": 11.48, "end_time_s": 13.02, "words": [{"attribute": {"event": "speech"}, "end_time": 11720, "start_time": 11480, "text": "配"}, {"attribute": {"event": "speech"}, "end_time": 11960, "start_time": 11800, "text": "合"}, {"attribute": {"event": "speech"}, "end_time": 12080, "start_time": 11960, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 12240, "start_time": 12080, "text": "十"}, {"attribute": {"event": "speech"}, "end_time": 12360, "start_time": 12240, "text": "分"}, {"attribute": {"event": "speech"}, "end_time": 12520, "start_time": 12360, "text": "默"}, {"attribute": {"event": "speech"}, "end_time": 13020, "start_time": 12560, "text": "契"}], "keyword": "配合"}], "keywords": ["龚子华", "国家", "外敌", "王刚", "琼斗", "性格", "目标", "配合"]}, {"chapter": 97, "story_board": "而在咸阳，秦王政也在密切关注各方动态。他知道，秦国正处于关键时期，任何风吹草动都可能影响国家的未来。他不断调整策略，确保国家稳定发展。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "和王刚一起在蜀郡的办公场所交流、探讨改革事宜，时而在文件上写写画画", "expression": "坚定、沉稳"}, {"name": "秦王政", "gender": "男", "age": "青年", "clothes": "高领圆领黑色帝王冕服，主色调黑色，搭配金色，有龙纹，头戴冕旒，腰间系玉带，配玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "秦国大王", "other": "眼神如刀，威严十足", "from_chapter": 67, "action": "坐在宫殿中，时而翻阅奏章，时而提笔书写", "expression": "严肃、专注"}, {"name": "许莫负", "gender": "女", "age": "少年（前期）、青年（后期）", "clothes": "前期：高领圆领淡蓝色秦代女童服饰，主色调淡蓝色，搭配白色，有简单花卉纹样，头上扎双马尾，佩戴红色发带；后期：高领圆领淡粉色秦代少女长裙，主色调淡粉色，搭配浅黄色，裙摆绣有蝴蝶花纹，发髻上插着银质发簪圆形衣领，圆形衣领", "hairstyle": "前期为双马尾，黑色；后期为发髻，黑色", "figure": "苗条", "identity": "温城县具有神秘色彩的女孩", "other": "出生时手握带八卦图案的玉块", "from_chapter": [0], "action": "皱着眉头转身离开，回到书桌前坐下翻开书籍阅读", "expression": "警惕"}, {"name": "王刚", "gender": "男", "age": "中年", "clothes": "高领圆领黑色秦代武将铠甲，主色调黑色，搭配红色，铠甲上有虎纹图案，头戴黑色头盔，腰间束黑色皮带，配有佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "秦国武将，邛都管理者", "other": "", "from_chapter": [0], "action": "认真倾听公子华的意见，不时点头，偶尔提出自己的看法", "expression": "专注、诚恳"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "站在一旁说话", "expression": "轻松、打趣"}, {"name": "许父", "gender": "男", "age": "中年", "clothes": "高领圆领褐色秦代普通男子服饰，主色调褐色，搭配土黄色，衣服有简单条纹，腰间系麻绳腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "许莫负的父亲", "other": "", "from_chapter": [0], "action": "为许莫负请老师，安排她接触各种知识", "expression": "满怀期待"}], "scene": "皇宫大殿", "image_prompt": "中景，坐在皇宫大殿中时而翻阅奏章、时而提笔书写，表情严肃且专注、眼神如刀、威严十足的青年男子，穿着高领圆领黑色帝王冕服，主色调黑色，搭配金色，有龙纹，头戴冕旒，腰间系玉带，配玉佩，圆形衣领，黑色束发，身姿挺拔, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753202667_20250723_004428.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_d9f4bc8c-27de-4538-b15d-aa4cc8f2f889_1.25x_20250722_203133.wav", "audio_duration": 11.850667, "video_url": "", "story_board_id": 19, "subtitle": [{"subtitle_id": 1, "text": "而在咸阳，", "timestamp": "00:00:00,160 --> 00:00:01,040", "duration": 0.88, "char_count": 5, "start_time_s": 0.16, "end_time_s": 1.04, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 440, "text": "咸"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 560, "text": "阳"}], "keyword": "咸阳"}, {"subtitle_id": 2, "text": "秦王正也在密切关注各方动态，", "timestamp": "00:00:01,040 --> 00:00:03,380", "duration": 2.34, "char_count": 14, "start_time_s": 1.04, "end_time_s": 3.38, "words": [{"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1040, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1200, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1360, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1520, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1640, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1800, "text": "密"}, {"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1960, "text": "切"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2080, "text": "关"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2240, "text": "注"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2400, "text": "各"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2560, "text": "方"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2720, "text": "动"}, {"attribute": {"event": "speech"}, "end_time": 3380, "start_time": 2880, "text": "态"}], "keyword": "秦王"}, {"subtitle_id": 3, "text": "他知道秦国正处于关键时期，", "timestamp": "00:00:03,480 --> 00:00:05,760", "duration": 2.28, "char_count": 13, "start_time_s": 3.48, "end_time_s": 5.76, "words": [{"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3480, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3640, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3800, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4160, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4360, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4520, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4680, "text": "处"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4840, "text": "于"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 4920, "text": "关"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5120, "text": "键"}, {"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5280, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 5760, "start_time": 5440, "text": "期"}], "keyword": "秦国"}, {"subtitle_id": 4, "text": "任何风吹草动", "timestamp": "00:00:05,760 --> 00:00:06,840", "duration": 1.08, "char_count": 6, "start_time_s": 5.76, "end_time_s": 6.84, "words": [{"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5760, "text": "任"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5960, "text": "何"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6120, "text": "风"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6280, "text": "吹"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6440, "text": "草"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6640, "text": "动"}], "keyword": "风吹草动"}, {"subtitle_id": 5, "text": "都可能影响国家的未来，", "timestamp": "00:00:06,840 --> 00:00:08,700", "duration": 1.86, "char_count": 11, "start_time_s": 6.84, "end_time_s": 8.7, "words": [{"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6840, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 7000, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 7160, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7280, "text": "影"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7400, "text": "响"}, {"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7600, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7800, "text": "家"}, {"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7960, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8080, "text": "未"}, {"attribute": {"event": "speech"}, "end_time": 8700, "start_time": 8200, "text": "来"}], "keyword": "国家未来"}, {"subtitle_id": 6, "text": "他不断调整策略，", "timestamp": "00:00:08,760 --> 00:00:10,200", "duration": 1.44, "char_count": 8, "start_time_s": 8.76, "end_time_s": 10.2, "words": [{"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8760, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 9120, "start_time": 8960, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 9280, "start_time": 9160, "text": "断"}, {"attribute": {"event": "speech"}, "end_time": 9480, "start_time": 9280, "text": "调"}, {"attribute": {"event": "speech"}, "end_time": 9600, "start_time": 9480, "text": "整"}, {"attribute": {"event": "speech"}, "end_time": 9800, "start_time": 9600, "text": "策"}, {"attribute": {"event": "speech"}, "end_time": 10200, "start_time": 9800, "text": "略"}], "keyword": "调整策略"}, {"subtitle_id": 7, "text": "确保国家稳定发展。", "timestamp": "00:00:10,200 --> 00:00:11,740", "duration": 1.54, "char_count": 9, "start_time_s": 10.2, "end_time_s": 11.74, "words": [{"attribute": {"event": "speech"}, "end_time": 10360, "start_time": 10200, "text": "确"}, {"attribute": {"event": "speech"}, "end_time": 10480, "start_time": 10360, "text": "保"}, {"attribute": {"event": "speech"}, "end_time": 10640, "start_time": 10480, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 10800, "start_time": 10640, "text": "家"}, {"attribute": {"event": "speech"}, "end_time": 10960, "start_time": 10800, "text": "稳"}, {"attribute": {"event": "speech"}, "end_time": 11120, "start_time": 10960, "text": "定"}, {"attribute": {"event": "speech"}, "end_time": 11320, "start_time": 11160, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 11740, "start_time": 11320, "text": "展"}], "keyword": "国家稳定"}], "keywords": ["咸阳", "秦王", "秦国", "风吹草动", "国家未来", "调整策略", "国家稳定"]}, {"chapter": 97, "story_board": "就这样，一个普通女孩的故事，逐渐牵动了整个时代的命运。她的出现，不仅改变了家庭的命运，也影响了国家的走向。而这一切，都始于那个阳光明媚的清晨，当她第一次睁开眼睛，看到这个世界的时候。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "和王刚一起在蜀郡的办公场所交流、探讨改革事宜，时而在文件上写写画画", "expression": "坚定、沉稳"}, {"name": "秦王政", "gender": "男", "age": "青年", "clothes": "高领圆领黑色帝王冕服，主色调黑色，搭配金色，有龙纹，头戴冕旒，腰间系玉带，配玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "秦国大王", "other": "眼神如刀，威严十足", "from_chapter": 67, "action": "坐在宫殿中，时而翻阅奏章，时而提笔书写", "expression": "严肃、专注"}, {"name": "许莫负", "gender": "女", "age": "少年（前期）、青年（后期）", "clothes": "前期：高领圆领淡蓝色秦代女童服饰，主色调淡蓝色，搭配白色，有简单花卉纹样，头上扎双马尾，佩戴红色发带；后期：高领圆领淡粉色秦代少女长裙，主色调淡粉色，搭配浅黄色，裙摆绣有蝴蝶花纹，发髻上插着银质发簪圆形衣领，圆形衣领", "hairstyle": "前期为双马尾，黑色；后期为发髻，黑色", "figure": "苗条", "identity": "温城县具有神秘色彩的女孩", "other": "出生时手握带八卦图案的玉块", "from_chapter": [0], "action": "在襁褓中睁开眼睛", "expression": "懵懂"}, {"name": "王刚", "gender": "男", "age": "中年", "clothes": "高领圆领黑色秦代武将铠甲，主色调黑色，搭配红色，铠甲上有虎纹图案，头戴黑色头盔，腰间束黑色皮带，配有佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "秦国武将，邛都管理者", "other": "", "from_chapter": [0], "action": "认真倾听公子华的意见，不时点头，偶尔提出自己的看法", "expression": "专注、诚恳"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "站在一旁说话", "expression": "轻松、打趣"}, {"name": "许父", "gender": "男", "age": "中年", "clothes": "高领圆领褐色秦代普通男子服饰，主色调褐色，搭配土黄色，衣服有简单条纹，腰间系麻绳腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "许莫负的父亲", "other": "", "from_chapter": [0], "action": "为许莫负请老师，安排她接触各种知识", "expression": "满怀期待"}], "scene": "古代县城", "image_prompt": "中景，站在古代县城街道的懵懂少女，手握带八卦图案的玉块，身着高领圆领淡蓝色秦代女童服饰，主色调淡蓝色，搭配白色，有简单花卉纹样，头上扎着双马尾并佩戴红色发带，双马尾为黑色，身形苗条, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://p9-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/20250723004637235496EFCAD4D485F206-9067-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1753289206&x-signature=B2X%2BRTtw%2BK8Xwfdnp9owE%2FBaUJE%3D", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_65ec9a1e-ea3e-4250-bf8c-97ca2486a66c_1.25x_20250722_203237.wav", "audio_duration": 15.114667, "video_url": "", "story_board_id": 20, "subtitle": [{"subtitle_id": 1, "text": "就这样，一个普通女孩的故事，", "timestamp": "00:00:00,200 --> 00:00:02,240", "duration": 2.04, "char_count": 14, "start_time_s": 0.2, "end_time_s": 2.24, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 200, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 320, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 400, "text": "样"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 720, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 920, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1040, "text": "普"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1240, "text": "通"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1400, "text": "女"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1560, "text": "孩"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1680, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1760, "text": "故"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 1920, "text": "事"}], "keyword": "女孩"}, {"subtitle_id": 2, "text": "逐渐牵动了整个时代的命运，", "timestamp": "00:00:02,240 --> 00:00:04,500", "duration": 2.26, "char_count": 13, "start_time_s": 2.24, "end_time_s": 4.5, "words": [{"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2240, "text": "逐"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2480, "text": "渐"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2640, "text": "牵"}, {"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2800, "text": "动"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2960, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3040, "text": "整"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3280, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3480, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3680, "text": "代"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3840, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3920, "text": "命"}, {"attribute": {"event": "speech"}, "end_time": 4500, "start_time": 4040, "text": "运"}], "keyword": "时代"}, {"subtitle_id": 3, "text": "她的出现不仅改变了家庭的命运，", "timestamp": "00:00:04,560 --> 00:00:06,960", "duration": 2.4, "char_count": 15, "start_time_s": 4.56, "end_time_s": 6.96, "words": [{"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4560, "text": "她"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4760, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4800, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 4960, "text": "现"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5280, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5480, "text": "仅"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5640, "text": "改"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5840, "text": "变"}, {"attribute": {"event": "speech"}, "end_time": 6080, "start_time": 5960, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6080, "text": "家"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6280, "text": "庭"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6400, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6520, "text": "命"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6640, "text": "运"}], "keyword": "家庭"}, {"subtitle_id": 4, "text": "也影响了国家的走向。", "timestamp": "00:00:06,960 --> 00:00:08,700", "duration": 1.74, "char_count": 10, "start_time_s": 6.96, "end_time_s": 8.7, "words": [{"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 6960, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7160, "text": "影"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7320, "text": "响"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7480, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7600, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7760, "text": "家"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7960, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8040, "text": "走"}, {"attribute": {"event": "speech"}, "end_time": 8700, "start_time": 8200, "text": "向"}], "keyword": "国家"}, {"subtitle_id": 5, "text": "而这一切，", "timestamp": "00:00:08,760 --> 00:00:09,600", "duration": 0.84, "char_count": 5, "start_time_s": 8.76, "end_time_s": 9.6, "words": [{"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8760, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 9120, "start_time": 9000, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 9240, "start_time": 9120, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 9600, "start_time": 9240, "text": "切"}], "keyword": "这一切"}, {"subtitle_id": 6, "text": "都始于那个阳光明媚的清晨，", "timestamp": "00:00:09,600 --> 00:00:11,680", "duration": 2.08, "char_count": 13, "start_time_s": 9.6, "end_time_s": 11.68, "words": [{"attribute": {"event": "speech"}, "end_time": 9800, "start_time": 9600, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 9920, "start_time": 9800, "text": "始"}, {"attribute": {"event": "speech"}, "end_time": 10040, "start_time": 9920, "text": "于"}, {"attribute": {"event": "speech"}, "end_time": 10160, "start_time": 10040, "text": "那"}, {"attribute": {"event": "speech"}, "end_time": 10280, "start_time": 10160, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 10440, "start_time": 10280, "text": "阳"}, {"attribute": {"event": "speech"}, "end_time": 10640, "start_time": 10480, "text": "光"}, {"attribute": {"event": "speech"}, "end_time": 10800, "start_time": 10640, "text": "明"}, {"attribute": {"event": "speech"}, "end_time": 10960, "start_time": 10800, "text": "媚"}, {"attribute": {"event": "speech"}, "end_time": 11080, "start_time": 10960, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 11280, "start_time": 11080, "text": "清"}, {"attribute": {"event": "speech"}, "end_time": 11680, "start_time": 11280, "text": "晨"}], "keyword": "清晨"}, {"subtitle_id": 7, "text": "当她第一次睁开眼睛", "timestamp": "00:00:11,680 --> 00:00:13,120", "duration": 1.44, "char_count": 9, "start_time_s": 11.68, "end_time_s": 13.12, "words": [{"attribute": {"event": "speech"}, "end_time": 11840, "start_time": 11680, "text": "当"}, {"attribute": {"event": "speech"}, "end_time": 11960, "start_time": 11840, "text": "她"}, {"attribute": {"event": "speech"}, "end_time": 12120, "start_time": 11960, "text": "第"}, {"attribute": {"event": "speech"}, "end_time": 12200, "start_time": 12120, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 12360, "start_time": 12200, "text": "次"}, {"attribute": {"event": "speech"}, "end_time": 12520, "start_time": 12360, "text": "睁"}, {"attribute": {"event": "speech"}, "end_time": 12680, "start_time": 12520, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 12840, "start_time": 12680, "text": "眼"}, {"attribute": {"event": "speech"}, "end_time": 13120, "start_time": 12840, "text": "睛"}], "keyword": "眼睛"}, {"subtitle_id": 8, "text": "看到这个世界的时候。", "timestamp": "00:00:13,120 --> 00:00:14,740", "duration": 1.62, "char_count": 10, "start_time_s": 13.12, "end_time_s": 14.74, "words": [{"attribute": {"event": "speech"}, "end_time": 13320, "start_time": 13120, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 13480, "start_time": 13320, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 13600, "start_time": 13480, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 13720, "start_time": 13600, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 13920, "start_time": 13720, "text": "世"}, {"attribute": {"event": "speech"}, "end_time": 14080, "start_time": 13920, "text": "界"}, {"attribute": {"event": "speech"}, "end_time": 14200, "start_time": 14080, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 14320, "start_time": 14200, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 14740, "start_time": 14320, "text": "候"}], "keyword": "世界"}], "keywords": ["女孩", "时代", "家庭", "国家", "这一切", "清晨", "眼睛", "世界"]}]