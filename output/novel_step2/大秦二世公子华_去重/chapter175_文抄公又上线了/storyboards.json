[{"chapter": 175, "story_board": "一个深夜，东宫的灯火突然全部熄灭。就在所有人以为是停电时，一道身影从书房中冲了出来，手中紧握着一叠厚厚的文稿，脸上满是兴奋与紧张。他大喊：“快！", "characters": [{"name": "太子华", "gender": "男", "age": "青年", "clothes": "高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "矫健", "identity": "太子", "other": "神情坚定", "from_chapter": 160, "action": "从书房中冲出来，手中紧握一叠厚厚的文稿，大喊", "expression": "兴奋与紧张"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "李由", "gender": "男", "age": "青年", "clothes": "高领圆领汉服长袍，主色调为青色，搭配白色，束发用青色发带，腰间系白色玉佩圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "修长", "identity": "与公子华交流工地情况之人", "other": "态度诚恳", "from_chapter": 85, "action": "", "expression": ""}, {"name": "尉羽", "gender": "男", "age": "青年", "clothes": "高领圆领秦国官服，主色调为深灰色，搭配浅灰色和银色，有回纹，束灰色腰带，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "健壮", "identity": "秦国官员", "other": "", "from_chapter": 142, "action": "", "expression": ""}, {"name": "相里飞", "gender": "男", "age": "青年", "clothes": "高领圆领汉服短衣，主色调为灰色，搭配白色，有简单几何花纹，腰间束布带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "正常", "identity": "公子华心腹", "other": "做事认真负责", "from_chapter": 19, "action": "", "expression": ""}], "scene": "书房", "image_prompt": "中景，从书房中冲出来、神情兴奋与紧张且手中紧握一叠厚厚文稿的青年男子，神情坚定，身姿矫健。该男子穿着高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪，圆形衣领，黑色长发束起，背景为书房, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753204978_20250723_012259.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_0aeaa96b-81e2-483a-93e7-c5acdacb25f3_1.25x_20250722_211243.wav", "audio_duration": 12.693333, "video_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/videos/video_1753205329_20250723_012849.mp4", "story_board_id": 1, "subtitle": [{"subtitle_id": 1, "text": "一个深夜，", "timestamp": "00:00:00,200 --> 00:00:00,880", "duration": 0.68, "char_count": 5, "start_time_s": 0.2, "end_time_s": 0.88, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 400, "text": "深"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 520, "text": "夜"}], "keyword": "深夜"}, {"subtitle_id": 2, "text": "东宫的灯火突然全部熄灭，", "timestamp": "00:00:00,880 --> 00:00:03,260", "duration": 2.38, "char_count": 12, "start_time_s": 0.88, "end_time_s": 3.26, "words": [{"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 880, "text": "东"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1040, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1200, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1320, "text": "灯"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1520, "text": "火"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1800, "text": "突"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1960, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2120, "text": "全"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2440, "text": "部"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2640, "text": "熄"}, {"attribute": {"event": "speech"}, "end_time": 3260, "start_time": 2800, "text": "灭"}], "keyword": "东宫"}, {"subtitle_id": 3, "text": "就在所有人以为是停电时，", "timestamp": "00:00:03,360 --> 00:00:05,400", "duration": 2.04, "char_count": 12, "start_time_s": 3.36, "end_time_s": 5.4, "words": [{"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3360, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3560, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3720, "text": "所"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3880, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3960, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4080, "text": "以"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4200, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4400, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4600, "text": "停"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4800, "text": "电"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5000, "text": "时"}], "keyword": "停电"}, {"subtitle_id": 4, "text": "一道身影从书房中冲了出来，", "timestamp": "00:00:05,400 --> 00:00:07,400", "duration": 2.0, "char_count": 13, "start_time_s": 5.4, "end_time_s": 7.4, "words": [{"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5400, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5560, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5720, "text": "身"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5880, "text": "影"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6000, "text": "从"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6160, "text": "书"}, {"attribute": {"event": "speech"}, "end_time": 6480, "start_time": 6320, "text": "房"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6480, "text": "中"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6680, "text": "冲"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6800, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6920, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7040, "text": "来"}], "keyword": "身影"}, {"subtitle_id": 5, "text": "手中紧握着一叠厚厚的文稿，", "timestamp": "00:00:07,400 --> 00:00:09,360", "duration": 1.96, "char_count": 13, "start_time_s": 7.4, "end_time_s": 9.36, "words": [{"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7400, "text": "手"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7560, "text": "中"}, {"attribute": {"event": "speech"}, "end_time": 7880, "start_time": 7720, "text": "紧"}, {"attribute": {"event": "speech"}, "end_time": 8000, "start_time": 7880, "text": "握"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 8000, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 8240, "start_time": 8120, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8240, "text": "叠"}, {"attribute": {"event": "speech"}, "end_time": 8440, "start_time": 8400, "text": "厚"}, {"attribute": {"event": "speech"}, "end_time": 8720, "start_time": 8440, "text": "厚"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8720, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8800, "text": "文"}, {"attribute": {"event": "speech"}, "end_time": 9360, "start_time": 9000, "text": "稿"}], "keyword": "文稿"}, {"subtitle_id": 6, "text": "脸上满是兴奋与紧张，", "timestamp": "00:00:09,360 --> 00:00:11,140", "duration": 1.78, "char_count": 10, "start_time_s": 9.36, "end_time_s": 11.14, "words": [{"attribute": {"event": "speech"}, "end_time": 9520, "start_time": 9360, "text": "脸"}, {"attribute": {"event": "speech"}, "end_time": 9640, "start_time": 9520, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 9800, "start_time": 9640, "text": "满"}, {"attribute": {"event": "speech"}, "end_time": 9960, "start_time": 9800, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 10120, "start_time": 9960, "text": "兴"}, {"attribute": {"event": "speech"}, "end_time": 10320, "start_time": 10160, "text": "奋"}, {"attribute": {"event": "speech"}, "end_time": 10480, "start_time": 10320, "text": "与"}, {"attribute": {"event": "speech"}, "end_time": 10640, "start_time": 10480, "text": "紧"}, {"attribute": {"event": "speech"}, "end_time": 11140, "start_time": 10680, "text": "张"}], "keyword": "兴奋"}, {"subtitle_id": 7, "text": "他大喊快！", "timestamp": "00:00:11,280 --> 00:00:12,580", "duration": 1.3, "char_count": 5, "start_time_s": 11.28, "end_time_s": 12.58, "words": [{"attribute": {"event": "speech"}, "end_time": 11480, "start_time": 11280, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 11640, "start_time": 11480, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 11800, "start_time": 11640, "text": "喊"}, {"attribute": {"event": "speech"}, "end_time": 12580, "start_time": 12040, "text": "快"}], "keyword": "大喊"}], "keywords": ["深夜", "东宫", "停电", "身影", "文稿", "兴奋", "大喊"]}, {"chapter": 175, "story_board": "把人叫来！”这一声怒吼，直接打破了宫廷的宁静，也让整个东宫陷入了一片慌乱。这个人就是太子华。", "characters": [{"name": "太子华", "gender": "男", "age": "青年", "clothes": "高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "矫健", "identity": "太子", "other": "神情坚定", "from_chapter": 160, "action": "从书房中冲出来，手中紧握着一叠厚厚的文稿，大喊", "expression": "兴奋与紧张"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "李由", "gender": "男", "age": "青年", "clothes": "高领圆领汉服长袍，主色调为青色，搭配白色，束发用青色发带，腰间系白色玉佩圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "修长", "identity": "与公子华交流工地情况之人", "other": "态度诚恳", "from_chapter": 85, "action": "", "expression": ""}, {"name": "尉羽", "gender": "男", "age": "青年", "clothes": "高领圆领秦国官服，主色调为深灰色，搭配浅灰色和银色，有回纹，束灰色腰带，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "健壮", "identity": "秦国官员", "other": "", "from_chapter": 142, "action": "", "expression": ""}, {"name": "相里飞", "gender": "男", "age": "青年", "clothes": "高领圆领汉服短衣，主色调为灰色，搭配白色，有简单几何花纹，腰间束布带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "正常", "identity": "公子华心腹", "other": "做事认真负责", "from_chapter": 19, "action": "", "expression": ""}], "scene": "书房", "image_prompt": "中景，一位神情兴奋又紧张、神情坚定的青年男子从书房中冲出来，手中紧握着一叠厚厚的文稿。该男子身着主色调为银色、搭配红色、刻有云纹的高圆领古代战甲，头戴盔缨，腰束皮带，配有长枪，圆形衣领，黑色长发束起，整体身姿矫健。背景为书房, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753205343_20250723_012904.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_dbeeaddd-d55d-480e-8553-2196d0badfb0_1.25x_20250722_211252.wav", "audio_duration": 8.512, "video_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/videos/video_1753206065_20250723_014106.mp4", "story_board_id": 2, "subtitle": [{"subtitle_id": 1, "text": "把人叫来，", "timestamp": "00:00:00,200 --> 00:00:01,140", "duration": 0.94, "char_count": 5, "start_time_s": 0.2, "end_time_s": 1.14, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "把"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 440, "text": "叫"}, {"attribute": {"event": "speech"}, "end_time": 1140, "start_time": 640, "text": "来"}], "keyword": "人"}, {"subtitle_id": 2, "text": "这一声怒吼", "timestamp": "00:00:01,200 --> 00:00:02,160", "duration": 0.96, "char_count": 5, "start_time_s": 1.2, "end_time_s": 2.16, "words": [{"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1200, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1400, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1480, "text": "声"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1600, "text": "怒"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 1760, "text": "吼"}], "keyword": "怒吼"}, {"subtitle_id": 3, "text": "直接打破了宫廷的宁静，", "timestamp": "00:00:02,160 --> 00:00:03,880", "duration": 1.72, "char_count": 11, "start_time_s": 2.16, "end_time_s": 3.88, "words": [{"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2160, "text": "直"}, {"attribute": {"event": "speech"}, "end_time": 2520, "start_time": 2400, "text": "接"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2520, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2720, "text": "破"}, {"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2880, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 2960, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3120, "text": "廷"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3280, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3400, "text": "宁"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3600, "text": "静"}], "keyword": "宫廷"}, {"subtitle_id": 4, "text": "也让整个东宫陷入了一片慌乱。", "timestamp": "00:00:03,880 --> 00:00:06,420", "duration": 2.54, "char_count": 14, "start_time_s": 3.88, "end_time_s": 6.42, "words": [{"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3880, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4040, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4200, "text": "整"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4400, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4560, "text": "东"}, {"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4720, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5040, "text": "陷"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5240, "text": "入"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5360, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5480, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5600, "text": "片"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5800, "text": "慌"}, {"attribute": {"event": "speech"}, "end_time": 6420, "start_time": 5960, "text": "乱"}], "keyword": "东宫"}, {"subtitle_id": 5, "text": "这个人就是太子华。", "timestamp": "00:00:06,520 --> 00:00:08,140", "duration": 1.62, "char_count": 9, "start_time_s": 6.52, "end_time_s": 8.14, "words": [{"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6520, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6720, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6840, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 7000, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7200, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 7520, "start_time": 7320, "text": "太"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7520, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 8140, "start_time": 7680, "text": "华"}], "keyword": "太子华"}], "keywords": ["人", "怒吼", "宫廷", "东宫", "太子华"]}, {"chapter": 175, "story_board": "他不是什么普通皇子，而是一个穿越者。他原本生活在后世，却意外来到了战国末期的大秦，成了太子。但问题来了——在这个讲究法度、禁止鬼神的国度里，他根本找不到任何可以让他打发时间的东西。", "characters": [{"name": "太子华", "gender": "男", "age": "青年", "clothes": "高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "矫健", "identity": "太子", "other": "神情坚定", "from_chapter": 160, "action": "坐在椅子上，手托下巴，眉头微皱", "expression": "苦恼"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "李由", "gender": "男", "age": "青年", "clothes": "高领圆领汉服长袍，主色调为青色，搭配白色，束发用青色发带，腰间系白色玉佩圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "修长", "identity": "与公子华交流工地情况之人", "other": "态度诚恳", "from_chapter": 85, "action": "", "expression": ""}, {"name": "尉羽", "gender": "男", "age": "青年", "clothes": "高领圆领秦国官服，主色调为深灰色，搭配浅灰色和银色，有回纹，束灰色腰带，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "健壮", "identity": "秦国官员", "other": "", "from_chapter": 142, "action": "", "expression": ""}, {"name": "相里飞", "gender": "男", "age": "青年", "clothes": "高领圆领汉服短衣，主色调为灰色，搭配白色，有简单几何花纹，腰间束布带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "正常", "identity": "公子华心腹", "other": "做事认真负责", "from_chapter": 19, "action": "", "expression": ""}], "scene": "书房", "image_prompt": "中景，一个神情苦恼、眉头微皱、手托下巴坐在椅子上的青年男子，其姿态矫健、神情坚定。画面背景为书房。该男子身着高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪，有着圆形衣领，黑色长发束起, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753206278_20250723_014438.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_a6b0a028-8e00-4de1-904c-d02207d7cd31_1.25x_20250722_211305.wav", "audio_duration": 14.346667, "video_url": "", "story_board_id": 3, "subtitle": [{"subtitle_id": 1, "text": "他不是什么普通皇子，", "timestamp": "00:00:00,160 --> 00:00:01,640", "duration": 1.48, "char_count": 10, "start_time_s": 0.16, "end_time_s": 1.64, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 440, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 600, "text": "什"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 680, "text": "么"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 800, "text": "普"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 960, "text": "通"}, {"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 1120, "text": "皇"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1320, "text": "子"}], "keyword": "皇子"}, {"subtitle_id": 2, "text": "而是一个穿越者。", "timestamp": "00:00:01,640 --> 00:00:03,020", "duration": 1.38, "char_count": 8, "start_time_s": 1.64, "end_time_s": 3.02, "words": [{"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1640, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1840, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1920, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2040, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2160, "text": "穿"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2360, "text": "越"}, {"attribute": {"event": "speech"}, "end_time": 3020, "start_time": 2560, "text": "者"}], "keyword": "穿越者"}, {"subtitle_id": 3, "text": "他原本生活在后世，", "timestamp": "00:00:03,160 --> 00:00:04,600", "duration": 1.44, "char_count": 9, "start_time_s": 3.16, "end_time_s": 4.6, "words": [{"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3320, "text": "原"}, {"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3480, "text": "本"}, {"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3640, "text": "生"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3800, "text": "活"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3920, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4040, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4240, "text": "世"}], "keyword": "后世"}, {"subtitle_id": 4, "text": "却意外来到了战国末期的大秦，", "timestamp": "00:00:04,600 --> 00:00:06,720", "duration": 2.12, "char_count": 14, "start_time_s": 4.6, "end_time_s": 6.72, "words": [{"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4600, "text": "却"}, {"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4720, "text": "意"}, {"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4880, "text": "外"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5000, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 5320, "start_time": 5160, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5320, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5400, "text": "战"}, {"attribute": {"event": "speech"}, "end_time": 5760, "start_time": 5600, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5760, "text": "末"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5920, "text": "期"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 6040, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6120, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6320, "text": "秦"}], "keyword": "大秦"}, {"subtitle_id": 5, "text": "成了太子。", "timestamp": "00:00:06,720 --> 00:00:07,780", "duration": 1.06, "char_count": 5, "start_time_s": 6.72, "end_time_s": 7.78, "words": [{"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6720, "text": "成"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6920, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7000, "text": "太"}, {"attribute": {"event": "speech"}, "end_time": 7780, "start_time": 7240, "text": "子"}], "keyword": "太子"}, {"subtitle_id": 6, "text": "但问题来了，", "timestamp": "00:00:07,800 --> 00:00:08,640", "duration": 0.84, "char_count": 6, "start_time_s": 7.8, "end_time_s": 8.64, "words": [{"attribute": {"event": "speech"}, "end_time": 8000, "start_time": 7800, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 8000, "text": "问"}, {"attribute": {"event": "speech"}, "end_time": 8240, "start_time": 8120, "text": "题"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8240, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8400, "text": "了"}], "keyword": "问题"}, {"subtitle_id": 7, "text": "在这个讲究法度，", "timestamp": "00:00:08,640 --> 00:00:09,760", "duration": 1.12, "char_count": 8, "start_time_s": 8.64, "end_time_s": 9.76, "words": [{"attribute": {"event": "speech"}, "end_time": 8840, "start_time": 8640, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8840, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 9040, "start_time": 8960, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 9240, "start_time": 9040, "text": "讲"}, {"attribute": {"event": "speech"}, "end_time": 9360, "start_time": 9240, "text": "究"}, {"attribute": {"event": "speech"}, "end_time": 9560, "start_time": 9360, "text": "法"}, {"attribute": {"event": "speech"}, "end_time": 9760, "start_time": 9600, "text": "度"}], "keyword": "法度"}, {"subtitle_id": 8, "text": "禁止鬼神的国度里，", "timestamp": "00:00:09,760 --> 00:00:11,160", "duration": 1.4, "char_count": 9, "start_time_s": 9.76, "end_time_s": 11.16, "words": [{"attribute": {"event": "speech"}, "end_time": 9920, "start_time": 9760, "text": "禁"}, {"attribute": {"event": "speech"}, "end_time": 10040, "start_time": 9920, "text": "止"}, {"attribute": {"event": "speech"}, "end_time": 10200, "start_time": 10040, "text": "鬼"}, {"attribute": {"event": "speech"}, "end_time": 10360, "start_time": 10240, "text": "神"}, {"attribute": {"event": "speech"}, "end_time": 10440, "start_time": 10360, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 10600, "start_time": 10440, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 10720, "start_time": 10600, "text": "度"}, {"attribute": {"event": "speech"}, "end_time": 11160, "start_time": 10720, "text": "里"}], "keyword": "鬼神"}, {"subtitle_id": 9, "text": "他根本找不到任何", "timestamp": "00:00:11,160 --> 00:00:12,360", "duration": 1.2, "char_count": 8, "start_time_s": 11.16, "end_time_s": 12.36, "words": [{"attribute": {"event": "speech"}, "end_time": 11320, "start_time": 11160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 11440, "start_time": 11320, "text": "根"}, {"attribute": {"event": "speech"}, "end_time": 11560, "start_time": 11440, "text": "本"}, {"attribute": {"event": "speech"}, "end_time": 11760, "start_time": 11600, "text": "找"}, {"attribute": {"event": "speech"}, "end_time": 11800, "start_time": 11760, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 11960, "start_time": 11800, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 12200, "start_time": 12000, "text": "任"}, {"attribute": {"event": "speech"}, "end_time": 12360, "start_time": 12200, "text": "何"}], "keyword": "时间"}, {"subtitle_id": 10, "text": "可以让他打发时间的东西。", "timestamp": "00:00:12,360 --> 00:00:14,020", "duration": 1.66, "char_count": 12, "start_time_s": 12.36, "end_time_s": 14.02, "words": [{"attribute": {"event": "speech"}, "end_time": 12480, "start_time": 12360, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 12520, "start_time": 12480, "text": "以"}, {"attribute": {"event": "speech"}, "end_time": 12680, "start_time": 12520, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 12800, "start_time": 12680, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 13000, "start_time": 12800, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 13120, "start_time": 13000, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 13280, "start_time": 13120, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 13400, "start_time": 13280, "text": "间"}, {"attribute": {"event": "speech"}, "end_time": 13480, "start_time": 13400, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 13600, "start_time": 13480, "text": "东"}, {"attribute": {"event": "speech"}, "end_time": 14020, "start_time": 13600, "text": "西"}], "keyword": "打发"}], "keywords": ["皇子", "穿越者", "后世", "大秦", "太子", "问题", "法度", "鬼神", "时间", "打发"]}, {"chapter": 175, "story_board": "于是，他决定做一件没人敢做的事：把后世的小说和歌曲统统写下来，让它们在这个时代流传开来。这不是为了炫耀，而是因为他觉得，既然自己穿越了，就不能白白浪费这次机会。他先是抄写了《射雕英雄传》，那里面的故事让他看到了江湖的豪情与侠义。", "characters": [{"name": "太子华", "gender": "男", "age": "青年", "clothes": "高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "矫健", "identity": "太子", "other": "神情坚定", "from_chapter": 160, "action": "坐在书桌前抄写文稿", "expression": "专注"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "李由", "gender": "男", "age": "青年", "clothes": "高领圆领汉服长袍，主色调为青色，搭配白色，束发用青色发带，腰间系白色玉佩圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "修长", "identity": "与公子华交流工地情况之人", "other": "态度诚恳", "from_chapter": 85, "action": "", "expression": ""}, {"name": "尉羽", "gender": "男", "age": "青年", "clothes": "高领圆领秦国官服，主色调为深灰色，搭配浅灰色和银色，有回纹，束灰色腰带，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "健壮", "identity": "秦国官员", "other": "", "from_chapter": 142, "action": "", "expression": ""}, {"name": "相里飞", "gender": "男", "age": "青年", "clothes": "高领圆领汉服短衣，主色调为灰色，搭配白色，有简单几何花纹，腰间束布带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "正常", "identity": "公子华心腹", "other": "做事认真负责", "from_chapter": 19, "action": "", "expression": ""}], "scene": "书房", "image_prompt": "中景，神情坚定、专注地坐在书桌前抄写文稿的青年男子，他身姿矫健，穿着高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪，圆形衣领，黑色长发束起，身后是书房的背景, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753206488_20250723_014808.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_a87f6537-2f30-4fdc-b55a-2e6c6f1d1398_1.25x_20250722_211248.wav", "audio_duration": 17.629333, "video_url": "", "story_board_id": 4, "subtitle": [{"subtitle_id": 1, "text": "于是他决定做一件没人敢做的事，", "timestamp": "00:00:00,200 --> 00:00:02,560", "duration": 2.36, "char_count": 15, "start_time_s": 0.2, "end_time_s": 2.56, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "于"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 280, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 600, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 760, "text": "决"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 880, "text": "定"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1040, "text": "做"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1200, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1360, "text": "件"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1520, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1640, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1760, "text": "敢"}, {"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1960, "text": "做"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2080, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2160, "text": "事"}], "keyword": "他"}, {"subtitle_id": 2, "text": "把后世的小说和歌曲统统写下来，", "timestamp": "00:00:02,560 --> 00:00:04,840", "duration": 2.28, "char_count": 15, "start_time_s": 2.56, "end_time_s": 4.84, "words": [{"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2560, "text": "把"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2720, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2920, "text": "世"}, {"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 3040, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3160, "text": "小"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3320, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3480, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3640, "text": "歌"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3760, "text": "曲"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3920, "text": "统"}, {"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 3960, "text": "统"}, {"attribute": {"event": "speech"}, "end_time": 4440, "start_time": 4280, "text": "写"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4440, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4560, "text": "来"}], "keyword": "小说"}, {"subtitle_id": 3, "text": "让他们在这个时代流传开来。", "timestamp": "00:00:04,840 --> 00:00:06,740", "duration": 1.9, "char_count": 13, "start_time_s": 4.84, "end_time_s": 6.74, "words": [{"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4840, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5040, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5160, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5240, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5360, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5480, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5600, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5680, "text": "代"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5840, "text": "流"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 6000, "text": "传"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6120, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 6740, "start_time": 6280, "text": "来"}], "keyword": "时代"}, {"subtitle_id": 4, "text": "这不是为了炫耀，", "timestamp": "00:00:06,800 --> 00:00:08,080", "duration": 1.28, "char_count": 8, "start_time_s": 6.8, "end_time_s": 8.08, "words": [{"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6800, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 7000, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7200, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7320, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7440, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7560, "text": "炫"}, {"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7720, "text": "耀"}], "keyword": "炫耀"}, {"subtitle_id": 5, "text": "而是因为他觉得既然自己穿越了，", "timestamp": "00:00:08,080 --> 00:00:10,520", "duration": 2.44, "char_count": 15, "start_time_s": 8.08, "end_time_s": 10.52, "words": [{"attribute": {"event": "speech"}, "end_time": 8280, "start_time": 8080, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8280, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8360, "text": "因"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8480, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 8840, "start_time": 8680, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8840, "text": "觉"}, {"attribute": {"event": "speech"}, "end_time": 9120, "start_time": 9000, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 9440, "start_time": 9280, "text": "既"}, {"attribute": {"event": "speech"}, "end_time": 9560, "start_time": 9440, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 9680, "start_time": 9560, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 9800, "start_time": 9680, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 10040, "start_time": 9840, "text": "穿"}, {"attribute": {"event": "speech"}, "end_time": 10200, "start_time": 10040, "text": "越"}, {"attribute": {"event": "speech"}, "end_time": 10520, "start_time": 10200, "text": "了"}], "keyword": "穿越"}, {"subtitle_id": 6, "text": "就不能白白浪费这次机会。", "timestamp": "00:00:10,520 --> 00:00:12,460", "duration": 1.94, "char_count": 12, "start_time_s": 10.52, "end_time_s": 12.46, "words": [{"attribute": {"event": "speech"}, "end_time": 10680, "start_time": 10520, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 10760, "start_time": 10680, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 10880, "start_time": 10760, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 11080, "start_time": 10880, "text": "白"}, {"attribute": {"event": "speech"}, "end_time": 11240, "start_time": 11120, "text": "白"}, {"attribute": {"event": "speech"}, "end_time": 11440, "start_time": 11280, "text": "浪"}, {"attribute": {"event": "speech"}, "end_time": 11560, "start_time": 11440, "text": "费"}, {"attribute": {"event": "speech"}, "end_time": 11720, "start_time": 11560, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 11840, "start_time": 11720, "text": "次"}, {"attribute": {"event": "speech"}, "end_time": 11960, "start_time": 11840, "text": "机"}, {"attribute": {"event": "speech"}, "end_time": 12460, "start_time": 11960, "text": "会"}], "keyword": "机会"}, {"subtitle_id": 7, "text": "他先是抄写了射雕英雄传，", "timestamp": "00:00:12,520 --> 00:00:14,320", "duration": 1.8, "char_count": 12, "start_time_s": 12.52, "end_time_s": 14.32, "words": [{"attribute": {"event": "speech"}, "end_time": 12720, "start_time": 12520, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 12840, "start_time": 12720, "text": "先"}, {"attribute": {"event": "speech"}, "end_time": 12960, "start_time": 12840, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 13160, "start_time": 12960, "text": "抄"}, {"attribute": {"event": "speech"}, "end_time": 13280, "start_time": 13160, "text": "写"}, {"attribute": {"event": "speech"}, "end_time": 13400, "start_time": 13280, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 13560, "start_time": 13400, "text": "射"}, {"attribute": {"event": "speech"}, "end_time": 13680, "start_time": 13560, "text": "雕"}, {"attribute": {"event": "speech"}, "end_time": 13800, "start_time": 13680, "text": "英"}, {"attribute": {"event": "speech"}, "end_time": 13960, "start_time": 13800, "text": "雄"}, {"attribute": {"event": "speech"}, "end_time": 14320, "start_time": 13960, "text": "传"}], "keyword": "射雕英雄"}, {"subtitle_id": 8, "text": "那里面的故事", "timestamp": "00:00:14,320 --> 00:00:15,200", "duration": 0.88, "char_count": 6, "start_time_s": 14.32, "end_time_s": 15.2, "words": [{"attribute": {"event": "speech"}, "end_time": 14520, "start_time": 14320, "text": "那"}, {"attribute": {"event": "speech"}, "end_time": 14600, "start_time": 14520, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 14720, "start_time": 14600, "text": "面"}, {"attribute": {"event": "speech"}, "end_time": 14840, "start_time": 14720, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 14960, "start_time": 14840, "text": "故"}, {"attribute": {"event": "speech"}, "end_time": 15200, "start_time": 14960, "text": "事"}], "keyword": "故事"}, {"subtitle_id": 9, "text": "让他看到了江湖的豪情与侠义。", "timestamp": "00:00:15,200 --> 00:00:17,580", "duration": 2.38, "char_count": 14, "start_time_s": 15.2, "end_time_s": 17.58, "words": [{"attribute": {"event": "speech"}, "end_time": 15400, "start_time": 15200, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 15520, "start_time": 15400, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 15720, "start_time": 15520, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 15840, "start_time": 15720, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 15920, "start_time": 15840, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 16080, "start_time": 15920, "text": "江"}, {"attribute": {"event": "speech"}, "end_time": 16280, "start_time": 16120, "text": "湖"}, {"attribute": {"event": "speech"}, "end_time": 16400, "start_time": 16280, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 16600, "start_time": 16400, "text": "豪"}, {"attribute": {"event": "speech"}, "end_time": 16760, "start_time": 16600, "text": "情"}, {"attribute": {"event": "speech"}, "end_time": 16920, "start_time": 16760, "text": "与"}, {"attribute": {"event": "speech"}, "end_time": 17120, "start_time": 16920, "text": "侠"}, {"attribute": {"event": "speech"}, "end_time": 17580, "start_time": 17120, "text": "义"}], "keyword": "江湖"}], "keywords": ["他", "小说", "时代", "炫耀", "穿越", "机会", "射雕英雄", "故事", "江湖"]}, {"chapter": 175, "story_board": "接着是《神雕侠侣》，那些爱情故事虽然有点复杂，但他还是坚持写了下来。最后是《笑傲江湖》，里面的人物个性鲜明，情节跌宕起伏，让他越写越上瘾。但光是写还不够，他还想把这些故事变成音乐。", "characters": [{"name": "太子华", "gender": "男", "age": "青年", "clothes": "高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "矫健", "identity": "太子", "other": "神情坚定", "from_chapter": 160, "action": "坐在书桌前奋笔疾书，时而停下思考，脸上露出专注且兴奋的神情；写完后站起身来，来回踱步，眼神坚定", "expression": "专注、兴奋"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "李由", "gender": "男", "age": "青年", "clothes": "高领圆领汉服长袍，主色调为青色，搭配白色，束发用青色发带，腰间系白色玉佩圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "修长", "identity": "与公子华交流工地情况之人", "other": "态度诚恳", "from_chapter": 85, "action": "", "expression": ""}, {"name": "尉羽", "gender": "男", "age": "青年", "clothes": "高领圆领秦国官服，主色调为深灰色，搭配浅灰色和银色，有回纹，束灰色腰带，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "健壮", "identity": "秦国官员", "other": "", "from_chapter": 142, "action": "", "expression": ""}, {"name": "相里飞", "gender": "男", "age": "青年", "clothes": "高领圆领汉服短衣，主色调为灰色，搭配白色，有简单几何花纹，腰间束布带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "正常", "identity": "公子华心腹", "other": "做事认真负责", "from_chapter": 19, "action": "", "expression": ""}], "scene": "书房", "image_prompt": "中景，在书房里，一个神情专注且兴奋的青年男子坐在书桌前奋笔疾书，时而停下思考，写完后站起身来回踱步，眼神坚定。青年身着高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪，圆形衣领，黑色长发束起，身姿矫健, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753206716_20250723_015156.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_35346e6f-3eb8-445a-a20d-29e3d933242d_1.25x_20250722_211310.wav", "audio_duration": 14.805333, "video_url": "", "story_board_id": 5, "subtitle": [{"subtitle_id": 1, "text": "接着是神雕侠侣，", "timestamp": "00:00:00,240 --> 00:00:01,440", "duration": 1.2, "char_count": 8, "start_time_s": 0.24, "end_time_s": 1.44, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 240, "text": "接"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 320, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 400, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 600, "text": "神"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 760, "text": "雕"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 960, "text": "侠"}, {"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1160, "text": "侣"}], "keyword": "神雕侠侣"}, {"subtitle_id": 2, "text": "那些爱情故事虽然有点复杂，", "timestamp": "00:00:01,440 --> 00:00:03,560", "duration": 2.12, "char_count": 13, "start_time_s": 1.44, "end_time_s": 3.56, "words": [{"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1440, "text": "那"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1640, "text": "些"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1720, "text": "爱"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1920, "text": "情"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2040, "text": "故"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2200, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2480, "text": "虽"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2640, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2720, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2880, "text": "点"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3040, "text": "复"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3240, "text": "杂"}], "keyword": "爱情故事"}, {"subtitle_id": 3, "text": "但他还是坚持写了下来。", "timestamp": "00:00:03,560 --> 00:00:05,460", "duration": 1.9, "char_count": 11, "start_time_s": 3.56, "end_time_s": 5.46, "words": [{"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3560, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3760, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3880, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4120, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 4440, "start_time": 4280, "text": "坚"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4480, "text": "持"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4640, "text": "写"}, {"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4760, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4840, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 5460, "start_time": 5000, "text": "来"}], "keyword": "坚持"}, {"subtitle_id": 4, "text": "最后是笑傲江湖，", "timestamp": "00:00:05,560 --> 00:00:06,880", "duration": 1.32, "char_count": 8, "start_time_s": 5.56, "end_time_s": 6.88, "words": [{"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5560, "text": "最"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5720, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5880, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 6080, "text": "笑"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6240, "text": "傲"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6360, "text": "江"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6560, "text": "湖"}], "keyword": "笑傲江湖"}, {"subtitle_id": 5, "text": "里面的人物个性鲜明，", "timestamp": "00:00:06,880 --> 00:00:08,400", "duration": 1.52, "char_count": 10, "start_time_s": 6.88, "end_time_s": 8.4, "words": [{"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6880, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 7080, "text": "面"}, {"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 7200, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7280, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7400, "text": "物"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7560, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7760, "text": "性"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7920, "text": "鲜"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8040, "text": "明"}], "keyword": "人物"}, {"subtitle_id": 6, "text": "情节跌宕起伏，", "timestamp": "00:00:08,400 --> 00:00:09,480", "duration": 1.08, "char_count": 7, "start_time_s": 8.4, "end_time_s": 9.48, "words": [{"attribute": {"event": "speech"}, "end_time": 8560, "start_time": 8400, "text": "情"}, {"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8560, "text": "节"}, {"attribute": {"event": "speech"}, "end_time": 8840, "start_time": 8680, "text": "跌"}, {"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8840, "text": "宕"}, {"attribute": {"event": "speech"}, "end_time": 9160, "start_time": 9000, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 9480, "start_time": 9160, "text": "伏"}], "keyword": "情节"}, {"subtitle_id": 7, "text": "让他越写越上瘾。", "timestamp": "00:00:09,480 --> 00:00:10,980", "duration": 1.5, "char_count": 8, "start_time_s": 9.48, "end_time_s": 10.98, "words": [{"attribute": {"event": "speech"}, "end_time": 9640, "start_time": 9480, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 9760, "start_time": 9640, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 10000, "start_time": 9800, "text": "越"}, {"attribute": {"event": "speech"}, "end_time": 10200, "start_time": 10040, "text": "写"}, {"attribute": {"event": "speech"}, "end_time": 10360, "start_time": 10200, "text": "越"}, {"attribute": {"event": "speech"}, "end_time": 10520, "start_time": 10360, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 10980, "start_time": 10520, "text": "瘾"}], "keyword": "写作"}, {"subtitle_id": 8, "text": "但光是写还不够，", "timestamp": "00:00:11,080 --> 00:00:12,320", "duration": 1.24, "char_count": 8, "start_time_s": 11.08, "end_time_s": 12.32, "words": [{"attribute": {"event": "speech"}, "end_time": 11280, "start_time": 11080, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 11480, "start_time": 11320, "text": "光"}, {"attribute": {"event": "speech"}, "end_time": 11640, "start_time": 11480, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 11720, "start_time": 11640, "text": "写"}, {"attribute": {"event": "speech"}, "end_time": 11880, "start_time": 11720, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 12000, "start_time": 11880, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 12320, "start_time": 12000, "text": "够"}], "keyword": "写"}, {"subtitle_id": 9, "text": "他还想把这些故事变成音乐。", "timestamp": "00:00:12,320 --> 00:00:14,500", "duration": 2.18, "char_count": 13, "start_time_s": 12.32, "end_time_s": 14.5, "words": [{"attribute": {"event": "speech"}, "end_time": 12480, "start_time": 12320, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 12640, "start_time": 12480, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 12760, "start_time": 12640, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 12880, "start_time": 12760, "text": "把"}, {"attribute": {"event": "speech"}, "end_time": 13000, "start_time": 12880, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 13120, "start_time": 13000, "text": "些"}, {"attribute": {"event": "speech"}, "end_time": 13320, "start_time": 13120, "text": "故"}, {"attribute": {"event": "speech"}, "end_time": 13520, "start_time": 13320, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 13720, "start_time": 13560, "text": "变"}, {"attribute": {"event": "speech"}, "end_time": 13880, "start_time": 13760, "text": "成"}, {"attribute": {"event": "speech"}, "end_time": 14040, "start_time": 13880, "text": "音"}, {"attribute": {"event": "speech"}, "end_time": 14500, "start_time": 14040, "text": "乐"}], "keyword": "音乐"}], "keywords": ["神雕侠侣", "爱情故事", "坚持", "笑傲江湖", "人物", "情节", "写作", "写", "音乐"]}, {"chapter": 175, "story_board": "他请来了宫廷里的乐师，让他们试着演奏他写下的曲子。一开始，大家都不太懂这些新奇的曲谱，节奏也不对，听起来怪怪的。但太子华没有放弃，他一遍遍地指导他们，直到有一天，乐师们终于能完整地唱出一首歌。", "characters": [{"name": "太子华", "gender": "男", "age": "青年", "clothes": "高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "矫健", "identity": "太子", "other": "神情坚定", "from_chapter": 160, "action": "一遍遍地指导乐师演奏曲子", "expression": "专注且坚定"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "李由", "gender": "男", "age": "青年", "clothes": "高领圆领汉服长袍，主色调为青色，搭配白色，束发用青色发带，腰间系白色玉佩圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "修长", "identity": "与公子华交流工地情况之人", "other": "态度诚恳", "from_chapter": 85, "action": "", "expression": ""}, {"name": "尉羽", "gender": "男", "age": "青年", "clothes": "高领圆领秦国官服，主色调为深灰色，搭配浅灰色和银色，有回纹，束灰色腰带，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "健壮", "identity": "秦国官员", "other": "", "from_chapter": 142, "action": "", "expression": ""}, {"name": "相里飞", "gender": "男", "age": "青年", "clothes": "高领圆领汉服短衣，主色调为灰色，搭配白色，有简单几何花纹，腰间束布带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "正常", "identity": "公子华心腹", "other": "做事认真负责", "from_chapter": 19, "action": "", "expression": ""}], "scene": "书房", "image_prompt": "中景，在书房中，一位神情专注且坚定的青年男子，正在一遍遍地指导乐师演奏曲子。该男子穿着高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪，圆形衣领，黑色长发束起，身姿矫健, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753206924_20250723_015524.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_d9a84139-9b63-43c1-b1cf-cfedf46fa461_1.25x_20250722_211238.wav", "audio_duration": 15.232, "video_url": "", "story_board_id": 6, "subtitle": [{"subtitle_id": 1, "text": "他请来了宫廷里的乐师，", "timestamp": "00:00:00,200 --> 00:00:01,640", "duration": 1.44, "char_count": 11, "start_time_s": 0.2, "end_time_s": 1.64, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "请"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 400, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 520, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 600, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 760, "text": "廷"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 920, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 1040, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 1120, "text": "乐"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1280, "text": "师"}], "keyword": "乐师"}, {"subtitle_id": 2, "text": "让他们试着演奏他写下的曲子。", "timestamp": "00:00:01,640 --> 00:00:03,860", "duration": 2.22, "char_count": 14, "start_time_s": 1.64, "end_time_s": 3.86, "words": [{"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1640, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1840, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1960, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2040, "text": "试"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2200, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2280, "text": "演"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2480, "text": "奏"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2640, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2760, "text": "写"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 2960, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3120, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3200, "text": "曲"}, {"attribute": {"event": "speech"}, "end_time": 3860, "start_time": 3400, "text": "子"}], "keyword": "曲子"}, {"subtitle_id": 3, "text": "一开始，", "timestamp": "00:00:03,920 --> 00:00:04,640", "duration": 0.72, "char_count": 4, "start_time_s": 3.92, "end_time_s": 4.64, "words": [{"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 3920, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4120, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4280, "text": "始"}], "keyword": "一开始"}, {"subtitle_id": 4, "text": "大家都不太懂这些新奇的曲谱，", "timestamp": "00:00:04,640 --> 00:00:06,720", "duration": 2.08, "char_count": 14, "start_time_s": 4.64, "end_time_s": 6.72, "words": [{"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4640, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4800, "text": "家"}, {"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 4960, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5080, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 5320, "start_time": 5200, "text": "太"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5360, "text": "懂"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5480, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 5760, "start_time": 5600, "text": "些"}, {"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5760, "text": "新"}, {"attribute": {"event": "speech"}, "end_time": 6080, "start_time": 5960, "text": "奇"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6080, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6200, "text": "曲"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6400, "text": "谱"}], "keyword": "曲谱"}, {"subtitle_id": 5, "text": "节奏也不对，", "timestamp": "00:00:06,720 --> 00:00:07,520", "duration": 0.8, "char_count": 6, "start_time_s": 6.72, "end_time_s": 7.52, "words": [{"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6720, "text": "节"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6920, "text": "奏"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 7040, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7160, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 7520, "start_time": 7240, "text": "对"}], "keyword": "节奏"}, {"subtitle_id": 6, "text": "听起来怪怪的。", "timestamp": "00:00:07,520 --> 00:00:08,740", "duration": 1.22, "char_count": 7, "start_time_s": 7.52, "end_time_s": 8.74, "words": [{"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7520, "text": "听"}, {"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7720, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7800, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7920, "text": "怪"}, {"attribute": {"event": "speech"}, "end_time": 8240, "start_time": 7960, "text": "怪"}, {"attribute": {"event": "speech"}, "end_time": 8740, "start_time": 8280, "text": "的"}], "keyword": "听起来"}, {"subtitle_id": 7, "text": "但太子华没有放弃，", "timestamp": "00:00:08,800 --> 00:00:10,280", "duration": 1.48, "char_count": 9, "start_time_s": 8.8, "end_time_s": 10.28, "words": [{"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8800, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 9200, "start_time": 9000, "text": "太"}, {"attribute": {"event": "speech"}, "end_time": 9320, "start_time": 9200, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 9480, "start_time": 9320, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 9640, "start_time": 9480, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 9720, "start_time": 9640, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 9920, "start_time": 9720, "text": "放"}, {"attribute": {"event": "speech"}, "end_time": 10280, "start_time": 9920, "text": "弃"}], "keyword": "太子华"}, {"subtitle_id": 8, "text": "他一遍遍地指导他们，", "timestamp": "00:00:10,280 --> 00:00:11,760", "duration": 1.48, "char_count": 10, "start_time_s": 10.28, "end_time_s": 11.76, "words": [{"attribute": {"event": "speech"}, "end_time": 10440, "start_time": 10280, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 10600, "start_time": 10440, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 10640, "start_time": 10600, "text": "遍"}, {"attribute": {"event": "speech"}, "end_time": 10920, "start_time": 10640, "text": "遍"}, {"attribute": {"event": "speech"}, "end_time": 11040, "start_time": 10920, "text": "地"}, {"attribute": {"event": "speech"}, "end_time": 11200, "start_time": 11040, "text": "指"}, {"attribute": {"event": "speech"}, "end_time": 11320, "start_time": 11200, "text": "导"}, {"attribute": {"event": "speech"}, "end_time": 11480, "start_time": 11320, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 11760, "start_time": 11480, "text": "们"}], "keyword": "指导"}, {"subtitle_id": 9, "text": "直到有一天，", "timestamp": "00:00:11,760 --> 00:00:12,720", "duration": 0.96, "char_count": 6, "start_time_s": 11.76, "end_time_s": 12.72, "words": [{"attribute": {"event": "speech"}, "end_time": 11920, "start_time": 11760, "text": "直"}, {"attribute": {"event": "speech"}, "end_time": 12080, "start_time": 11920, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 12240, "start_time": 12080, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 12320, "start_time": 12240, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 12720, "start_time": 12320, "text": "天"}], "keyword": "有一天"}, {"subtitle_id": 10, "text": "乐师们终于能完整地唱出一首歌。", "timestamp": "00:00:12,720 --> 00:00:15,140", "duration": 2.42, "char_count": 15, "start_time_s": 12.72, "end_time_s": 15.14, "words": [{"attribute": {"event": "speech"}, "end_time": 12880, "start_time": 12720, "text": "乐"}, {"attribute": {"event": "speech"}, "end_time": 13040, "start_time": 12920, "text": "师"}, {"attribute": {"event": "speech"}, "end_time": 13160, "start_time": 13040, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 13360, "start_time": 13200, "text": "终"}, {"attribute": {"event": "speech"}, "end_time": 13480, "start_time": 13360, "text": "于"}, {"attribute": {"event": "speech"}, "end_time": 13600, "start_time": 13480, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 13760, "start_time": 13600, "text": "完"}, {"attribute": {"event": "speech"}, "end_time": 13960, "start_time": 13800, "text": "整"}, {"attribute": {"event": "speech"}, "end_time": 14040, "start_time": 13960, "text": "地"}, {"attribute": {"event": "speech"}, "end_time": 14240, "start_time": 14040, "text": "唱"}, {"attribute": {"event": "speech"}, "end_time": 14440, "start_time": 14280, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 14520, "start_time": 14440, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 14680, "start_time": 14520, "text": "首"}, {"attribute": {"event": "speech"}, "end_time": 15140, "start_time": 14680, "text": "歌"}], "keyword": "歌"}], "keywords": ["乐师", "曲子", "一开始", "曲谱", "节奏", "听起来", "太子华", "指导", "有一天", "歌"]}, {"chapter": 175, "story_board": "可他总觉得还差一点。虽然旋律出来了，但那种情感的表达还不够。他开始思考，是不是需要一种新的乐器？", "characters": [{"name": "太子华", "gender": "男", "age": "青年", "clothes": "高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "矫健", "identity": "太子", "other": "神情坚定", "from_chapter": 160, "action": "一手托着下巴，低头沉思", "expression": "思索"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "李由", "gender": "男", "age": "青年", "clothes": "高领圆领汉服长袍，主色调为青色，搭配白色，束发用青色发带，腰间系白色玉佩圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "修长", "identity": "与公子华交流工地情况之人", "other": "态度诚恳", "from_chapter": 85, "action": "", "expression": ""}, {"name": "尉羽", "gender": "男", "age": "青年", "clothes": "高领圆领秦国官服，主色调为深灰色，搭配浅灰色和银色，有回纹，束灰色腰带，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "健壮", "identity": "秦国官员", "other": "", "from_chapter": 142, "action": "", "expression": ""}, {"name": "相里飞", "gender": "男", "age": "青年", "clothes": "高领圆领汉服短衣，主色调为灰色，搭配白色，有简单几何花纹，腰间束布带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "正常", "identity": "公子华心腹", "other": "做事认真负责", "from_chapter": 19, "action": "", "expression": ""}], "scene": "书房", "image_prompt": "中景，一位神情坚定、矫健的青年男子站在书房中，一手托着下巴，低头沉思。他身着高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪，圆形衣领，黑色长发束起, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753207123_20250723_015843.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_d973a091-9f82-4830-8473-ac11ad2fba5c_1.25x_20250722_211314.wav", "audio_duration": 8.376, "video_url": "", "story_board_id": 7, "subtitle": [{"subtitle_id": 1, "text": "可他总觉得还差一点，", "timestamp": "00:00:00,120 --> 00:00:01,740", "duration": 1.62, "char_count": 10, "start_time_s": 0.12, "end_time_s": 1.74, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 120, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 400, "text": "总"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 600, "text": "觉"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 720, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 840, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 1000, "text": "差"}, {"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 1160, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 1740, "start_time": 1280, "text": "点"}], "keyword": "他"}, {"subtitle_id": 2, "text": "虽然旋律出来了，", "timestamp": "00:00:01,960 --> 00:00:03,120", "duration": 1.16, "char_count": 8, "start_time_s": 1.96, "end_time_s": 3.12, "words": [{"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1960, "text": "虽"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2080, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2200, "text": "旋"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2360, "text": "律"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2480, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2640, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 2760, "text": "了"}], "keyword": "旋律"}, {"subtitle_id": 3, "text": "但那种情感的表达还不够。", "timestamp": "00:00:03,120 --> 00:00:05,020", "duration": 1.9, "char_count": 12, "start_time_s": 3.12, "end_time_s": 5.02, "words": [{"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3120, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3280, "text": "那"}, {"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3400, "text": "种"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3560, "text": "情"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3760, "text": "感"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3880, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 3960, "text": "表"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4160, "text": "达"}, {"attribute": {"event": "speech"}, "end_time": 4440, "start_time": 4280, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4440, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 5020, "start_time": 4560, "text": "够"}], "keyword": "情感"}, {"subtitle_id": 4, "text": "他开始思考，", "timestamp": "00:00:05,120 --> 00:00:06,160", "duration": 1.04, "char_count": 6, "start_time_s": 5.12, "end_time_s": 6.16, "words": [{"attribute": {"event": "speech"}, "end_time": 5320, "start_time": 5120, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5320, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5480, "text": "始"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5640, "text": "思"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 5800, "text": "考"}], "keyword": "思考"}, {"subtitle_id": 5, "text": "是不是需要一种新的乐器。", "timestamp": "00:00:06,160 --> 00:00:08,060", "duration": 1.9, "char_count": 12, "start_time_s": 6.16, "end_time_s": 8.06, "words": [{"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6160, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6280, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6400, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6520, "text": "需"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6640, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6760, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6880, "text": "种"}, {"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 7080, "text": "新"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7280, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7400, "text": "乐"}, {"attribute": {"event": "speech"}, "end_time": 8060, "start_time": 7560, "text": "器"}], "keyword": "乐器"}], "keywords": ["他", "旋律", "情感", "思考", "乐器"]}, {"chapter": 175, "story_board": "或者找一些懂得音乐的人帮忙？就在这时，他想到了蒙恬。传说他是发明毛笔的人，说不定对乐器也有研究。", "characters": [{"name": "太子华", "gender": "男", "age": "青年", "clothes": "高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "矫健", "identity": "太子", "other": "神情坚定", "from_chapter": 160, "action": "坐在桌前，手托下巴，低头沉思", "expression": "思索"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "李由", "gender": "男", "age": "青年", "clothes": "高领圆领汉服长袍，主色调为青色，搭配白色，束发用青色发带，腰间系白色玉佩圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "修长", "identity": "与公子华交流工地情况之人", "other": "态度诚恳", "from_chapter": 85, "action": "", "expression": ""}, {"name": "尉羽", "gender": "男", "age": "青年", "clothes": "高领圆领秦国官服，主色调为深灰色，搭配浅灰色和银色，有回纹，束灰色腰带，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "健壮", "identity": "秦国官员", "other": "", "from_chapter": 142, "action": "", "expression": ""}, {"name": "相里飞", "gender": "男", "age": "青年", "clothes": "高领圆领汉服短衣，主色调为灰色，搭配白色，有简单几何花纹，腰间束布带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "正常", "identity": "公子华心腹", "other": "做事认真负责", "from_chapter": 19, "action": "", "expression": ""}], "scene": "书房", "image_prompt": "中景，坐在桌前手托下巴低头沉思、神情坚定的青年男子，身着高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪，圆形衣领，黑色长发束起，整体形象矫健，画面背景为书房, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753207309_20250723_020149.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_d12d6677-0f9c-4e6b-ba8c-1ab59a4b2361_1.25x_20250722_211256.wav", "audio_duration": 8.394667, "video_url": "", "story_board_id": 8, "subtitle": [{"subtitle_id": 1, "text": "或者找一些懂得音乐的人帮忙。", "timestamp": "00:00:00,200 --> 00:00:02,300", "duration": 2.1, "char_count": 14, "start_time_s": 0.2, "end_time_s": 2.3, "words": [{"attribute": {"event": "speech"}, "end_time": 360, "start_time": 200, "text": "或"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 360, "text": "者"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 480, "text": "找"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 600, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 720, "text": "些"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 840, "text": "懂"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 1000, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 1120, "text": "音"}, {"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1280, "text": "乐"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1440, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1520, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1640, "text": "帮"}, {"attribute": {"event": "speech"}, "end_time": 2300, "start_time": 1800, "text": "忙"}], "keyword": "音乐人"}, {"subtitle_id": 2, "text": "就在这时，", "timestamp": "00:00:02,480 --> 00:00:03,360", "duration": 0.88, "char_count": 5, "start_time_s": 2.48, "end_time_s": 3.36, "words": [{"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2480, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2680, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2800, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 2960, "text": "时"}], "keyword": "这时"}, {"subtitle_id": 3, "text": "他想到了蒙恬，", "timestamp": "00:00:03,360 --> 00:00:04,620", "duration": 1.26, "char_count": 7, "start_time_s": 3.36, "end_time_s": 4.62, "words": [{"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3360, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3520, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3720, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3880, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 3960, "text": "蒙"}, {"attribute": {"event": "speech"}, "end_time": 4620, "start_time": 4160, "text": "恬"}], "keyword": "蒙恬"}, {"subtitle_id": 4, "text": "传说他是发明毛笔的人，", "timestamp": "00:00:04,840 --> 00:00:06,360", "duration": 1.52, "char_count": 11, "start_time_s": 4.84, "end_time_s": 6.36, "words": [{"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4840, "text": "传"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 5000, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5120, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5240, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5360, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5520, "text": "明"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5640, "text": "毛"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5800, "text": "笔"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5880, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 5960, "text": "人"}], "keyword": "毛笔"}, {"subtitle_id": 5, "text": "说不定对乐器也有研究。", "timestamp": "00:00:06,360 --> 00:00:08,060", "duration": 1.7, "char_count": 11, "start_time_s": 6.36, "end_time_s": 8.06, "words": [{"attribute": {"event": "speech"}, "end_time": 6480, "start_time": 6360, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6480, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6560, "text": "定"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6680, "text": "对"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6800, "text": "乐"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 7000, "text": "器"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7160, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7320, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7440, "text": "研"}, {"attribute": {"event": "speech"}, "end_time": 8060, "start_time": 7600, "text": "究"}], "keyword": "乐器"}], "keywords": ["音乐人", "这时", "蒙恬", "毛笔", "乐器"]}, {"chapter": 175, "story_board": "但很快他就打消了这个念头——因为古筝早在战国时期就已经存在了，蒙恬并没有发明它。太子华叹了口气，心想：有些事不能强求，人生总得留点遗憾。他决定不再纠结，继续推进自己的计划。", "characters": [{"name": "太子华", "gender": "男", "age": "青年", "clothes": "高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "矫健", "identity": "太子", "other": "神情坚定", "from_chapter": 160, "action": "先是低头思考，随后轻轻摇头，叹了口气，接着开始整理桌上的文稿", "expression": "略有失落但又坦然"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "李由", "gender": "男", "age": "青年", "clothes": "高领圆领汉服长袍，主色调为青色，搭配白色，束发用青色发带，腰间系白色玉佩圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "修长", "identity": "与公子华交流工地情况之人", "other": "态度诚恳", "from_chapter": 85, "action": "", "expression": ""}, {"name": "尉羽", "gender": "男", "age": "青年", "clothes": "高领圆领秦国官服，主色调为深灰色，搭配浅灰色和银色，有回纹，束灰色腰带，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "健壮", "identity": "秦国官员", "other": "", "from_chapter": 142, "action": "", "expression": ""}, {"name": "相里飞", "gender": "男", "age": "青年", "clothes": "高领圆领汉服短衣，主色调为灰色，搭配白色，有简单几何花纹，腰间束布带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "正常", "identity": "公子华心腹", "other": "做事认真负责", "from_chapter": 19, "action": "", "expression": ""}], "scene": "书房", "image_prompt": "中景，在书房中，先是低头思考随后轻轻摇头叹气，接着开始整理桌上文稿，神情略有失落但又坦然的青年男子。该男子穿着高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪，圆形衣领，黑色长发束起，身姿矫健, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753207502_20250723_020502.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_cf5c17bc-f91d-40fc-ab23-495696ff8c42_1.25x_20250722_211318.wav", "audio_duration": 14.290667, "video_url": "", "story_board_id": 9, "subtitle": [{"subtitle_id": 1, "text": "但很快他就打消了这个念头，", "timestamp": "00:00:00,160 --> 00:00:02,000", "duration": 1.84, "char_count": 13, "start_time_s": 0.16, "end_time_s": 2.0, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "很"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 440, "text": "快"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 680, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 840, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 960, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1160, "text": "消"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1320, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1400, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1520, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1600, "text": "念"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1720, "text": "头"}], "keyword": "念头"}, {"subtitle_id": 2, "text": "因为", "timestamp": "00:00:02,000 --> 00:00:02,240", "duration": 0.24, "char_count": 2, "start_time_s": 2.0, "end_time_s": 2.24, "words": [{"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 2000, "text": "因"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2120, "text": "为"}], "keyword": "因为"}, {"subtitle_id": 3, "text": "古筝早在战国时期就已经存在了，", "timestamp": "00:00:02,240 --> 00:00:04,560", "duration": 2.32, "char_count": 15, "start_time_s": 2.24, "end_time_s": 4.56, "words": [{"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2240, "text": "古"}, {"attribute": {"event": "speech"}, "end_time": 2520, "start_time": 2400, "text": "筝"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2520, "text": "早"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2720, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2840, "text": "战"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 3000, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3120, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3280, "text": "期"}, {"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3520, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3680, "text": "已"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3800, "text": "经"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 3920, "text": "存"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4120, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4280, "text": "了"}], "keyword": "古筝"}, {"subtitle_id": 4, "text": "蒙恬并没有发明。", "timestamp": "00:00:04,560 --> 00:00:05,640", "duration": 1.08, "char_count": 8, "start_time_s": 4.56, "end_time_s": 5.64, "words": [{"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4560, "text": "蒙"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4760, "text": "恬"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 4920, "text": "并"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5120, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5240, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5360, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5480, "text": "明"}], "keyword": "蒙恬"}, {"subtitle_id": 5, "text": "他太子华叹了口气，", "timestamp": "00:00:05,640 --> 00:00:07,560", "duration": 1.92, "char_count": 9, "start_time_s": 5.64, "end_time_s": 7.56, "words": [{"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5640, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 6480, "start_time": 6240, "text": "太"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6480, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6640, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6800, "text": "叹"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6920, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 7000, "text": "口"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7200, "text": "气"}], "keyword": "太子华"}, {"subtitle_id": 6, "text": "心想有些事不能强求，", "timestamp": "00:00:07,560 --> 00:00:09,360", "duration": 1.8, "char_count": 10, "start_time_s": 7.56, "end_time_s": 9.36, "words": [{"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7560, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7760, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8120, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 8440, "start_time": 8320, "text": "些"}, {"attribute": {"event": "speech"}, "end_time": 8560, "start_time": 8440, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8560, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8680, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 9040, "start_time": 8800, "text": "强"}, {"attribute": {"event": "speech"}, "end_time": 9360, "start_time": 9040, "text": "求"}], "keyword": "心想"}, {"subtitle_id": 7, "text": "人生总得留点遗憾。", "timestamp": "00:00:09,360 --> 00:00:10,940", "duration": 1.58, "char_count": 9, "start_time_s": 9.36, "end_time_s": 10.94, "words": [{"attribute": {"event": "speech"}, "end_time": 9600, "start_time": 9360, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 9760, "start_time": 9600, "text": "生"}, {"attribute": {"event": "speech"}, "end_time": 9880, "start_time": 9760, "text": "总"}, {"attribute": {"event": "speech"}, "end_time": 10040, "start_time": 9880, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 10200, "start_time": 10040, "text": "留"}, {"attribute": {"event": "speech"}, "end_time": 10320, "start_time": 10200, "text": "点"}, {"attribute": {"event": "speech"}, "end_time": 10480, "start_time": 10360, "text": "遗"}, {"attribute": {"event": "speech"}, "end_time": 10940, "start_time": 10480, "text": "憾"}], "keyword": "遗憾"}, {"subtitle_id": 8, "text": "他决定不再纠结，", "timestamp": "00:00:11,080 --> 00:00:12,320", "duration": 1.24, "char_count": 8, "start_time_s": 11.08, "end_time_s": 12.32, "words": [{"attribute": {"event": "speech"}, "end_time": 11240, "start_time": 11080, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 11400, "start_time": 11240, "text": "决"}, {"attribute": {"event": "speech"}, "end_time": 11520, "start_time": 11400, "text": "定"}, {"attribute": {"event": "speech"}, "end_time": 11680, "start_time": 11520, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 11800, "start_time": 11680, "text": "再"}, {"attribute": {"event": "speech"}, "end_time": 11960, "start_time": 11840, "text": "纠"}, {"attribute": {"event": "speech"}, "end_time": 12320, "start_time": 11960, "text": "结"}], "keyword": "纠结"}, {"subtitle_id": 9, "text": "继续推进自己的计划。", "timestamp": "00:00:12,320 --> 00:00:13,980", "duration": 1.66, "char_count": 10, "start_time_s": 12.32, "end_time_s": 13.98, "words": [{"attribute": {"event": "speech"}, "end_time": 12520, "start_time": 12320, "text": "继"}, {"attribute": {"event": "speech"}, "end_time": 12640, "start_time": 12520, "text": "续"}, {"attribute": {"event": "speech"}, "end_time": 12800, "start_time": 12640, "text": "推"}, {"attribute": {"event": "speech"}, "end_time": 12960, "start_time": 12840, "text": "进"}, {"attribute": {"event": "speech"}, "end_time": 13120, "start_time": 12960, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 13240, "start_time": 13120, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 13360, "start_time": 13240, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 13480, "start_time": 13360, "text": "计"}, {"attribute": {"event": "speech"}, "end_time": 13980, "start_time": 13480, "text": "划"}], "keyword": "计划"}], "keywords": ["念头", "因为", "古筝", "蒙恬", "太子华", "心想", "遗憾", "纠结", "计划"]}, {"chapter": 175, "story_board": "他派人去请了三位心腹大臣——李由、尉羽、相里飞。这三个人平时负责国家大事，很少接触小说。但今天，他们收到了一份特殊的礼物：几叠文稿，上面写着《射雕英雄传》《神雕侠侣》和《笑傲江湖》的前二十章。", "characters": [{"name": "太子华", "gender": "男", "age": "青年", "clothes": "高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "矫健", "identity": "太子", "other": "神情坚定", "from_chapter": 160, "action": "将几叠文稿递给李由、尉羽、相里飞，坐在一旁静静看着", "expression": "平静且期待"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "李由", "gender": "男", "age": "青年", "clothes": "高领圆领汉服长袍，主色调为青色，搭配白色，束发用青色发带，腰间系白色玉佩圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "修长", "identity": "与公子华交流工地情况之人", "other": "态度诚恳", "from_chapter": 85, "action": "接过文稿", "expression": "疑惑"}, {"name": "尉羽", "gender": "男", "age": "青年", "clothes": "高领圆领秦国官服，主色调为深灰色，搭配浅灰色和银色，有回纹，束灰色腰带，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "健壮", "identity": "秦国官员", "other": "", "from_chapter": 142, "action": "接过文稿", "expression": "疑惑"}, {"name": "相里飞", "gender": "男", "age": "青年", "clothes": "高领圆领汉服短衣，主色调为灰色，搭配白色，有简单几何花纹，腰间束布带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "正常", "identity": "公子华心腹", "other": "做事认真负责", "from_chapter": 19, "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿中，一位神情坚定、态度矫健的青年男子将几叠文稿递给另外两位青年男子后，坐在一旁平静且期待地看着他们。两位接过文稿的青年男子脸上带着疑惑的神情。递文稿的青年男子束起黑色长发，身着主色调为银色、搭配红色、刻有云纹的高圆领古代战甲，头戴盔缨，腰束皮带，配有长枪，有着圆形衣领。其中一位接过文稿的青年男子束起黑色长发，用青色发带束发，腰间系着白色玉佩，身着主色调为青色、搭配白色的高领圆领汉服长袍，有着圆形衣领，身姿修长。另一位接过文稿的青年男子束起黑色头发，头戴黑色官帽，身着主色调为深灰色、搭配浅灰色和银色、有回纹的高领圆领秦国官服，束着灰色腰带，有着圆形衣领，身体健壮。, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753207690_20250723_020810.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_e1cf975b-ec36-4ada-9a38-50e63474cade_1.25x_20250722_211300.wav", "audio_duration": 14.866667, "video_url": "", "story_board_id": 10, "subtitle": [{"subtitle_id": 1, "text": "他派人去请了三位心腹大臣，", "timestamp": "00:00:00,160 --> 00:00:01,920", "duration": 1.76, "char_count": 13, "start_time_s": 0.16, "end_time_s": 1.92, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "派"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 400, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 560, "text": "去"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 720, "text": "请"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 840, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 920, "text": "三"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1080, "text": "位"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1240, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1400, "text": "腹"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1520, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1680, "text": "臣"}], "keyword": "心腹大臣"}, {"subtitle_id": 2, "text": "理由玉羽、像、李飞", "timestamp": "00:00:01,920 --> 00:00:03,220", "duration": 1.3, "char_count": 9, "start_time_s": 1.92, "end_time_s": 3.22, "words": [{"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1920, "text": "理"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2080, "text": "由"}, {"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2200, "text": "玉"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2320, "text": "羽"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2480, "text": "像"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2640, "text": "李"}, {"attribute": {"event": "speech"}, "end_time": 3220, "start_time": 2760, "text": "飞"}], "keyword": "玉羽"}, {"subtitle_id": 3, "text": "这三个人平时负责国家大事，", "timestamp": "00:00:03,360 --> 00:00:05,400", "duration": 2.04, "char_count": 13, "start_time_s": 3.36, "end_time_s": 5.4, "words": [{"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3360, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3560, "text": "三"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3680, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3760, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4000, "text": "平"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4200, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4360, "text": "负"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4520, "text": "责"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4640, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4800, "text": "家"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 4960, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5120, "text": "事"}], "keyword": "国家大事"}, {"subtitle_id": 4, "text": "很少接触小说。", "timestamp": "00:00:05,400 --> 00:00:06,780", "duration": 1.38, "char_count": 7, "start_time_s": 5.4, "end_time_s": 6.78, "words": [{"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5400, "text": "很"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5680, "text": "少"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5880, "text": "接"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 6040, "text": "触"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6120, "text": "小"}, {"attribute": {"event": "speech"}, "end_time": 6780, "start_time": 6320, "text": "说"}], "keyword": "小说"}, {"subtitle_id": 5, "text": "但今天他们收到了一份特殊的礼物", "timestamp": "00:00:06,880 --> 00:00:09,520", "duration": 2.64, "char_count": 15, "start_time_s": 6.88, "end_time_s": 9.52, "words": [{"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6880, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 7080, "text": "今"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7200, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7600, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 7840, "start_time": 7760, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7840, "text": "收"}, {"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7960, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 8080, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 8280, "start_time": 8120, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8280, "text": "份"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8400, "text": "特"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8640, "text": "殊"}, {"attribute": {"event": "speech"}, "end_time": 8920, "start_time": 8800, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 9080, "start_time": 8920, "text": "礼"}, {"attribute": {"event": "speech"}, "end_time": 9520, "start_time": 9080, "text": "物"}], "keyword": "礼物"}, {"subtitle_id": 6, "text": "几叠文稿，", "timestamp": "00:00:09,520 --> 00:00:10,320", "duration": 0.8, "char_count": 5, "start_time_s": 9.52, "end_time_s": 10.32, "words": [{"attribute": {"event": "speech"}, "end_time": 9680, "start_time": 9520, "text": "几"}, {"attribute": {"event": "speech"}, "end_time": 9800, "start_time": 9680, "text": "叠"}, {"attribute": {"event": "speech"}, "end_time": 9960, "start_time": 9840, "text": "文"}, {"attribute": {"event": "speech"}, "end_time": 10320, "start_time": 10040, "text": "稿"}], "keyword": "文稿"}, {"subtitle_id": 7, "text": "上面写着射雕英雄传、", "timestamp": "00:00:10,320 --> 00:00:11,800", "duration": 1.48, "char_count": 10, "start_time_s": 10.32, "end_time_s": 11.8, "words": [{"attribute": {"event": "speech"}, "end_time": 10480, "start_time": 10320, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 10560, "start_time": 10480, "text": "面"}, {"attribute": {"event": "speech"}, "end_time": 10720, "start_time": 10560, "text": "写"}, {"attribute": {"event": "speech"}, "end_time": 10880, "start_time": 10720, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 11160, "start_time": 11000, "text": "射"}, {"attribute": {"event": "speech"}, "end_time": 11280, "start_time": 11200, "text": "雕"}, {"attribute": {"event": "speech"}, "end_time": 11440, "start_time": 11320, "text": "英"}, {"attribute": {"event": "speech"}, "end_time": 11600, "start_time": 11440, "text": "雄"}, {"attribute": {"event": "speech"}, "end_time": 11800, "start_time": 11600, "text": "传"}], "keyword": "射雕英雄"}, {"subtitle_id": 8, "text": "神雕侠侣和笑傲江湖的前20章。", "timestamp": "00:00:11,800 --> 00:00:14,540", "duration": 2.74, "char_count": 15, "start_time_s": 11.8, "end_time_s": 14.54, "words": [{"attribute": {"event": "speech"}, "end_time": 11960, "start_time": 11800, "text": "神"}, {"attribute": {"event": "speech"}, "end_time": 12160, "start_time": 12000, "text": "雕"}, {"attribute": {"event": "speech"}, "end_time": 12360, "start_time": 12160, "text": "侠"}, {"attribute": {"event": "speech"}, "end_time": 12560, "start_time": 12400, "text": "侣"}, {"attribute": {"event": "speech"}, "end_time": 12800, "start_time": 12640, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 13000, "start_time": 12840, "text": "笑"}, {"attribute": {"event": "speech"}, "end_time": 13120, "start_time": 13000, "text": "傲"}, {"attribute": {"event": "speech"}, "end_time": 13320, "start_time": 13160, "text": "江"}, {"attribute": {"event": "speech"}, "end_time": 13480, "start_time": 13360, "text": "湖"}, {"attribute": {"event": "speech"}, "end_time": 13640, "start_time": 13480, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 13800, "start_time": 13640, "text": "前"}, {"attribute": {"event": "speech"}, "end_time": 14080, "start_time": 13800, "text": "20"}, {"attribute": {"event": "speech"}, "end_time": 14540, "start_time": 14080, "text": "章"}], "keyword": "神雕侠侣"}], "keywords": ["心腹大臣", "玉羽", "国家大事", "小说", "礼物", "文稿", "射雕英雄", "神雕侠侣"]}, {"chapter": 175, "story_board": "三人接过文稿，一脸疑惑地看着太子华。他们没想到，这位平日里严肃的太子，竟然会拿出这种“闲书”来给他们看。但他们不知道的是，这些文字背后藏着一个巨大的秘密。", "characters": [{"name": "太子华", "gender": "男", "age": "青年", "clothes": "高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "矫健", "identity": "太子", "other": "神情坚定", "from_chapter": 160, "action": "将文稿递给李由、尉羽、相里飞后，站在一旁", "expression": "平静"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "李由", "gender": "男", "age": "青年", "clothes": "高领圆领汉服长袍，主色调为青色，搭配白色，束发用青色发带，腰间系白色玉佩圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "修长", "identity": "与公子华交流工地情况之人", "other": "态度诚恳", "from_chapter": 85, "action": "接过文稿，一脸疑惑地看着太子华", "expression": "疑惑"}, {"name": "尉羽", "gender": "男", "age": "青年", "clothes": "高领圆领秦国官服，主色调为深灰色，搭配浅灰色和银色，有回纹，束灰色腰带，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "健壮", "identity": "秦国官员", "other": "", "from_chapter": 142, "action": "接过文稿，一脸疑惑地看着太子华", "expression": "疑惑"}, {"name": "相里飞", "gender": "男", "age": "青年", "clothes": "高领圆领汉服短衣，主色调为灰色，搭配白色，有简单几何花纹，腰间束布带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "正常", "identity": "公子华心腹", "other": "做事认真负责", "from_chapter": 19, "action": "", "expression": ""}], "scene": "书房", "image_prompt": "中景，青年男子神情坚定且平静地将文稿递给另外三位青年男子后站在一旁，另外三位青年男子接过文稿，一脸疑惑地看着递文稿的男子。递文稿的青年男子穿着高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪，圆形衣领，黑色长发束起，身形矫健；其中一位接过文稿的青年男子穿着高领圆领汉服长袍，主色调为青色，搭配白色，束发用青色发带，腰间系白色玉佩，圆形衣领，黑色长发束起，身形修长；另一位接过文稿的青年男子穿着高领圆领秦国官服，主色调为深灰色，搭配浅灰色和银色，有回纹，束灰色腰带，头戴黑色官帽，圆形衣领，黑色束发，身形健壮。背景是书房, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753207883_20250723_021124.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_ad5f07bb-f78d-4857-bbd1-8b792f8f66e8_1.25x_20250722_211359.wav", "audio_duration": 12.904, "video_url": "", "story_board_id": 11, "subtitle": [{"subtitle_id": 1, "text": "三人接过文稿，", "timestamp": "00:00:00,240 --> 00:00:01,320", "duration": 1.08, "char_count": 7, "start_time_s": 0.24, "end_time_s": 1.32, "words": [{"attribute": {"event": "speech"}, "end_time": 360, "start_time": 240, "text": "三"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 360, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 560, "text": "接"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 720, "text": "过"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 840, "text": "文"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1040, "text": "稿"}], "keyword": "文稿"}, {"subtitle_id": 2, "text": "一脸疑惑的看着太子华，", "timestamp": "00:00:01,320 --> 00:00:03,260", "duration": 1.94, "char_count": 11, "start_time_s": 1.32, "end_time_s": 3.26, "words": [{"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1320, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1480, "text": "脸"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1640, "text": "疑"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1840, "text": "惑"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 2000, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2120, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2320, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2400, "text": "太"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2600, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 3260, "start_time": 2760, "text": "华"}], "keyword": "太子华"}, {"subtitle_id": 3, "text": "他们没想到这位平日里严肃的太子", "timestamp": "00:00:03,360 --> 00:00:06,120", "duration": 2.76, "char_count": 15, "start_time_s": 3.36, "end_time_s": 6.12, "words": [{"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3360, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3560, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3600, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3760, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3920, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4320, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4480, "text": "位"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4600, "text": "平"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4800, "text": "日"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4920, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5040, "text": "严"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5240, "text": "肃"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5360, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5480, "text": "太"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5680, "text": "子"}], "keyword": "太子"}, {"subtitle_id": 4, "text": "竟然会拿出这种闲书来给他们看。", "timestamp": "00:00:06,120 --> 00:00:08,460", "duration": 2.34, "char_count": 15, "start_time_s": 6.12, "end_time_s": 8.46, "words": [{"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 6120, "text": "竟"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6240, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6400, "text": "会"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6520, "text": "拿"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6680, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6800, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6920, "text": "种"}, {"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 7080, "text": "闲"}, {"attribute": {"event": "speech"}, "end_time": 7520, "start_time": 7360, "text": "书"}, {"attribute": {"event": "speech"}, "end_time": 7640, "start_time": 7520, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7640, "text": "给"}, {"attribute": {"event": "speech"}, "end_time": 7880, "start_time": 7760, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7880, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 8460, "start_time": 7920, "text": "看"}], "keyword": "闲书"}, {"subtitle_id": 5, "text": "但他们不知道的是，", "timestamp": "00:00:08,520 --> 00:00:09,880", "duration": 1.36, "char_count": 9, "start_time_s": 8.52, "end_time_s": 9.88, "words": [{"attribute": {"event": "speech"}, "end_time": 8720, "start_time": 8520, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 8880, "start_time": 8720, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8880, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 9200, "start_time": 9000, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 9320, "start_time": 9200, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 9440, "start_time": 9320, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 9520, "start_time": 9440, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 9880, "start_time": 9520, "text": "是"}], "keyword": "他们"}, {"subtitle_id": 6, "text": "这些文字背后藏着一个巨大的秘密。", "timestamp": "00:00:09,880 --> 00:00:12,580", "duration": 2.7, "char_count": 16, "start_time_s": 9.88, "end_time_s": 12.58, "words": [{"attribute": {"event": "speech"}, "end_time": 10080, "start_time": 9880, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 10200, "start_time": 10080, "text": "些"}, {"attribute": {"event": "speech"}, "end_time": 10360, "start_time": 10200, "text": "文"}, {"attribute": {"event": "speech"}, "end_time": 10520, "start_time": 10360, "text": "字"}, {"attribute": {"event": "speech"}, "end_time": 10680, "start_time": 10520, "text": "背"}, {"attribute": {"event": "speech"}, "end_time": 10880, "start_time": 10680, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 11160, "start_time": 10960, "text": "藏"}, {"attribute": {"event": "speech"}, "end_time": 11280, "start_time": 11160, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 11400, "start_time": 11280, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 11520, "start_time": 11400, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 11760, "start_time": 11520, "text": "巨"}, {"attribute": {"event": "speech"}, "end_time": 11920, "start_time": 11760, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 12040, "start_time": 11920, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 12120, "start_time": 12040, "text": "秘"}, {"attribute": {"event": "speech"}, "end_time": 12580, "start_time": 12120, "text": "密"}], "keyword": "秘密"}], "keywords": ["文稿", "太子华", "太子", "闲书", "他们", "秘密"]}, {"chapter": 175, "story_board": "当他们翻开文稿，立刻被其中的情节吸引住了。李由看得入迷，尉羽一边读一边低声念叨，相里飞更是忍不住拍案叫绝。太子华坐在一旁，静静地看着他们。", "characters": [{"name": "太子华", "gender": "男", "age": "青年", "clothes": "高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "矫健", "identity": "太子", "other": "神情坚定", "from_chapter": 160, "action": "将文稿递给李由、尉羽、相里飞后，站在一旁", "expression": "平静"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "李由", "gender": "男", "age": "青年", "clothes": "高领圆领汉服长袍，主色调为青色，搭配白色，束发用青色发带，腰间系白色玉佩圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "修长", "identity": "与公子华交流工地情况之人", "other": "态度诚恳", "from_chapter": 85, "action": "翻开文稿专注阅读", "expression": "入迷"}, {"name": "尉羽", "gender": "男", "age": "青年", "clothes": "高领圆领秦国官服，主色调为深灰色，搭配浅灰色和银色，有回纹，束灰色腰带，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "健壮", "identity": "秦国官员", "other": "", "from_chapter": 142, "action": "一边翻看文稿一边低声念叨", "expression": "专注"}, {"name": "相里飞", "gender": "男", "age": "青年", "clothes": "高领圆领汉服短衣，主色调为灰色，搭配白色，有简单几何花纹，腰间束布带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "正常", "identity": "公子华心腹", "other": "做事认真负责", "from_chapter": 19, "action": "翻开文稿后拍案", "expression": "惊喜、激动"}], "scene": "书房", "image_prompt": "中景，在书房里，一个翻开文稿专注阅读、态度诚恳且入迷的青年男子，旁边是一个一边翻看文稿一边低声念叨、神情专注的健壮青年男子，不远处有一个翻开文稿后拍案、满脸惊喜激动的青年男子。专注阅读的青年穿着高领圆领汉服长袍，主色调为青色，搭配白色，束发用青色发带，腰间系白色玉佩，黑色长发束起，身形修长；低声念叨的青年穿着高领圆领秦国官服，主色调为深灰色，搭配浅灰色和银色，有回纹，束灰色腰带，头戴黑色官帽，黑色束发；拍案的青年穿着高领圆领汉服短衣，主色调为灰色，搭配白色，有简单几何花纹，腰间束布带，头戴黑色布帽，黑色短发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753208079_20250723_021439.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_545442ed-cabe-4cd1-9ac9-f7b9c9ceaca8_1.25x_20250722_211340.wav", "audio_duration": 11.677333, "video_url": "", "story_board_id": 12, "subtitle": [{"subtitle_id": 1, "text": "当他们翻开文稿，", "timestamp": "00:00:00,160 --> 00:00:01,240", "duration": 1.08, "char_count": 8, "start_time_s": 0.16, "end_time_s": 1.24, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "当"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 400, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 520, "text": "翻"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 640, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 800, "text": "文"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 920, "text": "稿"}], "keyword": "文稿"}, {"subtitle_id": 2, "text": "立刻被其中的情节吸引住了，", "timestamp": "00:00:01,240 --> 00:00:03,420", "duration": 2.18, "char_count": 13, "start_time_s": 1.24, "end_time_s": 3.42, "words": [{"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1240, "text": "立"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1440, "text": "刻"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1560, "text": "被"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1720, "text": "其"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1880, "text": "中"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 2000, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2120, "text": "情"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2280, "text": "节"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2480, "text": "吸"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2640, "text": "引"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2800, "text": "住"}, {"attribute": {"event": "speech"}, "end_time": 3420, "start_time": 2920, "text": "了"}], "keyword": "情节"}, {"subtitle_id": 3, "text": "李由看得入迷，", "timestamp": "00:00:03,480 --> 00:00:04,560", "duration": 1.08, "char_count": 7, "start_time_s": 3.48, "end_time_s": 4.56, "words": [{"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3480, "text": "李"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3640, "text": "由"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3800, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3960, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4040, "text": "入"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4200, "text": "迷"}], "keyword": "李由"}, {"subtitle_id": 4, "text": "欲以一边读一边低声念叨香，", "timestamp": "00:00:04,560 --> 00:00:06,720", "duration": 2.16, "char_count": 13, "start_time_s": 4.56, "end_time_s": 6.72, "words": [{"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4560, "text": "欲"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4760, "text": "以"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4800, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 4920, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5080, "text": "读"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5320, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5480, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5640, "text": "低"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5840, "text": "声"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6000, "text": "念"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6160, "text": "叨"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6520, "text": "香"}], "keyword": "读"}, {"subtitle_id": 5, "text": "李飞更是忍不住拍案叫绝。", "timestamp": "00:00:06,720 --> 00:00:08,620", "duration": 1.9, "char_count": 12, "start_time_s": 6.72, "end_time_s": 8.62, "words": [{"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6720, "text": "李"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6800, "text": "飞"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 7000, "text": "更"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7160, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7320, "text": "忍"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7440, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7560, "text": "住"}, {"attribute": {"event": "speech"}, "end_time": 7840, "start_time": 7680, "text": "拍"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7840, "text": "案"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 7920, "text": "叫"}, {"attribute": {"event": "speech"}, "end_time": 8620, "start_time": 8120, "text": "绝"}], "keyword": "李飞"}, {"subtitle_id": 6, "text": "太子华坐在一旁静静地看着他们。", "timestamp": "00:00:08,760 --> 00:00:11,580", "duration": 2.82, "char_count": 15, "start_time_s": 8.76, "end_time_s": 11.58, "words": [{"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8760, "text": "太"}, {"attribute": {"event": "speech"}, "end_time": 9120, "start_time": 8960, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 9320, "start_time": 9120, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 9480, "start_time": 9320, "text": "坐"}, {"attribute": {"event": "speech"}, "end_time": 9600, "start_time": 9480, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 9720, "start_time": 9600, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 9920, "start_time": 9720, "text": "旁"}, {"attribute": {"event": "speech"}, "end_time": 10360, "start_time": 10200, "text": "静"}, {"attribute": {"event": "speech"}, "end_time": 10560, "start_time": 10440, "text": "静"}, {"attribute": {"event": "speech"}, "end_time": 10680, "start_time": 10560, "text": "地"}, {"attribute": {"event": "speech"}, "end_time": 10880, "start_time": 10680, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 11040, "start_time": 10920, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 11200, "start_time": 11040, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 11580, "start_time": 11200, "text": "们"}], "keyword": "太子华"}], "keywords": ["文稿", "情节", "李由", "读", "李飞", "太子华"]}, {"chapter": 175, "story_board": "他知道，自己已经迈出了关键的一步。这些小说不仅仅是娱乐，它们有可能改变这个时代的思想格局。", "characters": [{"name": "太子华", "gender": "男", "age": "青年", "clothes": "高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "矫健", "identity": "太子", "other": "神情坚定", "from_chapter": 160, "action": "坐在椅子上，双手抱臂，微微点头", "expression": "自信、坚定"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "李由", "gender": "男", "age": "青年", "clothes": "高领圆领汉服长袍，主色调为青色，搭配白色，束发用青色发带，腰间系白色玉佩圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "修长", "identity": "与公子华交流工地情况之人", "other": "态度诚恳", "from_chapter": 85, "action": "翻开文稿专注阅读", "expression": "入迷"}, {"name": "尉羽", "gender": "男", "age": "青年", "clothes": "高领圆领秦国官服，主色调为深灰色，搭配浅灰色和银色，有回纹，束灰色腰带，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "健壮", "identity": "秦国官员", "other": "", "from_chapter": 142, "action": "一边翻看文稿一边低声念叨", "expression": "专注"}, {"name": "相里飞", "gender": "男", "age": "青年", "clothes": "高领圆领汉服短衣，主色调为灰色，搭配白色，有简单几何花纹，腰间束布带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "正常", "identity": "公子华心腹", "other": "做事认真负责", "from_chapter": 19, "action": "翻开文稿后拍案", "expression": "惊喜、激动"}], "scene": "书房", "image_prompt": "中景，一个神情自信坚定、坐在椅子上双手抱臂微微点头的青年男子，身处书房之中。该男子穿着高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪，圆形衣领，黑色长发束起，身姿矫健, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753208266_20250723_021746.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_81cb61c4-ae68-436a-a33b-0e2850bf491c_1.25x_20250722_211325.wav", "audio_duration": 7.186667, "video_url": "", "story_board_id": 13, "subtitle": [{"subtitle_id": 1, "text": "他知道自己已经迈出了关键的一步。", "timestamp": "00:00:00,200 --> 00:00:02,820", "duration": 2.62, "char_count": 16, "start_time_s": 0.2, "end_time_s": 2.82, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 400, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 760, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 920, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 1000, "text": "已"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1120, "text": "经"}, {"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1280, "text": "迈"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1440, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1600, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1720, "text": "关"}, {"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1920, "text": "键"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2080, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2240, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 2820, "start_time": 2360, "text": "步"}], "keyword": "关键步"}, {"subtitle_id": 2, "text": "这些小说不仅仅是娱乐，", "timestamp": "00:00:02,960 --> 00:00:04,640", "duration": 1.68, "char_count": 11, "start_time_s": 2.96, "end_time_s": 4.64, "words": [{"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 2960, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3120, "text": "些"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3240, "text": "小"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3400, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3560, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3720, "text": "仅"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3920, "text": "仅"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4040, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4160, "text": "娱"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4280, "text": "乐"}], "keyword": "小说"}, {"subtitle_id": 3, "text": "他们", "timestamp": "00:00:04,640 --> 00:00:04,880", "duration": 0.24, "char_count": 2, "start_time_s": 4.64, "end_time_s": 4.88, "words": [{"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4640, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4800, "text": "们"}], "keyword": "他们"}, {"subtitle_id": 4, "text": "有可能改变这个时代的思想格局。", "timestamp": "00:00:04,880 --> 00:00:07,140", "duration": 2.26, "char_count": 15, "start_time_s": 4.88, "end_time_s": 7.14, "words": [{"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4880, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 5000, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5120, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5240, "text": "改"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5440, "text": "变"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5600, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5720, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5840, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 6080, "start_time": 5960, "text": "代"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6080, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6200, "text": "思"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6320, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6440, "text": "格"}, {"attribute": {"event": "speech"}, "end_time": 7140, "start_time": 6640, "text": "局"}], "keyword": "思想格局"}], "keywords": ["关键步", "小说", "他们", "思想格局"]}, {"chapter": 175, "story_board": "他并不打算让这些东西只是在宫廷中流传。他想要让更多人看到，想要让更多人感受到这些故事的魅力。", "characters": [{"name": "太子华", "gender": "男", "age": "青年", "clothes": "高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "矫健", "identity": "太子", "other": "神情坚定", "from_chapter": 160, "action": "双手抱胸，凝视远方", "expression": "坚定"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "李由", "gender": "男", "age": "青年", "clothes": "高领圆领汉服长袍，主色调为青色，搭配白色，束发用青色发带，腰间系白色玉佩圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "修长", "identity": "与公子华交流工地情况之人", "other": "态度诚恳", "from_chapter": 85, "action": "翻开文稿专注阅读", "expression": "入迷"}, {"name": "尉羽", "gender": "男", "age": "青年", "clothes": "高领圆领秦国官服，主色调为深灰色，搭配浅灰色和银色，有回纹，束灰色腰带，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "健壮", "identity": "秦国官员", "other": "", "from_chapter": 142, "action": "一边翻看文稿一边低声念叨", "expression": "专注"}, {"name": "相里飞", "gender": "男", "age": "青年", "clothes": "高领圆领汉服短衣，主色调为灰色，搭配白色，有简单几何花纹，腰间束布带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "正常", "identity": "公子华心腹", "other": "做事认真负责", "from_chapter": 19, "action": "翻开文稿后拍案", "expression": "惊喜、激动"}], "scene": "皇宫大殿", "image_prompt": "中景，双手抱胸、神情坚定地凝视远方的青年男子站在皇宫大殿中。该男子身着高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪，有着圆形衣领，黑色长发束起，身姿矫健, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753208434_20250723_022034.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_a0b078cf-38c7-4566-832b-f6b7783b162a_1.25x_20250722_211352.wav", "audio_duration": 8.048, "video_url": "", "story_board_id": 14, "subtitle": [{"subtitle_id": 1, "text": "他并不打算让这些东西", "timestamp": "00:00:00,160 --> 00:00:01,600", "duration": 1.44, "char_count": 10, "start_time_s": 0.16, "end_time_s": 1.6, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "并"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 400, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 520, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 680, "text": "算"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 800, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 920, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 1080, "text": "些"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1160, "text": "东"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1320, "text": "西"}], "keyword": "他"}, {"subtitle_id": 2, "text": "只是在宫廷中流传，", "timestamp": "00:00:01,600 --> 00:00:03,180", "duration": 1.58, "char_count": 9, "start_time_s": 1.6, "end_time_s": 3.18, "words": [{"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1600, "text": "只"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1760, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1880, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2040, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2200, "text": "廷"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2360, "text": "中"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2480, "text": "流"}, {"attribute": {"event": "speech"}, "end_time": 3180, "start_time": 2680, "text": "传"}], "keyword": "宫廷"}, {"subtitle_id": 3, "text": "他想要让更多人看到，", "timestamp": "00:00:03,280 --> 00:00:04,880", "duration": 1.6, "char_count": 10, "start_time_s": 3.28, "end_time_s": 4.88, "words": [{"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3280, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3480, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3600, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3720, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3880, "text": "更"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4080, "text": "多"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4200, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4320, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4480, "text": "到"}], "keyword": "他"}, {"subtitle_id": 4, "text": "想要让更多人感受到", "timestamp": "00:00:04,880 --> 00:00:06,360", "duration": 1.48, "char_count": 9, "start_time_s": 4.88, "end_time_s": 6.36, "words": [{"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4880, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5040, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 5320, "start_time": 5160, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5320, "text": "更"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5520, "text": "多"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5680, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5800, "text": "感"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5960, "text": "受"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6120, "text": "到"}], "keyword": "他"}, {"subtitle_id": 5, "text": "这些故事的魅力。", "timestamp": "00:00:06,360 --> 00:00:07,740", "duration": 1.38, "char_count": 8, "start_time_s": 6.36, "end_time_s": 7.74, "words": [{"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6360, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6560, "text": "些"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6680, "text": "故"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6920, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 7040, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7160, "text": "魅"}, {"attribute": {"event": "speech"}, "end_time": 7740, "start_time": 7320, "text": "力"}], "keyword": "故事"}], "keywords": ["他", "宫廷", "他", "他", "故事"]}, {"chapter": 175, "story_board": "他相信，只要有人愿意接受，这些故事就能像火种一样，在这片土地上点燃起来。但事情远没有那么简单。", "characters": [{"name": "太子华", "gender": "男", "age": "青年", "clothes": "高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "矫健", "identity": "太子", "other": "神情坚定", "from_chapter": 160, "action": "站在窗前，望着远方的星空", "expression": "充满期待又略带忧虑"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "李由", "gender": "男", "age": "青年", "clothes": "高领圆领汉服长袍，主色调为青色，搭配白色，束发用青色发带，腰间系白色玉佩圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "修长", "identity": "与公子华交流工地情况之人", "other": "态度诚恳", "from_chapter": 85, "action": "翻开文稿专注阅读", "expression": "入迷"}, {"name": "尉羽", "gender": "男", "age": "青年", "clothes": "高领圆领秦国官服，主色调为深灰色，搭配浅灰色和银色，有回纹，束灰色腰带，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "健壮", "identity": "秦国官员", "other": "", "from_chapter": 142, "action": "一边翻看文稿一边低声念叨", "expression": "专注"}, {"name": "相里飞", "gender": "男", "age": "青年", "clothes": "高领圆领汉服短衣，主色调为灰色，搭配白色，有简单几何花纹，腰间束布带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "正常", "identity": "公子华心腹", "other": "做事认真负责", "from_chapter": 19, "action": "翻开文稿后拍案", "expression": "惊喜、激动"}], "scene": "皇宫大殿", "image_prompt": "中景，站在皇宫大殿窗前，望着远方星空，神情坚定，充满期待又略带忧虑的青年男子，他身着高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪，圆形衣领，黑色长发束起，身姿矫健, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753208600_20250723_022320.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_32378d4d-9291-4a2a-85c0-4f37a2be7f4d_1.25x_20250722_211356.wav", "audio_duration": 8.066667, "video_url": "", "story_board_id": 15, "subtitle": [{"subtitle_id": 1, "text": "他相信，只要有人愿意接受，", "timestamp": "00:00:00,200 --> 00:00:02,200", "duration": 2.0, "char_count": 13, "start_time_s": 0.2, "end_time_s": 2.2, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "相"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 400, "text": "信"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 840, "text": "只"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 960, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1120, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1240, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1360, "text": "愿"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1520, "text": "意"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1680, "text": "接"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 1800, "text": "受"}], "keyword": "他"}, {"subtitle_id": 2, "text": "这些故事就能像火种一样", "timestamp": "00:00:02,200 --> 00:00:03,920", "duration": 1.72, "char_count": 11, "start_time_s": 2.2, "end_time_s": 3.92, "words": [{"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2200, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2360, "text": "些"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2480, "text": "故"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2640, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2800, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2920, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 3040, "text": "像"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3200, "text": "火"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3400, "text": "种"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3560, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3640, "text": "样"}], "keyword": "故事"}, {"subtitle_id": 3, "text": "在这片土地上点燃起来。", "timestamp": "00:00:03,920 --> 00:00:05,780", "duration": 1.86, "char_count": 11, "start_time_s": 3.92, "end_time_s": 5.78, "words": [{"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3920, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4080, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4200, "text": "片"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4320, "text": "土"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4520, "text": "地"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4680, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4800, "text": "点"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5000, "text": "燃"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5200, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 5780, "start_time": 5360, "text": "来"}], "keyword": "土地"}, {"subtitle_id": 4, "text": "但事情远没有那么简单。", "timestamp": "00:00:05,880 --> 00:00:07,700", "duration": 1.82, "char_count": 11, "start_time_s": 5.88, "end_time_s": 7.7, "words": [{"attribute": {"event": "speech"}, "end_time": 6080, "start_time": 5880, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 6080, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6240, "text": "情"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6360, "text": "远"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6560, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6720, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6840, "text": "那"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6920, "text": "么"}, {"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 7040, "text": "简"}, {"attribute": {"event": "speech"}, "end_time": 7700, "start_time": 7200, "text": "单"}], "keyword": "事情"}], "keywords": ["他", "故事", "土地", "事情"]}, {"chapter": 175, "story_board": "他清楚，自己这么做可能会引来麻烦。毕竟在这个时代，思想自由是不被允许的。", "characters": [{"name": "太子华", "gender": "男", "age": "青年", "clothes": "高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "矫健", "identity": "太子", "other": "神情坚定", "from_chapter": 160, "action": "站在窗前，望着远方的星空", "expression": "若有所思、坚定"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "李由", "gender": "男", "age": "青年", "clothes": "高领圆领汉服长袍，主色调为青色，搭配白色，束发用青色发带，腰间系白色玉佩圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "修长", "identity": "与公子华交流工地情况之人", "other": "态度诚恳", "from_chapter": 85, "action": "翻开文稿专注阅读", "expression": "入迷"}, {"name": "尉羽", "gender": "男", "age": "青年", "clothes": "高领圆领秦国官服，主色调为深灰色，搭配浅灰色和银色，有回纹，束灰色腰带，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "健壮", "identity": "秦国官员", "other": "", "from_chapter": 142, "action": "一边翻看文稿一边低声念叨", "expression": "专注"}, {"name": "相里飞", "gender": "男", "age": "青年", "clothes": "高领圆领汉服短衣，主色调为灰色，搭配白色，有简单几何花纹，腰间束布带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "正常", "identity": "公子华心腹", "other": "做事认真负责", "from_chapter": 19, "action": "翻开文稿后拍案", "expression": "惊喜、激动"}], "scene": "皇宫大殿", "image_prompt": "中景，站在皇宫大殿窗前、神情坚定且若有所思地望着远方星空的青年男子，他身姿矫健，黑色长发束起，身着高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪，战甲为圆形衣领, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753208715_20250723_022515.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_5826176a-55ad-4ddf-99ce-a82714555acc_1.25x_20250722_211329.wav", "audio_duration": 6.32, "video_url": "", "story_board_id": 16, "subtitle": [{"subtitle_id": 1, "text": "他清楚自己这么做可能会引来麻烦，", "timestamp": "00:00:00,160 --> 00:00:02,900", "duration": 2.74, "char_count": 16, "start_time_s": 0.16, "end_time_s": 2.9, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "清"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 440, "text": "楚"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 760, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 920, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1080, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1200, "text": "么"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1320, "text": "做"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1560, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1720, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1840, "text": "会"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2040, "text": "引"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2160, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2280, "text": "麻"}, {"attribute": {"event": "speech"}, "end_time": 2900, "start_time": 2480, "text": "烦"}], "keyword": "他"}, {"subtitle_id": 2, "text": "毕竟在这个时代，", "timestamp": "00:00:03,040 --> 00:00:04,240", "duration": 1.2, "char_count": 8, "start_time_s": 3.04, "end_time_s": 4.24, "words": [{"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3040, "text": "毕"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3200, "text": "竟"}, {"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3360, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3520, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3640, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3720, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 3880, "text": "代"}], "keyword": "时代"}, {"subtitle_id": 3, "text": "思想自由是不被允许的。", "timestamp": "00:00:04,240 --> 00:00:06,260", "duration": 2.02, "char_count": 11, "start_time_s": 4.24, "end_time_s": 6.26, "words": [{"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4240, "text": "思"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4400, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4560, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4640, "text": "由"}, {"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4760, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5000, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5240, "text": "被"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5400, "text": "允"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5640, "text": "许"}, {"attribute": {"event": "speech"}, "end_time": 6260, "start_time": 5800, "text": "的"}], "keyword": "思想自由"}], "keywords": ["他", "时代", "思想自由"]}, {"chapter": 175, "story_board": "但他也知道，如果不去尝试，那就永远不会有改变。夜深了，东宫的灯火重新亮起。", "characters": [{"name": "太子华", "gender": "男", "age": "青年", "clothes": "高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "矫健", "identity": "太子", "other": "神情坚定", "from_chapter": 160, "action": "站在窗前，望着远方的星空", "expression": "充满期待"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "李由", "gender": "男", "age": "青年", "clothes": "高领圆领汉服长袍，主色调为青色，搭配白色，束发用青色发带，腰间系白色玉佩圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "修长", "identity": "与公子华交流工地情况之人", "other": "态度诚恳", "from_chapter": 85, "action": "翻开文稿专注阅读", "expression": "入迷"}, {"name": "尉羽", "gender": "男", "age": "青年", "clothes": "高领圆领秦国官服，主色调为深灰色，搭配浅灰色和银色，有回纹，束灰色腰带，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "健壮", "identity": "秦国官员", "other": "", "from_chapter": 142, "action": "一边翻看文稿一边低声念叨", "expression": "专注"}, {"name": "相里飞", "gender": "男", "age": "青年", "clothes": "高领圆领汉服短衣，主色调为灰色，搭配白色，有简单几何花纹，腰间束布带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "正常", "identity": "公子华心腹", "other": "做事认真负责", "from_chapter": 19, "action": "翻开文稿后拍案", "expression": "惊喜、激动"}], "scene": "中国古代建筑", "image_prompt": "中景，站在窗前望着远方星空、神情坚定且充满期待的青年男子，身着高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪，圆形衣领，黑色长发束起，身姿矫健，背景是中国古代建筑, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753208824_20250723_022704.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_11d216b1-234d-4fcc-91eb-983f648c91ff_1.25x_20250722_211337.wav", "audio_duration": 6.84, "video_url": "", "story_board_id": 17, "subtitle": [{"subtitle_id": 1, "text": "但他也知道，", "timestamp": "00:00:00,200 --> 00:00:01,000", "duration": 0.8, "char_count": 6, "start_time_s": 0.2, "end_time_s": 1.0, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 440, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 560, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 720, "text": "道"}], "keyword": "他"}, {"subtitle_id": 2, "text": "如果不去尝试，", "timestamp": "00:00:01,000 --> 00:00:02,120", "duration": 1.12, "char_count": 7, "start_time_s": 1.0, "end_time_s": 2.12, "words": [{"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 1000, "text": "如"}, {"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 1160, "text": "果"}, {"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1280, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1440, "text": "去"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1560, "text": "尝"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1800, "text": "试"}], "keyword": "尝试"}, {"subtitle_id": 3, "text": "那就永远不会有改变。", "timestamp": "00:00:02,120 --> 00:00:03,940", "duration": 1.82, "char_count": 10, "start_time_s": 2.12, "end_time_s": 3.94, "words": [{"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2120, "text": "那"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2280, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2440, "text": "永"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2640, "text": "远"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2840, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 3000, "text": "会"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3120, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3240, "text": "改"}, {"attribute": {"event": "speech"}, "end_time": 3940, "start_time": 3440, "text": "变"}], "keyword": "改变"}, {"subtitle_id": 4, "text": "夜深了，东宫的灯火重新亮起。", "timestamp": "00:00:04,040 --> 00:00:06,780", "duration": 2.74, "char_count": 14, "start_time_s": 4.04, "end_time_s": 6.78, "words": [{"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4040, "text": "夜"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4280, "text": "深"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4400, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4760, "text": "东"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 4960, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5120, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5240, "text": "灯"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5440, "text": "火"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5680, "text": "重"}, {"attribute": {"event": "speech"}, "end_time": 6080, "start_time": 5920, "text": "新"}, {"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 6080, "text": "亮"}, {"attribute": {"event": "speech"}, "end_time": 6780, "start_time": 6280, "text": "起"}], "keyword": "东宫"}], "keywords": ["他", "尝试", "改变", "东宫"]}, {"chapter": 175, "story_board": "太子华站在窗前，望着远方的星空，心中充满期待。他知道，自己正在做一件前所未有的事。", "characters": [{"name": "太子华", "gender": "男", "age": "青年", "clothes": "高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "矫健", "identity": "太子", "other": "神情坚定", "from_chapter": 160, "action": "站在窗前，望着远方的星空", "expression": "充满期待"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "李由", "gender": "男", "age": "青年", "clothes": "高领圆领汉服长袍，主色调为青色，搭配白色，束发用青色发带，腰间系白色玉佩圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "修长", "identity": "与公子华交流工地情况之人", "other": "态度诚恳", "from_chapter": 85, "action": "翻开文稿专注阅读", "expression": "入迷"}, {"name": "尉羽", "gender": "男", "age": "青年", "clothes": "高领圆领秦国官服，主色调为深灰色，搭配浅灰色和银色，有回纹，束灰色腰带，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "健壮", "identity": "秦国官员", "other": "", "from_chapter": 142, "action": "一边翻看文稿一边低声念叨", "expression": "专注"}, {"name": "相里飞", "gender": "男", "age": "青年", "clothes": "高领圆领汉服短衣，主色调为灰色，搭配白色，有简单几何花纹，腰间束布带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "正常", "identity": "公子华心腹", "other": "做事认真负责", "from_chapter": 19, "action": "翻开文稿后拍案", "expression": "惊喜、激动"}], "scene": "皇宫大殿", "image_prompt": "中景，一个神情坚定、充满期待的青年男子站在皇宫大殿的窗前，望着远方的星空。他身姿矫健，束起黑色长发，头戴盔缨，腰束皮带，手持长枪，身着高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，战甲配有圆形衣领, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753208918_20250723_022838.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_89240c27-1e49-41cc-9bfe-326670241f36_1.25x_20250722_211348.wav", "audio_duration": 7.490667, "video_url": "", "story_board_id": 18, "subtitle": [{"subtitle_id": 1, "text": "太子华站在窗前，", "timestamp": "00:00:00,200 --> 00:00:01,560", "duration": 1.36, "char_count": 8, "start_time_s": 0.2, "end_time_s": 1.56, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 200, "text": "太"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 320, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 480, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 680, "text": "站"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 800, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 960, "text": "窗"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1160, "text": "前"}], "keyword": "太子华"}, {"subtitle_id": 2, "text": "望着远方的星空，", "timestamp": "00:00:01,560 --> 00:00:02,840", "duration": 1.28, "char_count": 8, "start_time_s": 1.56, "end_time_s": 2.84, "words": [{"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1560, "text": "望"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1760, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1880, "text": "远"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2080, "text": "方"}, {"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2200, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2320, "text": "星"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2480, "text": "空"}], "keyword": "星空"}, {"subtitle_id": 3, "text": "心中充满期待，", "timestamp": "00:00:02,840 --> 00:00:04,220", "duration": 1.38, "char_count": 7, "start_time_s": 2.84, "end_time_s": 4.22, "words": [{"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2840, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 3000, "text": "中"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3160, "text": "充"}, {"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3400, "text": "满"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3560, "text": "期"}, {"attribute": {"event": "speech"}, "end_time": 4220, "start_time": 3760, "text": "待"}], "keyword": "期待"}, {"subtitle_id": 4, "text": "他知道", "timestamp": "00:00:04,320 --> 00:00:04,920", "duration": 0.6, "char_count": 3, "start_time_s": 4.32, "end_time_s": 4.92, "words": [{"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4320, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4480, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4600, "text": "道"}], "keyword": "自己"}, {"subtitle_id": 5, "text": "自己正在做一件前所未有的事。", "timestamp": "00:00:04,920 --> 00:00:07,220", "duration": 2.3, "char_count": 14, "start_time_s": 4.92, "end_time_s": 7.22, "words": [{"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 4920, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5080, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5200, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5360, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5520, "text": "做"}, {"attribute": {"event": "speech"}, "end_time": 5760, "start_time": 5640, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5760, "text": "件"}, {"attribute": {"event": "speech"}, "end_time": 6080, "start_time": 5920, "text": "前"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6160, "text": "所"}, {"attribute": {"event": "speech"}, "end_time": 6480, "start_time": 6320, "text": "未"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6480, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6600, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7220, "start_time": 6720, "text": "事"}], "keyword": "事情"}], "keywords": ["太子华", "星空", "期待", "自己", "事情"]}, {"chapter": 175, "story_board": "也许未来会有无数人质疑他的选择，但至少，他已经在路上了。这是一个关于梦想、勇气和坚持的故事。", "characters": [{"name": "太子华", "gender": "男", "age": "青年", "clothes": "高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "矫健", "identity": "太子", "other": "神情坚定", "from_chapter": 160, "action": "站在窗前，望着远方的星空", "expression": "充满期待"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "李由", "gender": "男", "age": "青年", "clothes": "高领圆领汉服长袍，主色调为青色，搭配白色，束发用青色发带，腰间系白色玉佩圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "修长", "identity": "与公子华交流工地情况之人", "other": "态度诚恳", "from_chapter": 85, "action": "翻开文稿专注阅读", "expression": "入迷"}, {"name": "尉羽", "gender": "男", "age": "青年", "clothes": "高领圆领秦国官服，主色调为深灰色，搭配浅灰色和银色，有回纹，束灰色腰带，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "健壮", "identity": "秦国官员", "other": "", "from_chapter": 142, "action": "一边翻看文稿一边低声念叨", "expression": "专注"}, {"name": "相里飞", "gender": "男", "age": "青年", "clothes": "高领圆领汉服短衣，主色调为灰色，搭配白色，有简单几何花纹，腰间束布带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "正常", "identity": "公子华心腹", "other": "做事认真负责", "from_chapter": 19, "action": "翻开文稿后拍案", "expression": "惊喜、激动"}], "scene": "皇宫大殿", "image_prompt": "中景，站在皇宫大殿窗前，神情坚定、充满期待地望着远方星空的青年男子，身着高圆领、主色调为银色并搭配红色、刻有云纹的古代战甲，头戴盔缨，腰束皮带，配有长枪，圆形衣领，黑色长发束起，身姿矫健, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753208981_20250723_022941.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_42615e6d-b69f-45e4-907e-57f44e70a8c0_1.25x_20250722_211344.wav", "audio_duration": 7.936, "video_url": "", "story_board_id": 19, "subtitle": [{"subtitle_id": 1, "text": "也许未来会有无数人质疑他的选择，", "timestamp": "00:00:00,200 --> 00:00:02,600", "duration": 2.4, "char_count": 16, "start_time_s": 0.2, "end_time_s": 2.6, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 200, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "许"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 440, "text": "未"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 560, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 880, "text": "会"}, {"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 1040, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1160, "text": "无"}, {"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1320, "text": "数"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1440, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1640, "text": "质"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1760, "text": "疑"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1840, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 2000, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2080, "text": "选"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2240, "text": "择"}], "keyword": "他"}, {"subtitle_id": 2, "text": "但至少他已经在路上了。", "timestamp": "00:00:02,600 --> 00:00:04,740", "duration": 2.14, "char_count": 11, "start_time_s": 2.6, "end_time_s": 4.74, "words": [{"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2600, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2760, "text": "至"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 2960, "text": "少"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3320, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3480, "text": "已"}, {"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3640, "text": "经"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3800, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 3960, "text": "路"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4160, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 4740, "start_time": 4280, "text": "了"}], "keyword": "路"}, {"subtitle_id": 3, "text": "这是一个关于梦想、", "timestamp": "00:00:04,800 --> 00:00:06,040", "duration": 1.24, "char_count": 9, "start_time_s": 4.8, "end_time_s": 6.04, "words": [{"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4800, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 5000, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5120, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5240, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5480, "text": "关"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5600, "text": "于"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5720, "text": "梦"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5880, "text": "想"}], "keyword": "梦想"}, {"subtitle_id": 4, "text": "勇气和坚持的故事。", "timestamp": "00:00:06,040 --> 00:00:07,620", "duration": 1.58, "char_count": 9, "start_time_s": 6.04, "end_time_s": 7.62, "words": [{"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6040, "text": "勇"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6240, "text": "气"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6400, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6560, "text": "坚"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6760, "text": "持"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6960, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 7040, "text": "故"}, {"attribute": {"event": "speech"}, "end_time": 7620, "start_time": 7200, "text": "事"}], "keyword": "勇气"}], "keywords": ["他", "路", "梦想", "勇气"]}, {"chapter": 175, "story_board": "一个穿越者，用文字和音乐，在一个封闭的时代里，开辟出一条属于自己的道路。他不是为了权力，也不是为了名声，而是因为他相信，有些东西，值得被传承下去。", "characters": [{"name": "太子华", "gender": "男", "age": "青年", "clothes": "高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "矫健", "identity": "太子", "other": "神情坚定", "from_chapter": 160, "action": "站在窗前，望着远方的星空", "expression": "充满期待"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "李由", "gender": "男", "age": "青年", "clothes": "高领圆领汉服长袍，主色调为青色，搭配白色，束发用青色发带，腰间系白色玉佩圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "修长", "identity": "与公子华交流工地情况之人", "other": "态度诚恳", "from_chapter": 85, "action": "翻开文稿专注阅读", "expression": "入迷"}, {"name": "尉羽", "gender": "男", "age": "青年", "clothes": "高领圆领秦国官服，主色调为深灰色，搭配浅灰色和银色，有回纹，束灰色腰带，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "健壮", "identity": "秦国官员", "other": "", "from_chapter": 142, "action": "一边翻看文稿一边低声念叨", "expression": "专注"}, {"name": "相里飞", "gender": "男", "age": "青年", "clothes": "高领圆领汉服短衣，主色调为灰色，搭配白色，有简单几何花纹，腰间束布带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "正常", "identity": "公子华心腹", "other": "做事认真负责", "from_chapter": 19, "action": "翻开文稿后拍案", "expression": "惊喜、激动"}], "scene": "皇宫大殿", "image_prompt": "中景，站在皇宫大殿窗前、神情坚定且充满期待地望着远方星空的青年男子，身穿高圆领古代战甲，主色调为银色，搭配红色，甲上刻有云纹，头戴盔缨，腰束皮带，配有长枪，圆形衣领，黑色长发束起，身形矫健, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753209000_20250723_023000.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_fd044a7c-a185-4b81-b35f-bdab4761868b_1.25x_20250722_211333.wav", "audio_duration": 11.621333, "video_url": "", "story_board_id": 20, "subtitle": [{"subtitle_id": 1, "text": "一个穿越者，", "timestamp": "00:00:00,200 --> 00:00:01,040", "duration": 0.84, "char_count": 6, "start_time_s": 0.2, "end_time_s": 1.04, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 400, "text": "穿"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 560, "text": "越"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 680, "text": "者"}], "keyword": "穿越者"}, {"subtitle_id": 2, "text": "用文字和音乐，", "timestamp": "00:00:01,040 --> 00:00:02,160", "duration": 1.12, "char_count": 7, "start_time_s": 1.04, "end_time_s": 2.16, "words": [{"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1040, "text": "用"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1200, "text": "文"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1400, "text": "字"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1560, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1680, "text": "音"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 1800, "text": "乐"}], "keyword": "文字音乐"}, {"subtitle_id": 3, "text": "在一个封闭的时代里，", "timestamp": "00:00:02,160 --> 00:00:03,560", "duration": 1.4, "char_count": 10, "start_time_s": 2.16, "end_time_s": 3.56, "words": [{"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2160, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2320, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2440, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2560, "text": "封"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2760, "text": "闭"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2880, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 3040, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3160, "text": "代"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3280, "text": "里"}], "keyword": "封闭时代"}, {"subtitle_id": 4, "text": "开辟出一条属于自己的道路。", "timestamp": "00:00:03,560 --> 00:00:05,740", "duration": 2.18, "char_count": 13, "start_time_s": 3.56, "end_time_s": 5.74, "words": [{"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3560, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3760, "text": "辟"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3880, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 4040, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4120, "text": "条"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4400, "text": "属"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4520, "text": "于"}, {"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4640, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4840, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 5000, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5080, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 5740, "start_time": 5280, "text": "路"}], "keyword": "开辟道路"}, {"subtitle_id": 5, "text": "他不是为了权利，", "timestamp": "00:00:05,800 --> 00:00:06,920", "duration": 1.12, "char_count": 8, "start_time_s": 5.8, "end_time_s": 6.92, "words": [{"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5800, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5960, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6160, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6280, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6400, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6520, "text": "权"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6720, "text": "利"}], "keyword": "权利"}, {"subtitle_id": 6, "text": "也不是为了名声，", "timestamp": "00:00:06,920 --> 00:00:08,160", "duration": 1.24, "char_count": 8, "start_time_s": 6.92, "end_time_s": 8.16, "words": [{"attribute": {"event": "speech"}, "end_time": 7120, "start_time": 6920, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 7120, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7280, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7440, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 7640, "start_time": 7560, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7640, "text": "名"}, {"attribute": {"event": "speech"}, "end_time": 8160, "start_time": 7800, "text": "声"}], "keyword": "名声"}, {"subtitle_id": 7, "text": "而是因为他相信", "timestamp": "00:00:08,160 --> 00:00:09,240", "duration": 1.08, "char_count": 7, "start_time_s": 8.16, "end_time_s": 9.24, "words": [{"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8160, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 8440, "start_time": 8360, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 8560, "start_time": 8440, "text": "因"}, {"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8560, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8680, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8800, "text": "相"}, {"attribute": {"event": "speech"}, "end_time": 9240, "start_time": 9000, "text": "信"}], "keyword": "相信"}, {"subtitle_id": 8, "text": "有些东西值得被传承下去。", "timestamp": "00:00:09,240 --> 00:00:11,580", "duration": 2.34, "char_count": 12, "start_time_s": 9.24, "end_time_s": 11.58, "words": [{"attribute": {"event": "speech"}, "end_time": 9440, "start_time": 9240, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 9560, "start_time": 9440, "text": "些"}, {"attribute": {"event": "speech"}, "end_time": 9680, "start_time": 9560, "text": "东"}, {"attribute": {"event": "speech"}, "end_time": 9880, "start_time": 9680, "text": "西"}, {"attribute": {"event": "speech"}, "end_time": 10200, "start_time": 10000, "text": "值"}, {"attribute": {"event": "speech"}, "end_time": 10360, "start_time": 10200, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 10480, "start_time": 10360, "text": "被"}, {"attribute": {"event": "speech"}, "end_time": 10720, "start_time": 10520, "text": "传"}, {"attribute": {"event": "speech"}, "end_time": 10880, "start_time": 10720, "text": "承"}, {"attribute": {"event": "speech"}, "end_time": 11080, "start_time": 10920, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 11580, "start_time": 11080, "text": "去"}], "keyword": "传承"}], "keywords": ["穿越者", "文字音乐", "封闭时代", "开辟道路", "权利", "名声", "相信", "传承"]}]