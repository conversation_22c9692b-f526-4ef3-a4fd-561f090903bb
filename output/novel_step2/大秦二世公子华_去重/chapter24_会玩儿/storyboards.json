[{"chapter": 24, "story_board": "夜色沉沉，一场突如其来的冲突在朝堂上爆发。有人怒火中烧，有人心如死灰，有人却笑得合不拢嘴。这不是普通的早朝，而是一场暗流涌动的博弈。", "characters": [{"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "坐在龙椅上，表情严肃地看着朝堂上的众人", "expression": "威严"}, {"name": "楚国代表熊犹", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为暗红色，搭配银色，袖口绣银色楚国云纹，头戴黑色方巾，腰间束腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "楚国代表", "other": "眼神狡黠，善于周旋", "from_chapter": [0], "action": "站在朝堂中，眉头紧皱，满脸怒色", "expression": "愤怒"}, {"name": "左丞相昌平君", "gender": "男", "age": "中年", "clothes": "高领圆领汉服丞相朝服，主色调为紫色，搭配黑色，有黑色暗纹，头戴黑色丞相帽，配朝珠圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色丞相帽", "figure": "清瘦", "identity": "秦国左丞相", "other": "眉头常皱，面带忧虑", "from_chapter": [0], "action": "低头沉思，偶尔抬头观察众人反应，神情落寞", "expression": "心如死灰"}, {"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "", "expression": ""}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "李信", "gender": "男", "age": "青年", "clothes": "高领圆领古代战甲，主色调为银色，搭配金色、白色，有金色花纹点缀，外披白色披风，头戴银色头盔，腰系战带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴银色头盔", "figure": "矫健", "identity": "秦国将领", "other": "眼神锐利，充满自信", "from_chapter": [0], "action": "", "expression": ""}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "冯艺", "gender": "男", "age": "青年", "clothes": "高领圆领汉服灰色长袍，主色调为灰色，袍上绣灰色花纹，腰间束一条灰色腰带，脚蹬黑色布鞋，头戴灰色毡帽圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴灰色毡帽", "figure": "中等身材", "identity": "公子华的下属，负责工匠安排", "other": "", "from_chapter": 100, "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿里，坐在龙椅上表情严肃且威严地看着朝堂众人的中年男子，他魁梧且决策果断，身穿主色调为黑色、搭配金色、绣满金色龙纹的高领圆领汉服帝王龙袍，腰间束黑色镶金边腰带，头戴垂有珠玉串饰的冕旒，黑色束发；站在朝堂中眉头紧皱、满脸怒色的中年男子，中等身材、眼神狡黠，身穿主色调为暗红色、搭配银色、袖口绣银色楚国云纹的高领圆领汉服长袍，腰间束腰带，头戴黑色方巾，黑色长发束起；低头沉思、偶尔抬头观察众人反应、神情落寞且心如死灰的清瘦中年男子，眉头常皱、面带忧虑，身穿主色调为紫色、搭配黑色、有黑色暗纹的高领圆领汉服丞相朝服，头戴黑色丞相帽，配朝珠，黑色长发束起, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241077_20250723_112438.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_a6227f68-0100-414b-9267-dad4c784f0d6_1.25x_20250723_105715.wav", "audio_duration": 11.925333, "video_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/videos/video_1753241105_20250723_112506.mp4", "story_board_id": 1, "subtitle": [{"subtitle_id": 1, "text": "夜色沉沉，", "timestamp": "00:00:00,200 --> 00:00:01,040", "duration": 0.84, "char_count": 5, "start_time_s": 0.2, "end_time_s": 1.04, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 200, "text": "夜"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "色"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 440, "text": "沉"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 480, "text": "沉"}], "keyword": "夜色"}, {"subtitle_id": 2, "text": "一场突如其来的冲突在朝堂上爆发，", "timestamp": "00:00:01,040 --> 00:00:03,940", "duration": 2.9, "char_count": 16, "start_time_s": 1.04, "end_time_s": 3.94, "words": [{"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1040, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1240, "text": "场"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1440, "text": "突"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1600, "text": "如"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1720, "text": "其"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1880, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 2000, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2120, "text": "冲"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2280, "text": "突"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2560, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2720, "text": "朝"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 2960, "text": "堂"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3120, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3240, "text": "爆"}, {"attribute": {"event": "speech"}, "end_time": 3940, "start_time": 3440, "text": "发"}], "keyword": "冲突"}, {"subtitle_id": 3, "text": "有人怒火中烧，", "timestamp": "00:00:04,040 --> 00:00:05,160", "duration": 1.12, "char_count": 7, "start_time_s": 4.04, "end_time_s": 5.16, "words": [{"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4040, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4200, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4360, "text": "怒"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4480, "text": "火"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4640, "text": "中"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 4800, "text": "烧"}], "keyword": "怒火"}, {"subtitle_id": 4, "text": "有人心如死灰，", "timestamp": "00:00:05,160 --> 00:00:06,400", "duration": 1.24, "char_count": 7, "start_time_s": 5.16, "end_time_s": 6.4, "words": [{"attribute": {"event": "speech"}, "end_time": 5320, "start_time": 5160, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5320, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5480, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5680, "text": "如"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5800, "text": "死"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6040, "text": "灰"}], "keyword": "心灰"}, {"subtitle_id": 5, "text": "有人却笑得合不拢嘴。", "timestamp": "00:00:06,400 --> 00:00:08,140", "duration": 1.74, "char_count": 10, "start_time_s": 6.4, "end_time_s": 8.14, "words": [{"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6400, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6560, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6720, "text": "却"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6880, "text": "笑"}, {"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 7080, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7200, "text": "合"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7400, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 7640, "start_time": 7480, "text": "拢"}, {"attribute": {"event": "speech"}, "end_time": 8140, "start_time": 7640, "text": "嘴"}], "keyword": "笑脸"}, {"subtitle_id": 6, "text": "这不是普通的早朝，", "timestamp": "00:00:08,240 --> 00:00:09,760", "duration": 1.52, "char_count": 9, "start_time_s": 8.24, "end_time_s": 9.76, "words": [{"attribute": {"event": "speech"}, "end_time": 8440, "start_time": 8240, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8440, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 8760, "start_time": 8640, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8760, "text": "普"}, {"attribute": {"event": "speech"}, "end_time": 9080, "start_time": 8960, "text": "通"}, {"attribute": {"event": "speech"}, "end_time": 9200, "start_time": 9080, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 9360, "start_time": 9200, "text": "早"}, {"attribute": {"event": "speech"}, "end_time": 9760, "start_time": 9400, "text": "朝"}], "keyword": "早朝"}, {"subtitle_id": 7, "text": "而是一场暗流涌动的博弈。", "timestamp": "00:00:09,760 --> 00:00:11,860", "duration": 2.1, "char_count": 12, "start_time_s": 9.76, "end_time_s": 11.86, "words": [{"attribute": {"event": "speech"}, "end_time": 10000, "start_time": 9760, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 10080, "start_time": 10000, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 10200, "start_time": 10080, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 10360, "start_time": 10200, "text": "场"}, {"attribute": {"event": "speech"}, "end_time": 10560, "start_time": 10400, "text": "暗"}, {"attribute": {"event": "speech"}, "end_time": 10760, "start_time": 10560, "text": "流"}, {"attribute": {"event": "speech"}, "end_time": 10920, "start_time": 10760, "text": "涌"}, {"attribute": {"event": "speech"}, "end_time": 11120, "start_time": 10920, "text": "动"}, {"attribute": {"event": "speech"}, "end_time": 11240, "start_time": 11120, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 11400, "start_time": 11240, "text": "博"}, {"attribute": {"event": "speech"}, "end_time": 11860, "start_time": 11400, "text": "弈"}], "keyword": "博弈"}], "keywords": ["夜色", "冲突", "怒火", "心灰", "笑脸", "早朝", "博弈"]}, {"chapter": 24, "story_board": "秦国大王赢政一声令下，楚国代表熊犹交出了15座城池，可谁来接管？谁来坐镇南郡？众人争得面红耳赤，最终定下左丞相昌平君和李信两人负责。", "characters": [{"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "坐在王座上，威严地发出指令", "expression": "严肃"}, {"name": "楚国代表熊犹", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为暗红色，搭配银色，袖口绣银色楚国云纹，头戴黑色方巾，腰间束腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "楚国代表", "other": "眼神狡黠，善于周旋", "from_chapter": [0], "action": "无奈地低下头，双手呈上城池的交接文书", "expression": "沮丧"}, {"name": "左丞相昌平君", "gender": "男", "age": "中年", "clothes": "高领圆领汉服丞相朝服，主色调为紫色，搭配黑色，有黑色暗纹，头戴黑色丞相帽，配朝珠圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色丞相帽", "figure": "清瘦", "identity": "秦国左丞相", "other": "眉头常皱，面带忧虑", "from_chapter": [0], "action": "站在人群中，听着众人争论，微微皱眉", "expression": "忧虑"}, {"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "", "expression": ""}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "李信", "gender": "男", "age": "青年", "clothes": "高领圆领古代战甲，主色调为银色，搭配金色、白色，有金色花纹点缀，外披白色披风，头戴银色头盔，腰系战带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴银色头盔", "figure": "矫健", "identity": "秦国将领", "other": "眼神锐利，充满自信", "from_chapter": [0], "action": "", "expression": ""}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "冯艺", "gender": "男", "age": "青年", "clothes": "高领圆领汉服灰色长袍，主色调为灰色，袍上绣灰色花纹，腰间束一条灰色腰带，脚蹬黑色布鞋，头戴灰色毡帽圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴灰色毡帽", "figure": "中等身材", "identity": "公子华的下属，负责工匠安排", "other": "", "from_chapter": 100, "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿的背景下，坐在王座上严肃且威严地发出指令的中年男子，他身材魁梧，身着主色调为黑色、搭配金色、绣满金色龙纹的高领圆领汉服帝王龙袍，腰间束黑色镶金边腰带，头戴垂有珠玉串饰的冕旒，黑色头发束起；旁边无奈地低下头、双手呈上城池交接文书的中年男子，他中等身材，神情沮丧，身着主色调为暗红色、搭配银色、袖口绣有银色楚国云纹的高领圆领汉服长袍，腰间束腰带，头戴黑色方巾，黑色长发束起；还有站在人群中微微皱眉、面带忧虑的中年男子，他清瘦，身着主色调为紫色、搭配黑色、有黑色暗纹的高领圆领汉服丞相朝服，头戴黑色丞相帽，配朝珠，黑色长发束起, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241126_20250723_112527.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_a4aa6672-1fb3-4049-8f29-c9da15686747_1.25x_20250723_105728.wav", "audio_duration": 11.429333, "video_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/videos/video_1753241170_20250723_112611.mp4", "story_board_id": 2, "subtitle": [{"subtitle_id": 1, "text": "秦国大王嬴政一声令下，", "timestamp": "00:00:00,200 --> 00:00:01,880", "duration": 1.68, "char_count": 11, "start_time_s": 0.2, "end_time_s": 1.88, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 200, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 440, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 600, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 760, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 960, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1120, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1240, "text": "声"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1360, "text": "令"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1520, "text": "下"}], "keyword": "嬴政"}, {"subtitle_id": 2, "text": "楚国代表雄游交出了座城池，", "timestamp": "00:00:01,880 --> 00:00:04,040", "duration": 2.16, "char_count": 13, "start_time_s": 1.88, "end_time_s": 4.04, "words": [{"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1880, "text": "楚"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2080, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2200, "text": "代"}, {"attribute": {"event": "speech"}, "end_time": 2520, "start_time": 2360, "text": "表"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2520, "text": "雄"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2680, "text": "游"}, {"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 2960, "text": "交"}, {"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3160, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3320, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3400, "text": "座"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3520, "text": "城"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3720, "text": "池"}], "keyword": "雄游"}, {"subtitle_id": 3, "text": "可谁来接管，", "timestamp": "00:00:04,040 --> 00:00:05,100", "duration": 1.06, "char_count": 6, "start_time_s": 4.04, "end_time_s": 5.1, "words": [{"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4040, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4240, "text": "谁"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4360, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4520, "text": "接"}, {"attribute": {"event": "speech"}, "end_time": 5100, "start_time": 4680, "text": "管"}], "keyword": "接管"}, {"subtitle_id": 4, "text": "谁来坐镇南郡？", "timestamp": "00:00:05,320 --> 00:00:06,700", "duration": 1.38, "char_count": 7, "start_time_s": 5.32, "end_time_s": 6.7, "words": [{"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5320, "text": "谁"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5520, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5720, "text": "坐"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5920, "text": "镇"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6040, "text": "南"}, {"attribute": {"event": "speech"}, "end_time": 6700, "start_time": 6240, "text": "郡"}], "keyword": "坐镇"}, {"subtitle_id": 5, "text": "众人争得面红耳赤，", "timestamp": "00:00:06,800 --> 00:00:08,240", "duration": 1.44, "char_count": 9, "start_time_s": 6.8, "end_time_s": 8.24, "words": [{"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6800, "text": "众"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 7000, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 7160, "text": "争"}, {"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7280, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7440, "text": "面"}, {"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7600, "text": "红"}, {"attribute": {"event": "speech"}, "end_time": 7840, "start_time": 7720, "text": "耳"}, {"attribute": {"event": "speech"}, "end_time": 8240, "start_time": 7840, "text": "赤"}], "keyword": "争执"}, {"subtitle_id": 6, "text": "最终", "timestamp": "00:00:08,240 --> 00:00:08,520", "duration": 0.28, "char_count": 2, "start_time_s": 8.24, "end_time_s": 8.52, "words": [{"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8240, "text": "最"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8400, "text": "终"}], "keyword": "定下"}, {"subtitle_id": 7, "text": "定下左丞相昌平君和李信两人负责。", "timestamp": "00:00:08,520 --> 00:00:11,340", "duration": 2.82, "char_count": 16, "start_time_s": 8.52, "end_time_s": 11.34, "words": [{"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8520, "text": "定"}, {"attribute": {"event": "speech"}, "end_time": 8840, "start_time": 8680, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 9040, "start_time": 8880, "text": "左"}, {"attribute": {"event": "speech"}, "end_time": 9200, "start_time": 9040, "text": "丞"}, {"attribute": {"event": "speech"}, "end_time": 9360, "start_time": 9200, "text": "相"}, {"attribute": {"event": "speech"}, "end_time": 9520, "start_time": 9360, "text": "昌"}, {"attribute": {"event": "speech"}, "end_time": 9680, "start_time": 9520, "text": "平"}, {"attribute": {"event": "speech"}, "end_time": 9880, "start_time": 9720, "text": "君"}, {"attribute": {"event": "speech"}, "end_time": 10120, "start_time": 9960, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 10280, "start_time": 10120, "text": "李"}, {"attribute": {"event": "speech"}, "end_time": 10440, "start_time": 10320, "text": "信"}, {"attribute": {"event": "speech"}, "end_time": 10600, "start_time": 10480, "text": "两"}, {"attribute": {"event": "speech"}, "end_time": 10760, "start_time": 10600, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 10880, "start_time": 10760, "text": "负"}, {"attribute": {"event": "speech"}, "end_time": 11340, "start_time": 10880, "text": "责"}], "keyword": "昌平君"}], "keywords": ["嬴政", "雄游", "接管", "坐镇", "争执", "定下", "昌平君"]}, {"chapter": 24, "story_board": "可这看似简单的任务，却让昌平君心里直打鼓——他清楚，这一去，恐怕就是三年起步。与此同时，公子华正在忙碌着一件事。他让人准备了香皂和茶叶，亲自送到勤政殿，作为寒食节的赏赐。", "characters": [{"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "坐在王座上，威严地发出指令", "expression": "严肃"}, {"name": "楚国代表熊犹", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为暗红色，搭配银色，袖口绣银色楚国云纹，头戴黑色方巾，腰间束腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "楚国代表", "other": "眼神狡黠，善于周旋", "from_chapter": [0], "action": "无奈地低下头，双手呈上城池的交接文书", "expression": "沮丧"}, {"name": "左丞相昌平君", "gender": "男", "age": "中年", "clothes": "高领圆领汉服丞相朝服，主色调为紫色，搭配黑色，有黑色暗纹，头戴黑色丞相帽，配朝珠圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色丞相帽", "figure": "清瘦", "identity": "秦国左丞相", "other": "眉头常皱，面带忧虑", "from_chapter": [0], "action": "站在人群中，听着众人争论，微微皱眉", "expression": "忧虑"}, {"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "指挥人准备物品，亲自捧着香皂和茶叶走向勤政殿", "expression": "认真、专注"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "李信", "gender": "男", "age": "青年", "clothes": "高领圆领古代战甲，主色调为银色，搭配金色、白色，有金色花纹点缀，外披白色披风，头戴银色头盔，腰系战带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴银色头盔", "figure": "矫健", "identity": "秦国将领", "other": "眼神锐利，充满自信", "from_chapter": [0], "action": "", "expression": ""}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "冯艺", "gender": "男", "age": "青年", "clothes": "高领圆领汉服灰色长袍，主色调为灰色，袍上绣灰色花纹，腰间束一条灰色腰带，脚蹬黑色布鞋，头戴灰色毡帽圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴灰色毡帽", "figure": "中等身材", "identity": "公子华的下属，负责工匠安排", "other": "", "from_chapter": 100, "action": "", "expression": ""}], "scene": "勤政殿", "image_prompt": "中景，一个神情认真专注、挺拔的青年男子，正亲自捧着香皂和茶叶走向勤政殿，此前还在指挥人准备物品。他身穿高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带并佩戴玉佩，圆形衣领，黑色束发，背景是勤政殿, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241187_20250723_112628.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_274385c9-c24a-41fc-b56a-3a96aa7b4af2_1.25x_20250723_105744.wav", "audio_duration": 14.154667, "video_url": "", "story_board_id": 3, "subtitle": [{"subtitle_id": 1, "text": "可这看似简单的任务，", "timestamp": "00:00:00,160 --> 00:00:01,600", "duration": 1.44, "char_count": 10, "start_time_s": 0.16, "end_time_s": 1.6, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 360, "start_time": 280, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 360, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 560, "text": "似"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 720, "text": "简"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 920, "text": "单"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 1040, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 1120, "text": "任"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1280, "text": "务"}], "keyword": "任务"}, {"subtitle_id": 2, "text": "却让昌平君心里直打鼓，", "timestamp": "00:00:01,600 --> 00:00:03,240", "duration": 1.64, "char_count": 11, "start_time_s": 1.6, "end_time_s": 3.24, "words": [{"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1600, "text": "却"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1720, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1840, "text": "昌"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2000, "text": "平"}, {"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2160, "text": "君"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2320, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2480, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2760, "text": "直"}, {"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2920, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3080, "text": "鼓"}], "keyword": "昌平君"}, {"subtitle_id": 3, "text": "他清楚，这一去，", "timestamp": "00:00:03,240 --> 00:00:04,680", "duration": 1.44, "char_count": 8, "start_time_s": 3.24, "end_time_s": 4.68, "words": [{"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3240, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3400, "text": "清"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3560, "text": "楚"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 3920, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4120, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4240, "text": "去"}], "keyword": "他"}, {"subtitle_id": 4, "text": "恐怕就是3年起步。", "timestamp": "00:00:04,680 --> 00:00:06,300", "duration": 1.62, "char_count": 9, "start_time_s": 4.68, "end_time_s": 6.3, "words": [{"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4680, "text": "恐"}, {"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4880, "text": "怕"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5000, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 5320, "start_time": 5160, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5320, "text": "3"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5480, "text": "年"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5640, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 6300, "start_time": 5840, "text": "步"}], "keyword": "3年"}, {"subtitle_id": 5, "text": "与此同时，", "timestamp": "00:00:06,400 --> 00:00:07,240", "duration": 0.84, "char_count": 5, "start_time_s": 6.4, "end_time_s": 7.24, "words": [{"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6400, "text": "与"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6600, "text": "此"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6720, "text": "同"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 6840, "text": "时"}], "keyword": "同时"}, {"subtitle_id": 6, "text": "龚子华正在忙碌着一件事，", "timestamp": "00:00:07,240 --> 00:00:09,340", "duration": 2.1, "char_count": 12, "start_time_s": 7.24, "end_time_s": 9.34, "words": [{"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7240, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 7520, "start_time": 7400, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7520, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 7840, "start_time": 7720, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 8000, "start_time": 7840, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 8160, "start_time": 8000, "text": "忙"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8200, "text": "碌"}, {"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8360, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8480, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8640, "text": "件"}, {"attribute": {"event": "speech"}, "end_time": 9340, "start_time": 8840, "text": "事"}], "keyword": "龚子华"}, {"subtitle_id": 7, "text": "他让人准备了香皂和茶叶，", "timestamp": "00:00:09,400 --> 00:00:11,280", "duration": 1.88, "char_count": 12, "start_time_s": 9.4, "end_time_s": 11.28, "words": [{"attribute": {"event": "speech"}, "end_time": 9560, "start_time": 9400, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 9680, "start_time": 9560, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 9840, "start_time": 9680, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 9960, "start_time": 9840, "text": "准"}, {"attribute": {"event": "speech"}, "end_time": 10120, "start_time": 9960, "text": "备"}, {"attribute": {"event": "speech"}, "end_time": 10240, "start_time": 10120, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 10400, "start_time": 10240, "text": "香"}, {"attribute": {"event": "speech"}, "end_time": 10600, "start_time": 10440, "text": "皂"}, {"attribute": {"event": "speech"}, "end_time": 10720, "start_time": 10600, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 10920, "start_time": 10720, "text": "茶"}, {"attribute": {"event": "speech"}, "end_time": 11280, "start_time": 10960, "text": "叶"}], "keyword": "香皂"}, {"subtitle_id": 8, "text": "亲自送到秦政殿，", "timestamp": "00:00:11,280 --> 00:00:12,520", "duration": 1.24, "char_count": 8, "start_time_s": 11.28, "end_time_s": 12.52, "words": [{"attribute": {"event": "speech"}, "end_time": 11480, "start_time": 11280, "text": "亲"}, {"attribute": {"event": "speech"}, "end_time": 11640, "start_time": 11480, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 11760, "start_time": 11640, "text": "送"}, {"attribute": {"event": "speech"}, "end_time": 11880, "start_time": 11760, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 12080, "start_time": 11920, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 12200, "start_time": 12080, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 12520, "start_time": 12200, "text": "殿"}], "keyword": "茶叶"}, {"subtitle_id": 9, "text": "作为寒食节的赏赐。", "timestamp": "00:00:12,520 --> 00:00:14,100", "duration": 1.58, "char_count": 9, "start_time_s": 12.52, "end_time_s": 14.1, "words": [{"attribute": {"event": "speech"}, "end_time": 12680, "start_time": 12520, "text": "作"}, {"attribute": {"event": "speech"}, "end_time": 12760, "start_time": 12680, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 12960, "start_time": 12800, "text": "寒"}, {"attribute": {"event": "speech"}, "end_time": 13200, "start_time": 13040, "text": "食"}, {"attribute": {"event": "speech"}, "end_time": 13320, "start_time": 13200, "text": "节"}, {"attribute": {"event": "speech"}, "end_time": 13440, "start_time": 13320, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 13600, "start_time": 13440, "text": "赏"}, {"attribute": {"event": "speech"}, "end_time": 14100, "start_time": 13640, "text": "赐"}], "keyword": "秦政殿"}], "keywords": ["任务", "昌平君", "他", "3年", "同时", "龚子华", "香皂", "茶叶", "秦政殿"]}, {"chapter": 24, "story_board": "这些看似普通的物品，背后却藏着深意。因为它们来自四海商会，而这个商会，正悄悄渗透进秦国的各个角落。王贲、李信等人看到盒子上的标志，笑得合不拢嘴——他们知道，自己已经成了这场大棋局中的参与者。", "characters": [{"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "坐在王座上，威严地发出指令", "expression": "严肃"}, {"name": "楚国代表熊犹", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为暗红色，搭配银色，袖口绣银色楚国云纹，头戴黑色方巾，腰间束腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "楚国代表", "other": "眼神狡黠，善于周旋", "from_chapter": [0], "action": "无奈地低下头，双手呈上城池的交接文书", "expression": "沮丧"}, {"name": "左丞相昌平君", "gender": "男", "age": "中年", "clothes": "高领圆领汉服丞相朝服，主色调为紫色，搭配黑色，有黑色暗纹，头戴黑色丞相帽，配朝珠圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色丞相帽", "figure": "清瘦", "identity": "秦国左丞相", "other": "眉头常皱，面带忧虑", "from_chapter": [0], "action": "站在人群中，听着众人争论，微微皱眉", "expression": "忧虑"}, {"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "指挥人准备物品，亲自捧着香皂和茶叶走向勤政殿", "expression": "认真、专注"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "李信", "gender": "男", "age": "青年", "clothes": "高领圆领古代战甲，主色调为银色，搭配金色、白色，有金色花纹点缀，外披白色披风，头戴银色头盔，腰系战带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴银色头盔", "figure": "矫健", "identity": "秦国将领", "other": "眼神锐利，充满自信", "from_chapter": [0], "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "冯艺", "gender": "男", "age": "青年", "clothes": "高领圆领汉服灰色长袍，主色调为灰色，袍上绣灰色花纹，腰间束一条灰色腰带，脚蹬黑色布鞋，头戴灰色毡帽圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴灰色毡帽", "figure": "中等身材", "identity": "公子华的下属，负责工匠安排", "other": "", "from_chapter": 100, "action": "", "expression": ""}], "scene": "勤政殿", "image_prompt": "中景，在勤政殿内，两个青年男子看着盒子上的标志，笑得合不拢嘴。其中一个青年英武矫健，眼神锐利，充满自信；另一个青年同样神情愉悦。前者穿着高领圆领古代战甲，主色调为银色，搭配金色、白色，有金色花纹点缀，外披白色披风，头戴银色头盔，腰系战带，圆形衣领，黑色长发束起；后者穿着秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241214_20250723_112655.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_bfde7c6b-4ffc-4c53-a58a-e600bed87e5a_1.25x_20250723_105740.wav", "audio_duration": 15.573333, "video_url": "", "story_board_id": 4, "subtitle": [{"subtitle_id": 1, "text": "这些看似普通的物品，", "timestamp": "00:00:00,200 --> 00:00:01,720", "duration": 1.52, "char_count": 10, "start_time_s": 0.2, "end_time_s": 1.72, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "些"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 400, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 600, "text": "似"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 760, "text": "普"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 960, "text": "通"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1120, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1240, "text": "物"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1360, "text": "品"}], "keyword": "物品"}, {"subtitle_id": 2, "text": "背后却藏着深意，", "timestamp": "00:00:01,720 --> 00:00:03,260", "duration": 1.54, "char_count": 8, "start_time_s": 1.72, "end_time_s": 3.26, "words": [{"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1720, "text": "背"}, {"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1920, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2120, "text": "却"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2280, "text": "藏"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2520, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2640, "text": "深"}, {"attribute": {"event": "speech"}, "end_time": 3260, "start_time": 2800, "text": "意"}], "keyword": "深意"}, {"subtitle_id": 3, "text": "因为他们来自四海商会，", "timestamp": "00:00:03,320 --> 00:00:04,840", "duration": 1.52, "char_count": 11, "start_time_s": 3.32, "end_time_s": 4.84, "words": [{"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3320, "text": "因"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3480, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3560, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3680, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3760, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3880, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4000, "text": "四"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4200, "text": "海"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4360, "text": "商"}, {"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4520, "text": "会"}], "keyword": "四海商会"}, {"subtitle_id": 4, "text": "而这个商会", "timestamp": "00:00:04,840 --> 00:00:05,760", "duration": 0.92, "char_count": 5, "start_time_s": 4.84, "end_time_s": 5.76, "words": [{"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4840, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5040, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5160, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5240, "text": "商"}, {"attribute": {"event": "speech"}, "end_time": 5760, "start_time": 5440, "text": "会"}], "keyword": "商会"}, {"subtitle_id": 5, "text": "正悄悄渗透进秦国的各个角落。", "timestamp": "00:00:05,760 --> 00:00:08,300", "duration": 2.54, "char_count": 14, "start_time_s": 5.76, "end_time_s": 8.3, "words": [{"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5760, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5960, "text": "悄"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6000, "text": "悄"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6360, "text": "渗"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6520, "text": "透"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6720, "text": "进"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6880, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7080, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 7360, "start_time": 7240, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7520, "start_time": 7360, "text": "各"}, {"attribute": {"event": "speech"}, "end_time": 7640, "start_time": 7520, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7640, "text": "角"}, {"attribute": {"event": "speech"}, "end_time": 8300, "start_time": 7800, "text": "落"}], "keyword": "秦国"}, {"subtitle_id": 6, "text": "王本、李信等人看到盒子上的标志，", "timestamp": "00:00:08,400 --> 00:00:10,840", "duration": 2.44, "char_count": 16, "start_time_s": 8.4, "end_time_s": 10.84, "words": [{"attribute": {"event": "speech"}, "end_time": 8600, "start_time": 8400, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 8760, "start_time": 8600, "text": "本"}, {"attribute": {"event": "speech"}, "end_time": 8880, "start_time": 8760, "text": "李"}, {"attribute": {"event": "speech"}, "end_time": 9040, "start_time": 8920, "text": "信"}, {"attribute": {"event": "speech"}, "end_time": 9200, "start_time": 9080, "text": "等"}, {"attribute": {"event": "speech"}, "end_time": 9400, "start_time": 9200, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 9640, "start_time": 9480, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 9760, "start_time": 9640, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 9960, "start_time": 9760, "text": "盒"}, {"attribute": {"event": "speech"}, "end_time": 10080, "start_time": 9960, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 10200, "start_time": 10080, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 10320, "start_time": 10200, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 10480, "start_time": 10320, "text": "标"}, {"attribute": {"event": "speech"}, "end_time": 10840, "start_time": 10480, "text": "志"}], "keyword": "盒子标志"}, {"subtitle_id": 7, "text": "笑得合不拢嘴，", "timestamp": "00:00:10,840 --> 00:00:11,760", "duration": 0.92, "char_count": 7, "start_time_s": 10.84, "end_time_s": 11.76, "words": [{"attribute": {"event": "speech"}, "end_time": 11040, "start_time": 10840, "text": "笑"}, {"attribute": {"event": "speech"}, "end_time": 11160, "start_time": 11040, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 11320, "start_time": 11160, "text": "合"}, {"attribute": {"event": "speech"}, "end_time": 11400, "start_time": 11320, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 11560, "start_time": 11400, "text": "拢"}, {"attribute": {"event": "speech"}, "end_time": 11760, "start_time": 11560, "text": "嘴"}], "keyword": "合不拢嘴"}, {"subtitle_id": 8, "text": "他们知道，", "timestamp": "00:00:11,760 --> 00:00:12,480", "duration": 0.72, "char_count": 5, "start_time_s": 11.76, "end_time_s": 12.48, "words": [{"attribute": {"event": "speech"}, "end_time": 11920, "start_time": 11760, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 12000, "start_time": 11920, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 12120, "start_time": 12000, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 12480, "start_time": 12120, "text": "道"}], "keyword": "知道"}, {"subtitle_id": 9, "text": "自己", "timestamp": "00:00:12,480 --> 00:00:12,760", "duration": 0.28, "char_count": 2, "start_time_s": 12.48, "end_time_s": 12.76, "words": [{"attribute": {"event": "speech"}, "end_time": 12640, "start_time": 12480, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 12760, "start_time": 12640, "text": "己"}], "keyword": "自己"}, {"subtitle_id": 10, "text": "已经成了这场大棋局中的参与者。", "timestamp": "00:00:12,760 --> 00:00:15,260", "duration": 2.5, "char_count": 15, "start_time_s": 12.76, "end_time_s": 15.26, "words": [{"attribute": {"event": "speech"}, "end_time": 12920, "start_time": 12760, "text": "已"}, {"attribute": {"event": "speech"}, "end_time": 13040, "start_time": 12920, "text": "经"}, {"attribute": {"event": "speech"}, "end_time": 13240, "start_time": 13040, "text": "成"}, {"attribute": {"event": "speech"}, "end_time": 13320, "start_time": 13240, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 13480, "start_time": 13320, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 13640, "start_time": 13480, "text": "场"}, {"attribute": {"event": "speech"}, "end_time": 13840, "start_time": 13640, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 14040, "start_time": 13880, "text": "棋"}, {"attribute": {"event": "speech"}, "end_time": 14200, "start_time": 14040, "text": "局"}, {"attribute": {"event": "speech"}, "end_time": 14320, "start_time": 14200, "text": "中"}, {"attribute": {"event": "speech"}, "end_time": 14480, "start_time": 14320, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 14640, "start_time": 14480, "text": "参"}, {"attribute": {"event": "speech"}, "end_time": 14800, "start_time": 14640, "text": "与"}, {"attribute": {"event": "speech"}, "end_time": 15260, "start_time": 14800, "text": "者"}], "keyword": "大棋局"}], "keywords": ["物品", "深意", "四海商会", "商会", "秦国", "盒子标志", "合不拢嘴", "知道", "自己", "大棋局"]}, {"chapter": 24, "story_board": "早朝结束后，赢政走出殿门，一眼就看见熊犹在远处等着。两人一见面，便开始低声交谈。而另一边，昌平君脸色阴沉，显然对这次任务心有不甘。", "characters": [{"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "走出殿门，与熊犹见面后开始低声交谈", "expression": "严肃"}, {"name": "楚国代表熊犹", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为暗红色，搭配银色，袖口绣银色楚国云纹，头戴黑色方巾，腰间束腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "楚国代表", "other": "眼神狡黠，善于周旋", "from_chapter": [0], "action": "在远处等待，与嬴政见面后开始低声交谈", "expression": "平静"}, {"name": "左丞相昌平君", "gender": "男", "age": "中年", "clothes": "高领圆领汉服丞相朝服，主色调为紫色，搭配黑色，有黑色暗纹，头戴黑色丞相帽，配朝珠圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色丞相帽", "figure": "清瘦", "identity": "秦国左丞相", "other": "眉头常皱，面带忧虑", "from_chapter": [0], "action": "站在一旁", "expression": "阴沉、心有不甘"}, {"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "指挥人准备物品，亲自捧着香皂和茶叶走向勤政殿", "expression": "认真、专注"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "李信", "gender": "男", "age": "青年", "clothes": "高领圆领古代战甲，主色调为银色，搭配金色、白色，有金色花纹点缀，外披白色披风，头戴银色头盔，腰系战带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴银色头盔", "figure": "矫健", "identity": "秦国将领", "other": "眼神锐利，充满自信", "from_chapter": [0], "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "冯艺", "gender": "男", "age": "青年", "clothes": "高领圆领汉服灰色长袍，主色调为灰色，袍上绣灰色花纹，腰间束一条灰色腰带，脚蹬黑色布鞋，头戴灰色毡帽圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴灰色毡帽", "figure": "中等身材", "identity": "公子华的下属，负责工匠安排", "other": "", "from_chapter": 100, "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿的场景中，一位严肃、决策果断且魁梧的中年男子身着高领圆领、主色调为黑色并搭配金色、绣满金色龙纹的帝王龙袍，腰间束着黑色镶金边腰带，头戴垂有珠玉串饰的冕旒，黑色束发，正走出殿门与远处一位平静、眼神狡黠、中等身材的中年男子低声交流；这位中年男子穿着高领圆领、主色调为暗红色并搭配银色、袖口绣有银色楚国云纹的长袍，头戴黑色方巾，黑色长发束起，腰间束着腰带。旁边站着一位阴沉、清瘦且眉头紧皱、面带忧虑、心有不甘的中年男子，身着高领圆领、主色调为紫色并搭配黑色、有黑色暗纹的丞相朝服，头戴黑色丞相帽，配着朝珠，黑色长发束起, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241248_20250723_112728.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_0e54fd75-c8ac-4296-94c4-c0c929a016ca_1.25x_20250723_105732.wav", "audio_duration": 11.621333, "video_url": "", "story_board_id": 5, "subtitle": [{"subtitle_id": 1, "text": "早朝结束后，", "timestamp": "00:00:00,160 --> 00:00:01,080", "duration": 0.92, "char_count": 6, "start_time_s": 0.16, "end_time_s": 1.08, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 160, "text": "早"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 320, "text": "朝"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 480, "text": "结"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 640, "text": "束"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 760, "text": "后"}], "keyword": "早朝"}, {"subtitle_id": 2, "text": "嬴政走出殿门，", "timestamp": "00:00:01,080 --> 00:00:02,200", "duration": 1.12, "char_count": 7, "start_time_s": 1.08, "end_time_s": 2.2, "words": [{"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 1080, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1280, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1480, "text": "走"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1640, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1760, "text": "殿"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 1920, "text": "门"}], "keyword": "嬴政"}, {"subtitle_id": 3, "text": "一眼就看见雄游在远处等着，", "timestamp": "00:00:02,200 --> 00:00:04,700", "duration": 2.5, "char_count": 13, "start_time_s": 2.2, "end_time_s": 4.7, "words": [{"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2200, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 2520, "start_time": 2360, "text": "眼"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2560, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2720, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2920, "text": "见"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3200, "text": "雄"}, {"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3360, "text": "游"}, {"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3520, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3680, "text": "远"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3920, "text": "处"}, {"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4080, "text": "等"}, {"attribute": {"event": "speech"}, "end_time": 4700, "start_time": 4240, "text": "着"}], "keyword": "雄游"}, {"subtitle_id": 4, "text": "两人一见面便开始低声交谈，", "timestamp": "00:00:04,800 --> 00:00:07,220", "duration": 2.42, "char_count": 13, "start_time_s": 4.8, "end_time_s": 7.22, "words": [{"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4800, "text": "两"}, {"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 4960, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5080, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5280, "text": "见"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5400, "text": "面"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5720, "text": "便"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5880, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6040, "text": "始"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6200, "text": "低"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6400, "text": "声"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6560, "text": "交"}, {"attribute": {"event": "speech"}, "end_time": 7220, "start_time": 6720, "text": "谈"}], "keyword": "交谈"}, {"subtitle_id": 5, "text": "而另一边，", "timestamp": "00:00:07,280 --> 00:00:08,160", "duration": 0.88, "char_count": 5, "start_time_s": 7.28, "end_time_s": 8.16, "words": [{"attribute": {"event": "speech"}, "end_time": 7520, "start_time": 7280, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 7640, "start_time": 7520, "text": "另"}, {"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7640, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 8160, "start_time": 7720, "text": "边"}], "keyword": "另一边"}, {"subtitle_id": 6, "text": "昌平君脸色阴沉，", "timestamp": "00:00:08,160 --> 00:00:09,600", "duration": 1.44, "char_count": 8, "start_time_s": 8.16, "end_time_s": 9.6, "words": [{"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8160, "text": "昌"}, {"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8320, "text": "平"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8480, "text": "君"}, {"attribute": {"event": "speech"}, "end_time": 8840, "start_time": 8680, "text": "脸"}, {"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8840, "text": "色"}, {"attribute": {"event": "speech"}, "end_time": 9120, "start_time": 9000, "text": "阴"}, {"attribute": {"event": "speech"}, "end_time": 9600, "start_time": 9160, "text": "沉"}], "keyword": "昌平君"}, {"subtitle_id": 7, "text": "显然对这次任务心有不甘。", "timestamp": "00:00:09,600 --> 00:00:11,500", "duration": 1.9, "char_count": 12, "start_time_s": 9.6, "end_time_s": 11.5, "words": [{"attribute": {"event": "speech"}, "end_time": 9760, "start_time": 9600, "text": "显"}, {"attribute": {"event": "speech"}, "end_time": 9880, "start_time": 9760, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 10000, "start_time": 9880, "text": "对"}, {"attribute": {"event": "speech"}, "end_time": 10200, "start_time": 10000, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 10320, "start_time": 10200, "text": "次"}, {"attribute": {"event": "speech"}, "end_time": 10440, "start_time": 10320, "text": "任"}, {"attribute": {"event": "speech"}, "end_time": 10560, "start_time": 10440, "text": "务"}, {"attribute": {"event": "speech"}, "end_time": 10760, "start_time": 10600, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 10920, "start_time": 10760, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 11040, "start_time": 10920, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 11500, "start_time": 11040, "text": "甘"}], "keyword": "任务"}], "keywords": ["早朝", "嬴政", "雄游", "交谈", "另一边", "昌平君", "任务"]}, {"chapter": 24, "story_board": "他心里明白，一旦离开京城，楚系势力可能再难翻身。他不禁回想起过去的选择，如果当初支持嫪毐，结果会不会不一样？回到东宫，熊犹向公子华透露了自己的收获。", "characters": [{"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "走出殿门，与熊犹见面后开始低声交谈", "expression": "严肃"}, {"name": "楚国代表熊犹", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为暗红色，搭配银色，袖口绣银色楚国云纹，头戴黑色方巾，腰间束腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "楚国代表", "other": "眼神狡黠，善于周旋", "from_chapter": [0], "action": "站在公子华面前，双手摊开", "expression": "得意"}, {"name": "左丞相昌平君", "gender": "男", "age": "中年", "clothes": "高领圆领汉服丞相朝服，主色调为紫色，搭配黑色，有黑色暗纹，头戴黑色丞相帽，配朝珠圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色丞相帽", "figure": "清瘦", "identity": "秦国左丞相", "other": "眉头常皱，面带忧虑", "from_chapter": [0], "action": "低头沉思，脚步缓慢地走着", "expression": "阴沉、懊悔"}, {"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站着倾听，身体微微前倾", "expression": "惊讶"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "李信", "gender": "男", "age": "青年", "clothes": "高领圆领古代战甲，主色调为银色，搭配金色、白色，有金色花纹点缀，外披白色披风，头戴银色头盔，腰系战带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴银色头盔", "figure": "矫健", "identity": "秦国将领", "other": "眼神锐利，充满自信", "from_chapter": [0], "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "冯艺", "gender": "男", "age": "青年", "clothes": "高领圆领汉服灰色长袍，主色调为灰色，袍上绣灰色花纹，腰间束一条灰色腰带，脚蹬黑色布鞋，头戴灰色毡帽圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴灰色毡帽", "figure": "中等身材", "identity": "公子华的下属，负责工匠安排", "other": "", "from_chapter": 100, "action": "", "expression": ""}], "scene": "东宫", "image_prompt": "中景，东宫背景下，一个眼神狡黠、中等身材的中年男人双手摊开，一脸得意地站着；旁边一个挺拔的青年身体微微前倾、满脸惊讶地站着倾听；不远处一个清瘦的中年男人眉头紧皱、面带忧虑，低头沉思着缓慢踱步，神情阴沉又懊悔。双手摊开的中年男人穿着高领圆领暗红色汉服长袍，主色调为暗红色，搭配银色，袖口绣银色楚国云纹，头戴黑色方巾，腰间束腰带，黑色长发束起；身体前倾的青年穿着高领圆领紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，黑色束发；缓慢踱步的中年男人穿着高领圆领紫色汉服丞相朝服，主色调为紫色，搭配黑色，有黑色暗纹，头戴黑色丞相帽，配朝珠，黑色长发束起, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://p9-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/202507231127536FB2C13665A939C118F7-2189-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1753327680&x-signature=5fKTRfzSymOxqM5tnWMml8hhSJ4%3D", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_10a867e0-f0fa-4073-9549-266902b3c4fc_1.25x_20250723_105723.wav", "audio_duration": 12.866667, "video_url": "", "story_board_id": 6, "subtitle": [{"subtitle_id": 1, "text": "他心里明白，", "timestamp": "00:00:00,160 --> 00:00:00,960", "duration": 0.8, "char_count": 6, "start_time_s": 0.16, "end_time_s": 0.96, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 400, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 480, "text": "明"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 600, "text": "白"}], "keyword": "他"}, {"subtitle_id": 2, "text": "一旦离开京城，", "timestamp": "00:00:00,960 --> 00:00:02,200", "duration": 1.24, "char_count": 7, "start_time_s": 0.96, "end_time_s": 2.2, "words": [{"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 960, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 1120, "text": "旦"}, {"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1280, "text": "离"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1440, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1640, "text": "京"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 1800, "text": "城"}], "keyword": "京城"}, {"subtitle_id": 3, "text": "楚系势力可能再难翻身。", "timestamp": "00:00:02,200 --> 00:00:04,180", "duration": 1.98, "char_count": 11, "start_time_s": 2.2, "end_time_s": 4.18, "words": [{"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2200, "text": "楚"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2360, "text": "系"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2480, "text": "势"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2680, "text": "力"}, {"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2880, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3080, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3200, "text": "再"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3320, "text": "难"}, {"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3520, "text": "翻"}, {"attribute": {"event": "speech"}, "end_time": 4180, "start_time": 3680, "text": "身"}], "keyword": "楚系势力"}, {"subtitle_id": 4, "text": "他不禁回想起过去的选择，", "timestamp": "00:00:04,280 --> 00:00:06,000", "duration": 1.72, "char_count": 12, "start_time_s": 4.28, "end_time_s": 6.0, "words": [{"attribute": {"event": "speech"}, "end_time": 4440, "start_time": 4280, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4440, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4560, "text": "禁"}, {"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4680, "text": "回"}, {"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4840, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 5000, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5120, "text": "过"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5280, "text": "去"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5400, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5520, "text": "选"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5640, "text": "择"}], "keyword": "选择"}, {"subtitle_id": 5, "text": "如果当初支持嫪毐，", "timestamp": "00:00:06,000 --> 00:00:07,400", "duration": 1.4, "char_count": 9, "start_time_s": 6.0, "end_time_s": 7.4, "words": [{"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6000, "text": "如"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6200, "text": "果"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6320, "text": "当"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6440, "text": "初"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6600, "text": "支"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6760, "text": "持"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6880, "text": "嫪"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7040, "text": "毐"}], "keyword": "嫪毐"}, {"subtitle_id": 6, "text": "结果会不会不一样？", "timestamp": "00:00:07,400 --> 00:00:08,940", "duration": 1.54, "char_count": 9, "start_time_s": 7.4, "end_time_s": 8.94, "words": [{"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7400, "text": "结"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7560, "text": "果"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7680, "text": "会"}, {"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7960, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 8240, "start_time": 8080, "text": "会"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8240, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8360, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 8940, "start_time": 8480, "text": "样"}], "keyword": "结果"}, {"subtitle_id": 7, "text": "回到东宫，", "timestamp": "00:00:09,120 --> 00:00:10,000", "duration": 0.88, "char_count": 5, "start_time_s": 9.12, "end_time_s": 10.0, "words": [{"attribute": {"event": "speech"}, "end_time": 9320, "start_time": 9120, "text": "回"}, {"attribute": {"event": "speech"}, "end_time": 9440, "start_time": 9320, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 9600, "start_time": 9440, "text": "东"}, {"attribute": {"event": "speech"}, "end_time": 10000, "start_time": 9600, "text": "宫"}], "keyword": "东宫"}, {"subtitle_id": 8, "text": "雄游向公子华透露了自己的收获。", "timestamp": "00:00:10,000 --> 00:00:12,540", "duration": 2.54, "char_count": 15, "start_time_s": 10.0, "end_time_s": 12.54, "words": [{"attribute": {"event": "speech"}, "end_time": 10160, "start_time": 10000, "text": "雄"}, {"attribute": {"event": "speech"}, "end_time": 10360, "start_time": 10160, "text": "游"}, {"attribute": {"event": "speech"}, "end_time": 10520, "start_time": 10360, "text": "向"}, {"attribute": {"event": "speech"}, "end_time": 10640, "start_time": 10520, "text": "公"}, {"attribute": {"event": "speech"}, "end_time": 10800, "start_time": 10640, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 11000, "start_time": 10800, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 11280, "start_time": 11080, "text": "透"}, {"attribute": {"event": "speech"}, "end_time": 11400, "start_time": 11280, "text": "露"}, {"attribute": {"event": "speech"}, "end_time": 11520, "start_time": 11400, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 11680, "start_time": 11520, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 11800, "start_time": 11680, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 11920, "start_time": 11800, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 12080, "start_time": 11920, "text": "收"}, {"attribute": {"event": "speech"}, "end_time": 12540, "start_time": 12080, "text": "获"}], "keyword": "公子华"}], "keywords": ["他", "京城", "楚系势力", "选择", "嫪毐", "结果", "东宫", "公子华"]}, {"chapter": 24, "story_board": "原来，他从昌平君手中拿到了一笔巨额钱财，1300两黄金，6000两白银。这让公子华也震惊不已。", "characters": [{"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "走出殿门，与熊犹见面后开始低声交谈", "expression": "严肃"}, {"name": "楚国代表熊犹", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为暗红色，搭配银色，袖口绣银色楚国云纹，头戴黑色方巾，腰间束腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "楚国代表", "other": "眼神狡黠，善于周旋", "from_chapter": [0], "action": "站着，向公子华透露钱财情况", "expression": "得意"}, {"name": "左丞相昌平君", "gender": "男", "age": "中年", "clothes": "高领圆领汉服丞相朝服，主色调为紫色，搭配黑色，有黑色暗纹，头戴黑色丞相帽，配朝珠圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色丞相帽", "figure": "清瘦", "identity": "秦国左丞相", "other": "眉头常皱，面带忧虑", "from_chapter": [0], "action": "低头沉思，脚步缓慢地走着", "expression": "阴沉、懊悔"}, {"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站着听熊犹说话", "expression": "震惊"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "李信", "gender": "男", "age": "青年", "clothes": "高领圆领古代战甲，主色调为银色，搭配金色、白色，有金色花纹点缀，外披白色披风，头戴银色头盔，腰系战带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴银色头盔", "figure": "矫健", "identity": "秦国将领", "other": "眼神锐利，充满自信", "from_chapter": [0], "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "冯艺", "gender": "男", "age": "青年", "clothes": "高领圆领汉服灰色长袍，主色调为灰色，袍上绣灰色花纹，腰间束一条灰色腰带，脚蹬黑色布鞋，头戴灰色毡帽圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴灰色毡帽", "figure": "中等身材", "identity": "公子华的下属，负责工匠安排", "other": "", "from_chapter": 100, "action": "", "expression": ""}], "scene": "东宫", "image_prompt": "中景，东宫背景前，一个得意地站着的中年男子正向一个震惊地站着聆听的青年男子透露钱财情况。站着的中年男子眼神狡黠，中等身材，穿着高领圆领暗红色主色调、搭配银色的汉服长袍，袖口绣有银色楚国云纹，头戴黑色方巾，黑色长发束起，腰间束着腰带，有着圆形衣领；站着聆听的青年男子挺拔，穿着高领圆领紫色主色调、搭配白色且绣有云纹的锦袍，腰间束着黑色腰带并佩戴玉佩，黑色束发，有着圆形衣领, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241311_20250723_112832.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_9dd3ee24-8bf5-43cb-83da-6cec5bbf4e11_1.25x_20250723_105736.wav", "audio_duration": 7.608, "video_url": "", "story_board_id": 7, "subtitle": [{"subtitle_id": 1, "text": "原来，他从昌平军手中", "timestamp": "00:00:00,200 --> 00:00:01,800", "duration": 1.6, "char_count": 10, "start_time_s": 0.2, "end_time_s": 1.8, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "原"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 640, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 800, "text": "从"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 960, "text": "昌"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1120, "text": "平"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1280, "text": "军"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1440, "text": "手"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1560, "text": "中"}], "keyword": "昌平军"}, {"subtitle_id": 2, "text": "拿到了一笔巨额钱财，", "timestamp": "00:00:01,800 --> 00:00:03,520", "duration": 1.72, "char_count": 10, "start_time_s": 1.8, "end_time_s": 3.52, "words": [{"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1800, "text": "拿"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 2000, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2120, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2160, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2280, "text": "笔"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2520, "text": "巨"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2720, "text": "额"}, {"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2880, "text": "钱"}, {"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3080, "text": "财"}], "keyword": "钱财"}, {"subtitle_id": 3, "text": "两黄金、两白银，", "timestamp": "00:00:03,520 --> 00:00:05,060", "duration": 1.54, "char_count": 8, "start_time_s": 3.52, "end_time_s": 5.06, "words": [{"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3520, "text": "两"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3680, "text": "黄"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3880, "text": "金"}, {"attribute": {"event": "speech"}, "end_time": 4440, "start_time": 4240, "text": "两"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4440, "text": "白"}, {"attribute": {"event": "speech"}, "end_time": 5060, "start_time": 4640, "text": "银"}], "keyword": "黄金白银"}, {"subtitle_id": 4, "text": "这让龚子华也震惊不已。", "timestamp": "00:00:05,240 --> 00:00:07,260", "duration": 2.02, "char_count": 11, "start_time_s": 5.24, "end_time_s": 7.26, "words": [{"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5240, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5440, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5560, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5720, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 6080, "start_time": 5920, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 6080, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6240, "text": "震"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6520, "text": "惊"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6640, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 7260, "start_time": 6840, "text": "已"}], "keyword": "龚子华"}], "keywords": ["昌平军", "钱财", "黄金白银", "龚子华"]}, {"chapter": 24, "story_board": "熊犹打算带着这些钱回楚国，但公子华早已安排好后路——他在宛城准备了200坛酒、1800斤盐、茶和香皂各200盒，让他先带走一部分，剩下的等后续对接。熊犹满意地点头，两人约定未来互帮互助。", "characters": [{"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "走出殿门，与熊犹见面后开始低声交谈", "expression": "严肃"}, {"name": "楚国代表熊犹", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为暗红色，搭配银色，袖口绣银色楚国云纹，头戴黑色方巾，腰间束腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "楚国代表", "other": "眼神狡黠，善于周旋", "from_chapter": [0], "action": "站着，向公子华透露钱财情况", "expression": "得意"}, {"name": "左丞相昌平君", "gender": "男", "age": "中年", "clothes": "高领圆领汉服丞相朝服，主色调为紫色，搭配黑色，有黑色暗纹，头戴黑色丞相帽，配朝珠圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色丞相帽", "figure": "清瘦", "identity": "秦国左丞相", "other": "眉头常皱，面带忧虑", "from_chapter": [0], "action": "低头沉思，脚步缓慢地走着", "expression": "阴沉、懊悔"}, {"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "向熊犹介绍在宛城准备的物资，并告知让其先带走一部分，剩下后续对接", "expression": "自信"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "李信", "gender": "男", "age": "青年", "clothes": "高领圆领古代战甲，主色调为银色，搭配金色、白色，有金色花纹点缀，外披白色披风，头戴银色头盔，腰系战带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴银色头盔", "figure": "矫健", "identity": "秦国将领", "other": "眼神锐利，充满自信", "from_chapter": [0], "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "冯艺", "gender": "男", "age": "青年", "clothes": "高领圆领汉服灰色长袍，主色调为灰色，袍上绣灰色花纹，腰间束一条灰色腰带，脚蹬黑色布鞋，头戴灰色毡帽圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴灰色毡帽", "figure": "中等身材", "identity": "公子华的下属，负责工匠安排", "other": "", "from_chapter": 100, "action": "", "expression": ""}], "scene": "东宫", "image_prompt": "中景，在东宫场景中，自信地向身旁之人介绍着物资情况的青年男子，他挺拔地站立着。他穿着高领圆领的汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束着黑色腰带，佩戴玉佩，有着圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241332_20250723_112852.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_f1a7605b-035e-44c6-ac33-cc47e9297885_1.25x_20250723_105711.wav", "audio_duration": 13.269333, "video_url": "", "story_board_id": 8, "subtitle": [{"subtitle_id": 1, "text": "熊友打算带着这些钱回楚国，", "timestamp": "00:00:00,200 --> 00:00:02,200", "duration": 2.0, "char_count": 13, "start_time_s": 0.2, "end_time_s": 2.2, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 200, "text": "熊"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 320, "text": "友"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 400, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 560, "text": "算"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 720, "text": "带"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 880, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 960, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1080, "text": "些"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1200, "text": "钱"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1440, "text": "回"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1600, "text": "楚"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 1760, "text": "国"}], "keyword": "熊友"}, {"subtitle_id": 2, "text": "但龚子华早已安排好后路，", "timestamp": "00:00:02,200 --> 00:00:03,840", "duration": 1.64, "char_count": 12, "start_time_s": 2.2, "end_time_s": 3.84, "words": [{"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2200, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 2520, "start_time": 2360, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2520, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2640, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2840, "text": "早"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 3000, "text": "已"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3120, "text": "安"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3200, "text": "排"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3360, "text": "好"}, {"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3480, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3600, "text": "路"}], "keyword": "龚子华"}, {"subtitle_id": 3, "text": "他在宛城准备了坛酒，", "timestamp": "00:00:03,840 --> 00:00:05,240", "duration": 1.4, "char_count": 10, "start_time_s": 3.84, "end_time_s": 5.24, "words": [{"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3840, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 4000, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4120, "text": "宛"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4280, "text": "城"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4520, "text": "准"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4640, "text": "备"}, {"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4760, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4880, "text": "坛"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5080, "text": "酒"}], "keyword": "宛城"}, {"subtitle_id": 4, "text": "金岩茶盒香造个盒，", "timestamp": "00:00:05,240 --> 00:00:06,680", "duration": 1.44, "char_count": 9, "start_time_s": 5.24, "end_time_s": 6.68, "words": [{"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5240, "text": "金"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5400, "text": "岩"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5520, "text": "茶"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5720, "text": "盒"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5880, "text": "香"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6040, "text": "造"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6200, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6320, "text": "盒"}], "keyword": "金岩茶盒"}, {"subtitle_id": 5, "text": "让他先带走一部分，", "timestamp": "00:00:06,680 --> 00:00:08,040", "duration": 1.36, "char_count": 9, "start_time_s": 6.68, "end_time_s": 8.04, "words": [{"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6680, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6840, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6960, "text": "先"}, {"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 7120, "text": "带"}, {"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7280, "text": "走"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7440, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7560, "text": "部"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7720, "text": "分"}], "keyword": "钱"}, {"subtitle_id": 6, "text": "剩下的等后续对接。", "timestamp": "00:00:08,040 --> 00:00:09,780", "duration": 1.74, "char_count": 9, "start_time_s": 8.04, "end_time_s": 9.78, "words": [{"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8040, "text": "剩"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8200, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 8560, "start_time": 8400, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 8760, "start_time": 8560, "text": "等"}, {"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8800, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 9160, "start_time": 9000, "text": "续"}, {"attribute": {"event": "speech"}, "end_time": 9280, "start_time": 9160, "text": "对"}, {"attribute": {"event": "speech"}, "end_time": 9780, "start_time": 9320, "text": "接"}], "keyword": "后续"}, {"subtitle_id": 7, "text": "熊友满意的点头，", "timestamp": "00:00:09,960 --> 00:00:11,200", "duration": 1.24, "char_count": 8, "start_time_s": 9.96, "end_time_s": 11.2, "words": [{"attribute": {"event": "speech"}, "end_time": 10120, "start_time": 9960, "text": "熊"}, {"attribute": {"event": "speech"}, "end_time": 10240, "start_time": 10120, "text": "友"}, {"attribute": {"event": "speech"}, "end_time": 10400, "start_time": 10240, "text": "满"}, {"attribute": {"event": "speech"}, "end_time": 10480, "start_time": 10400, "text": "意"}, {"attribute": {"event": "speech"}, "end_time": 10600, "start_time": 10480, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 10760, "start_time": 10600, "text": "点"}, {"attribute": {"event": "speech"}, "end_time": 11200, "start_time": 10760, "text": "头"}], "keyword": "熊友"}, {"subtitle_id": 8, "text": "两人约定未来互帮互助。", "timestamp": "00:00:11,200 --> 00:00:13,180", "duration": 1.98, "char_count": 11, "start_time_s": 11.2, "end_time_s": 13.18, "words": [{"attribute": {"event": "speech"}, "end_time": 11360, "start_time": 11200, "text": "两"}, {"attribute": {"event": "speech"}, "end_time": 11480, "start_time": 11360, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 11600, "start_time": 11480, "text": "约"}, {"attribute": {"event": "speech"}, "end_time": 11760, "start_time": 11600, "text": "定"}, {"attribute": {"event": "speech"}, "end_time": 12040, "start_time": 11840, "text": "未"}, {"attribute": {"event": "speech"}, "end_time": 12240, "start_time": 12040, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 12400, "start_time": 12240, "text": "互"}, {"attribute": {"event": "speech"}, "end_time": 12560, "start_time": 12400, "text": "帮"}, {"attribute": {"event": "speech"}, "end_time": 12720, "start_time": 12600, "text": "互"}, {"attribute": {"event": "speech"}, "end_time": 13180, "start_time": 12720, "text": "助"}], "keyword": "互帮互助"}], "keywords": ["熊友", "龚子华", "宛城", "金岩茶盒", "钱", "后续", "熊友", "互帮互助"]}, {"chapter": 24, "story_board": "与此同时，公子华正在筹建一个庞大的产业园。这里聚集了各种资源：煤、铁矿、陶泥、石灰、木材、树皮和竹子，源源不断地被运进来。", "characters": [{"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "走出殿门，与熊犹见面后开始低声交谈", "expression": "严肃"}, {"name": "楚国代表熊犹", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为暗红色，搭配银色，袖口绣银色楚国云纹，头戴黑色方巾，腰间束腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "楚国代表", "other": "眼神狡黠，善于周旋", "from_chapter": [0], "action": "站着，向公子华透露钱财情况", "expression": "得意"}, {"name": "左丞相昌平君", "gender": "男", "age": "中年", "clothes": "高领圆领汉服丞相朝服，主色调为紫色，搭配黑色，有黑色暗纹，头戴黑色丞相帽，配朝珠圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色丞相帽", "figure": "清瘦", "identity": "秦国左丞相", "other": "眉头常皱，面带忧虑", "from_chapter": [0], "action": "低头沉思，脚步缓慢地走着", "expression": "阴沉、懊悔"}, {"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站在产业园中，看着各种资源被运进来", "expression": "自信"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "李信", "gender": "男", "age": "青年", "clothes": "高领圆领古代战甲，主色调为银色，搭配金色、白色，有金色花纹点缀，外披白色披风，头戴银色头盔，腰系战带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴银色头盔", "figure": "矫健", "identity": "秦国将领", "other": "眼神锐利，充满自信", "from_chapter": [0], "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "冯艺", "gender": "男", "age": "青年", "clothes": "高领圆领汉服灰色长袍，主色调为灰色，袍上绣灰色花纹，腰间束一条灰色腰带，脚蹬黑色布鞋，头戴灰色毡帽圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴灰色毡帽", "figure": "中等身材", "identity": "公子华的下属，负责工匠安排", "other": "", "from_chapter": 100, "action": "", "expression": ""}], "scene": "产业园", "image_prompt": "中景，站在产业园中的自信青年男子，看着各种资源被运进来，他身姿挺拔，神情自信。该男子身着高领圆领紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，有着圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241351_20250723_112912.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_2edc0b3c-fe75-4dcc-abc0-8437fa0399d5_1.25x_20250723_105707.wav", "audio_duration": 9.392, "video_url": "", "story_board_id": 9, "subtitle": [{"subtitle_id": 1, "text": "与此同时，", "timestamp": "00:00:00,200 --> 00:00:00,960", "duration": 0.76, "char_count": 5, "start_time_s": 0.2, "end_time_s": 0.96, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "与"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "此"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 400, "text": "同"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 520, "text": "时"}], "keyword": "与此同时"}, {"subtitle_id": 2, "text": "龚子华正在筹建一个庞大的产业园，", "timestamp": "00:00:00,960 --> 00:00:03,580", "duration": 2.62, "char_count": 16, "start_time_s": 0.96, "end_time_s": 3.58, "words": [{"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 960, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1120, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1240, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1440, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1560, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1720, "text": "筹"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1920, "text": "建"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2040, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2160, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2240, "text": "庞"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2480, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2680, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2800, "text": "产"}, {"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2960, "text": "业"}, {"attribute": {"event": "speech"}, "end_time": 3580, "start_time": 3080, "text": "园"}], "keyword": "龚子华"}, {"subtitle_id": 3, "text": "这里聚集了各种资源，", "timestamp": "00:00:03,720 --> 00:00:05,160", "duration": 1.44, "char_count": 10, "start_time_s": 3.72, "end_time_s": 5.16, "words": [{"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3720, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3880, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4000, "text": "聚"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4160, "text": "集"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4280, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4400, "text": "各"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4520, "text": "种"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4680, "text": "资"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 4800, "text": "源"}], "keyword": "产业园"}, {"subtitle_id": 4, "text": "煤、铁矿、陶泥、石灰、木材、树皮和竹子", "timestamp": "00:00:05,160 --> 00:00:07,680", "duration": 2.52, "char_count": 19, "start_time_s": 5.16, "end_time_s": 7.68, "words": [{"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5160, "text": "煤"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5360, "text": "铁"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5520, "text": "矿"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5680, "text": "陶"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5840, "text": "泥"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5960, "text": "石"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6120, "text": "灰"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6360, "text": "木"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6520, "text": "材"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6680, "text": "树"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6840, "text": "皮"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 7040, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 7360, "start_time": 7200, "text": "竹"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7360, "text": "子"}], "keyword": "资源"}, {"subtitle_id": 5, "text": "源源不断的被运进来。", "timestamp": "00:00:07,680 --> 00:00:09,340", "duration": 1.66, "char_count": 10, "start_time_s": 7.68, "end_time_s": 9.34, "words": [{"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7680, "text": "源"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7720, "text": "源"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 7960, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 8280, "start_time": 8160, "text": "断"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8280, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 8560, "start_time": 8400, "text": "被"}, {"attribute": {"event": "speech"}, "end_time": 8720, "start_time": 8560, "text": "运"}, {"attribute": {"event": "speech"}, "end_time": 8880, "start_time": 8720, "text": "进"}, {"attribute": {"event": "speech"}, "end_time": 9340, "start_time": 8880, "text": "来"}], "keyword": "运输"}], "keywords": ["与此同时", "龚子华", "产业园", "资源", "运输"]}, {"chapter": 24, "story_board": "章邯看着眼前的景象，忍不住问：“这么多东西，真的能用完吗？”公子华却笑着说：“我们做的是大事，格局要打开。", "characters": [{"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "走出殿门，与熊犹见面后开始低声交谈", "expression": "严肃"}, {"name": "楚国代表熊犹", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为暗红色，搭配银色，袖口绣银色楚国云纹，头戴黑色方巾，腰间束腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "楚国代表", "other": "眼神狡黠，善于周旋", "from_chapter": [0], "action": "站着，向公子华透露钱财情况", "expression": "得意"}, {"name": "左丞相昌平君", "gender": "男", "age": "中年", "clothes": "高领圆领汉服丞相朝服，主色调为紫色，搭配黑色，有黑色暗纹，头戴黑色丞相帽，配朝珠圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色丞相帽", "figure": "清瘦", "identity": "秦国左丞相", "other": "眉头常皱，面带忧虑", "from_chapter": [0], "action": "低头沉思，脚步缓慢地走着", "expression": "阴沉、懊悔"}, {"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "笑着回应", "expression": "自信"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "李信", "gender": "男", "age": "青年", "clothes": "高领圆领古代战甲，主色调为银色，搭配金色、白色，有金色花纹点缀，外披白色披风，头戴银色头盔，腰系战带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴银色头盔", "figure": "矫健", "identity": "秦国将领", "other": "眼神锐利，充满自信", "from_chapter": [0], "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "看着眼前景象，张嘴询问", "expression": "疑惑"}, {"name": "冯艺", "gender": "男", "age": "青年", "clothes": "高领圆领汉服灰色长袍，主色调为灰色，袍上绣灰色花纹，腰间束一条灰色腰带，脚蹬黑色布鞋，头戴灰色毡帽圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴灰色毡帽", "figure": "中等身材", "identity": "公子华的下属，负责工匠安排", "other": "", "from_chapter": 100, "action": "", "expression": ""}], "scene": "产业园", "image_prompt": "中景，一个青年男子张嘴，满脸疑惑地看着眼前景象，旁边另一个青年男子面带自信的笑容回应着。前者魁梧，穿着高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾，圆形衣领，黑色束发；后者挺拔，穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发，背景是产业园, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241377_20250723_112937.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_509bc860-11f5-4c8d-af1f-4cb9d5671334_1.25x_20250723_105719.wav", "audio_duration": 8.68, "video_url": "", "story_board_id": 10, "subtitle": [{"subtitle_id": 1, "text": "张晗看着眼前的景象，", "timestamp": "00:00:00,160 --> 00:00:01,720", "duration": 1.56, "char_count": 10, "start_time_s": 0.16, "end_time_s": 1.72, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 160, "text": "张"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 360, "text": "晗"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 520, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 720, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 840, "text": "眼"}, {"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 1000, "text": "前"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1160, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1240, "text": "景"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1400, "text": "象"}], "keyword": "张晗"}, {"subtitle_id": 2, "text": "忍不住问这么多东西真的能用完吗？", "timestamp": "00:00:01,720 --> 00:00:05,020", "duration": 3.3, "char_count": 16, "start_time_s": 1.72, "end_time_s": 5.02, "words": [{"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1720, "text": "忍"}, {"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1920, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2080, "text": "住"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2200, "text": "问"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2560, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2760, "text": "么"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2880, "text": "多"}, {"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 3000, "text": "东"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3160, "text": "西"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3560, "text": "真"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3760, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3920, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4120, "text": "用"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4280, "text": "完"}, {"attribute": {"event": "speech"}, "end_time": 5020, "start_time": 4480, "text": "吗"}], "keyword": "东西"}, {"subtitle_id": 3, "text": "龚子华却笑着说我们做的是大事，", "timestamp": "00:00:05,080 --> 00:00:07,400", "duration": 2.32, "char_count": 15, "start_time_s": 5.08, "end_time_s": 7.4, "words": [{"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5080, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5280, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5400, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5600, "text": "却"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5720, "text": "笑"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5880, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 5960, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6280, "text": "我"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6520, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6560, "text": "做"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6680, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6760, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6880, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7080, "text": "事"}], "keyword": "龚子华"}, {"subtitle_id": 4, "text": "格局要打开。", "timestamp": "00:00:07,400 --> 00:00:08,580", "duration": 1.18, "char_count": 6, "start_time_s": 7.4, "end_time_s": 8.58, "words": [{"attribute": {"event": "speech"}, "end_time": 7640, "start_time": 7400, "text": "格"}, {"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7640, "text": "局"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7800, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7920, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 8580, "start_time": 8080, "text": "开"}], "keyword": "格局"}], "keywords": ["张晗", "东西", "龚子华", "格局"]}, {"chapter": 24, "story_board": "”他深知，只有把资源集中起来，才能打造出真正的力量。产业园正式开业，众人都激动不已。", "characters": [{"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "走出殿门，与熊犹见面后开始低声交谈", "expression": "严肃"}, {"name": "楚国代表熊犹", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为暗红色，搭配银色，袖口绣银色楚国云纹，头戴黑色方巾，腰间束腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "楚国代表", "other": "眼神狡黠，善于周旋", "from_chapter": [0], "action": "站着，向公子华透露钱财情况", "expression": "得意"}, {"name": "左丞相昌平君", "gender": "男", "age": "中年", "clothes": "高领圆领汉服丞相朝服，主色调为紫色，搭配黑色，有黑色暗纹，头戴黑色丞相帽，配朝珠圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色丞相帽", "figure": "清瘦", "identity": "秦国左丞相", "other": "眉头常皱，面带忧虑", "from_chapter": [0], "action": "低头沉思，脚步缓慢地走着", "expression": "阴沉、懊悔"}, {"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站在产业园开业现场，面带微笑环顾四周", "expression": "自信"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "李信", "gender": "男", "age": "青年", "clothes": "高领圆领古代战甲，主色调为银色，搭配金色、白色，有金色花纹点缀，外披白色披风，头戴银色头盔，腰系战带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴银色头盔", "figure": "矫健", "identity": "秦国将领", "other": "眼神锐利，充满自信", "from_chapter": [0], "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "看着眼前景象，张嘴询问", "expression": "疑惑"}, {"name": "冯艺", "gender": "男", "age": "青年", "clothes": "高领圆领汉服灰色长袍，主色调为灰色，袍上绣灰色花纹，腰间束一条灰色腰带，脚蹬黑色布鞋，头戴灰色毡帽圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴灰色毡帽", "figure": "中等身材", "identity": "公子华的下属，负责工匠安排", "other": "", "from_chapter": 100, "action": "", "expression": ""}], "scene": "产业园", "image_prompt": "中景，一个面带微笑、自信地站在产业园开业现场环顾四周的青年男子，他言辞犀利、敢言善辩且身姿挺拔。该男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，有着圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241396_20250723_112956.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_ea314334-b607-43ca-8947-ae5114786628_1.25x_20250723_105807.wav", "audio_duration": 6.976, "video_url": "", "story_board_id": 11, "subtitle": [{"subtitle_id": 1, "text": "他深知，只有把资源集中起来，", "timestamp": "00:00:00,160 --> 00:00:02,280", "duration": 2.12, "char_count": 14, "start_time_s": 0.16, "end_time_s": 2.28, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "深"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 440, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 840, "text": "只"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 960, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1040, "text": "把"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1200, "text": "资"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1360, "text": "源"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1520, "text": "集"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1720, "text": "中"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1880, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2000, "text": "来"}], "keyword": "他"}, {"subtitle_id": 2, "text": "才能打造出真正的力量。", "timestamp": "00:00:02,280 --> 00:00:04,140", "duration": 1.86, "char_count": 11, "start_time_s": 2.28, "end_time_s": 4.14, "words": [{"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2280, "text": "才"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2440, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2560, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2720, "text": "造"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2880, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3040, "text": "真"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3240, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3400, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3520, "text": "力"}, {"attribute": {"event": "speech"}, "end_time": 4140, "start_time": 3680, "text": "量"}], "keyword": "力量"}, {"subtitle_id": 3, "text": "产业园正式开业，", "timestamp": "00:00:04,280 --> 00:00:05,600", "duration": 1.32, "char_count": 8, "start_time_s": 4.28, "end_time_s": 5.6, "words": [{"attribute": {"event": "speech"}, "end_time": 4440, "start_time": 4280, "text": "产"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4480, "text": "业"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4600, "text": "园"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4800, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 4960, "text": "式"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5080, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5240, "text": "业"}], "keyword": "产业园"}, {"subtitle_id": 4, "text": "众人都激动不已。", "timestamp": "00:00:05,600 --> 00:00:06,900", "duration": 1.3, "char_count": 8, "start_time_s": 5.6, "end_time_s": 6.9, "words": [{"attribute": {"event": "speech"}, "end_time": 5760, "start_time": 5600, "text": "众"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5760, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5880, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6040, "text": "激"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6200, "text": "动"}, {"attribute": {"event": "speech"}, "end_time": 6480, "start_time": 6320, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 6900, "start_time": 6480, "text": "已"}], "keyword": "众人"}], "keywords": ["他", "力量", "产业园", "众人"]}, {"chapter": 24, "story_board": "公子华任命了多位负责人，每个人都有明确的任务和俸禄。他还特别提到要为工人的孩子建一所学堂，让他们免费上学。", "characters": [{"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "走出殿门，与熊犹见面后开始低声交谈", "expression": "严肃"}, {"name": "楚国代表熊犹", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为暗红色，搭配银色，袖口绣银色楚国云纹，头戴黑色方巾，腰间束腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "楚国代表", "other": "眼神狡黠，善于周旋", "from_chapter": [0], "action": "站着，向公子华透露钱财情况", "expression": "得意"}, {"name": "左丞相昌平君", "gender": "男", "age": "中年", "clothes": "高领圆领汉服丞相朝服，主色调为紫色，搭配黑色，有黑色暗纹，头戴黑色丞相帽，配朝珠圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色丞相帽", "figure": "清瘦", "identity": "秦国左丞相", "other": "眉头常皱，面带忧虑", "from_chapter": [0], "action": "低头沉思，脚步缓慢地走着", "expression": "阴沉、懊悔"}, {"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站在众人面前，手指着手中的名册依次宣布任命，讲话时双手摊开", "expression": "严肃且自信"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "李信", "gender": "男", "age": "青年", "clothes": "高领圆领古代战甲，主色调为银色，搭配金色、白色，有金色花纹点缀，外披白色披风，头戴银色头盔，腰系战带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴银色头盔", "figure": "矫健", "identity": "秦国将领", "other": "眼神锐利，充满自信", "from_chapter": [0], "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "看着眼前景象，张嘴询问", "expression": "疑惑"}, {"name": "冯艺", "gender": "男", "age": "青年", "clothes": "高领圆领汉服灰色长袍，主色调为灰色，袍上绣灰色花纹，腰间束一条灰色腰带，脚蹬黑色布鞋，头戴灰色毡帽圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴灰色毡帽", "figure": "中等身材", "identity": "公子华的下属，负责工匠安排", "other": "", "from_chapter": 100, "action": "", "expression": ""}], "scene": "产业园", "image_prompt": "中景，站在众人面前的青年男子手指着手中的名册依次宣布任命，随后双手摊开，神情严肃且自信，整体形象挺拔。男子身着高领圆领的汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束着黑色腰带并佩戴玉佩，有着圆形衣领，黑色束发。背景为产业园, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241425_20250723_113025.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_9bc7a3df-85b1-46d0-8a30-68df90d1ce87_1.25x_20250723_105825.wav", "audio_duration": 9.237333, "video_url": "", "story_board_id": 12, "subtitle": [{"subtitle_id": 1, "text": "龚子华任命了多位负责人，", "timestamp": "00:00:00,160 --> 00:00:01,920", "duration": 1.76, "char_count": 12, "start_time_s": 0.16, "end_time_s": 1.92, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 440, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 640, "text": "任"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 760, "text": "命"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 920, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1000, "text": "多"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1200, "text": "位"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1360, "text": "负"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1480, "text": "责"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1600, "text": "人"}], "keyword": "龚子华"}, {"subtitle_id": 2, "text": "每个人都有明确的任务和俸禄。", "timestamp": "00:00:01,920 --> 00:00:04,220", "duration": 2.3, "char_count": 14, "start_time_s": 1.92, "end_time_s": 4.22, "words": [{"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 1920, "text": "每"}, {"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2160, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2320, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2480, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2640, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2760, "text": "明"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 2960, "text": "确"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3120, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3200, "text": "任"}, {"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3320, "text": "务"}, {"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3440, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3600, "text": "俸"}, {"attribute": {"event": "speech"}, "end_time": 4220, "start_time": 3760, "text": "禄"}], "keyword": "任务"}, {"subtitle_id": 3, "text": "他还特别提到，", "timestamp": "00:00:04,320 --> 00:00:05,240", "duration": 0.92, "char_count": 7, "start_time_s": 4.32, "end_time_s": 5.24, "words": [{"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4320, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4480, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4560, "text": "特"}, {"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4760, "text": "别"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4840, "text": "提"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5040, "text": "到"}], "keyword": "特别"}, {"subtitle_id": 4, "text": "要为工人的孩子建一所学堂，", "timestamp": "00:00:05,240 --> 00:00:07,440", "duration": 2.2, "char_count": 13, "start_time_s": 5.24, "end_time_s": 7.44, "words": [{"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5240, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5440, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5560, "text": "工"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5720, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5840, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5960, "text": "孩"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6120, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6400, "text": "建"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6560, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6760, "text": "所"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6920, "text": "学"}, {"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7120, "text": "堂"}], "keyword": "学堂"}, {"subtitle_id": 5, "text": "让他们免费上学。", "timestamp": "00:00:07,440 --> 00:00:08,900", "duration": 1.46, "char_count": 8, "start_time_s": 7.44, "end_time_s": 8.9, "words": [{"attribute": {"event": "speech"}, "end_time": 7640, "start_time": 7440, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7640, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 7880, "start_time": 7760, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7880, "text": "免"}, {"attribute": {"event": "speech"}, "end_time": 8240, "start_time": 8080, "text": "费"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8280, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 8900, "start_time": 8440, "text": "学"}], "keyword": "上学"}], "keywords": ["龚子华", "任务", "特别", "学堂", "上学"]}, {"chapter": 24, "story_board": "这个决定让工人们感动不已，纷纷感谢公子华的恩情。接下来，公子华又安排人前往宛城接收粮食和黄金。", "characters": [{"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "走出殿门，与熊犹见面后开始低声交谈", "expression": "严肃"}, {"name": "楚国代表熊犹", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为暗红色，搭配银色，袖口绣银色楚国云纹，头戴黑色方巾，腰间束腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "楚国代表", "other": "眼神狡黠，善于周旋", "from_chapter": [0], "action": "站着，向公子华透露钱财情况", "expression": "得意"}, {"name": "左丞相昌平君", "gender": "男", "age": "中年", "clothes": "高领圆领汉服丞相朝服，主色调为紫色，搭配黑色，有黑色暗纹，头戴黑色丞相帽，配朝珠圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色丞相帽", "figure": "清瘦", "identity": "秦国左丞相", "other": "眉头常皱，面带忧虑", "from_chapter": [0], "action": "低头沉思，脚步缓慢地走着", "expression": "阴沉、懊悔"}, {"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站在产业园中，一边微笑着回应工人的感谢，一边挥手安排人前往宛城接收粮食和黄金", "expression": "自信从容"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "李信", "gender": "男", "age": "青年", "clothes": "高领圆领古代战甲，主色调为银色，搭配金色、白色，有金色花纹点缀，外披白色披风，头戴银色头盔，腰系战带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴银色头盔", "figure": "矫健", "identity": "秦国将领", "other": "眼神锐利，充满自信", "from_chapter": [0], "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "看着眼前景象，张嘴询问", "expression": "疑惑"}, {"name": "冯艺", "gender": "男", "age": "青年", "clothes": "高领圆领汉服灰色长袍，主色调为灰色，袍上绣灰色花纹，腰间束一条灰色腰带，脚蹬黑色布鞋，头戴灰色毡帽圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴灰色毡帽", "figure": "中等身材", "identity": "公子华的下属，负责工匠安排", "other": "", "from_chapter": 100, "action": "", "expression": ""}], "scene": "产业园", "image_prompt": "中景，在产业园中，一位自信从容、面带微笑、一边挥手安排人前往宛城接收粮食和黄金，一边回应他人的青年男子。该男子挺拔，敢言善辩，穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241449_20250723_113049.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_a4802c9a-935e-4714-a13d-2b2dee8dd633_1.25x_20250723_105759.wav", "audio_duration": 8.24, "video_url": "", "story_board_id": 13, "subtitle": [{"subtitle_id": 1, "text": "这个决定让工人们感动不已，", "timestamp": "00:00:00,160 --> 00:00:02,240", "duration": 2.08, "char_count": 13, "start_time_s": 0.16, "end_time_s": 2.24, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 360, "start_time": 280, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 360, "text": "决"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 520, "text": "定"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 800, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 1000, "text": "工"}, {"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 1120, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1280, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1400, "text": "感"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1600, "text": "动"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1760, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 1920, "text": "已"}], "keyword": "工人"}, {"subtitle_id": 2, "text": "纷纷感谢龚子华的恩情。", "timestamp": "00:00:02,240 --> 00:00:04,140", "duration": 1.9, "char_count": 11, "start_time_s": 2.24, "end_time_s": 4.14, "words": [{"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2240, "text": "纷"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2280, "text": "纷"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2560, "text": "感"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2760, "text": "谢"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2880, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3040, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3200, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3360, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3440, "text": "恩"}, {"attribute": {"event": "speech"}, "end_time": 4140, "start_time": 3640, "text": "情"}], "keyword": "龚子华"}, {"subtitle_id": 3, "text": "接下来，龚子华又安排人前往宛城", "timestamp": "00:00:04,280 --> 00:00:06,760", "duration": 2.48, "char_count": 15, "start_time_s": 4.28, "end_time_s": 6.76, "words": [{"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4280, "text": "接"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4400, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4520, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 4920, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5080, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5240, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5400, "text": "又"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5520, "text": "安"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5680, "text": "排"}, {"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5800, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5920, "text": "前"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6120, "text": "往"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6280, "text": "宛"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6440, "text": "城"}], "keyword": "宛城"}, {"subtitle_id": 4, "text": "接收粮食和黄金。", "timestamp": "00:00:06,760 --> 00:00:08,140", "duration": 1.38, "char_count": 8, "start_time_s": 6.76, "end_time_s": 8.14, "words": [{"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6760, "text": "接"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6920, "text": "收"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7040, "text": "粮"}, {"attribute": {"event": "speech"}, "end_time": 7360, "start_time": 7240, "text": "食"}, {"attribute": {"event": "speech"}, "end_time": 7520, "start_time": 7360, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7520, "text": "黄"}, {"attribute": {"event": "speech"}, "end_time": 8140, "start_time": 7720, "text": "金"}], "keyword": "粮食"}], "keywords": ["工人", "龚子华", "宛城", "粮食"]}, {"chapter": 24, "story_board": "冯艺主动请缨，带着护卫前去。而其他负责人也开始各司其职，整个产业园井然有序地运转起来。", "characters": [{"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "走出殿门，与熊犹见面后开始低声交谈", "expression": "严肃"}, {"name": "楚国代表熊犹", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为暗红色，搭配银色，袖口绣银色楚国云纹，头戴黑色方巾，腰间束腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "楚国代表", "other": "眼神狡黠，善于周旋", "from_chapter": [0], "action": "站着，向公子华透露钱财情况", "expression": "得意"}, {"name": "左丞相昌平君", "gender": "男", "age": "中年", "clothes": "高领圆领汉服丞相朝服，主色调为紫色，搭配黑色，有黑色暗纹，头戴黑色丞相帽，配朝珠圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色丞相帽", "figure": "清瘦", "identity": "秦国左丞相", "other": "眉头常皱，面带忧虑", "from_chapter": [0], "action": "低头沉思，脚步缓慢地走着", "expression": "阴沉、懊悔"}, {"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站在产业园中，一边微笑着回应工人的感谢，一边挥手安排人前往宛城接收粮食和黄金", "expression": "自信从容"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "李信", "gender": "男", "age": "青年", "clothes": "高领圆领古代战甲，主色调为银色，搭配金色、白色，有金色花纹点缀，外披白色披风，头戴银色头盔，腰系战带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴银色头盔", "figure": "矫健", "identity": "秦国将领", "other": "眼神锐利，充满自信", "from_chapter": [0], "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "看着眼前景象，张嘴询问", "expression": "疑惑"}, {"name": "冯艺", "gender": "男", "age": "青年", "clothes": "高领圆领汉服灰色长袍，主色调为灰色，袍上绣灰色花纹，腰间束一条灰色腰带，脚蹬黑色布鞋，头戴灰色毡帽圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴灰色毡帽", "figure": "中等身材", "identity": "公子华的下属，负责工匠安排", "other": "", "from_chapter": 100, "action": "主动站出来请缨，然后带着护卫准备出发", "expression": "坚定"}], "scene": "产业园", "image_prompt": "中景，在产业园的场景中，一位神情坚定、主动站出来请缨后带着护卫准备出发的青年男子，中等身材，黑色长发束起，头戴灰色毡帽，身着高领圆领的汉服灰色长袍，主色调为灰色，袍上绣有灰色花纹，腰间束着一条灰色腰带，脚蹬黑色布鞋；旁边跟随的护卫形象简单展现, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241473_20250723_113114.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_022c9c11-7618-4733-98ac-da929d0bf2cb_1.25x_20250723_105810.wav", "audio_duration": 7.744, "video_url": "", "story_board_id": 14, "subtitle": [{"subtitle_id": 1, "text": "冯毅主动请缨，", "timestamp": "00:00:00,200 --> 00:00:01,440", "duration": 1.24, "char_count": 7, "start_time_s": 0.2, "end_time_s": 1.44, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 200, "text": "冯"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 360, "text": "毅"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 520, "text": "主"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 680, "text": "动"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 880, "text": "请"}, {"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1040, "text": "缨"}], "keyword": "冯毅"}, {"subtitle_id": 2, "text": "带着护卫前去，", "timestamp": "00:00:01,440 --> 00:00:02,820", "duration": 1.38, "char_count": 7, "start_time_s": 1.44, "end_time_s": 2.82, "words": [{"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1440, "text": "带"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1640, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1800, "text": "护"}, {"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1920, "text": "卫"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2080, "text": "前"}, {"attribute": {"event": "speech"}, "end_time": 2820, "start_time": 2280, "text": "去"}], "keyword": "护卫"}, {"subtitle_id": 3, "text": "而其他负责人也开始各司其职，", "timestamp": "00:00:02,840 --> 00:00:05,120", "duration": 2.28, "char_count": 14, "start_time_s": 2.84, "end_time_s": 5.12, "words": [{"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2840, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3080, "text": "其"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3360, "text": "负"}, {"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3480, "text": "责"}, {"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3600, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3800, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4000, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4160, "text": "始"}, {"attribute": {"event": "speech"}, "end_time": 4440, "start_time": 4280, "text": "各"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4440, "text": "司"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4600, "text": "其"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 4760, "text": "职"}], "keyword": "负责人"}, {"subtitle_id": 4, "text": "整个产业园井然有序地运转起来。", "timestamp": "00:00:05,120 --> 00:00:07,700", "duration": 2.58, "char_count": 15, "start_time_s": 5.12, "end_time_s": 7.7, "words": [{"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5120, "text": "整"}, {"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5280, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5440, "text": "产"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5600, "text": "业"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5720, "text": "园"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6000, "text": "井"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6160, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 6480, "start_time": 6280, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6520, "text": "序"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6640, "text": "地"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6760, "text": "运"}, {"attribute": {"event": "speech"}, "end_time": 7120, "start_time": 6960, "text": "转"}, {"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 7160, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 7700, "start_time": 7280, "text": "来"}], "keyword": "产业园"}], "keywords": ["冯毅", "护卫", "负责人", "产业园"]}, {"chapter": 24, "story_board": "晚上，公子华来到军营，看士兵们训练。他和章邯聊起白马义从和先登营的状况，发现他们之间有些摩擦。", "characters": [{"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "走出殿门，与熊犹见面后开始低声交谈", "expression": "严肃"}, {"name": "楚国代表熊犹", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为暗红色，搭配银色，袖口绣银色楚国云纹，头戴黑色方巾，腰间束腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "楚国代表", "other": "眼神狡黠，善于周旋", "from_chapter": [0], "action": "站着，向公子华透露钱财情况", "expression": "得意"}, {"name": "左丞相昌平君", "gender": "男", "age": "中年", "clothes": "高领圆领汉服丞相朝服，主色调为紫色，搭配黑色，有黑色暗纹，头戴黑色丞相帽，配朝珠圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色丞相帽", "figure": "清瘦", "identity": "秦国左丞相", "other": "眉头常皱，面带忧虑", "from_chapter": [0], "action": "低头沉思，脚步缓慢地走着", "expression": "阴沉、懊悔"}, {"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "来到军营观看士兵训练，与章邯交谈", "expression": "专注"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "李信", "gender": "男", "age": "青年", "clothes": "高领圆领古代战甲，主色调为银色，搭配金色、白色，有金色花纹点缀，外披白色披风，头戴银色头盔，腰系战带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴银色头盔", "figure": "矫健", "identity": "秦国将领", "other": "眼神锐利，充满自信", "from_chapter": [0], "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "与公子华交谈", "expression": "认真"}, {"name": "冯艺", "gender": "男", "age": "青年", "clothes": "高领圆领汉服灰色长袍，主色调为灰色，袍上绣灰色花纹，腰间束一条灰色腰带，脚蹬黑色布鞋，头戴灰色毡帽圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴灰色毡帽", "figure": "中等身材", "identity": "公子华的下属，负责工匠安排", "other": "", "from_chapter": 100, "action": "主动站出来请缨，然后带着护卫准备出发", "expression": "坚定"}], "scene": "军营", "image_prompt": "中景，在军营里，两个青年男子专注认真地站着。一个挺拔的青年男子专注地观看士兵训练，另一个魁梧的青年男子站在一旁，两人的神态都很认真。挺拔的青年穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发；魁梧的青年穿着高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241492_20250723_113132.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_aec4e008-5d7a-4939-a283-1bacda14613f_1.25x_20250723_105818.wav", "audio_duration": 8.338667, "video_url": "", "story_board_id": 15, "subtitle": [{"subtitle_id": 1, "text": "晚上，", "timestamp": "00:00:00,160 --> 00:00:00,640", "duration": 0.48, "char_count": 3, "start_time_s": 0.16, "end_time_s": 0.64, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 160, "text": "晚"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 320, "text": "上"}], "keyword": "晚上"}, {"subtitle_id": 2, "text": "龚子华来到军营看士兵们训练，", "timestamp": "00:00:00,640 --> 00:00:03,220", "duration": 2.58, "char_count": 14, "start_time_s": 0.64, "end_time_s": 3.22, "words": [{"attribute": {"event": "speech"}, "end_time": 800, "start_time": 640, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 800, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 920, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1080, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1200, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1320, "text": "军"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1480, "text": "营"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1800, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2040, "text": "士"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2280, "text": "兵"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2440, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2600, "text": "训"}, {"attribute": {"event": "speech"}, "end_time": 3220, "start_time": 2760, "text": "练"}], "keyword": "龚子华"}, {"subtitle_id": 3, "text": "他和张涵", "timestamp": "00:00:03,320 --> 00:00:04,040", "duration": 0.72, "char_count": 4, "start_time_s": 3.32, "end_time_s": 4.04, "words": [{"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3320, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3520, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3640, "text": "张"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3800, "text": "涵"}], "keyword": "张涵"}, {"subtitle_id": 4, "text": "聊起白马义从和先登营的状况，", "timestamp": "00:00:04,040 --> 00:00:06,240", "duration": 2.2, "char_count": 14, "start_time_s": 4.04, "end_time_s": 6.24, "words": [{"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4040, "text": "聊"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4200, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4360, "text": "白"}, {"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4520, "text": "马"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4680, "text": "义"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4840, "text": "从"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 4960, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5160, "text": "先"}, {"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5280, "text": "登"}, {"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5440, "text": "营"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5560, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5720, "text": "状"}, {"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 5880, "text": "况"}], "keyword": "白马义从"}, {"subtitle_id": 5, "text": "发现他们之间有些摩擦。", "timestamp": "00:00:06,240 --> 00:00:07,980", "duration": 1.74, "char_count": 11, "start_time_s": 6.24, "end_time_s": 7.98, "words": [{"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6240, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6440, "text": "现"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6560, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6680, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6760, "text": "之"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6880, "text": "间"}, {"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 7040, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7200, "text": "些"}, {"attribute": {"event": "speech"}, "end_time": 7520, "start_time": 7320, "text": "摩"}, {"attribute": {"event": "speech"}, "end_time": 7980, "start_time": 7520, "text": "擦"}], "keyword": "摩擦"}], "keywords": ["晚上", "龚子华", "张涵", "白马义从", "摩擦"]}, {"chapter": 24, "story_board": "为了激发他们的斗志，公子华故意安排了一场“意外”的冲突，让士兵们打了起来。虽然场面一度混乱，但最终还是被控制住了。", "characters": [{"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "走出殿门，与熊犹见面后开始低声交谈", "expression": "严肃"}, {"name": "楚国代表熊犹", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为暗红色，搭配银色，袖口绣银色楚国云纹，头戴黑色方巾，腰间束腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "楚国代表", "other": "眼神狡黠，善于周旋", "from_chapter": [0], "action": "站着，向公子华透露钱财情况", "expression": "得意"}, {"name": "左丞相昌平君", "gender": "男", "age": "中年", "clothes": "高领圆领汉服丞相朝服，主色调为紫色，搭配黑色，有黑色暗纹，头戴黑色丞相帽，配朝珠圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色丞相帽", "figure": "清瘦", "identity": "秦国左丞相", "other": "眉头常皱，面带忧虑", "from_chapter": [0], "action": "低头沉思，脚步缓慢地走着", "expression": "阴沉、懊悔"}, {"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "故意安排冲突，站在一旁观察，看着士兵们打斗", "expression": "冷静、若有所思"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "李信", "gender": "男", "age": "青年", "clothes": "高领圆领古代战甲，主色调为银色，搭配金色、白色，有金色花纹点缀，外披白色披风，头戴银色头盔，腰系战带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴银色头盔", "figure": "矫健", "identity": "秦国将领", "other": "眼神锐利，充满自信", "from_chapter": [0], "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "与公子华交谈", "expression": "认真"}, {"name": "冯艺", "gender": "男", "age": "青年", "clothes": "高领圆领汉服灰色长袍，主色调为灰色，袍上绣灰色花纹，腰间束一条灰色腰带，脚蹬黑色布鞋，头戴灰色毡帽圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴灰色毡帽", "figure": "中等身材", "identity": "公子华的下属，负责工匠安排", "other": "", "from_chapter": 100, "action": "主动站出来请缨，然后带着护卫准备出发", "expression": "坚定"}], "scene": "军营", "image_prompt": "中景，站在军营中冷静且若有所思地观察着士兵们打斗的青年男子，他故意安排冲突想从中观察情况。该男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://p9-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/20250723113146C8E6F590BE2E4CC43EC7-4208-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1753327913&x-signature=yWvmlI%2FK6opmwz6sXVpsUuttqz8%3D", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_42977fa5-ddd0-415c-9f7f-f2f56e42cde7_1.25x_20250723_105803.wav", "audio_duration": 8.722667, "video_url": "", "story_board_id": 16, "subtitle": [{"subtitle_id": 1, "text": "为了激发他们的斗志，", "timestamp": "00:00:00,160 --> 00:00:01,480", "duration": 1.32, "char_count": 10, "start_time_s": 0.16, "end_time_s": 1.48, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 360, "start_time": 280, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 360, "text": "激"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 520, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 640, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 800, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 840, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 960, "text": "斗"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1120, "text": "志"}], "keyword": "斗志"}, {"subtitle_id": 2, "text": "龚子华故意安排了一场意外的冲突，", "timestamp": "00:00:01,480 --> 00:00:03,800", "duration": 2.32, "char_count": 16, "start_time_s": 1.48, "end_time_s": 3.8, "words": [{"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1480, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1680, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1800, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2000, "text": "故"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2160, "text": "意"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2280, "text": "安"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2400, "text": "排"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2560, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2640, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2680, "text": "场"}, {"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2920, "text": "意"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3080, "text": "外"}, {"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3200, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3320, "text": "冲"}, {"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3480, "text": "突"}], "keyword": "龚子华"}, {"subtitle_id": 3, "text": "让士兵们打了起来，", "timestamp": "00:00:03,800 --> 00:00:05,260", "duration": 1.46, "char_count": 9, "start_time_s": 3.8, "end_time_s": 5.26, "words": [{"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3800, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 4000, "text": "士"}, {"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4120, "text": "兵"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4280, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4400, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4640, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4720, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 5260, "start_time": 4840, "text": "来"}], "keyword": "冲突"}, {"subtitle_id": 4, "text": "虽然场面一度混乱，", "timestamp": "00:00:05,400 --> 00:00:06,800", "duration": 1.4, "char_count": 9, "start_time_s": 5.4, "end_time_s": 6.8, "words": [{"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5400, "text": "虽"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5560, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5680, "text": "场"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5840, "text": "面"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6000, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6160, "text": "度"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6280, "text": "混"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6440, "text": "乱"}], "keyword": "场面"}, {"subtitle_id": 5, "text": "但最终还是被控制住了。", "timestamp": "00:00:06,800 --> 00:00:08,700", "duration": 1.9, "char_count": 11, "start_time_s": 6.8, "end_time_s": 8.7, "words": [{"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6800, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 7120, "start_time": 6960, "text": "最"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7120, "text": "终"}, {"attribute": {"event": "speech"}, "end_time": 7520, "start_time": 7280, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 7640, "start_time": 7520, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7640, "text": "被"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7760, "text": "控"}, {"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7920, "text": "制"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8080, "text": "住"}, {"attribute": {"event": "speech"}, "end_time": 8700, "start_time": 8200, "text": "了"}], "keyword": "控制"}], "keywords": ["斗志", "龚子华", "冲突", "场面", "控制"]}, {"chapter": 24, "story_board": "这场“戏”让公子华看到了军队的潜力，也让他更加坚定自己的计划。与此同时，赢政也在关注着公子华的一举一动。", "characters": [{"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "坐在宫殿里，眼睛望向军营的方向，手托下巴", "expression": "沉思"}, {"name": "楚国代表熊犹", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为暗红色，搭配银色，袖口绣银色楚国云纹，头戴黑色方巾，腰间束腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "楚国代表", "other": "眼神狡黠，善于周旋", "from_chapter": [0], "action": "站着，向公子华透露钱财情况", "expression": "得意"}, {"name": "左丞相昌平君", "gender": "男", "age": "中年", "clothes": "高领圆领汉服丞相朝服，主色调为紫色，搭配黑色，有黑色暗纹，头戴黑色丞相帽，配朝珠圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色丞相帽", "figure": "清瘦", "identity": "秦国左丞相", "other": "眉头常皱，面带忧虑", "from_chapter": [0], "action": "低头沉思，脚步缓慢地走着", "expression": "阴沉、懊悔"}, {"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站在军营中，眼神坚定地看着士兵们，双手抱臂", "expression": "自信"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "李信", "gender": "男", "age": "青年", "clothes": "高领圆领古代战甲，主色调为银色，搭配金色、白色，有金色花纹点缀，外披白色披风，头戴银色头盔，腰系战带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴银色头盔", "figure": "矫健", "identity": "秦国将领", "other": "眼神锐利，充满自信", "from_chapter": [0], "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "与公子华交谈", "expression": "认真"}, {"name": "冯艺", "gender": "男", "age": "青年", "clothes": "高领圆领汉服灰色长袍，主色调为灰色，袍上绣灰色花纹，腰间束一条灰色腰带，脚蹬黑色布鞋，头戴灰色毡帽圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴灰色毡帽", "figure": "中等身材", "identity": "公子华的下属，负责工匠安排", "other": "", "from_chapter": 100, "action": "主动站出来请缨，然后带着护卫准备出发", "expression": "坚定"}], "scene": "军营", "image_prompt": "中景，在军营场景中，一名双手抱臂、眼神坚定且自信的青年男子站着，望向士兵们；不远处宫殿里，一名手托下巴、陷入沉思的中年男子坐在那里，眼睛望向军营方向。站着的青年男子身穿主色调为紫色、搭配白色、绣有云纹的高领圆领汉服紫色锦袍，腰间束黑色腰带并佩戴玉佩，圆形衣领，黑色束发；坐着的中年男子身着主色调为黑色、搭配金色、绣满金色龙纹的高领圆领汉服帝王龙袍，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://p9-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/20250723113211E2AB629F297355C6370A-6931-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1753327938&x-signature=WoX71y%2FORJ1G3XUtb9kXdo3%2BX94%3D", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_154df3c6-671d-4638-a224-b2410a463fcb_1.25x_20250723_105751.wav", "audio_duration": 8.605333, "video_url": "", "story_board_id": 17, "subtitle": [{"subtitle_id": 1, "text": "这场戏让龚子华看到了军队的潜力，", "timestamp": "00:00:00,200 --> 00:00:02,720", "duration": 2.52, "char_count": 16, "start_time_s": 0.2, "end_time_s": 2.72, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 200, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "场"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 480, "text": "戏"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 760, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 920, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1080, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1200, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1360, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1560, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1680, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1800, "text": "军"}, {"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1920, "text": "队"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2080, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2200, "text": "潜"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2400, "text": "力"}], "keyword": "龚子华"}, {"subtitle_id": 2, "text": "也让他更加坚定自己的计划。", "timestamp": "00:00:02,720 --> 00:00:04,900", "duration": 2.18, "char_count": 13, "start_time_s": 2.72, "end_time_s": 4.9, "words": [{"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2720, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2880, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 3040, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3160, "text": "更"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3400, "text": "加"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3560, "text": "坚"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3720, "text": "定"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3880, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4040, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4160, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4280, "text": "计"}, {"attribute": {"event": "speech"}, "end_time": 4900, "start_time": 4400, "text": "划"}], "keyword": "计划"}, {"subtitle_id": 3, "text": "与此同时，", "timestamp": "00:00:05,000 --> 00:00:05,840", "duration": 0.84, "char_count": 5, "start_time_s": 5.0, "end_time_s": 5.84, "words": [{"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5000, "text": "与"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5160, "text": "此"}, {"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5280, "text": "同"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5440, "text": "时"}], "keyword": "此时"}, {"subtitle_id": 4, "text": "嬴政也在关注着龚子华的一举一动。", "timestamp": "00:00:05,840 --> 00:00:08,540", "duration": 2.7, "char_count": 16, "start_time_s": 5.84, "end_time_s": 8.54, "words": [{"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5840, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6040, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6240, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 6480, "start_time": 6360, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6480, "text": "关"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6640, "text": "注"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6800, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6920, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7120, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7280, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7440, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7600, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7800, "text": "举"}, {"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7920, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 8540, "start_time": 8080, "text": "动"}], "keyword": "嬴政"}], "keywords": ["龚子华", "计划", "此时", "嬴政"]}, {"chapter": 24, "story_board": "他知道，这个儿子正在悄然布局，试图将更多人拉入自己的阵营。而昌平君离开京城后，赢政也没有放松警惕，继续加强对楚系势力的监控。", "characters": [{"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "坐在宫殿中，手托下巴思考，眼神警惕，派人加强对楚系势力的监控", "expression": "严肃"}, {"name": "楚国代表熊犹", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为暗红色，搭配银色，袖口绣银色楚国云纹，头戴黑色方巾，腰间束腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "楚国代表", "other": "眼神狡黠，善于周旋", "from_chapter": [0], "action": "站着，向公子华透露钱财情况", "expression": "得意"}, {"name": "左丞相昌平君", "gender": "男", "age": "中年", "clothes": "高领圆领汉服丞相朝服，主色调为紫色，搭配黑色，有黑色暗纹，头戴黑色丞相帽，配朝珠圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色丞相帽", "figure": "清瘦", "identity": "秦国左丞相", "other": "眉头常皱，面带忧虑", "from_chapter": [0], "action": "低头沉思，脚步缓慢地走着", "expression": "阴沉、懊悔"}, {"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站在军营中，眼神坚定地看着士兵们，双手抱臂", "expression": "自信"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "李信", "gender": "男", "age": "青年", "clothes": "高领圆领古代战甲，主色调为银色，搭配金色、白色，有金色花纹点缀，外披白色披风，头戴银色头盔，腰系战带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴银色头盔", "figure": "矫健", "identity": "秦国将领", "other": "眼神锐利，充满自信", "from_chapter": [0], "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "与公子华交谈", "expression": "认真"}, {"name": "冯艺", "gender": "男", "age": "青年", "clothes": "高领圆领汉服灰色长袍，主色调为灰色，袍上绣灰色花纹，腰间束一条灰色腰带，脚蹬黑色布鞋，头戴灰色毡帽圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴灰色毡帽", "figure": "中等身材", "identity": "公子华的下属，负责工匠安排", "other": "", "from_chapter": 100, "action": "主动站出来请缨，然后带着护卫准备出发", "expression": "坚定"}], "scene": "勤政殿", "image_prompt": "中景，坐在宫殿中手托下巴思考、眼神警惕且表情严肃的中年男子，派人加强对楚系势力的监控，此男子魁梧且决策果断、坚持自己的主张。该男子穿着高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰，圆形衣领，黑色束发，背景是勤政殿, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241556_20250723_113236.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_7fc94cd6-c173-4105-a3ea-1151e0db5a1f_1.25x_20250723_105755.wav", "audio_duration": 10.6, "video_url": "", "story_board_id": 18, "subtitle": [{"subtitle_id": 1, "text": "他知道这个儿子正在悄然布局，", "timestamp": "00:00:00,160 --> 00:00:02,640", "duration": 2.48, "char_count": 14, "start_time_s": 0.16, "end_time_s": 2.64, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 400, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 800, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 1000, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1080, "text": "儿"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1240, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1400, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1560, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1760, "text": "悄"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1960, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2080, "text": "布"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2240, "text": "局"}], "keyword": "嬴政"}, {"subtitle_id": 2, "text": "试图将更多人拉入自己的阵营。", "timestamp": "00:00:02,640 --> 00:00:05,100", "duration": 2.46, "char_count": 14, "start_time_s": 2.64, "end_time_s": 5.1, "words": [{"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2640, "text": "试"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2800, "text": "图"}, {"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2920, "text": "将"}, {"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3080, "text": "更"}, {"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3280, "text": "多"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3440, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3680, "text": "拉"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3880, "text": "入"}, {"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4040, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4240, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4360, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4480, "text": "阵"}, {"attribute": {"event": "speech"}, "end_time": 5100, "start_time": 4640, "text": "营"}], "keyword": "阵营"}, {"subtitle_id": 3, "text": "而昌平军离开京城后，", "timestamp": "00:00:05,200 --> 00:00:06,840", "duration": 1.64, "char_count": 10, "start_time_s": 5.2, "end_time_s": 6.84, "words": [{"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5200, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5440, "text": "昌"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5560, "text": "平"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5680, "text": "军"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5840, "text": "离"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6000, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6160, "text": "京"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6280, "text": "城"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6440, "text": "后"}], "keyword": "昌平军"}, {"subtitle_id": 4, "text": "嬴政也没有放松警惕，", "timestamp": "00:00:06,840 --> 00:00:08,400", "duration": 1.56, "char_count": 10, "start_time_s": 6.84, "end_time_s": 8.4, "words": [{"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6840, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 7040, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7200, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7320, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7440, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7560, "text": "放"}, {"attribute": {"event": "speech"}, "end_time": 7840, "start_time": 7720, "text": "松"}, {"attribute": {"event": "speech"}, "end_time": 8000, "start_time": 7880, "text": "警"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8040, "text": "惕"}], "keyword": "嬴政"}, {"subtitle_id": 5, "text": "继续加强对楚系势力的监控。", "timestamp": "00:00:08,400 --> 00:00:10,500", "duration": 2.1, "char_count": 13, "start_time_s": 8.4, "end_time_s": 10.5, "words": [{"attribute": {"event": "speech"}, "end_time": 8560, "start_time": 8400, "text": "继"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8560, "text": "续"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8640, "text": "加"}, {"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8800, "text": "强"}, {"attribute": {"event": "speech"}, "end_time": 9120, "start_time": 8960, "text": "对"}, {"attribute": {"event": "speech"}, "end_time": 9320, "start_time": 9120, "text": "楚"}, {"attribute": {"event": "speech"}, "end_time": 9480, "start_time": 9360, "text": "系"}, {"attribute": {"event": "speech"}, "end_time": 9680, "start_time": 9480, "text": "势"}, {"attribute": {"event": "speech"}, "end_time": 9760, "start_time": 9680, "text": "力"}, {"attribute": {"event": "speech"}, "end_time": 9920, "start_time": 9760, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 10040, "start_time": 9920, "text": "监"}, {"attribute": {"event": "speech"}, "end_time": 10500, "start_time": 10080, "text": "控"}], "keyword": "楚系势力"}], "keywords": ["嬴政", "阵营", "昌平军", "嬴政", "楚系势力"]}, {"chapter": 24, "story_board": "最后，公子华还派人去太医院请来几位医师，为士兵准备跌打药。他始终相信，只有保障士兵的身体健康，才能让他们更好地执行任务。", "characters": [{"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "坐在宫殿中，手托下巴思考，眼神警惕，派人加强对楚系势力的监控", "expression": "严肃"}, {"name": "楚国代表熊犹", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为暗红色，搭配银色，袖口绣银色楚国云纹，头戴黑色方巾，腰间束腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "楚国代表", "other": "眼神狡黠，善于周旋", "from_chapter": [0], "action": "站着，向公子华透露钱财情况", "expression": "得意"}, {"name": "左丞相昌平君", "gender": "男", "age": "中年", "clothes": "高领圆领汉服丞相朝服，主色调为紫色，搭配黑色，有黑色暗纹，头戴黑色丞相帽，配朝珠圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色丞相帽", "figure": "清瘦", "identity": "秦国左丞相", "other": "眉头常皱，面带忧虑", "from_chapter": [0], "action": "低头沉思，脚步缓慢地走着", "expression": "阴沉、懊悔"}, {"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "派人去太医院请医师，站在军营中若有所思", "expression": "坚定"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "李信", "gender": "男", "age": "青年", "clothes": "高领圆领古代战甲，主色调为银色，搭配金色、白色，有金色花纹点缀，外披白色披风，头戴银色头盔，腰系战带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴银色头盔", "figure": "矫健", "identity": "秦国将领", "other": "眼神锐利，充满自信", "from_chapter": [0], "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "与公子华交谈", "expression": "认真"}, {"name": "冯艺", "gender": "男", "age": "青年", "clothes": "高领圆领汉服灰色长袍，主色调为灰色，袍上绣灰色花纹，腰间束一条灰色腰带，脚蹬黑色布鞋，头戴灰色毡帽圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴灰色毡帽", "figure": "中等身材", "identity": "公子华的下属，负责工匠安排", "other": "", "from_chapter": 100, "action": "主动站出来请缨，然后带着护卫准备出发", "expression": "坚定"}], "scene": "军营", "image_prompt": "中景，站在军营中正若有所思的青年男子，表情坚定，他刚派人去太医院请医师。男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发，身形挺拔, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://p9-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/20250723113250A0D21163BE2B87C71B40-9820-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1753327976&x-signature=Hc5qHkGOdfHDQPkOzD05mhJ%2FJ0E%3D", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_2f594023-41c2-40af-a053-7a8ad7140b57_1.25x_20250723_105814.wav", "audio_duration": 10.178667, "video_url": "", "story_board_id": 19, "subtitle": [{"subtitle_id": 1, "text": "最后，龚子华还派人去太医院", "timestamp": "00:00:00,200 --> 00:00:02,240", "duration": 2.04, "char_count": 13, "start_time_s": 0.2, "end_time_s": 2.24, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "最"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 680, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 840, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 960, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1080, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1240, "text": "派"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1400, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1560, "text": "去"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1680, "text": "太"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1880, "text": "医"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 1960, "text": "院"}], "keyword": "龚子华"}, {"subtitle_id": 2, "text": "请来几位医师，", "timestamp": "00:00:02,240 --> 00:00:03,200", "duration": 0.96, "char_count": 7, "start_time_s": 2.24, "end_time_s": 3.2, "words": [{"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2240, "text": "请"}, {"attribute": {"event": "speech"}, "end_time": 2520, "start_time": 2400, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2520, "text": "几"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2640, "text": "位"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2720, "text": "医"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 2840, "text": "师"}], "keyword": "医师"}, {"subtitle_id": 3, "text": "为士兵准备跌打药。", "timestamp": "00:00:03,200 --> 00:00:04,740", "duration": 1.54, "char_count": 9, "start_time_s": 3.2, "end_time_s": 4.74, "words": [{"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3200, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3400, "text": "士"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3560, "text": "兵"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3720, "text": "准"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3880, "text": "备"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 4000, "text": "跌"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4120, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 4740, "start_time": 4280, "text": "药"}], "keyword": "跌打药"}, {"subtitle_id": 4, "text": "他始终相信，", "timestamp": "00:00:04,840 --> 00:00:05,920", "duration": 1.08, "char_count": 6, "start_time_s": 4.84, "end_time_s": 5.92, "words": [{"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4840, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5040, "text": "始"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5240, "text": "终"}, {"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5360, "text": "相"}, {"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5560, "text": "信"}], "keyword": "相信"}, {"subtitle_id": 5, "text": "只有保障士兵的身体健康，", "timestamp": "00:00:05,920 --> 00:00:07,840", "duration": 1.92, "char_count": 12, "start_time_s": 5.92, "end_time_s": 7.84, "words": [{"attribute": {"event": "speech"}, "end_time": 6080, "start_time": 5920, "text": "只"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6080, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6160, "text": "保"}, {"attribute": {"event": "speech"}, "end_time": 6480, "start_time": 6360, "text": "障"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6520, "text": "士"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6680, "text": "兵"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6800, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6960, "text": "身"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7080, "text": "体"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7240, "text": "健"}, {"attribute": {"event": "speech"}, "end_time": 7840, "start_time": 7440, "text": "康"}], "keyword": "健康"}, {"subtitle_id": 6, "text": "才能让他们更好地执行任务。", "timestamp": "00:00:07,840 --> 00:00:09,860", "duration": 2.02, "char_count": 13, "start_time_s": 7.84, "end_time_s": 9.86, "words": [{"attribute": {"event": "speech"}, "end_time": 8000, "start_time": 7840, "text": "才"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 8000, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 8240, "start_time": 8120, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8240, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8400, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8480, "text": "更"}, {"attribute": {"event": "speech"}, "end_time": 8880, "start_time": 8680, "text": "好"}, {"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8880, "text": "地"}, {"attribute": {"event": "speech"}, "end_time": 9160, "start_time": 9000, "text": "执"}, {"attribute": {"event": "speech"}, "end_time": 9320, "start_time": 9160, "text": "行"}, {"attribute": {"event": "speech"}, "end_time": 9440, "start_time": 9320, "text": "任"}, {"attribute": {"event": "speech"}, "end_time": 9860, "start_time": 9440, "text": "务"}], "keyword": "任务"}], "keywords": ["龚子华", "医师", "跌打药", "相信", "健康", "任务"]}, {"chapter": 24, "story_board": "整件事下来，公子华不仅完成了任务，还为秦国的未来发展打下了坚实的基础。他用智慧和行动，一步步推动着秦帝国走向更辉煌的明天。", "characters": [{"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "坐在宫殿中，手托下巴思考，眼神警惕，派人加强对楚系势力的监控", "expression": "严肃"}, {"name": "楚国代表熊犹", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为暗红色，搭配银色，袖口绣银色楚国云纹，头戴黑色方巾，腰间束腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "楚国代表", "other": "眼神狡黠，善于周旋", "from_chapter": [0], "action": "站着，向公子华透露钱财情况", "expression": "得意"}, {"name": "左丞相昌平君", "gender": "男", "age": "中年", "clothes": "高领圆领汉服丞相朝服，主色调为紫色，搭配黑色，有黑色暗纹，头戴黑色丞相帽，配朝珠圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色丞相帽", "figure": "清瘦", "identity": "秦国左丞相", "other": "眉头常皱，面带忧虑", "from_chapter": [0], "action": "低头沉思，脚步缓慢地走着", "expression": "阴沉、懊悔"}, {"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "双手背后，昂首挺胸站立，眼神坚定望向远方", "expression": "自信从容"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "李信", "gender": "男", "age": "青年", "clothes": "高领圆领古代战甲，主色调为银色，搭配金色、白色，有金色花纹点缀，外披白色披风，头戴银色头盔，腰系战带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴银色头盔", "figure": "矫健", "identity": "秦国将领", "other": "眼神锐利，充满自信", "from_chapter": [0], "action": "看着盒子上的标志", "expression": "笑得合不拢嘴"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "与公子华交谈", "expression": "认真"}, {"name": "冯艺", "gender": "男", "age": "青年", "clothes": "高领圆领汉服灰色长袍，主色调为灰色，袍上绣灰色花纹，腰间束一条灰色腰带，脚蹬黑色布鞋，头戴灰色毡帽圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴灰色毡帽", "figure": "中等身材", "identity": "公子华的下属，负责工匠安排", "other": "", "from_chapter": 100, "action": "主动站出来请缨，然后带着护卫准备出发", "expression": "坚定"}], "scene": "中国古代建筑", "image_prompt": "中景，一个昂首挺胸、双手背后站立的青年男子，眼神坚定地望向远方，表情自信从容，身后是中国古代建筑。该男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241593_20250723_113314.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_f9920d47-e1c4-4b97-bdd9-679ee565cedc_1.25x_20250723_105821.wav", "audio_duration": 10.68, "video_url": "", "story_board_id": 20, "subtitle": [{"subtitle_id": 1, "text": "整件事下来，", "timestamp": "00:00:00,200 --> 00:00:01,040", "duration": 0.84, "char_count": 6, "start_time_s": 0.2, "end_time_s": 1.04, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "整"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "件"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 440, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 520, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 680, "text": "来"}], "keyword": "整件事"}, {"subtitle_id": 2, "text": "龚子华不仅完成了任务，", "timestamp": "00:00:01,040 --> 00:00:02,640", "duration": 1.6, "char_count": 11, "start_time_s": 1.04, "end_time_s": 2.64, "words": [{"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1040, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1240, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1360, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1520, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1680, "text": "仅"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1800, "text": "完"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1960, "text": "成"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2120, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2160, "text": "任"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2360, "text": "务"}], "keyword": "龚子华"}, {"subtitle_id": 3, "text": "还为秦国的未来发展", "timestamp": "00:00:02,640 --> 00:00:04,080", "duration": 1.44, "char_count": 9, "start_time_s": 2.64, "end_time_s": 4.08, "words": [{"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2640, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2840, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 3000, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3160, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3280, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3400, "text": "未"}, {"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3520, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3680, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3840, "text": "展"}], "keyword": "秦国"}, {"subtitle_id": 4, "text": "打下了坚实的基础，", "timestamp": "00:00:04,080 --> 00:00:05,660", "duration": 1.58, "char_count": 9, "start_time_s": 4.08, "end_time_s": 5.66, "words": [{"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4080, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 4440, "start_time": 4280, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4440, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4560, "text": "坚"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4760, "text": "实"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4920, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5040, "text": "基"}, {"attribute": {"event": "speech"}, "end_time": 5660, "start_time": 5200, "text": "础"}], "keyword": "基础"}, {"subtitle_id": 5, "text": "他用智慧和行动，", "timestamp": "00:00:05,760 --> 00:00:07,080", "duration": 1.32, "char_count": 8, "start_time_s": 5.76, "end_time_s": 7.08, "words": [{"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5760, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 6080, "start_time": 5960, "text": "用"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6120, "text": "智"}, {"attribute": {"event": "speech"}, "end_time": 6480, "start_time": 6320, "text": "慧"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6480, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6600, "text": "行"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6760, "text": "动"}], "keyword": "智慧"}, {"subtitle_id": 6, "text": "一步步推动着秦帝国", "timestamp": "00:00:07,080 --> 00:00:08,640", "duration": 1.56, "char_count": 9, "start_time_s": 7.08, "end_time_s": 8.64, "words": [{"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 7080, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7280, "text": "步"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7320, "text": "步"}, {"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7560, "text": "推"}, {"attribute": {"event": "speech"}, "end_time": 7880, "start_time": 7760, "text": "动"}, {"attribute": {"event": "speech"}, "end_time": 8000, "start_time": 7880, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 8160, "start_time": 8040, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8160, "text": "帝"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8320, "text": "国"}], "keyword": "秦帝国"}, {"subtitle_id": 7, "text": "走向更辉煌的明天。", "timestamp": "00:00:08,640 --> 00:00:10,340", "duration": 1.7, "char_count": 9, "start_time_s": 8.64, "end_time_s": 10.34, "words": [{"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8640, "text": "走"}, {"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8800, "text": "向"}, {"attribute": {"event": "speech"}, "end_time": 9160, "start_time": 8960, "text": "更"}, {"attribute": {"event": "speech"}, "end_time": 9400, "start_time": 9240, "text": "辉"}, {"attribute": {"event": "speech"}, "end_time": 9600, "start_time": 9440, "text": "煌"}, {"attribute": {"event": "speech"}, "end_time": 9720, "start_time": 9600, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 9840, "start_time": 9720, "text": "明"}, {"attribute": {"event": "speech"}, "end_time": 10340, "start_time": 9840, "text": "天"}], "keyword": "明天"}], "keywords": ["整件事", "龚子华", "秦国", "基础", "智慧", "秦帝国", "明天"]}]