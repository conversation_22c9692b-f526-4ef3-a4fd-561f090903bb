[{"chapter": 46, "story_board": "今天咱们讲的故事，开头就炸了！有人在朝堂上突然大喊：“韩王被擒！”整个朝廷瞬间沸腾，连秦王都激动得差点把茶杯打翻。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "", "expression": ""}, {"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "坐在朝堂上，差点把茶杯打翻", "expression": "激动"}, {"name": "清", "gender": "男", "age": "青年", "clothes": "高领圆领灰色商人长袍，主色调为灰色，搭配白色，有简单几何纹，束蓝色腰带，挂有钱袋圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "商人", "other": "在商会经营有方", "from_chapter": 70, "action": "", "expression": ""}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "辛胜", "gender": "男", "age": "青年", "clothes": "高领圆领黑色秦国士兵服饰，主色调为黑色，搭配灰色，有简单线条纹，束黑色腰带，背箭囊圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "秦国士兵", "other": "负责传递情报", "from_chapter": 70, "action": "", "expression": ""}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "韩王韩安", "gender": "男", "age": "中年", "clothes": "高领圆领韩国黄色锦袍，主色调为黄色，搭配红色，绣有云纹和花卉纹样，头戴王冠，腰间束黄色锦带，配玉珏圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "韩王", "other": "面容忧虑，心怀忐忑", "from_chapter": 52, "action": "", "expression": ""}, {"name": "斥候河", "gender": "男", "age": "青年", "clothes": "高领圆领青色秦制军装，主色调青色，搭配淡蓝色和白色，军装绣有草叶纹，腰系青色腰带，配有短刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "精瘦", "identity": "秦军斥候", "other": "熟悉战事情况", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，一个激动到差点把茶杯打翻、坐在皇宫大殿朝堂上的中年男子，他决策果断，坚持自己的主张且身形魁梧，身着高领圆领、主色调为黑色并搭配金色、绣满金色龙纹的帝王龙袍，腰间束着黑色镶金边腰带，头戴两侧垂有珠玉串饰的冕旒，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753204878_20250723_012119.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_6828275c-e1b8-4f1f-a9b0-4310c7036b21_1.25x_20250722_200000.wav", "audio_duration": 10.296, "video_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/videos/video_1753204962_20250723_012242.mp4", "story_board_id": 1, "subtitle": [{"subtitle_id": 1, "text": "今天咱们讲的故事开头就炸了，", "timestamp": "00:00:00,200 --> 00:00:02,740", "duration": 2.54, "char_count": 14, "start_time_s": 0.2, "end_time_s": 2.74, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 200, "text": "今"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 320, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 480, "text": "咱"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 600, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 680, "text": "讲"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 840, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 920, "text": "故"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1080, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1400, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1680, "text": "头"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1840, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2000, "text": "炸"}, {"attribute": {"event": "speech"}, "end_time": 2740, "start_time": 2200, "text": "了"}], "keyword": "韩王"}, {"subtitle_id": 2, "text": "有人在朝堂上突然大喊韩王被擒！", "timestamp": "00:00:02,800 --> 00:00:05,740", "duration": 2.94, "char_count": 15, "start_time_s": 2.8, "end_time_s": 5.74, "words": [{"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2800, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 3000, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3120, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3240, "text": "朝"}, {"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3440, "text": "堂"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3600, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3760, "text": "突"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3880, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4000, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4200, "text": "喊"}, {"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4640, "text": "韩"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4880, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5040, "text": "被"}, {"attribute": {"event": "speech"}, "end_time": 5740, "start_time": 5280, "text": "擒"}], "keyword": "朝堂"}, {"subtitle_id": 3, "text": "整个朝廷瞬间沸腾，", "timestamp": "00:00:05,920 --> 00:00:07,440", "duration": 1.52, "char_count": 9, "start_time_s": 5.92, "end_time_s": 7.44, "words": [{"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5920, "text": "整"}, {"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 6120, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6240, "text": "朝"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6400, "text": "廷"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6600, "text": "瞬"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6760, "text": "间"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6880, "text": "沸"}, {"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7080, "text": "腾"}], "keyword": "朝廷"}, {"subtitle_id": 4, "text": "连秦王都激动得差点把茶杯打翻。", "timestamp": "00:00:07,440 --> 00:00:09,940", "duration": 2.5, "char_count": 15, "start_time_s": 7.44, "end_time_s": 9.94, "words": [{"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7440, "text": "连"}, {"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7640, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7840, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7960, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 8280, "start_time": 8120, "text": "激"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8280, "text": "动"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8400, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 8760, "start_time": 8600, "text": "差"}, {"attribute": {"event": "speech"}, "end_time": 8880, "start_time": 8760, "text": "点"}, {"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8880, "text": "把"}, {"attribute": {"event": "speech"}, "end_time": 9200, "start_time": 9000, "text": "茶"}, {"attribute": {"event": "speech"}, "end_time": 9320, "start_time": 9200, "text": "杯"}, {"attribute": {"event": "speech"}, "end_time": 9480, "start_time": 9320, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 9940, "start_time": 9480, "text": "翻"}], "keyword": "秦王"}], "keywords": ["韩王", "朝堂", "朝廷", "秦王"]}, {"chapter": 46, "story_board": "这到底是怎么回事？为什么一个小小的韩国会突然被秦国拿下？背后又藏着什么不为人知的计划？", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站在朝堂上，手托下巴作思考状", "expression": "疑惑"}, {"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "坐在龙椅上，身体前倾", "expression": "思索"}, {"name": "清", "gender": "男", "age": "青年", "clothes": "高领圆领灰色商人长袍，主色调为灰色，搭配白色，有简单几何纹，束蓝色腰带，挂有钱袋圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "商人", "other": "在商会经营有方", "from_chapter": 70, "action": "", "expression": ""}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "辛胜", "gender": "男", "age": "青年", "clothes": "高领圆领黑色秦国士兵服饰，主色调为黑色，搭配灰色，有简单线条纹，束黑色腰带，背箭囊圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "秦国士兵", "other": "负责传递情报", "from_chapter": 70, "action": "", "expression": ""}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "韩王韩安", "gender": "男", "age": "中年", "clothes": "高领圆领韩国黄色锦袍，主色调为黄色，搭配红色，绣有云纹和花卉纹样，头戴王冠，腰间束黄色锦带，配玉珏圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "韩王", "other": "面容忧虑，心怀忐忑", "from_chapter": 52, "action": "", "expression": ""}, {"name": "斥候河", "gender": "男", "age": "青年", "clothes": "高领圆领青色秦制军装，主色调青色，搭配淡蓝色和白色，军装绣有草叶纹，腰系青色腰带，配有短刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "精瘦", "identity": "秦军斥候", "other": "熟悉战事情况", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，皇宫大殿中，坐在龙椅上身体前倾作思索状的中年男子，魁梧，决策果断，坚持自己的主张，穿着高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰，圆形衣领，黑色束发；旁边站着手托下巴作思考状、面带疑惑的青年男子，挺拔，言辞犀利，敢言善辩，穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753205008_20250723_012329.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_2cba1fc3-4325-429c-b908-e82c94f7b95d_1.25x_20250722_195947.wav", "audio_duration": 7.973333, "video_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/videos/video_1753205471_20250723_013112.mp4", "story_board_id": 2, "subtitle": [{"subtitle_id": 1, "text": "这到底是怎么回事？", "timestamp": "00:00:00,160 --> 00:00:01,700", "duration": 1.54, "char_count": 9, "start_time_s": 0.16, "end_time_s": 1.7, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 280, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 480, "text": "底"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 600, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 760, "text": "怎"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 880, "text": "么"}, {"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 1000, "text": "回"}, {"attribute": {"event": "speech"}, "end_time": 1700, "start_time": 1160, "text": "事"}], "keyword": "怎么回事"}, {"subtitle_id": 2, "text": "为什么一个小小的韩国", "timestamp": "00:00:01,760 --> 00:00:03,400", "duration": 1.64, "char_count": 10, "start_time_s": 1.76, "end_time_s": 3.4, "words": [{"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1760, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2040, "text": "什"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2160, "text": "么"}, {"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2200, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2320, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2440, "text": "小"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2480, "text": "小"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2800, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2920, "text": "韩"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3080, "text": "国"}], "keyword": "韩国"}, {"subtitle_id": 3, "text": "会突然被秦国拿下？", "timestamp": "00:00:03,400 --> 00:00:05,100", "duration": 1.7, "char_count": 9, "start_time_s": 3.4, "end_time_s": 5.1, "words": [{"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3400, "text": "会"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3600, "text": "突"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3760, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3880, "text": "被"}, {"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4080, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 4440, "start_time": 4280, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4440, "text": "拿"}, {"attribute": {"event": "speech"}, "end_time": 5100, "start_time": 4600, "text": "下"}], "keyword": "秦国"}, {"subtitle_id": 4, "text": "背后又藏着什么不为人知的计划？", "timestamp": "00:00:05,200 --> 00:00:07,620", "duration": 2.42, "char_count": 15, "start_time_s": 5.2, "end_time_s": 7.62, "words": [{"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5200, "text": "背"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5360, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5520, "text": "又"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5720, "text": "藏"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5920, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6040, "text": "什"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6200, "text": "么"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6320, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6440, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6560, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6760, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6880, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7120, "start_time": 7000, "text": "计"}, {"attribute": {"event": "speech"}, "end_time": 7620, "start_time": 7120, "text": "划"}], "keyword": "计划"}], "keywords": ["怎么回事", "韩国", "秦国", "计划"]}, {"chapter": 46, "story_board": "故事发生在秦王嬴政统治时期，主角是公子华，也就是秦王的儿子。他是个很有头脑的年轻人，平时喜欢四处走动，结交朋友。这一天，他和一位叫清的客人一起在酒楼吃饭。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "与清坐在酒楼吃饭、交谈，听夸赞后有点不好意思地笑", "expression": "轻松、略带羞涩"}, {"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "坐在龙椅上，身体前倾", "expression": "思索"}, {"name": "清", "gender": "男", "age": "青年", "clothes": "高领圆领灰色商人长袍，主色调为灰色，搭配白色，有简单几何纹，束蓝色腰带，挂有钱袋圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "商人", "other": "在商会经营有方", "from_chapter": 70, "action": "与公子华在酒楼吃饭、交谈，夸赞公子华", "expression": "真诚"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "辛胜", "gender": "男", "age": "青年", "clothes": "高领圆领黑色秦国士兵服饰，主色调为黑色，搭配灰色，有简单线条纹，束黑色腰带，背箭囊圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "秦国士兵", "other": "负责传递情报", "from_chapter": 70, "action": "", "expression": ""}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "韩王韩安", "gender": "男", "age": "中年", "clothes": "高领圆领韩国黄色锦袍，主色调为黄色，搭配红色，绣有云纹和花卉纹样，头戴王冠，腰间束黄色锦带，配玉珏圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "韩王", "other": "面容忧虑，心怀忐忑", "from_chapter": 52, "action": "", "expression": ""}, {"name": "斥候河", "gender": "男", "age": "青年", "clothes": "高领圆领青色秦制军装，主色调青色，搭配淡蓝色和白色，军装绣有草叶纹，腰系青色腰带，配有短刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "精瘦", "identity": "秦军斥候", "other": "熟悉战事情况", "from_chapter": [0], "action": "", "expression": ""}], "scene": "酒楼", "image_prompt": "中景，在酒楼里，一个略带羞涩、轻松笑着的青年男子坐在桌前吃饭，他之前听了夸赞后有点不好意思。旁边一个真诚的青年男子坐在桌前。前者穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发；后者穿着高领圆领灰色商人长袍，主色调为灰色，搭配白色，有简单几何纹，束蓝色腰带，挂有钱袋，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753205535_20250723_013215.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_71f4a69b-0ce5-4387-a9ae-381b8bc4daef_1.25x_20250722_195955.wav", "audio_duration": 13.368, "video_url": "", "story_board_id": 3, "subtitle": [{"subtitle_id": 1, "text": "故事发生在秦王嬴政统治时期，", "timestamp": "00:00:00,160 --> 00:00:02,400", "duration": 2.24, "char_count": 14, "start_time_s": 0.16, "end_time_s": 2.4, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 160, "text": "故"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 320, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 400, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 560, "text": "生"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 680, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 880, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1040, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1200, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1400, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1560, "text": "统"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1760, "text": "治"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1880, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2040, "text": "期"}], "keyword": "秦王嬴政"}, {"subtitle_id": 2, "text": "主角是龚子华，", "timestamp": "00:00:02,400 --> 00:00:03,560", "duration": 1.16, "char_count": 7, "start_time_s": 2.4, "end_time_s": 3.56, "words": [{"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2400, "text": "主"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2600, "text": "角"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2760, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2880, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3040, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3200, "text": "华"}], "keyword": "龚子华"}, {"subtitle_id": 3, "text": "也就是秦王的儿子。", "timestamp": "00:00:03,560 --> 00:00:05,140", "duration": 1.58, "char_count": 9, "start_time_s": 3.56, "end_time_s": 5.14, "words": [{"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3560, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3720, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3880, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4040, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4240, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4400, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4480, "text": "儿"}, {"attribute": {"event": "speech"}, "end_time": 5140, "start_time": 4680, "text": "子"}], "keyword": "秦王"}, {"subtitle_id": 4, "text": "他是个很有头脑的年轻人，", "timestamp": "00:00:05,200 --> 00:00:07,000", "duration": 1.8, "char_count": 12, "start_time_s": 5.2, "end_time_s": 7.0, "words": [{"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5400, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5520, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 5760, "start_time": 5640, "text": "很"}, {"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5760, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5920, "text": "头"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6120, "text": "脑"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6280, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6360, "text": "年"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6520, "text": "轻"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6640, "text": "人"}], "keyword": "年轻人"}, {"subtitle_id": 5, "text": "平时喜欢四处走动，", "timestamp": "00:00:07,000 --> 00:00:08,480", "duration": 1.48, "char_count": 9, "start_time_s": 7.0, "end_time_s": 8.48, "words": [{"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 7000, "text": "平"}, {"attribute": {"event": "speech"}, "end_time": 7360, "start_time": 7200, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 7520, "start_time": 7360, "text": "喜"}, {"attribute": {"event": "speech"}, "end_time": 7640, "start_time": 7520, "text": "欢"}, {"attribute": {"event": "speech"}, "end_time": 7880, "start_time": 7680, "text": "四"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7880, "text": "处"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8040, "text": "走"}, {"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8200, "text": "动"}], "keyword": "走动"}, {"subtitle_id": 6, "text": "结交朋友。", "timestamp": "00:00:08,480 --> 00:00:09,460", "duration": 0.98, "char_count": 5, "start_time_s": 8.48, "end_time_s": 9.46, "words": [{"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8480, "text": "结"}, {"attribute": {"event": "speech"}, "end_time": 8840, "start_time": 8680, "text": "交"}, {"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8840, "text": "朋"}, {"attribute": {"event": "speech"}, "end_time": 9460, "start_time": 9000, "text": "友"}], "keyword": "朋友"}, {"subtitle_id": 7, "text": "这一天，他和一位较轻的客人", "timestamp": "00:00:09,600 --> 00:00:11,640", "duration": 2.04, "char_count": 13, "start_time_s": 9.6, "end_time_s": 11.64, "words": [{"attribute": {"event": "speech"}, "end_time": 9760, "start_time": 9600, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 9840, "start_time": 9760, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 10040, "start_time": 9840, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 10440, "start_time": 10240, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 10560, "start_time": 10440, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 10640, "start_time": 10560, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 10800, "start_time": 10640, "text": "位"}, {"attribute": {"event": "speech"}, "end_time": 11000, "start_time": 10840, "text": "较"}, {"attribute": {"event": "speech"}, "end_time": 11160, "start_time": 11040, "text": "轻"}, {"attribute": {"event": "speech"}, "end_time": 11280, "start_time": 11160, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 11440, "start_time": 11280, "text": "客"}, {"attribute": {"event": "speech"}, "end_time": 11640, "start_time": 11440, "text": "人"}], "keyword": "客人"}, {"subtitle_id": 8, "text": "一起在酒楼吃饭。", "timestamp": "00:00:11,640 --> 00:00:13,060", "duration": 1.42, "char_count": 8, "start_time_s": 11.64, "end_time_s": 13.06, "words": [{"attribute": {"event": "speech"}, "end_time": 11840, "start_time": 11640, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 12000, "start_time": 11840, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 12160, "start_time": 12000, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 12320, "start_time": 12160, "text": "酒"}, {"attribute": {"event": "speech"}, "end_time": 12440, "start_time": 12320, "text": "楼"}, {"attribute": {"event": "speech"}, "end_time": 12600, "start_time": 12440, "text": "吃"}, {"attribute": {"event": "speech"}, "end_time": 13060, "start_time": 12600, "text": "饭"}], "keyword": "酒楼"}], "keywords": ["秦王嬴政", "龚子华", "秦王", "年轻人", "走动", "朋友", "客人", "酒楼"]}, {"chapter": 46, "story_board": "清来自巴蜀，对公子华早有耳闻，一见面就夸他有钱、有才、还爱国。公子华听着这些话，有点不好意思地笑了：“你这么说我都不好意思了。”两人边吃边聊，气氛很轻松。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "坐在酒楼餐桌旁，边吃边聊，听着清夸赞时露出不好意思的笑容", "expression": "羞涩"}, {"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "坐在龙椅上，身体前倾", "expression": "思索"}, {"name": "清", "gender": "男", "age": "青年", "clothes": "高领圆领灰色商人长袍，主色调为灰色，搭配白色，有简单几何纹，束蓝色腰带，挂有钱袋圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "商人", "other": "在商会经营有方", "from_chapter": 70, "action": "坐在酒楼餐桌旁，边吃边夸赞公子华", "expression": "真诚"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "辛胜", "gender": "男", "age": "青年", "clothes": "高领圆领黑色秦国士兵服饰，主色调为黑色，搭配灰色，有简单线条纹，束黑色腰带，背箭囊圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "秦国士兵", "other": "负责传递情报", "from_chapter": 70, "action": "", "expression": ""}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "韩王韩安", "gender": "男", "age": "中年", "clothes": "高领圆领韩国黄色锦袍，主色调为黄色，搭配红色，绣有云纹和花卉纹样，头戴王冠，腰间束黄色锦带，配玉珏圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "韩王", "other": "面容忧虑，心怀忐忑", "from_chapter": 52, "action": "", "expression": ""}, {"name": "斥候河", "gender": "男", "age": "青年", "clothes": "高领圆领青色秦制军装，主色调青色，搭配淡蓝色和白色，军装绣有草叶纹，腰系青色腰带，配有短刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "精瘦", "identity": "秦军斥候", "other": "熟悉战事情况", "from_chapter": [0], "action": "", "expression": ""}], "scene": "酒楼", "image_prompt": "中景，在酒楼餐桌旁，一位羞涩地露出不好意思笑容的青年男子边吃边听着夸赞，另一位真诚的青年男子坐在对面边吃边向他投以欣赏的神情。主要人物是前者，他挺拔且敢言善辩。前者穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发；后者中等身材，穿着高领圆领灰色商人长袍，主色调为灰色，搭配白色，有简单几何纹，束蓝色腰带，挂有钱袋，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753205605_20250723_013325.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_459ee3b7-d1dd-4791-8123-a19adb9de074_1.25x_20250722_200110.wav", "audio_duration": 12.408, "video_url": "", "story_board_id": 4, "subtitle": [{"subtitle_id": 1, "text": "青来自巴蜀，", "timestamp": "00:00:00,200 --> 00:00:01,120", "duration": 0.92, "char_count": 6, "start_time_s": 0.2, "end_time_s": 1.12, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "青"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 440, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 560, "text": "巴"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 760, "text": "蜀"}], "keyword": "青"}, {"subtitle_id": 2, "text": "对龚子华早有耳闻，", "timestamp": "00:00:01,120 --> 00:00:02,520", "duration": 1.4, "char_count": 9, "start_time_s": 1.12, "end_time_s": 2.52, "words": [{"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 1120, "text": "对"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1280, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1440, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1560, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1760, "text": "早"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1920, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2040, "text": "耳"}, {"attribute": {"event": "speech"}, "end_time": 2520, "start_time": 2200, "text": "闻"}], "keyword": "龚子华"}, {"subtitle_id": 3, "text": "一见面就夸他有钱有才还爱国。", "timestamp": "00:00:02,520 --> 00:00:05,180", "duration": 2.66, "char_count": 14, "start_time_s": 2.52, "end_time_s": 5.18, "words": [{"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2520, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2760, "text": "见"}, {"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2880, "text": "面"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3080, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3240, "text": "夸"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3400, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3640, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3840, "text": "钱"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4000, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4200, "text": "才"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4400, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4520, "text": "爱"}, {"attribute": {"event": "speech"}, "end_time": 5180, "start_time": 4720, "text": "国"}], "keyword": "有钱有才"}, {"subtitle_id": 4, "text": "龚子华听着这些话，", "timestamp": "00:00:05,280 --> 00:00:06,640", "duration": 1.36, "char_count": 9, "start_time_s": 5.28, "end_time_s": 6.64, "words": [{"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5280, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5480, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 5760, "start_time": 5600, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5800, "text": "听"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5960, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6040, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6160, "text": "些"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6280, "text": "话"}], "keyword": "龚子华"}, {"subtitle_id": 5, "text": "有点不好意思的笑了你这么说", "timestamp": "00:00:06,640 --> 00:00:08,560", "duration": 1.92, "char_count": 13, "start_time_s": 6.64, "end_time_s": 8.56, "words": [{"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6640, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6840, "text": "点"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6960, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 7080, "text": "好"}, {"attribute": {"event": "speech"}, "end_time": 7360, "start_time": 7200, "text": "意"}, {"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7360, "text": "思"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7440, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7560, "text": "笑"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7720, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 7960, "text": "你"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8200, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 8440, "start_time": 8320, "text": "么"}, {"attribute": {"event": "speech"}, "end_time": 8560, "start_time": 8440, "text": "说"}], "keyword": "笑"}, {"subtitle_id": 6, "text": "我都不好意思了。", "timestamp": "00:00:08,560 --> 00:00:09,740", "duration": 1.18, "char_count": 8, "start_time_s": 8.56, "end_time_s": 9.74, "words": [{"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8560, "text": "我"}, {"attribute": {"event": "speech"}, "end_time": 8760, "start_time": 8640, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 8880, "start_time": 8760, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8880, "text": "好"}, {"attribute": {"event": "speech"}, "end_time": 9160, "start_time": 9000, "text": "意"}, {"attribute": {"event": "speech"}, "end_time": 9320, "start_time": 9160, "text": "思"}, {"attribute": {"event": "speech"}, "end_time": 9740, "start_time": 9320, "text": "了"}], "keyword": "不好意思"}, {"subtitle_id": 7, "text": "两人边吃边聊，", "timestamp": "00:00:09,800 --> 00:00:10,920", "duration": 1.12, "char_count": 7, "start_time_s": 9.8, "end_time_s": 10.92, "words": [{"attribute": {"event": "speech"}, "end_time": 10000, "start_time": 9800, "text": "两"}, {"attribute": {"event": "speech"}, "end_time": 10080, "start_time": 10000, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 10240, "start_time": 10080, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 10400, "start_time": 10240, "text": "吃"}, {"attribute": {"event": "speech"}, "end_time": 10520, "start_time": 10400, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 10920, "start_time": 10520, "text": "聊"}], "keyword": "两人"}, {"subtitle_id": 8, "text": "气氛很轻松。", "timestamp": "00:00:10,920 --> 00:00:12,060", "duration": 1.14, "char_count": 6, "start_time_s": 10.92, "end_time_s": 12.06, "words": [{"attribute": {"event": "speech"}, "end_time": 11080, "start_time": 10920, "text": "气"}, {"attribute": {"event": "speech"}, "end_time": 11200, "start_time": 11080, "text": "氛"}, {"attribute": {"event": "speech"}, "end_time": 11400, "start_time": 11200, "text": "很"}, {"attribute": {"event": "speech"}, "end_time": 11600, "start_time": 11440, "text": "轻"}, {"attribute": {"event": "speech"}, "end_time": 12060, "start_time": 11600, "text": "松"}], "keyword": "气氛"}], "keywords": ["青", "龚子华", "有钱有才", "龚子华", "笑", "不好意思", "两人", "气氛"]}, {"chapter": 46, "story_board": "清说他们家族这次来咸阳，就是想让年轻人多看看外面的世界。公子华听了也很高兴，觉得这是个难得的机会。饭后，公子华亲自送清回驿站，然后回到东宫。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "先是坐在酒楼与清交谈，脸上带着高兴的神情；饭后亲自送清回驿站，之后回到东宫", "expression": "高兴"}, {"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "坐在龙椅上，身体前倾", "expression": "思索"}, {"name": "清", "gender": "男", "age": "青年", "clothes": "高领圆领灰色商人长袍，主色调为灰色，搭配白色，有简单几何纹，束蓝色腰带，挂有钱袋圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "商人", "other": "在商会经营有方", "from_chapter": 70, "action": "坐在酒楼与公子华交谈，向公子华讲述家族来咸阳的目的", "expression": "自然"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "辛胜", "gender": "男", "age": "青年", "clothes": "高领圆领黑色秦国士兵服饰，主色调为黑色，搭配灰色，有简单线条纹，束黑色腰带，背箭囊圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "秦国士兵", "other": "负责传递情报", "from_chapter": 70, "action": "", "expression": ""}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "韩王韩安", "gender": "男", "age": "中年", "clothes": "高领圆领韩国黄色锦袍，主色调为黄色，搭配红色，绣有云纹和花卉纹样，头戴王冠，腰间束黄色锦带，配玉珏圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "韩王", "other": "面容忧虑，心怀忐忑", "from_chapter": 52, "action": "", "expression": ""}, {"name": "斥候河", "gender": "男", "age": "青年", "clothes": "高领圆领青色秦制军装，主色调青色，搭配淡蓝色和白色，军装绣有草叶纹，腰系青色腰带，配有短刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "精瘦", "identity": "秦军斥候", "other": "熟悉战事情况", "from_chapter": [0], "action": "", "expression": ""}], "scene": "酒楼", "image_prompt": "中景，在古色古香的酒楼里，两个青年男子相对而坐，脸上带着高兴神情的青年男子身体微微前倾，姿态挺拔；另一个青年男子神情自然，身体放松。神情高兴的青年男子身着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发；神情自然的青年男子穿着高领圆领灰色商人长袍，主色调为灰色，搭配白色，有简单几何纹，束蓝色腰带，挂有钱袋，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753205681_20250723_013441.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_bbb18d36-1214-4add-8df3-1d63ce48d5e5_1.25x_20250722_200105.wav", "audio_duration": 12.136, "video_url": "", "story_board_id": 5, "subtitle": [{"subtitle_id": 1, "text": "听说他们家族这次来咸阳，", "timestamp": "00:00:00,240 --> 00:00:01,880", "duration": 1.64, "char_count": 12, "start_time_s": 0.24, "end_time_s": 1.88, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 240, "text": "听"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 320, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 480, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 600, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 680, "text": "家"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 840, "text": "族"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 960, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1120, "text": "次"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1240, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1360, "text": "咸"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1520, "text": "阳"}], "keyword": "咸阳"}, {"subtitle_id": 2, "text": "就是想让年轻人多看看外面的世界，", "timestamp": "00:00:01,880 --> 00:00:04,460", "duration": 2.58, "char_count": 16, "start_time_s": 1.88, "end_time_s": 4.46, "words": [{"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1880, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2080, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2200, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2320, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2440, "text": "年"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2600, "text": "轻"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2720, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 2960, "text": "多"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3160, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3200, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3480, "text": "外"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3640, "text": "面"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3760, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3840, "text": "世"}, {"attribute": {"event": "speech"}, "end_time": 4460, "start_time": 4000, "text": "界"}], "keyword": "世界"}, {"subtitle_id": 3, "text": "龚子华听了也很高兴，", "timestamp": "00:00:04,560 --> 00:00:06,120", "duration": 1.56, "char_count": 10, "start_time_s": 4.56, "end_time_s": 6.12, "words": [{"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4560, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4760, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 4920, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5120, "text": "听"}, {"attribute": {"event": "speech"}, "end_time": 5320, "start_time": 5240, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5320, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5440, "text": "很"}, {"attribute": {"event": "speech"}, "end_time": 5760, "start_time": 5600, "text": "高"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5760, "text": "兴"}], "keyword": "龚子华"}, {"subtitle_id": 4, "text": "觉得这是个难得的机会。", "timestamp": "00:00:06,120 --> 00:00:07,860", "duration": 1.74, "char_count": 11, "start_time_s": 6.12, "end_time_s": 7.86, "words": [{"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6120, "text": "觉"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6280, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6400, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6560, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6680, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6800, "text": "难"}, {"attribute": {"event": "speech"}, "end_time": 7120, "start_time": 6960, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7120, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7360, "start_time": 7240, "text": "机"}, {"attribute": {"event": "speech"}, "end_time": 7860, "start_time": 7360, "text": "会"}], "keyword": "机会"}, {"subtitle_id": 5, "text": "饭后，龚子华亲自送青回驿站，", "timestamp": "00:00:07,960 --> 00:00:10,480", "duration": 2.52, "char_count": 14, "start_time_s": 7.96, "end_time_s": 10.48, "words": [{"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 7960, "text": "饭"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8120, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8520, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 8840, "start_time": 8680, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 9040, "start_time": 8840, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 9280, "start_time": 9120, "text": "亲"}, {"attribute": {"event": "speech"}, "end_time": 9480, "start_time": 9280, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 9640, "start_time": 9480, "text": "送"}, {"attribute": {"event": "speech"}, "end_time": 9800, "start_time": 9680, "text": "青"}, {"attribute": {"event": "speech"}, "end_time": 10000, "start_time": 9840, "text": "回"}, {"attribute": {"event": "speech"}, "end_time": 10120, "start_time": 10000, "text": "驿"}, {"attribute": {"event": "speech"}, "end_time": 10480, "start_time": 10120, "text": "站"}], "keyword": "青回"}, {"subtitle_id": 6, "text": "然后回到东宫。", "timestamp": "00:00:10,480 --> 00:00:11,780", "duration": 1.3, "char_count": 7, "start_time_s": 10.48, "end_time_s": 11.78, "words": [{"attribute": {"event": "speech"}, "end_time": 10680, "start_time": 10480, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 10840, "start_time": 10680, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 11040, "start_time": 10840, "text": "回"}, {"attribute": {"event": "speech"}, "end_time": 11200, "start_time": 11040, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 11320, "start_time": 11200, "text": "东"}, {"attribute": {"event": "speech"}, "end_time": 11780, "start_time": 11320, "text": "宫"}], "keyword": "东宫"}], "keywords": ["咸阳", "世界", "龚子华", "机会", "青回", "东宫"]}, {"chapter": 46, "story_board": "第二天早朝，他刚进勤政殿，就被蒙恬拦住了。蒙恬拿着一支毛笔，一脸疑惑：“这毛笔怎么来的？”公子华笑着说：“缘分到了你就知道了。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "刚走进勤政殿，面对蒙恬笑着说话", "expression": "微笑"}, {"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "坐在龙椅上，身体前倾", "expression": "思索"}, {"name": "清", "gender": "男", "age": "青年", "clothes": "高领圆领灰色商人长袍，主色调为灰色，搭配白色，有简单几何纹，束蓝色腰带，挂有钱袋圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "商人", "other": "在商会经营有方", "from_chapter": 70, "action": "坐在酒楼与公子华交谈，向公子华讲述家族来咸阳的目的", "expression": "自然"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "拿着一支毛笔拦住公子华，询问毛笔来源", "expression": "疑惑"}, {"name": "辛胜", "gender": "男", "age": "青年", "clothes": "高领圆领黑色秦国士兵服饰，主色调为黑色，搭配灰色，有简单线条纹，束黑色腰带，背箭囊圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "秦国士兵", "other": "负责传递情报", "from_chapter": 70, "action": "", "expression": ""}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "韩王韩安", "gender": "男", "age": "中年", "clothes": "高领圆领韩国黄色锦袍，主色调为黄色，搭配红色，绣有云纹和花卉纹样，头戴王冠，腰间束黄色锦带，配玉珏圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "韩王", "other": "面容忧虑，心怀忐忑", "from_chapter": 52, "action": "", "expression": ""}, {"name": "斥候河", "gender": "男", "age": "青年", "clothes": "高领圆领青色秦制军装，主色调青色，搭配淡蓝色和白色，军装绣有草叶纹，腰系青色腰带，配有短刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "精瘦", "identity": "秦军斥候", "other": "熟悉战事情况", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿中，刚走进来面带微笑的青年男子被另一个手持毛笔、满脸疑惑的青年男子拦住。被拦的青年男子穿着高领圆领的紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带并佩戴玉佩，圆形衣领，黑色束发；拦住人的青年男子穿着秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753205783_20250723_013624.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_9fa245e5-a3c1-405e-890d-d0bbf6f6cb38_1.25x_20250722_200115.wav", "audio_duration": 10.717333, "video_url": "", "story_board_id": 6, "subtitle": [{"subtitle_id": 1, "text": "第二天早朝，", "timestamp": "00:00:00,160 --> 00:00:01,080", "duration": 0.92, "char_count": 6, "start_time_s": 0.16, "end_time_s": 1.08, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "第"}, {"attribute": {"event": "speech"}, "end_time": 360, "start_time": 280, "text": "二"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 360, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 520, "text": "早"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 680, "text": "朝"}], "keyword": "早朝"}, {"subtitle_id": 2, "text": "他刚进秦正殿，", "timestamp": "00:00:01,080 --> 00:00:02,200", "duration": 1.12, "char_count": 7, "start_time_s": 1.08, "end_time_s": 2.2, "words": [{"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1080, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1240, "text": "刚"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1440, "text": "进"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1560, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1720, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 1880, "text": "殿"}], "keyword": "秦正殿"}, {"subtitle_id": 3, "text": "就被蒙恬拦住了。", "timestamp": "00:00:02,200 --> 00:00:03,700", "duration": 1.5, "char_count": 8, "start_time_s": 2.2, "end_time_s": 3.7, "words": [{"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2200, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2360, "text": "被"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2520, "text": "蒙"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2720, "text": "恬"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2880, "text": "拦"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3080, "text": "住"}, {"attribute": {"event": "speech"}, "end_time": 3700, "start_time": 3200, "text": "了"}], "keyword": "蒙恬"}, {"subtitle_id": 4, "text": "蒙恬拿着一支毛笔，", "timestamp": "00:00:03,760 --> 00:00:05,120", "duration": 1.36, "char_count": 9, "start_time_s": 3.76, "end_time_s": 5.12, "words": [{"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3760, "text": "蒙"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 3960, "text": "恬"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4120, "text": "拿"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4280, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4400, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4520, "text": "支"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4640, "text": "毛"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 4840, "text": "笔"}], "keyword": "毛笔"}, {"subtitle_id": 5, "text": "一脸疑惑这毛笔怎么来的？", "timestamp": "00:00:05,120 --> 00:00:07,500", "duration": 2.38, "char_count": 12, "start_time_s": 5.12, "end_time_s": 7.5, "words": [{"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5120, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5360, "text": "脸"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5480, "text": "疑"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5720, "text": "惑"}, {"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 6120, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6240, "text": "毛"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6440, "text": "笔"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6600, "text": "怎"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6760, "text": "么"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6840, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 7500, "start_time": 7040, "text": "的"}], "keyword": "毛笔"}, {"subtitle_id": 6, "text": "公子华笑着说缘分到了你就知道了。", "timestamp": "00:00:07,640 --> 00:00:10,420", "duration": 2.78, "char_count": 16, "start_time_s": 7.64, "end_time_s": 10.42, "words": [{"attribute": {"event": "speech"}, "end_time": 7840, "start_time": 7640, "text": "公"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7840, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 7960, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 8280, "start_time": 8160, "text": "笑"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8280, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 8560, "start_time": 8400, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8760, "text": "缘"}, {"attribute": {"event": "speech"}, "end_time": 9120, "start_time": 8960, "text": "分"}, {"attribute": {"event": "speech"}, "end_time": 9320, "start_time": 9120, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 9400, "start_time": 9320, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 9520, "start_time": 9400, "text": "你"}, {"attribute": {"event": "speech"}, "end_time": 9640, "start_time": 9520, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 9840, "start_time": 9640, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 9960, "start_time": 9840, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 10420, "start_time": 9960, "text": "了"}], "keyword": "公子华"}], "keywords": ["早朝", "秦正殿", "蒙恬", "毛笔", "毛笔", "公子华"]}, {"chapter": 46, "story_board": "”蒙恬一脸无奈，但也没再多问。这时，一个新面孔出现在朝堂上——辛胜，他是巴蜀大营的将领，刚刚被调回咸阳述职。公子华看了他一眼，辛胜也回了个礼，两人之间似乎有种莫名的默契。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站在朝堂上，笑着面对蒙恬，转头看向辛胜", "expression": "微笑"}, {"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "坐在龙椅上，身体前倾", "expression": "思索"}, {"name": "清", "gender": "男", "age": "青年", "clothes": "高领圆领灰色商人长袍，主色调为灰色，搭配白色，有简单几何纹，束蓝色腰带，挂有钱袋圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "商人", "other": "在商会经营有方", "from_chapter": 70, "action": "坐在酒楼与公子华交谈，向公子华讲述家族来咸阳的目的", "expression": "自然"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "拿着毛笔，站在朝堂上，面对公子华的回答后双手摊开", "expression": "无奈"}, {"name": "辛胜", "gender": "男", "age": "青年", "clothes": "高领圆领黑色秦国士兵服饰，主色调为黑色，搭配灰色，有简单线条纹，束黑色腰带，背箭囊圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "秦国士兵", "other": "负责传递情报", "from_chapter": 70, "action": "走进朝堂，看到公子华后拱手回礼", "expression": "严肃"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "韩王韩安", "gender": "男", "age": "中年", "clothes": "高领圆领韩国黄色锦袍，主色调为黄色，搭配红色，绣有云纹和花卉纹样，头戴王冠，腰间束黄色锦带，配玉珏圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "韩王", "other": "面容忧虑，心怀忐忑", "from_chapter": 52, "action": "", "expression": ""}, {"name": "斥候河", "gender": "男", "age": "青年", "clothes": "高领圆领青色秦制军装，主色调青色，搭配淡蓝色和白色，军装绣有草叶纹，腰系青色腰带，配有短刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "精瘦", "identity": "秦军斥候", "other": "熟悉战事情况", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，皇宫大殿中，双手摊开、一脸无奈的青年男子站着，他身着秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带，有着圆形衣领，黑色束发，身材高大；旁边微笑着转头看向他人的青年男子挺拔地站着，身着高领圆领紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，有着圆形衣领，黑色束发；一位严肃的青年男子走进，拱手回礼，他身着高领圆领黑色秦国士兵服饰，主色调为黑色，搭配灰色，有简单线条纹，束黑色腰带，背箭囊，有着圆形衣领，黑色短发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753205919_20250723_013839.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_ea9b6e0c-ac00-4129-9863-f364c488065f_1.25x_20250722_200046.wav", "audio_duration": 14.632, "video_url": "", "story_board_id": 7, "subtitle": [{"subtitle_id": 1, "text": "蒙恬一脸无奈，", "timestamp": "00:00:00,160 --> 00:00:01,360", "duration": 1.2, "char_count": 7, "start_time_s": 0.16, "end_time_s": 1.36, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 160, "text": "蒙"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 320, "text": "恬"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 480, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 640, "text": "脸"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 760, "text": "无"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 920, "text": "奈"}], "keyword": "蒙恬"}, {"subtitle_id": 2, "text": "但也没再多问。", "timestamp": "00:00:01,360 --> 00:00:02,580", "duration": 1.22, "char_count": 7, "start_time_s": 1.36, "end_time_s": 2.58, "words": [{"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1360, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1520, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1640, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1840, "text": "再"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 2000, "text": "多"}, {"attribute": {"event": "speech"}, "end_time": 2580, "start_time": 2120, "text": "问"}], "keyword": "问"}, {"subtitle_id": 3, "text": "这时，一个新面孔出现在朝堂上。", "timestamp": "00:00:02,680 --> 00:00:05,040", "duration": 2.36, "char_count": 15, "start_time_s": 2.68, "end_time_s": 5.04, "words": [{"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2680, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2880, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3240, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3400, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3560, "text": "新"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3680, "text": "面"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3840, "text": "孔"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4080, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4280, "text": "现"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4400, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4520, "text": "朝"}, {"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4720, "text": "堂"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4880, "text": "上"}], "keyword": "新面孔"}, {"subtitle_id": 4, "text": "新盛，他是巴蜀大营的将领，", "timestamp": "00:00:05,040 --> 00:00:07,040", "duration": 2.0, "char_count": 13, "start_time_s": 5.04, "end_time_s": 7.04, "words": [{"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5040, "text": "新"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5240, "text": "盛"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5560, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5720, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5840, "text": "巴"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6040, "text": "蜀"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6160, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 6480, "start_time": 6400, "text": "营"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6480, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6640, "text": "将"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6760, "text": "领"}], "keyword": "新盛"}, {"subtitle_id": 5, "text": "刚刚被调回咸阳戍植。", "timestamp": "00:00:07,040 --> 00:00:09,100", "duration": 2.06, "char_count": 10, "start_time_s": 7.04, "end_time_s": 9.1, "words": [{"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 7040, "text": "刚"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7080, "text": "刚"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7400, "text": "被"}, {"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7560, "text": "调"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7760, "text": "回"}, {"attribute": {"event": "speech"}, "end_time": 8240, "start_time": 8080, "text": "咸"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8240, "text": "阳"}, {"attribute": {"event": "speech"}, "end_time": 8600, "start_time": 8440, "text": "戍"}, {"attribute": {"event": "speech"}, "end_time": 9100, "start_time": 8640, "text": "植"}], "keyword": "咸阳"}, {"subtitle_id": 6, "text": "龚子华看了他一眼，", "timestamp": "00:00:09,200 --> 00:00:10,560", "duration": 1.36, "char_count": 9, "start_time_s": 9.2, "end_time_s": 10.56, "words": [{"attribute": {"event": "speech"}, "end_time": 9400, "start_time": 9200, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 9520, "start_time": 9400, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 9680, "start_time": 9520, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 9880, "start_time": 9680, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 9960, "start_time": 9880, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 10080, "start_time": 9960, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 10160, "start_time": 10080, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 10560, "start_time": 10160, "text": "眼"}], "keyword": "龚子华"}, {"subtitle_id": 7, "text": "新盛也回了个礼，", "timestamp": "00:00:10,560 --> 00:00:11,720", "duration": 1.16, "char_count": 8, "start_time_s": 10.56, "end_time_s": 11.72, "words": [{"attribute": {"event": "speech"}, "end_time": 10760, "start_time": 10560, "text": "新"}, {"attribute": {"event": "speech"}, "end_time": 10920, "start_time": 10760, "text": "盛"}, {"attribute": {"event": "speech"}, "end_time": 11080, "start_time": 10920, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 11200, "start_time": 11080, "text": "回"}, {"attribute": {"event": "speech"}, "end_time": 11280, "start_time": 11200, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 11400, "start_time": 11280, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 11720, "start_time": 11400, "text": "礼"}], "keyword": "礼"}, {"subtitle_id": 8, "text": "两人之间似乎有种莫名的默契。", "timestamp": "00:00:11,720 --> 00:00:14,300", "duration": 2.58, "char_count": 14, "start_time_s": 11.72, "end_time_s": 14.3, "words": [{"attribute": {"event": "speech"}, "end_time": 11880, "start_time": 11720, "text": "两"}, {"attribute": {"event": "speech"}, "end_time": 12000, "start_time": 11880, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 12160, "start_time": 12000, "text": "之"}, {"attribute": {"event": "speech"}, "end_time": 12320, "start_time": 12160, "text": "间"}, {"attribute": {"event": "speech"}, "end_time": 12640, "start_time": 12440, "text": "似"}, {"attribute": {"event": "speech"}, "end_time": 12760, "start_time": 12640, "text": "乎"}, {"attribute": {"event": "speech"}, "end_time": 12960, "start_time": 12760, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 13080, "start_time": 12960, "text": "种"}, {"attribute": {"event": "speech"}, "end_time": 13320, "start_time": 13120, "text": "莫"}, {"attribute": {"event": "speech"}, "end_time": 13560, "start_time": 13360, "text": "名"}, {"attribute": {"event": "speech"}, "end_time": 13680, "start_time": 13560, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 13840, "start_time": 13680, "text": "默"}, {"attribute": {"event": "speech"}, "end_time": 14300, "start_time": 13840, "text": "契"}], "keyword": "默契"}], "keywords": ["蒙恬", "问", "新面孔", "新盛", "咸阳", "龚子华", "礼", "默契"]}, {"chapter": 46, "story_board": "就在这时，殿外传来急报，一封八百里加急的信件被送了进来。秦王打开一看，脸上露出难以置信的表情：“韩王被俘了！”整个朝堂顿时炸开了锅，大臣们纷纷上前祝贺，而公子华则默默站在一旁，心里却在盘算着什么。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "默默站在一旁", "expression": "若有所思"}, {"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "打开信件查看", "expression": "难以置信"}, {"name": "清", "gender": "男", "age": "青年", "clothes": "高领圆领灰色商人长袍，主色调为灰色，搭配白色，有简单几何纹，束蓝色腰带，挂有钱袋圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "商人", "other": "在商会经营有方", "from_chapter": 70, "action": "坐在酒楼与公子华交谈，向公子华讲述家族来咸阳的目的", "expression": "自然"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "拿着毛笔，站在朝堂上，面对公子华的回答后双手摊开", "expression": "无奈"}, {"name": "辛胜", "gender": "男", "age": "青年", "clothes": "高领圆领黑色秦国士兵服饰，主色调为黑色，搭配灰色，有简单线条纹，束黑色腰带，背箭囊圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "秦国士兵", "other": "负责传递情报", "from_chapter": 70, "action": "走进朝堂，看到公子华后拱手回礼", "expression": "严肃"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "", "expression": ""}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "韩王韩安", "gender": "男", "age": "中年", "clothes": "高领圆领韩国黄色锦袍，主色调为黄色，搭配红色，绣有云纹和花卉纹样，头戴王冠，腰间束黄色锦带，配玉珏圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "韩王", "other": "面容忧虑，心怀忐忑", "from_chapter": 52, "action": "", "expression": ""}, {"name": "斥候河", "gender": "男", "age": "青年", "clothes": "高领圆领青色秦制军装，主色调青色，搭配淡蓝色和白色，军装绣有草叶纹，腰系青色腰带，配有短刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "精瘦", "identity": "秦军斥候", "other": "熟悉战事情况", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿里，一个打开信件查看、满脸难以置信的中年男子，他穿着高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰，圆形衣领，黑色束发，身材魁梧；旁边一个默默站着、若有所思的青年男子，他穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发，身材挺拔，画风为动漫分镜插图, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753206083_20250723_014124.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_ab839da0-3b2b-4496-bd9d-ddebb3830245_1.25x_20250722_200051.wav", "audio_duration": 16.552, "video_url": "", "story_board_id": 8, "subtitle": [{"subtitle_id": 1, "text": "就在这时，", "timestamp": "00:00:00,200 --> 00:00:01,000", "duration": 0.8, "char_count": 5, "start_time_s": 0.2, "end_time_s": 1.0, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 200, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 320, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 400, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 560, "text": "时"}], "keyword": "这时"}, {"subtitle_id": 2, "text": "殿外传来急报，", "timestamp": "00:00:01,000 --> 00:00:02,000", "duration": 1.0, "char_count": 7, "start_time_s": 1.0, "end_time_s": 2.0, "words": [{"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 1000, "text": "殿"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1160, "text": "外"}, {"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1320, "text": "传"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1440, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1560, "text": "急"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1760, "text": "报"}], "keyword": "殿外"}, {"subtitle_id": 3, "text": "一封800里加急的信件被送了进来。", "timestamp": "00:00:02,000 --> 00:00:04,700", "duration": 2.7, "char_count": 17, "start_time_s": 2.0, "end_time_s": 4.7, "words": [{"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2000, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2200, "text": "封"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2320, "text": "800"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2680, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2800, "text": "加"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 3000, "text": "急"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3120, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3240, "text": "信"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3400, "text": "件"}, {"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3640, "text": "被"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3800, "text": "送"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 4000, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4080, "text": "进"}, {"attribute": {"event": "speech"}, "end_time": 4700, "start_time": 4200, "text": "来"}], "keyword": "信件"}, {"subtitle_id": 4, "text": "秦王打开一看，", "timestamp": "00:00:04,800 --> 00:00:05,880", "duration": 1.08, "char_count": 7, "start_time_s": 4.8, "end_time_s": 5.88, "words": [{"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4800, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 4960, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5080, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5240, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5360, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5480, "text": "看"}], "keyword": "秦王"}, {"subtitle_id": 5, "text": "脸上露出难以置信的表情。", "timestamp": "00:00:05,880 --> 00:00:07,680", "duration": 1.8, "char_count": 12, "start_time_s": 5.88, "end_time_s": 7.68, "words": [{"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5880, "text": "脸"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6040, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6160, "text": "露"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6280, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6400, "text": "难"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6560, "text": "以"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6680, "text": "置"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6840, "text": "信"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6960, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7080, "text": "表"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7240, "text": "情"}], "keyword": "表情"}, {"subtitle_id": 6, "text": "韩王被俘了，", "timestamp": "00:00:07,680 --> 00:00:08,940", "duration": 1.26, "char_count": 6, "start_time_s": 7.68, "end_time_s": 8.94, "words": [{"attribute": {"event": "speech"}, "end_time": 7880, "start_time": 7680, "text": "韩"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7920, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8040, "text": "被"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8240, "text": "俘"}, {"attribute": {"event": "speech"}, "end_time": 8940, "start_time": 8400, "text": "了"}], "keyword": "韩王"}, {"subtitle_id": 7, "text": "整个朝堂顿时炸开了锅，", "timestamp": "00:00:09,040 --> 00:00:11,000", "duration": 1.96, "char_count": 11, "start_time_s": 9.04, "end_time_s": 11.0, "words": [{"attribute": {"event": "speech"}, "end_time": 9200, "start_time": 9040, "text": "整"}, {"attribute": {"event": "speech"}, "end_time": 9360, "start_time": 9200, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 9560, "start_time": 9360, "text": "朝"}, {"attribute": {"event": "speech"}, "end_time": 9680, "start_time": 9560, "text": "堂"}, {"attribute": {"event": "speech"}, "end_time": 9800, "start_time": 9680, "text": "顿"}, {"attribute": {"event": "speech"}, "end_time": 10040, "start_time": 9840, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 10320, "start_time": 10120, "text": "炸"}, {"attribute": {"event": "speech"}, "end_time": 10520, "start_time": 10360, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 10600, "start_time": 10520, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 11000, "start_time": 10600, "text": "锅"}], "keyword": "朝堂"}, {"subtitle_id": 8, "text": "大臣们纷纷上前祝贺，", "timestamp": "00:00:11,000 --> 00:00:12,640", "duration": 1.64, "char_count": 10, "start_time_s": 11.0, "end_time_s": 12.64, "words": [{"attribute": {"event": "speech"}, "end_time": 11200, "start_time": 11000, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 11360, "start_time": 11240, "text": "臣"}, {"attribute": {"event": "speech"}, "end_time": 11480, "start_time": 11360, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 11520, "start_time": 11480, "text": "纷"}, {"attribute": {"event": "speech"}, "end_time": 11800, "start_time": 11520, "text": "纷"}, {"attribute": {"event": "speech"}, "end_time": 12000, "start_time": 11840, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 12160, "start_time": 12000, "text": "前"}, {"attribute": {"event": "speech"}, "end_time": 12320, "start_time": 12160, "text": "祝"}, {"attribute": {"event": "speech"}, "end_time": 12640, "start_time": 12320, "text": "贺"}], "keyword": "大臣"}, {"subtitle_id": 9, "text": "而公子华则默默站在一旁，", "timestamp": "00:00:12,640 --> 00:00:14,600", "duration": 1.96, "char_count": 12, "start_time_s": 12.64, "end_time_s": 14.6, "words": [{"attribute": {"event": "speech"}, "end_time": 12840, "start_time": 12640, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 13000, "start_time": 12840, "text": "公"}, {"attribute": {"event": "speech"}, "end_time": 13120, "start_time": 13000, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 13320, "start_time": 13120, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 13480, "start_time": 13320, "text": "则"}, {"attribute": {"event": "speech"}, "end_time": 13520, "start_time": 13480, "text": "默"}, {"attribute": {"event": "speech"}, "end_time": 13760, "start_time": 13520, "text": "默"}, {"attribute": {"event": "speech"}, "end_time": 13920, "start_time": 13800, "text": "站"}, {"attribute": {"event": "speech"}, "end_time": 14080, "start_time": 13920, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 14200, "start_time": 14080, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 14600, "start_time": 14200, "text": "旁"}], "keyword": "公子华"}, {"subtitle_id": 10, "text": "心里却在盘算着什么。", "timestamp": "00:00:14,600 --> 00:00:16,220", "duration": 1.62, "char_count": 10, "start_time_s": 14.6, "end_time_s": 16.22, "words": [{"attribute": {"event": "speech"}, "end_time": 14720, "start_time": 14600, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 14840, "start_time": 14720, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 15000, "start_time": 14840, "text": "却"}, {"attribute": {"event": "speech"}, "end_time": 15120, "start_time": 15000, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 15320, "start_time": 15120, "text": "盘"}, {"attribute": {"event": "speech"}, "end_time": 15560, "start_time": 15440, "text": "算"}, {"attribute": {"event": "speech"}, "end_time": 15680, "start_time": 15560, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 15800, "start_time": 15680, "text": "什"}, {"attribute": {"event": "speech"}, "end_time": 16220, "start_time": 15800, "text": "么"}], "keyword": "盘算"}], "keywords": ["这时", "殿外", "信件", "秦王", "表情", "韩王", "朝堂", "大臣", "公子华", "盘算"]}, {"chapter": 46, "story_board": "原来，这场胜利不是偶然。秦军从南阳出发，突袭阳翟，三天就拿下了这座城池。接着，王贲将军率军从荥阳、白皋进攻华阳，俘虏了八万韩军。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "默默站在一旁", "expression": "若有所思"}, {"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "打开信件查看", "expression": "难以置信"}, {"name": "清", "gender": "男", "age": "青年", "clothes": "高领圆领灰色商人长袍，主色调为灰色，搭配白色，有简单几何纹，束蓝色腰带，挂有钱袋圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "商人", "other": "在商会经营有方", "from_chapter": 70, "action": "坐在酒楼与公子华交谈，向公子华讲述家族来咸阳的目的", "expression": "自然"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "拿着毛笔，站在朝堂上，面对公子华的回答后双手摊开", "expression": "无奈"}, {"name": "辛胜", "gender": "男", "age": "青年", "clothes": "高领圆领黑色秦国士兵服饰，主色调为黑色，搭配灰色，有简单线条纹，束黑色腰带，背箭囊圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "秦国士兵", "other": "负责传递情报", "from_chapter": 70, "action": "走进朝堂，看到公子华后拱手回礼", "expression": "严肃"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "骑在战马上，挥舞着手中的兵器指挥军队进攻", "expression": "坚毅"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "韩王韩安", "gender": "男", "age": "中年", "clothes": "高领圆领韩国黄色锦袍，主色调为黄色，搭配红色，绣有云纹和花卉纹样，头戴王冠，腰间束黄色锦带，配玉珏圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "韩王", "other": "面容忧虑，心怀忐忑", "from_chapter": 52, "action": "", "expression": ""}, {"name": "斥候河", "gender": "男", "age": "青年", "clothes": "高领圆领青色秦制军装，主色调青色，搭配淡蓝色和白色，军装绣有草叶纹，腰系青色腰带，配有短刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "精瘦", "identity": "秦军斥候", "other": "熟悉战事情况", "from_chapter": [0], "action": "", "expression": ""}], "scene": "古代沙场", "image_prompt": "中景，骑在战马上挥舞着手中兵器指挥军队进攻、表情坚毅且英武的青年男子，身着秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带，有圆形衣领，黑色束发，背景是古代沙场, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753206301_20250723_014501.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_c89d8c9f-6ce5-4dba-87ba-dc359af8a413_1.25x_20250722_200033.wav", "audio_duration": 11.392, "video_url": "", "story_board_id": 9, "subtitle": [{"subtitle_id": 1, "text": "原来这场胜利不是偶然，", "timestamp": "00:00:00,160 --> 00:00:02,220", "duration": 2.06, "char_count": 11, "start_time_s": 0.16, "end_time_s": 2.22, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "原"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 280, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 680, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 840, "text": "场"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1040, "text": "胜"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1200, "text": "利"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1360, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1520, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1640, "text": "偶"}, {"attribute": {"event": "speech"}, "end_time": 2220, "start_time": 1760, "text": "然"}], "keyword": "胜利"}, {"subtitle_id": 2, "text": "秦军从南阳出发，", "timestamp": "00:00:02,440 --> 00:00:03,680", "duration": 1.24, "char_count": 8, "start_time_s": 2.44, "end_time_s": 3.68, "words": [{"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2440, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2640, "text": "军"}, {"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2800, "text": "从"}, {"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2960, "text": "南"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3080, "text": "阳"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3240, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3400, "text": "发"}], "keyword": "秦军"}, {"subtitle_id": 3, "text": "突袭阳翟，", "timestamp": "00:00:03,680 --> 00:00:04,480", "duration": 0.8, "char_count": 5, "start_time_s": 3.68, "end_time_s": 4.48, "words": [{"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3680, "text": "突"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3880, "text": "袭"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 4000, "text": "阳"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4160, "text": "翟"}], "keyword": "阳翟"}, {"subtitle_id": 4, "text": "三天就拿下了这座城池。", "timestamp": "00:00:04,480 --> 00:00:06,300", "duration": 1.82, "char_count": 11, "start_time_s": 4.48, "end_time_s": 6.3, "words": [{"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4480, "text": "三"}, {"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4720, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4880, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5000, "text": "拿"}, {"attribute": {"event": "speech"}, "end_time": 5320, "start_time": 5160, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5320, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5400, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5520, "text": "座"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5640, "text": "城"}, {"attribute": {"event": "speech"}, "end_time": 6300, "start_time": 5840, "text": "池"}], "keyword": "城池"}, {"subtitle_id": 5, "text": "接着，", "timestamp": "00:00:06,440 --> 00:00:07,000", "duration": 0.56, "char_count": 3, "start_time_s": 6.44, "end_time_s": 7.0, "words": [{"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6440, "text": "接"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6600, "text": "着"}], "keyword": "接着"}, {"subtitle_id": 6, "text": "王奔将军率军从荥阳、白皋进攻华阳，", "timestamp": "00:00:07,000 --> 00:00:09,800", "duration": 2.8, "char_count": 17, "start_time_s": 7.0, "end_time_s": 9.8, "words": [{"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 7000, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7200, "text": "奔"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7320, "text": "将"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7480, "text": "军"}, {"attribute": {"event": "speech"}, "end_time": 7840, "start_time": 7640, "text": "率"}, {"attribute": {"event": "speech"}, "end_time": 8000, "start_time": 7840, "text": "军"}, {"attribute": {"event": "speech"}, "end_time": 8160, "start_time": 8000, "text": "从"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8200, "text": "荥"}, {"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8360, "text": "阳"}, {"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8480, "text": "白"}, {"attribute": {"event": "speech"}, "end_time": 8840, "start_time": 8680, "text": "皋"}, {"attribute": {"event": "speech"}, "end_time": 9040, "start_time": 8880, "text": "进"}, {"attribute": {"event": "speech"}, "end_time": 9200, "start_time": 9040, "text": "攻"}, {"attribute": {"event": "speech"}, "end_time": 9400, "start_time": 9200, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 9800, "start_time": 9440, "text": "阳"}], "keyword": "王奔"}, {"subtitle_id": 7, "text": "俘虏了8万韩军。", "timestamp": "00:00:09,800 --> 00:00:11,300", "duration": 1.5, "char_count": 8, "start_time_s": 9.8, "end_time_s": 11.3, "words": [{"attribute": {"event": "speech"}, "end_time": 9960, "start_time": 9800, "text": "俘"}, {"attribute": {"event": "speech"}, "end_time": 10120, "start_time": 10000, "text": "虏"}, {"attribute": {"event": "speech"}, "end_time": 10280, "start_time": 10120, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 10480, "start_time": 10280, "text": "8"}, {"attribute": {"event": "speech"}, "end_time": 10640, "start_time": 10480, "text": "万"}, {"attribute": {"event": "speech"}, "end_time": 10800, "start_time": 10640, "text": "韩"}, {"attribute": {"event": "speech"}, "end_time": 11300, "start_time": 10840, "text": "军"}], "keyword": "韩军"}], "keywords": ["胜利", "秦军", "阳翟", "城池", "接着", "王奔", "韩军"]}, {"chapter": 46, "story_board": "最后，章邯更是立下大功，直接攻入城中，活捉了韩王韩安。消息传回咸阳，秦王大喜，立刻下令赏赐有功之臣。差斥候河被叫到殿前，详细讲述了战事经过。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "默默站在一旁", "expression": "若有所思"}, {"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "坐在龙椅上，兴奋地拍桌子，随后大手一挥下令", "expression": "大喜"}, {"name": "清", "gender": "男", "age": "青年", "clothes": "高领圆领灰色商人长袍，主色调为灰色，搭配白色，有简单几何纹，束蓝色腰带，挂有钱袋圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "商人", "other": "在商会经营有方", "from_chapter": 70, "action": "坐在酒楼与公子华交谈，向公子华讲述家族来咸阳的目的", "expression": "自然"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "拿着毛笔，站在朝堂上，面对公子华的回答后双手摊开", "expression": "无奈"}, {"name": "辛胜", "gender": "男", "age": "青年", "clothes": "高领圆领黑色秦国士兵服饰，主色调为黑色，搭配灰色，有简单线条纹，束黑色腰带，背箭囊圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "秦国士兵", "other": "负责传递情报", "from_chapter": 70, "action": "走进朝堂，看到公子华后拱手回礼", "expression": "严肃"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "骑在战马上，挥舞着手中的兵器指挥军队进攻", "expression": "坚毅"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "骑马攻入城中，伸手活捉韩王韩安", "expression": "英勇、坚毅"}, {"name": "韩王韩安", "gender": "男", "age": "中年", "clothes": "高领圆领韩国黄色锦袍，主色调为黄色，搭配红色，绣有云纹和花卉纹样，头戴王冠，腰间束黄色锦带，配玉珏圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "韩王", "other": "面容忧虑，心怀忐忑", "from_chapter": 52, "action": "", "expression": ""}, {"name": "斥候河", "gender": "男", "age": "青年", "clothes": "高领圆领青色秦制军装，主色调青色，搭配淡蓝色和白色，军装绣有草叶纹，腰系青色腰带，配有短刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "精瘦", "identity": "秦军斥候", "other": "熟悉战事情况", "from_chapter": [0], "action": "快步走到殿前，抱拳，开始讲述战事经过", "expression": "严肃、坚定"}], "scene": "皇宫大殿", "image_prompt": "中景，皇宫大殿内，坐在龙椅上的中年男子兴奋地拍桌子后大手一挥，表情大喜；一旁快步走到殿前抱拳的青年男子表情严肃、坚定；画面背景处有一名骑马攻入城中伸手作活捉状的青年男子。坐在龙椅上的中年男子穿着高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰，圆形衣领，黑色束发，身材魁梧；快步走到殿前抱拳的青年男子穿着高领圆领青色秦制军装，主色调青色，搭配淡蓝色和白色，军装绣有草叶纹，腰系青色腰带，配有短刀，圆形衣领，黑色束发，身材精瘦；骑马攻入城中伸手作活捉状的青年男子穿着高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾，圆形衣领，黑色束发，身材魁梧, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753206509_20250723_014829.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_8168396d-23bd-4652-a255-822970ac69a9_1.25x_20250722_200100.wav", "audio_duration": 12.712, "video_url": "", "story_board_id": 10, "subtitle": [{"subtitle_id": 1, "text": "最后，张涵更是立下大功，", "timestamp": "00:00:00,200 --> 00:00:02,120", "duration": 1.92, "char_count": 12, "start_time_s": 0.2, "end_time_s": 2.12, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "最"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 280, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 680, "text": "张"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 880, "text": "涵"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1080, "text": "更"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1240, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1360, "text": "立"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1480, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1640, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1800, "text": "功"}], "keyword": "张涵"}, {"subtitle_id": 2, "text": "直接攻入城中，", "timestamp": "00:00:02,120 --> 00:00:03,240", "duration": 1.12, "char_count": 7, "start_time_s": 2.12, "end_time_s": 3.24, "words": [{"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2120, "text": "直"}, {"attribute": {"event": "speech"}, "end_time": 2520, "start_time": 2360, "text": "接"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2520, "text": "攻"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2640, "text": "入"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2760, "text": "城"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 2920, "text": "中"}], "keyword": "城中"}, {"subtitle_id": 3, "text": "活捉了韩王韩安。", "timestamp": "00:00:03,240 --> 00:00:04,740", "duration": 1.5, "char_count": 8, "start_time_s": 3.24, "end_time_s": 4.74, "words": [{"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3240, "text": "活"}, {"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3440, "text": "捉"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3600, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3720, "text": "韩"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3960, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4120, "text": "韩"}, {"attribute": {"event": "speech"}, "end_time": 4740, "start_time": 4280, "text": "安"}], "keyword": "韩王"}, {"subtitle_id": 4, "text": "消息传回咸阳，", "timestamp": "00:00:04,960 --> 00:00:06,040", "duration": 1.08, "char_count": 7, "start_time_s": 4.96, "end_time_s": 6.04, "words": [{"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 4960, "text": "消"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5120, "text": "息"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5240, "text": "传"}, {"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5400, "text": "回"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5560, "text": "咸"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5680, "text": "阳"}], "keyword": "咸阳"}, {"subtitle_id": 5, "text": "秦王大喜，", "timestamp": "00:00:06,040 --> 00:00:06,760", "duration": 0.72, "char_count": 5, "start_time_s": 6.04, "end_time_s": 6.76, "words": [{"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6040, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6160, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 6480, "start_time": 6280, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6480, "text": "喜"}], "keyword": "秦王"}, {"subtitle_id": 6, "text": "立刻下令赏赐有功之臣差池后河，", "timestamp": "00:00:06,760 --> 00:00:09,640", "duration": 2.88, "char_count": 15, "start_time_s": 6.76, "end_time_s": 9.64, "words": [{"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6760, "text": "立"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6960, "text": "刻"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7080, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7280, "text": "令"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7400, "text": "赏"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7600, "text": "赐"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7760, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7960, "text": "功"}, {"attribute": {"event": "speech"}, "end_time": 8240, "start_time": 8080, "text": "之"}, {"attribute": {"event": "speech"}, "end_time": 8440, "start_time": 8240, "text": "臣"}, {"attribute": {"event": "speech"}, "end_time": 9080, "start_time": 8840, "text": "差"}, {"attribute": {"event": "speech"}, "end_time": 9200, "start_time": 9080, "text": "池"}, {"attribute": {"event": "speech"}, "end_time": 9360, "start_time": 9200, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 9640, "start_time": 9360, "text": "河"}], "keyword": "赏赐"}, {"subtitle_id": 7, "text": "被叫到殿前，", "timestamp": "00:00:09,640 --> 00:00:10,640", "duration": 1.0, "char_count": 6, "start_time_s": 9.64, "end_time_s": 10.64, "words": [{"attribute": {"event": "speech"}, "end_time": 9800, "start_time": 9640, "text": "被"}, {"attribute": {"event": "speech"}, "end_time": 9960, "start_time": 9800, "text": "叫"}, {"attribute": {"event": "speech"}, "end_time": 10080, "start_time": 9960, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 10240, "start_time": 10080, "text": "殿"}, {"attribute": {"event": "speech"}, "end_time": 10640, "start_time": 10240, "text": "前"}], "keyword": "殿前"}, {"subtitle_id": 8, "text": "详细讲述了战事经过。", "timestamp": "00:00:10,640 --> 00:00:12,420", "duration": 1.78, "char_count": 10, "start_time_s": 10.64, "end_time_s": 12.42, "words": [{"attribute": {"event": "speech"}, "end_time": 10840, "start_time": 10640, "text": "详"}, {"attribute": {"event": "speech"}, "end_time": 11000, "start_time": 10880, "text": "细"}, {"attribute": {"event": "speech"}, "end_time": 11160, "start_time": 11000, "text": "讲"}, {"attribute": {"event": "speech"}, "end_time": 11360, "start_time": 11200, "text": "述"}, {"attribute": {"event": "speech"}, "end_time": 11480, "start_time": 11360, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 11640, "start_time": 11480, "text": "战"}, {"attribute": {"event": "speech"}, "end_time": 11800, "start_time": 11640, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 11920, "start_time": 11800, "text": "经"}, {"attribute": {"event": "speech"}, "end_time": 12420, "start_time": 11960, "text": "过"}], "keyword": "战事"}], "keywords": ["张涵", "城中", "韩王", "咸阳", "秦王", "赏赐", "殿前", "战事"]}, {"chapter": 46, "story_board": "他说到“赳赳老秦，共赴国难”时，语气坚定，让所有人都感受到了秦军的强大。散朝后，公子华和秦王一起回宫，两人开始讨论官制改革的事。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "和秦王一起回宫，与秦王讨论官制改革", "expression": "认真、期待"}, {"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "和公子华一起回宫，与公子华讨论官制改革", "expression": "沉思、认可"}, {"name": "清", "gender": "男", "age": "青年", "clothes": "高领圆领灰色商人长袍，主色调为灰色，搭配白色，有简单几何纹，束蓝色腰带，挂有钱袋圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "商人", "other": "在商会经营有方", "from_chapter": 70, "action": "坐在酒楼与公子华交谈，向公子华讲述家族来咸阳的目的", "expression": "自然"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "拿着毛笔，站在朝堂上，面对公子华的回答后双手摊开", "expression": "无奈"}, {"name": "辛胜", "gender": "男", "age": "青年", "clothes": "高领圆领黑色秦国士兵服饰，主色调为黑色，搭配灰色，有简单线条纹，束黑色腰带，背箭囊圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "秦国士兵", "other": "负责传递情报", "from_chapter": 70, "action": "走进朝堂，看到公子华后拱手回礼", "expression": "严肃"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "骑在战马上，挥舞着手中的兵器指挥军队进攻", "expression": "坚毅"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "骑马攻入城中，伸手活捉韩王韩安", "expression": "英勇、坚毅"}, {"name": "韩王韩安", "gender": "男", "age": "中年", "clothes": "高领圆领韩国黄色锦袍，主色调为黄色，搭配红色，绣有云纹和花卉纹样，头戴王冠，腰间束黄色锦带，配玉珏圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "韩王", "other": "面容忧虑，心怀忐忑", "from_chapter": 52, "action": "", "expression": ""}, {"name": "斥候河", "gender": "男", "age": "青年", "clothes": "高领圆领青色秦制军装，主色调青色，搭配淡蓝色和白色，军装绣有草叶纹，腰系青色腰带，配有短刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "精瘦", "identity": "秦军斥候", "other": "熟悉战事情况", "from_chapter": [0], "action": "站在殿前讲述战事经过", "expression": "坚定"}], "scene": "皇宫大殿", "image_prompt": "中景，皇宫大殿内，一个身着高领圆领青色秦制军装的青年斥候神情坚定地站在殿前。与此同时，一个身着高领圆领紫色锦袍的青年与一个头戴冕旒、身着高领圆领帝王龙袍的中年男子一起回宫，青年公子认真且期待，中年秦王则陷入沉思并带着认可的神情。站着讲述战事的青年斥候精瘦，其军装主色调为青色，搭配淡蓝色和白色，绣有草叶纹，腰系青色腰带，配有短刀，圆形衣领，黑色束发。和中年男子一起回宫的青年公子挺拔，其锦袍主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发。中年男子魁梧，其龙袍主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753206741_20250723_015222.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_3300ea83-5aeb-4521-ae81-998fa58babb6_1.25x_20250722_200055.wav", "audio_duration": 10.68, "video_url": "", "story_board_id": 11, "subtitle": [{"subtitle_id": 1, "text": "他说到啾啾老秦共赴国难时，", "timestamp": "00:00:00,160 --> 00:00:02,360", "duration": 2.2, "char_count": 13, "start_time_s": 0.16, "end_time_s": 2.36, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 360, "start_time": 280, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 360, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 560, "text": "啾"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 600, "text": "啾"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 800, "text": "老"}, {"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 1000, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1360, "text": "共"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1520, "text": "赴"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1680, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1840, "text": "难"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2000, "text": "时"}], "keyword": "啾啾老秦"}, {"subtitle_id": 2, "text": "语气坚定，", "timestamp": "00:00:02,360 --> 00:00:03,200", "duration": 0.84, "char_count": 5, "start_time_s": 2.36, "end_time_s": 3.2, "words": [{"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2360, "text": "语"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2560, "text": "气"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2720, "text": "坚"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 2880, "text": "定"}], "keyword": "语气"}, {"subtitle_id": 3, "text": "让所有人都感受到了秦军的强大。", "timestamp": "00:00:03,200 --> 00:00:05,740", "duration": 2.54, "char_count": 15, "start_time_s": 3.2, "end_time_s": 5.74, "words": [{"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3200, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3360, "text": "所"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3600, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3720, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3840, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4000, "text": "感"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4200, "text": "受"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4360, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4480, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4640, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4840, "text": "军"}, {"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 4960, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5080, "text": "强"}, {"attribute": {"event": "speech"}, "end_time": 5740, "start_time": 5280, "text": "大"}], "keyword": "秦军"}, {"subtitle_id": 4, "text": "散朝后，龚子华和秦王一起回宫，", "timestamp": "00:00:05,920 --> 00:00:08,360", "duration": 2.44, "char_count": 15, "start_time_s": 5.92, "end_time_s": 8.36, "words": [{"attribute": {"event": "speech"}, "end_time": 6080, "start_time": 5920, "text": "散"}, {"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 6080, "text": "朝"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6240, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6640, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6800, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 7120, "start_time": 6960, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7120, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7280, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7400, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7560, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 7840, "start_time": 7680, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 8000, "start_time": 7840, "text": "回"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8000, "text": "宫"}], "keyword": "龚子华"}, {"subtitle_id": 5, "text": "两人开始讨论官制改革的事。", "timestamp": "00:00:08,360 --> 00:00:10,380", "duration": 2.02, "char_count": 13, "start_time_s": 8.36, "end_time_s": 10.38, "words": [{"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8360, "text": "两"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8520, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8640, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 8920, "start_time": 8800, "text": "始"}, {"attribute": {"event": "speech"}, "end_time": 9080, "start_time": 8920, "text": "讨"}, {"attribute": {"event": "speech"}, "end_time": 9200, "start_time": 9120, "text": "论"}, {"attribute": {"event": "speech"}, "end_time": 9400, "start_time": 9240, "text": "官"}, {"attribute": {"event": "speech"}, "end_time": 9520, "start_time": 9400, "text": "制"}, {"attribute": {"event": "speech"}, "end_time": 9680, "start_time": 9520, "text": "改"}, {"attribute": {"event": "speech"}, "end_time": 9840, "start_time": 9680, "text": "革"}, {"attribute": {"event": "speech"}, "end_time": 9960, "start_time": 9840, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 10380, "start_time": 9960, "text": "事"}], "keyword": "官制改革"}], "keywords": ["啾啾老秦", "语气", "秦军", "龚子华", "官制改革"]}, {"chapter": 46, "story_board": "公子华提出了一套详细的改革方案，秦王虽然嘴上骂他“傻”，但心里其实很认可他的想法。“父王，您真的要改革吗？", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "向秦王提出改革方案，小心翼翼地询问", "expression": "期待"}, {"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "听公子华提出方案，嘴上骂着", "expression": "表面嗔怪，内心认可"}, {"name": "清", "gender": "男", "age": "青年", "clothes": "高领圆领灰色商人长袍，主色调为灰色，搭配白色，有简单几何纹，束蓝色腰带，挂有钱袋圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "商人", "other": "在商会经营有方", "from_chapter": 70, "action": "坐在酒楼与公子华交谈，向公子华讲述家族来咸阳的目的", "expression": "自然"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "拿着毛笔，站在朝堂上，面对公子华的回答后双手摊开", "expression": "无奈"}, {"name": "辛胜", "gender": "男", "age": "青年", "clothes": "高领圆领黑色秦国士兵服饰，主色调为黑色，搭配灰色，有简单线条纹，束黑色腰带，背箭囊圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "秦国士兵", "other": "负责传递情报", "from_chapter": 70, "action": "走进朝堂，看到公子华后拱手回礼", "expression": "严肃"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "骑在战马上，挥舞着手中的兵器指挥军队进攻", "expression": "坚毅"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "骑马攻入城中，伸手活捉韩王韩安", "expression": "英勇、坚毅"}, {"name": "韩王韩安", "gender": "男", "age": "中年", "clothes": "高领圆领韩国黄色锦袍，主色调为黄色，搭配红色，绣有云纹和花卉纹样，头戴王冠，腰间束黄色锦带，配玉珏圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "韩王", "other": "面容忧虑，心怀忐忑", "from_chapter": 52, "action": "", "expression": ""}, {"name": "斥候河", "gender": "男", "age": "青年", "clothes": "高领圆领青色秦制军装，主色调青色，搭配淡蓝色和白色，军装绣有草叶纹，腰系青色腰带，配有短刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "精瘦", "identity": "秦军斥候", "other": "熟悉战事情况", "from_chapter": [0], "action": "站在殿前讲述战事经过", "expression": "坚定"}], "scene": "皇宫大殿", "image_prompt": "这幅动漫分镜插图采用中景，在皇宫大殿中，一位挺拔的青年小心翼翼又满怀期待地呈上改革方案，对面一位魁梧的中年嘴上带着嗔怪的表情在听着。青年身着高领圆领的紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带并佩戴玉佩，圆形衣领，黑色束发；中年身着高领圆领的帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753206942_20250723_015542.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_1f4a427b-a0c8-46e1-a93d-cc17f079c47b_1.25x_20250722_200038.wav", "audio_duration": 8.778667, "video_url": "", "story_board_id": 12, "subtitle": [{"subtitle_id": 1, "text": "龚子华提出了一套详细的改革方案，", "timestamp": "00:00:00,160 --> 00:00:02,560", "duration": 2.4, "char_count": 16, "start_time_s": 0.16, "end_time_s": 2.56, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 400, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 560, "text": "提"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 760, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 880, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 960, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1040, "text": "套"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1280, "text": "详"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1520, "text": "细"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1640, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1720, "text": "改"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1920, "text": "革"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2040, "text": "方"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2160, "text": "案"}], "keyword": "龚子华"}, {"subtitle_id": 2, "text": "秦王虽然嘴上骂他傻，", "timestamp": "00:00:02,560 --> 00:00:04,200", "duration": 1.64, "char_count": 10, "start_time_s": 2.56, "end_time_s": 4.2, "words": [{"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2560, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2720, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 3040, "text": "虽"}, {"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3160, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3280, "text": "嘴"}, {"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3480, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3600, "text": "骂"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3760, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 3880, "text": "傻"}], "keyword": "秦王"}, {"subtitle_id": 3, "text": "但心里其实很认可他的想法。", "timestamp": "00:00:04,200 --> 00:00:06,220", "duration": 2.02, "char_count": 13, "start_time_s": 4.2, "end_time_s": 6.22, "words": [{"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4200, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4360, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4480, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4600, "text": "其"}, {"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4760, "text": "实"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4880, "text": "很"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5040, "text": "认"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5240, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5360, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5520, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 5760, "start_time": 5600, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 6220, "start_time": 5760, "text": "法"}], "keyword": "改革方案"}, {"subtitle_id": 4, "text": "父王，您真的要改革吗？", "timestamp": "00:00:06,400 --> 00:00:08,460", "duration": 2.06, "char_count": 11, "start_time_s": 6.4, "end_time_s": 8.46, "words": [{"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6400, "text": "父"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6560, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 6960, "text": "您"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7160, "text": "真"}, {"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7320, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7440, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7600, "text": "改"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7800, "text": "革"}, {"attribute": {"event": "speech"}, "end_time": 8460, "start_time": 7960, "text": "吗"}], "keyword": "父王"}], "keywords": ["龚子华", "秦王", "改革方案", "父王"]}, {"chapter": 46, "story_board": "”公子华小心翼翼地问。秦王叹了口气：“你提出的那些改革，我一直在想。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "面对秦王，身体微微前倾，轻声询问", "expression": "小心翼翼"}, {"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "微微叹气，神情若有所思", "expression": "沉稳"}, {"name": "清", "gender": "男", "age": "青年", "clothes": "高领圆领灰色商人长袍，主色调为灰色，搭配白色，有简单几何纹，束蓝色腰带，挂有钱袋圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "商人", "other": "在商会经营有方", "from_chapter": 70, "action": "坐在酒楼与公子华交谈，向公子华讲述家族来咸阳的目的", "expression": "自然"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "拿着毛笔，站在朝堂上，面对公子华的回答后双手摊开", "expression": "无奈"}, {"name": "辛胜", "gender": "男", "age": "青年", "clothes": "高领圆领黑色秦国士兵服饰，主色调为黑色，搭配灰色，有简单线条纹，束黑色腰带，背箭囊圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "秦国士兵", "other": "负责传递情报", "from_chapter": 70, "action": "走进朝堂，看到公子华后拱手回礼", "expression": "严肃"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "骑在战马上，挥舞着手中的兵器指挥军队进攻", "expression": "坚毅"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "骑马攻入城中，伸手活捉韩王韩安", "expression": "英勇、坚毅"}, {"name": "韩王韩安", "gender": "男", "age": "中年", "clothes": "高领圆领韩国黄色锦袍，主色调为黄色，搭配红色，绣有云纹和花卉纹样，头戴王冠，腰间束黄色锦带，配玉珏圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "韩王", "other": "面容忧虑，心怀忐忑", "from_chapter": 52, "action": "", "expression": ""}, {"name": "斥候河", "gender": "男", "age": "青年", "clothes": "高领圆领青色秦制军装，主色调青色，搭配淡蓝色和白色，军装绣有草叶纹，腰系青色腰带，配有短刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "精瘦", "identity": "秦军斥候", "other": "熟悉战事情况", "from_chapter": [0], "action": "站在殿前讲述战事经过", "expression": "坚定"}], "scene": "皇宫大殿", "image_prompt": "中景，皇宫大殿的背景下，一个身体微微前倾、神情小心翼翼的青年男子面对一个微微叹气、神情若有所思的中年男子。青年男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发；中年男子穿着高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753207149_20250723_015909.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_cffc0097-9f56-4f21-a767-4e1923b5b620_1.25x_20250722_200042.wav", "audio_duration": 6.128, "video_url": "", "story_board_id": 13, "subtitle": [{"subtitle_id": 1, "text": "龚子华小心翼翼的问。", "timestamp": "00:00:00,160 --> 00:00:01,940", "duration": 1.78, "char_count": 10, "start_time_s": 0.16, "end_time_s": 1.94, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 440, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 680, "text": "小"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 920, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 1040, "text": "翼"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1080, "text": "翼"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1360, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1940, "start_time": 1480, "text": "问"}], "keyword": "龚子华"}, {"subtitle_id": 2, "text": "秦王叹了口气你提出的那些改革，", "timestamp": "00:00:02,120 --> 00:00:04,600", "duration": 2.48, "char_count": 15, "start_time_s": 2.12, "end_time_s": 4.6, "words": [{"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2120, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2280, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2400, "text": "叹"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2560, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2640, "text": "口"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2800, "text": "气"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3120, "text": "你"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3360, "text": "提"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3560, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3720, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3800, "text": "那"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3960, "text": "些"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4080, "text": "改"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4320, "text": "革"}], "keyword": "秦王"}, {"subtitle_id": 3, "text": "我一直在想。", "timestamp": "00:00:04,600 --> 00:00:05,820", "duration": 1.22, "char_count": 6, "start_time_s": 4.6, "end_time_s": 5.82, "words": [{"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4600, "text": "我"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4840, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5040, "text": "直"}, {"attribute": {"event": "speech"}, "end_time": 5320, "start_time": 5160, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 5820, "start_time": 5320, "text": "想"}], "keyword": "我"}], "keywords": ["龚子华", "秦王", "我"]}, {"chapter": 46, "story_board": "纸上谈兵终觉浅，还是得动手试试。秦国能走到今天，靠的就是敢为人先。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站在一旁，身体微微前倾，认真倾听", "expression": "专注"}, {"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "站着，表情严肃，双手背后", "expression": "坚定"}, {"name": "清", "gender": "男", "age": "青年", "clothes": "高领圆领灰色商人长袍，主色调为灰色，搭配白色，有简单几何纹，束蓝色腰带，挂有钱袋圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "商人", "other": "在商会经营有方", "from_chapter": 70, "action": "坐在酒楼与公子华交谈，向公子华讲述家族来咸阳的目的", "expression": "自然"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "拿着毛笔，站在朝堂上，面对公子华的回答后双手摊开", "expression": "无奈"}, {"name": "辛胜", "gender": "男", "age": "青年", "clothes": "高领圆领黑色秦国士兵服饰，主色调为黑色，搭配灰色，有简单线条纹，束黑色腰带，背箭囊圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "秦国士兵", "other": "负责传递情报", "from_chapter": 70, "action": "走进朝堂，看到公子华后拱手回礼", "expression": "严肃"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "骑在战马上，挥舞着手中的兵器指挥军队进攻", "expression": "坚毅"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "骑马攻入城中，伸手活捉韩王韩安", "expression": "英勇、坚毅"}, {"name": "韩王韩安", "gender": "男", "age": "中年", "clothes": "高领圆领韩国黄色锦袍，主色调为黄色，搭配红色，绣有云纹和花卉纹样，头戴王冠，腰间束黄色锦带，配玉珏圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "韩王", "other": "面容忧虑，心怀忐忑", "from_chapter": 52, "action": "", "expression": ""}, {"name": "斥候河", "gender": "男", "age": "青年", "clothes": "高领圆领青色秦制军装，主色调青色，搭配淡蓝色和白色，军装绣有草叶纹，腰系青色腰带，配有短刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "精瘦", "identity": "秦军斥候", "other": "熟悉战事情况", "from_chapter": [0], "action": "站在殿前讲述战事经过", "expression": "坚定"}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿中，表情严肃、双手背后、坚定站立着的中年男子，他决策果断，坚持自己的主张，身穿高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰，圆形衣领，黑色束发，身材魁梧；旁边身体微微前倾、专注认真倾听的青年男子，他挺拔，言辞犀利，敢言善辩，穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://p9-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/2025072302020491F5F2A2A936BD8BF74D-2415-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1753293732&x-signature=NBzNMlAgZjFRWWlvEGC46%2FCV0ug%3D", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_931b9e81-8d9d-4cd2-ad62-8652d59a4c45_1.25x_20250722_200122.wav", "audio_duration": 6.208, "video_url": "", "story_board_id": 14, "subtitle": [{"subtitle_id": 1, "text": "纸上谈兵中绝遣，", "timestamp": "00:00:00,200 --> 00:00:01,440", "duration": 1.24, "char_count": 8, "start_time_s": 0.2, "end_time_s": 1.44, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "纸"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 440, "text": "谈"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 600, "text": "兵"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 720, "text": "中"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 920, "text": "绝"}, {"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1080, "text": "遣"}], "keyword": "纸上谈兵"}, {"subtitle_id": 2, "text": "还是得动手试试。", "timestamp": "00:00:01,440 --> 00:00:02,860", "duration": 1.42, "char_count": 8, "start_time_s": 1.44, "end_time_s": 2.86, "words": [{"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1440, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1680, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1760, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1880, "text": "动"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2080, "text": "手"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2240, "text": "试"}, {"attribute": {"event": "speech"}, "end_time": 2860, "start_time": 2280, "text": "试"}], "keyword": "动手"}, {"subtitle_id": 3, "text": "秦国能走到今天，", "timestamp": "00:00:03,040 --> 00:00:04,280", "duration": 1.24, "char_count": 8, "start_time_s": 3.04, "end_time_s": 4.28, "words": [{"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3040, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3240, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3360, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3520, "text": "走"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3640, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3760, "text": "今"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 3880, "text": "天"}], "keyword": "秦国"}, {"subtitle_id": 4, "text": "靠的就是敢为人先。", "timestamp": "00:00:04,280 --> 00:00:06,140", "duration": 1.86, "char_count": 9, "start_time_s": 4.28, "end_time_s": 6.14, "words": [{"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4280, "text": "靠"}, {"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4480, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4760, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5000, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 5320, "start_time": 5160, "text": "敢"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5360, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5480, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 6140, "start_time": 5640, "text": "先"}], "keyword": "敢为人先"}], "keywords": ["纸上谈兵", "动手", "秦国", "敢为人先"]}, {"chapter": 46, "story_board": "你以后也要改革，不如趁我现在还在，先把框架搭起来。”\n\n公子华点点头，心里明白，这不仅仅是一次简单的改革，而是为将来做准备。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "点头", "expression": "若有所思"}, {"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "说话", "expression": "语重心长"}, {"name": "清", "gender": "男", "age": "青年", "clothes": "高领圆领灰色商人长袍，主色调为灰色，搭配白色，有简单几何纹，束蓝色腰带，挂有钱袋圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "商人", "other": "在商会经营有方", "from_chapter": 70, "action": "坐在酒楼与公子华交谈，向公子华讲述家族来咸阳的目的", "expression": "自然"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "拿着毛笔，站在朝堂上，面对公子华的回答后双手摊开", "expression": "无奈"}, {"name": "辛胜", "gender": "男", "age": "青年", "clothes": "高领圆领黑色秦国士兵服饰，主色调为黑色，搭配灰色，有简单线条纹，束黑色腰带，背箭囊圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "秦国士兵", "other": "负责传递情报", "from_chapter": 70, "action": "走进朝堂，看到公子华后拱手回礼", "expression": "严肃"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "骑在战马上，挥舞着手中的兵器指挥军队进攻", "expression": "坚毅"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "骑马攻入城中，伸手活捉韩王韩安", "expression": "英勇、坚毅"}, {"name": "韩王韩安", "gender": "男", "age": "中年", "clothes": "高领圆领韩国黄色锦袍，主色调为黄色，搭配红色，绣有云纹和花卉纹样，头戴王冠，腰间束黄色锦带，配玉珏圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "韩王", "other": "面容忧虑，心怀忐忑", "from_chapter": 52, "action": "", "expression": ""}, {"name": "斥候河", "gender": "男", "age": "青年", "clothes": "高领圆领青色秦制军装，主色调青色，搭配淡蓝色和白色，军装绣有草叶纹，腰系青色腰带，配有短刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "精瘦", "identity": "秦军斥候", "other": "熟悉战事情况", "from_chapter": [0], "action": "站在殿前讲述战事经过", "expression": "坚定"}], "scene": "皇宫大殿", "image_prompt": "中景，皇宫大殿的背景下，一个语重心长、魁梧且决策果断、坚持自己主张的中年男子穿着高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰，圆形衣领，黑色束发；旁边一个若有所思地点头、挺拔且言辞犀利、敢言善辩的青年男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753207526_20250723_020527.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_af057bc3-ed68-4234-b4a3-20a209078428_1.25x_20250722_200151.wav", "audio_duration": 9.682667, "video_url": "", "story_board_id": 15, "subtitle": [{"subtitle_id": 1, "text": "你以后也要改革，", "timestamp": "00:00:00,200 --> 00:00:01,240", "duration": 1.04, "char_count": 8, "start_time_s": 0.2, "end_time_s": 1.24, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "你"}, {"attribute": {"event": "speech"}, "end_time": 360, "start_time": 280, "text": "以"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 360, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 480, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 600, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 720, "text": "改"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 920, "text": "革"}], "keyword": "改革"}, {"subtitle_id": 2, "text": "不如趁我现在还在，", "timestamp": "00:00:01,240 --> 00:00:02,600", "duration": 1.36, "char_count": 9, "start_time_s": 1.24, "end_time_s": 2.6, "words": [{"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1240, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1440, "text": "如"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1520, "text": "趁"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1680, "text": "我"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1800, "text": "现"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1960, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2040, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2200, "text": "在"}], "keyword": "现在"}, {"subtitle_id": 3, "text": "先把框架搭起来。", "timestamp": "00:00:02,600 --> 00:00:03,940", "duration": 1.34, "char_count": 8, "start_time_s": 2.6, "end_time_s": 3.94, "words": [{"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2600, "text": "先"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2720, "text": "把"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2880, "text": "框"}, {"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 3040, "text": "架"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3160, "text": "搭"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3360, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 3940, "start_time": 3480, "text": "来"}], "keyword": "框架"}, {"subtitle_id": 4, "text": "公子华点点头，", "timestamp": "00:00:04,000 --> 00:00:05,160", "duration": 1.16, "char_count": 7, "start_time_s": 4.0, "end_time_s": 5.16, "words": [{"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4000, "text": "公"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4200, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4360, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4480, "text": "点"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4520, "text": "点"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 4760, "text": "头"}], "keyword": "公子华"}, {"subtitle_id": 5, "text": "心里明白，", "timestamp": "00:00:05,160 --> 00:00:05,880", "duration": 0.72, "char_count": 5, "start_time_s": 5.16, "end_time_s": 5.88, "words": [{"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5160, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5280, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5400, "text": "明"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5520, "text": "白"}], "keyword": "心里"}, {"subtitle_id": 6, "text": "这不仅仅是一次简单的改革，", "timestamp": "00:00:05,880 --> 00:00:07,760", "duration": 1.88, "char_count": 13, "start_time_s": 5.88, "end_time_s": 7.76, "words": [{"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5880, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6040, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6160, "text": "仅"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6200, "text": "仅"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6440, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6560, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6680, "text": "次"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6840, "text": "简"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 7040, "text": "单"}, {"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 7160, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7280, "text": "改"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7440, "text": "革"}], "keyword": "改革"}, {"subtitle_id": 7, "text": "而是为将来做准备。", "timestamp": "00:00:07,760 --> 00:00:09,340", "duration": 1.58, "char_count": 9, "start_time_s": 7.76, "end_time_s": 9.34, "words": [{"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7760, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7960, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8040, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8200, "text": "将"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8360, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8560, "text": "做"}, {"attribute": {"event": "speech"}, "end_time": 8880, "start_time": 8680, "text": "准"}, {"attribute": {"event": "speech"}, "end_time": 9340, "start_time": 8880, "text": "备"}], "keyword": "准备"}], "keywords": ["改革", "现在", "框架", "公子华", "心里", "改革", "准备"]}, {"chapter": 46, "story_board": "他知道，自己肩上的担子越来越重了。从这一刻起，公子华不再是那个只会在酒楼喝茶的皇子，他开始真正参与到国家大事中。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站着，微微抬头，眼神坚定", "expression": "严肃、沉稳"}, {"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "说话", "expression": "语重心长"}, {"name": "清", "gender": "男", "age": "青年", "clothes": "高领圆领灰色商人长袍，主色调为灰色，搭配白色，有简单几何纹，束蓝色腰带，挂有钱袋圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "商人", "other": "在商会经营有方", "from_chapter": 70, "action": "坐在酒楼与公子华交谈，向公子华讲述家族来咸阳的目的", "expression": "自然"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "拿着毛笔，站在朝堂上，面对公子华的回答后双手摊开", "expression": "无奈"}, {"name": "辛胜", "gender": "男", "age": "青年", "clothes": "高领圆领黑色秦国士兵服饰，主色调为黑色，搭配灰色，有简单线条纹，束黑色腰带，背箭囊圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "秦国士兵", "other": "负责传递情报", "from_chapter": 70, "action": "走进朝堂，看到公子华后拱手回礼", "expression": "严肃"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "骑在战马上，挥舞着手中的兵器指挥军队进攻", "expression": "坚毅"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "骑马攻入城中，伸手活捉韩王韩安", "expression": "英勇、坚毅"}, {"name": "韩王韩安", "gender": "男", "age": "中年", "clothes": "高领圆领韩国黄色锦袍，主色调为黄色，搭配红色，绣有云纹和花卉纹样，头戴王冠，腰间束黄色锦带，配玉珏圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "韩王", "other": "面容忧虑，心怀忐忑", "from_chapter": 52, "action": "", "expression": ""}, {"name": "斥候河", "gender": "男", "age": "青年", "clothes": "高领圆领青色秦制军装，主色调青色，搭配淡蓝色和白色，军装绣有草叶纹，腰系青色腰带，配有短刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "精瘦", "identity": "秦军斥候", "other": "熟悉战事情况", "from_chapter": [0], "action": "站在殿前讲述战事经过", "expression": "坚定"}], "scene": "皇宫大殿", "image_prompt": "中景，一位青年男子站在皇宫大殿中，微微抬头，眼神坚定，表情严肃且沉稳。该男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发，身姿挺拔, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753207723_20250723_020843.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_7ffec9df-b0ee-46cc-9da2-97114810fd51_1.25x_20250722_200142.wav", "audio_duration": 9.472, "video_url": "", "story_board_id": 16, "subtitle": [{"subtitle_id": 1, "text": "他知道自己肩上的担子越来越重了。", "timestamp": "00:00:00,200 --> 00:00:03,060", "duration": 2.86, "char_count": 16, "start_time_s": 0.2, "end_time_s": 3.06, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 440, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 760, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 920, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1040, "text": "肩"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1240, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1400, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1480, "text": "担"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1640, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1880, "text": "越"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2120, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2280, "text": "越"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2440, "text": "重"}, {"attribute": {"event": "speech"}, "end_time": 3060, "start_time": 2560, "text": "了"}], "keyword": "担子"}, {"subtitle_id": 2, "text": "从这一刻起，", "timestamp": "00:00:03,120 --> 00:00:04,080", "duration": 0.96, "char_count": 6, "start_time_s": 3.12, "end_time_s": 4.08, "words": [{"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3120, "text": "从"}, {"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3320, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3440, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3480, "text": "刻"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3640, "text": "起"}], "keyword": "这一刻"}, {"subtitle_id": 3, "text": "龚子华", "timestamp": "00:00:04,080 --> 00:00:04,520", "duration": 0.44, "char_count": 3, "start_time_s": 4.08, "end_time_s": 4.52, "words": [{"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4080, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4240, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4360, "text": "华"}], "keyword": "龚子华"}, {"subtitle_id": 4, "text": "不再是那个只会在酒楼喝茶的皇子，", "timestamp": "00:00:04,520 --> 00:00:07,120", "duration": 2.6, "char_count": 16, "start_time_s": 4.52, "end_time_s": 7.12, "words": [{"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4520, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4680, "text": "再"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4840, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 4960, "text": "那"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5120, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5360, "text": "只"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5560, "text": "会"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5720, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5880, "text": "酒"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6040, "text": "楼"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6160, "text": "喝"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6360, "text": "茶"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6520, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6600, "text": "皇"}, {"attribute": {"event": "speech"}, "end_time": 7120, "start_time": 6800, "text": "子"}], "keyword": "皇子"}, {"subtitle_id": 5, "text": "他开始真正参与到国家大事中。", "timestamp": "00:00:07,120 --> 00:00:09,380", "duration": 2.26, "char_count": 14, "start_time_s": 7.12, "end_time_s": 9.38, "words": [{"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 7120, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7280, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7400, "text": "始"}, {"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7560, "text": "真"}, {"attribute": {"event": "speech"}, "end_time": 7880, "start_time": 7720, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7920, "text": "参"}, {"attribute": {"event": "speech"}, "end_time": 8160, "start_time": 8040, "text": "与"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8160, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8320, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8520, "text": "家"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8640, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8800, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 9380, "start_time": 8960, "text": "中"}], "keyword": "国家大事"}], "keywords": ["担子", "这一刻", "龚子华", "皇子", "国家大事"]}, {"chapter": 46, "story_board": "他看到了权力背后的重量，也明白了责任的意义。接下来的日子里，他一边协助父亲处理朝政，一边继续完善自己的改革计划。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "在朝堂协助秦王处理朝政，在书房伏案完善改革计划", "expression": "坚定、沉稳"}, {"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "说话", "expression": "语重心长"}, {"name": "清", "gender": "男", "age": "青年", "clothes": "高领圆领灰色商人长袍，主色调为灰色，搭配白色，有简单几何纹，束蓝色腰带，挂有钱袋圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "商人", "other": "在商会经营有方", "from_chapter": 70, "action": "坐在酒楼与公子华交谈，向公子华讲述家族来咸阳的目的", "expression": "自然"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "拿着毛笔，站在朝堂上，面对公子华的回答后双手摊开", "expression": "无奈"}, {"name": "辛胜", "gender": "男", "age": "青年", "clothes": "高领圆领黑色秦国士兵服饰，主色调为黑色，搭配灰色，有简单线条纹，束黑色腰带，背箭囊圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "秦国士兵", "other": "负责传递情报", "from_chapter": 70, "action": "走进朝堂，看到公子华后拱手回礼", "expression": "严肃"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "骑在战马上，挥舞着手中的兵器指挥军队进攻", "expression": "坚毅"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "骑马攻入城中，伸手活捉韩王韩安", "expression": "英勇、坚毅"}, {"name": "韩王韩安", "gender": "男", "age": "中年", "clothes": "高领圆领韩国黄色锦袍，主色调为黄色，搭配红色，绣有云纹和花卉纹样，头戴王冠，腰间束黄色锦带，配玉珏圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "韩王", "other": "面容忧虑，心怀忐忑", "from_chapter": 52, "action": "", "expression": ""}, {"name": "斥候河", "gender": "男", "age": "青年", "clothes": "高领圆领青色秦制军装，主色调青色，搭配淡蓝色和白色，军装绣有草叶纹，腰系青色腰带，配有短刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "精瘦", "identity": "秦军斥候", "other": "熟悉战事情况", "from_chapter": [0], "action": "站在殿前讲述战事经过", "expression": "坚定"}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿的书房内，一名神情坚定、沉稳的青年男子正伏案完善改革计划，他身姿挺拔。该男子身着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，有着圆形衣领，头发为黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://p9-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/20250723021141AABC09B690A8F18C1061-8489-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1753294306&x-signature=%2FBZg%2B0L80A73Q1%2BoAoSFjvW%2B81I%3D", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_08c19ece-f4b7-4a72-b427-16324c20038b_1.25x_20250722_200147.wav", "audio_duration": 9.448, "video_url": "", "story_board_id": 17, "subtitle": [{"subtitle_id": 1, "text": "他看到了权利背后的重量，", "timestamp": "00:00:00,200 --> 00:00:01,880", "duration": 1.68, "char_count": 12, "start_time_s": 0.2, "end_time_s": 1.88, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 280, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 480, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 600, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 680, "text": "权"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 840, "text": "利"}, {"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 1000, "text": "背"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1160, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1320, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1440, "text": "重"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1560, "text": "量"}], "keyword": "权利"}, {"subtitle_id": 2, "text": "也明白了责任的意义。", "timestamp": "00:00:01,880 --> 00:00:03,700", "duration": 1.82, "char_count": 10, "start_time_s": 1.88, "end_time_s": 3.7, "words": [{"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1880, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2080, "text": "明"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2240, "text": "白"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2400, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2560, "text": "责"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2800, "text": "任"}, {"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2920, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3080, "text": "意"}, {"attribute": {"event": "speech"}, "end_time": 3700, "start_time": 3200, "text": "义"}], "keyword": "责任"}, {"subtitle_id": 3, "text": "接下来的日子里，", "timestamp": "00:00:03,840 --> 00:00:04,880", "duration": 1.04, "char_count": 8, "start_time_s": 3.84, "end_time_s": 4.88, "words": [{"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3840, "text": "接"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4040, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4160, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4240, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4320, "text": "日"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4480, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4560, "text": "里"}], "keyword": "日子"}, {"subtitle_id": 4, "text": "他一边协助父亲处理朝政，", "timestamp": "00:00:04,880 --> 00:00:06,800", "duration": 1.92, "char_count": 12, "start_time_s": 4.88, "end_time_s": 6.8, "words": [{"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 4880, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5080, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5160, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5320, "text": "协"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5520, "text": "助"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5680, "text": "父"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5840, "text": "亲"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6040, "text": "处"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6200, "text": "理"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6320, "text": "朝"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6560, "text": "政"}], "keyword": "朝政"}, {"subtitle_id": 5, "text": "一边继续完善自己的改革计划。", "timestamp": "00:00:06,800 --> 00:00:09,140", "duration": 2.34, "char_count": 14, "start_time_s": 6.8, "end_time_s": 9.14, "words": [{"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6800, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 7120, "start_time": 7000, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 7360, "start_time": 7200, "text": "继"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7360, "text": "续"}, {"attribute": {"event": "speech"}, "end_time": 7640, "start_time": 7480, "text": "完"}, {"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7640, "text": "善"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7800, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7960, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 8160, "start_time": 8080, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8160, "text": "改"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8360, "text": "革"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8520, "text": "计"}, {"attribute": {"event": "speech"}, "end_time": 9140, "start_time": 8640, "text": "划"}], "keyword": "改革计划"}], "keywords": ["权利", "责任", "日子", "朝政", "改革计划"]}, {"chapter": 46, "story_board": "他深知，秦国的未来，不只是靠武力，更需要制度和人才的支持。而在朝堂之外，他也开始留意那些有才能的人，希望能为秦国培养更多栋梁之材。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "在街道上漫步，眼睛四处观察，不时与路人交谈", "expression": "专注、期待"}, {"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "说话", "expression": "语重心长"}, {"name": "清", "gender": "男", "age": "青年", "clothes": "高领圆领灰色商人长袍，主色调为灰色，搭配白色，有简单几何纹，束蓝色腰带，挂有钱袋圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "商人", "other": "在商会经营有方", "from_chapter": 70, "action": "坐在酒楼与公子华交谈，向公子华讲述家族来咸阳的目的", "expression": "自然"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "拿着毛笔，站在朝堂上，面对公子华的回答后双手摊开", "expression": "无奈"}, {"name": "辛胜", "gender": "男", "age": "青年", "clothes": "高领圆领黑色秦国士兵服饰，主色调为黑色，搭配灰色，有简单线条纹，束黑色腰带，背箭囊圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "秦国士兵", "other": "负责传递情报", "from_chapter": 70, "action": "走进朝堂，看到公子华后拱手回礼", "expression": "严肃"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "骑在战马上，挥舞着手中的兵器指挥军队进攻", "expression": "坚毅"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "骑马攻入城中，伸手活捉韩王韩安", "expression": "英勇、坚毅"}, {"name": "韩王韩安", "gender": "男", "age": "中年", "clothes": "高领圆领韩国黄色锦袍，主色调为黄色，搭配红色，绣有云纹和花卉纹样，头戴王冠，腰间束黄色锦带，配玉珏圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "韩王", "other": "面容忧虑，心怀忐忑", "from_chapter": 52, "action": "", "expression": ""}, {"name": "斥候河", "gender": "男", "age": "青年", "clothes": "高领圆领青色秦制军装，主色调青色，搭配淡蓝色和白色，军装绣有草叶纹，腰系青色腰带，配有短刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "精瘦", "identity": "秦军斥候", "other": "熟悉战事情况", "from_chapter": [0], "action": "站在殿前讲述战事经过", "expression": "坚定"}], "scene": "中国古代建筑", "image_prompt": "中景，在古色古香的中国古代建筑街道上，一个神情专注且带着期待的青年男子正漫步其中，眼睛四处观察。该男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发，身姿挺拔, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753208101_20250723_021501.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_5fb07034-2c89-42a6-a995-82084bb69b44_1.25x_20250722_200203.wav", "audio_duration": 11.602667, "video_url": "", "story_board_id": 18, "subtitle": [{"subtitle_id": 1, "text": "他深知秦国的未来不只是靠武力，", "timestamp": "00:00:00,200 --> 00:00:02,960", "duration": 2.76, "char_count": 15, "start_time_s": 0.2, "end_time_s": 2.96, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "深"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 440, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 840, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1040, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1200, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1320, "text": "未"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1480, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1800, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2040, "text": "只"}, {"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2160, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2320, "text": "靠"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2480, "text": "武"}, {"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2680, "text": "力"}], "keyword": "秦国"}, {"subtitle_id": 2, "text": "更需要制度和人才的支持。", "timestamp": "00:00:02,960 --> 00:00:05,100", "duration": 2.14, "char_count": 12, "start_time_s": 2.96, "end_time_s": 5.1, "words": [{"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 2960, "text": "更"}, {"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3160, "text": "需"}, {"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3280, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3440, "text": "制"}, {"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3640, "text": "度"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3800, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 3960, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4160, "text": "才"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4360, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4480, "text": "支"}, {"attribute": {"event": "speech"}, "end_time": 5100, "start_time": 4600, "text": "持"}], "keyword": "制度"}, {"subtitle_id": 3, "text": "而在朝堂之外，", "timestamp": "00:00:05,160 --> 00:00:06,280", "duration": 1.12, "char_count": 7, "start_time_s": 5.16, "end_time_s": 6.28, "words": [{"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5160, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5400, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5520, "text": "朝"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5720, "text": "堂"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5840, "text": "之"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 5960, "text": "外"}], "keyword": "朝堂"}, {"subtitle_id": 4, "text": "他也开始留意那些有才能的人，", "timestamp": "00:00:06,280 --> 00:00:08,520", "duration": 2.24, "char_count": 14, "start_time_s": 6.28, "end_time_s": 8.52, "words": [{"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6280, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6440, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6520, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6680, "text": "始"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6800, "text": "留"}, {"attribute": {"event": "speech"}, "end_time": 7120, "start_time": 6960, "text": "意"}, {"attribute": {"event": "speech"}, "end_time": 7360, "start_time": 7160, "text": "那"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7360, "text": "些"}, {"attribute": {"event": "speech"}, "end_time": 7640, "start_time": 7480, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 7880, "start_time": 7640, "text": "才"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7920, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 8040, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8120, "text": "人"}], "keyword": "人才"}, {"subtitle_id": 5, "text": "希望能为秦国培养更多栋梁之才。", "timestamp": "00:00:08,520 --> 00:00:11,260", "duration": 2.74, "char_count": 15, "start_time_s": 8.52, "end_time_s": 11.26, "words": [{"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8520, "text": "希"}, {"attribute": {"event": "speech"}, "end_time": 8760, "start_time": 8680, "text": "望"}, {"attribute": {"event": "speech"}, "end_time": 8880, "start_time": 8760, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8880, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 9200, "start_time": 9040, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 9400, "start_time": 9240, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 9680, "start_time": 9520, "text": "培"}, {"attribute": {"event": "speech"}, "end_time": 9840, "start_time": 9680, "text": "养"}, {"attribute": {"event": "speech"}, "end_time": 10080, "start_time": 9880, "text": "更"}, {"attribute": {"event": "speech"}, "end_time": 10280, "start_time": 10120, "text": "多"}, {"attribute": {"event": "speech"}, "end_time": 10400, "start_time": 10280, "text": "栋"}, {"attribute": {"event": "speech"}, "end_time": 10600, "start_time": 10440, "text": "梁"}, {"attribute": {"event": "speech"}, "end_time": 10760, "start_time": 10600, "text": "之"}, {"attribute": {"event": "speech"}, "end_time": 11260, "start_time": 10760, "text": "才"}], "keyword": "栋梁"}], "keywords": ["秦国", "制度", "朝堂", "人才", "栋梁"]}, {"chapter": 46, "story_board": "他相信，只要方向正确，秦国终有一天会统一六国，成为真正的天下霸主。这个故事告诉我们，成功从来不是一蹴而就的。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站在窗前，望向远方", "expression": "坚定、充满希望"}, {"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "说话", "expression": "语重心长"}, {"name": "清", "gender": "男", "age": "青年", "clothes": "高领圆领灰色商人长袍，主色调为灰色，搭配白色，有简单几何纹，束蓝色腰带，挂有钱袋圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "商人", "other": "在商会经营有方", "from_chapter": 70, "action": "坐在酒楼与公子华交谈，向公子华讲述家族来咸阳的目的", "expression": "自然"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "拿着毛笔，站在朝堂上，面对公子华的回答后双手摊开", "expression": "无奈"}, {"name": "辛胜", "gender": "男", "age": "青年", "clothes": "高领圆领黑色秦国士兵服饰，主色调为黑色，搭配灰色，有简单线条纹，束黑色腰带，背箭囊圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "秦国士兵", "other": "负责传递情报", "from_chapter": 70, "action": "走进朝堂，看到公子华后拱手回礼", "expression": "严肃"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "骑在战马上，挥舞着手中的兵器指挥军队进攻", "expression": "坚毅"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "骑马攻入城中，伸手活捉韩王韩安", "expression": "英勇、坚毅"}, {"name": "韩王韩安", "gender": "男", "age": "中年", "clothes": "高领圆领韩国黄色锦袍，主色调为黄色，搭配红色，绣有云纹和花卉纹样，头戴王冠，腰间束黄色锦带，配玉珏圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "韩王", "other": "面容忧虑，心怀忐忑", "from_chapter": 52, "action": "", "expression": ""}, {"name": "斥候河", "gender": "男", "age": "青年", "clothes": "高领圆领青色秦制军装，主色调青色，搭配淡蓝色和白色，军装绣有草叶纹，腰系青色腰带，配有短刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "精瘦", "identity": "秦军斥候", "other": "熟悉战事情况", "from_chapter": [0], "action": "站在殿前讲述战事经过", "expression": "坚定"}], "scene": "东宫", "image_prompt": "中景，站在窗前望向远方，表情坚定且充满希望的青年男子，挺拔身姿，言辞犀利、敢言善辩；背景是东宫。该青年男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753208279_20250723_021800.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_43432128-33b1-4b4d-8322-53f1fe87678f_1.25x_20250722_200159.wav", "audio_duration": 9.336, "video_url": "", "story_board_id": 19, "subtitle": [{"subtitle_id": 1, "text": "他相信，只要方向正确，", "timestamp": "00:00:00,160 --> 00:00:01,960", "duration": 1.8, "char_count": 11, "start_time_s": 0.16, "end_time_s": 1.96, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "相"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 440, "text": "信"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 840, "text": "只"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 960, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1080, "text": "方"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1240, "text": "向"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1400, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1600, "text": "确"}], "keyword": "他"}, {"subtitle_id": 2, "text": "秦国终有一天会统一六国，", "timestamp": "00:00:01,960 --> 00:00:04,080", "duration": 2.12, "char_count": 12, "start_time_s": 1.96, "end_time_s": 4.08, "words": [{"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1960, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2120, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2320, "text": "终"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2440, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2600, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2680, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3000, "text": "会"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3200, "text": "统"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3400, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3560, "text": "六"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3760, "text": "国"}], "keyword": "秦国"}, {"subtitle_id": 3, "text": "成为真正的天下霸主。", "timestamp": "00:00:04,080 --> 00:00:05,900", "duration": 1.82, "char_count": 10, "start_time_s": 4.08, "end_time_s": 5.9, "words": [{"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4080, "text": "成"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4240, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4360, "text": "真"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4600, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4720, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4880, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5080, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5240, "text": "霸"}, {"attribute": {"event": "speech"}, "end_time": 5900, "start_time": 5440, "text": "主"}], "keyword": "霸主"}, {"subtitle_id": 4, "text": "这个故事告诉我们，", "timestamp": "00:00:06,000 --> 00:00:07,320", "duration": 1.32, "char_count": 9, "start_time_s": 6.0, "end_time_s": 7.32, "words": [{"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6000, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6200, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6320, "text": "故"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6440, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6600, "text": "告"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6720, "text": "诉"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6840, "text": "我"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 6920, "text": "们"}], "keyword": "故事"}, {"subtitle_id": 5, "text": "成功从来不是一蹴而就的。", "timestamp": "00:00:07,320 --> 00:00:09,260", "duration": 1.94, "char_count": 12, "start_time_s": 7.32, "end_time_s": 9.26, "words": [{"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7320, "text": "成"}, {"attribute": {"event": "speech"}, "end_time": 7640, "start_time": 7480, "text": "功"}, {"attribute": {"event": "speech"}, "end_time": 7840, "start_time": 7640, "text": "从"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7840, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 7960, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8120, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8200, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8360, "text": "蹴"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8520, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8640, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 9260, "start_time": 8800, "text": "的"}], "keyword": "成功"}], "keywords": ["他", "秦国", "霸主", "故事", "成功"]}, {"chapter": 46, "story_board": "它需要智慧、勇气，还需要一点点运气。而公子华，正是那个在关键时刻站出来，推动历史前进的人。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "昂首挺胸，眼神坚定地站在朝堂之上", "expression": "自信、坚毅"}, {"name": "秦王嬴政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服帝王龙袍，主色调为黑色，搭配金色，袍身绣满金色龙纹，腰间束黑色镶金边腰带，头戴冕旒，两侧垂有珠玉串饰圆形衣领，圆形衣领", "hairstyle": "黑色束发，头戴冕旒", "figure": "魁梧", "identity": "秦国国君", "other": "决策果断，坚持自己的主张", "from_chapter": 31, "action": "说话", "expression": "语重心长"}, {"name": "清", "gender": "男", "age": "青年", "clothes": "高领圆领灰色商人长袍，主色调为灰色，搭配白色，有简单几何纹，束蓝色腰带，挂有钱袋圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "中等身材", "identity": "商人", "other": "在商会经营有方", "from_chapter": 70, "action": "坐在酒楼与公子华交谈，向公子华讲述家族来咸阳的目的", "expression": "自然"}, {"name": "蒙恬", "gender": "男", "age": "青年", "clothes": "秦代金色铠甲，主色调为金色，搭配黄色缨穗，铠甲上有虎纹图案，头戴金色束发盔，系黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "高大", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "拿着毛笔，站在朝堂上，面对公子华的回答后双手摊开", "expression": "无奈"}, {"name": "辛胜", "gender": "男", "age": "青年", "clothes": "高领圆领黑色秦国士兵服饰，主色调为黑色，搭配灰色，有简单线条纹，束黑色腰带，背箭囊圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "秦国士兵", "other": "负责传递情报", "from_chapter": 70, "action": "走进朝堂，看到公子华后拱手回礼", "expression": "严肃"}, {"name": "王贲", "gender": "男", "age": "青年", "clothes": "秦代银色铠甲，主色调为银色，搭配蓝色缨穗，铠甲上有鱼鳞纹，头戴银色束发盔，系蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "英武", "identity": "秦国将领", "other": "支持公子华", "from_chapter": 7, "action": "骑在战马上，挥舞着手中的兵器指挥军队进攻", "expression": "坚毅"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "骑马攻入城中，伸手活捉韩王韩安", "expression": "英勇、坚毅"}, {"name": "韩王韩安", "gender": "男", "age": "中年", "clothes": "高领圆领韩国黄色锦袍，主色调为黄色，搭配红色，绣有云纹和花卉纹样，头戴王冠，腰间束黄色锦带，配玉珏圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "韩王", "other": "面容忧虑，心怀忐忑", "from_chapter": 52, "action": "", "expression": ""}, {"name": "斥候河", "gender": "男", "age": "青年", "clothes": "高领圆领青色秦制军装，主色调青色，搭配淡蓝色和白色，军装绣有草叶纹，腰系青色腰带，配有短刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "精瘦", "identity": "秦军斥候", "other": "熟悉战事情况", "from_chapter": [0], "action": "站在殿前讲述战事经过", "expression": "坚定"}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿中，昂首挺胸、眼神坚定、自信坚毅地站立着的青年男子，他言辞犀利、敢言善辩。该青年男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，有着圆形衣领，黑色束发，身材挺拔, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753208446_20250723_022046.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_dc916e1c-008f-424e-9af0-d42c5359d2d5_1.25x_20250722_200155.wav", "audio_duration": 7.912, "video_url": "", "story_board_id": 20, "subtitle": [{"subtitle_id": 1, "text": "他需要智慧、勇气，", "timestamp": "00:00:00,200 --> 00:00:01,400", "duration": 1.2, "char_count": 9, "start_time_s": 0.2, "end_time_s": 1.4, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "需"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 400, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 600, "text": "智"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 800, "text": "慧"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 920, "text": "勇"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1120, "text": "气"}], "keyword": "智慧"}, {"subtitle_id": 2, "text": "还需要一点点运气。", "timestamp": "00:00:01,400 --> 00:00:03,060", "duration": 1.66, "char_count": 9, "start_time_s": 1.4, "end_time_s": 3.06, "words": [{"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1400, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1600, "text": "需"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1720, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1880, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 2040, "text": "点"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2080, "text": "点"}, {"attribute": {"event": "speech"}, "end_time": 2520, "start_time": 2360, "text": "运"}, {"attribute": {"event": "speech"}, "end_time": 3060, "start_time": 2600, "text": "气"}], "keyword": "运气"}, {"subtitle_id": 3, "text": "而龚子华", "timestamp": "00:00:03,120 --> 00:00:04,040", "duration": 0.92, "char_count": 4, "start_time_s": 3.12, "end_time_s": 4.04, "words": [{"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3120, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3360, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3480, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3640, "text": "华"}], "keyword": "龚子华"}, {"subtitle_id": 4, "text": "正是那个在关键时刻站出来", "timestamp": "00:00:04,040 --> 00:00:05,920", "duration": 1.88, "char_count": 12, "start_time_s": 4.04, "end_time_s": 5.92, "words": [{"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4040, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4200, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4360, "text": "那"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4480, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4600, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4720, "text": "关"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4880, "text": "键"}, {"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5040, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5200, "text": "刻"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5360, "text": "站"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5560, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5680, "text": "来"}], "keyword": "站出来"}, {"subtitle_id": 5, "text": "推动历史前进的人。", "timestamp": "00:00:05,920 --> 00:00:07,580", "duration": 1.66, "char_count": 9, "start_time_s": 5.92, "end_time_s": 7.58, "words": [{"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5920, "text": "推"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6160, "text": "动"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6280, "text": "历"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6440, "text": "史"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6600, "text": "前"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6880, "text": "进"}, {"attribute": {"event": "speech"}, "end_time": 7120, "start_time": 7000, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7580, "start_time": 7120, "text": "人"}], "keyword": "历史前进"}], "keywords": ["智慧", "运气", "龚子华", "站出来", "历史前进"]}]