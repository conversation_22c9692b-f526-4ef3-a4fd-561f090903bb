[{"chapter": 4, "story_board": "清晨的阳光刚洒进宫墙，一缕晨风掀动帘子，公子华被阿房女叫醒。他揉了揉眼睛，一脸懵懂地坐起来，嘴里嘟囔着：“这大清早谁啊，这么着急。”阿房女一把拉起他：“快点快点，早朝要开始了，你再不起来可就迟到了！", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": [0], "action": "被阿房女叫醒后揉了揉眼睛，一脸懵懂地坐起来，嘴里嘟囔着", "expression": "懵懂"}, {"name": "阿房女（夏阿房）", "gender": "女", "age": "中年", "clothes": "高领圆领汉服深粉色宫装，主色调为深粉色，搭配淡黄色，有花卉纹样，头戴金簪发饰，颈戴珍珠项链圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "端庄", "identity": "公子华的母亲", "other": "温柔关怀", "from_chapter": [0], "action": "一把拉起公子华", "expression": "着急"}, {"name": "赢政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服黑色龙袍，主色调为黑色，搭配金黄色，绣有五爪金龙纹，头戴冕旒，腰系黄色龙纹腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发头戴冕旒", "figure": "威严", "identity": "大秦大王", "other": "沉稳睿智", "from_chapter": [0], "action": "", "expression": ""}, {"name": "太监", "gender": "男", "age": "中年", "clothes": "高领圆领汉服灰色太监服饰，主色调为灰色，搭配白色，有简单的线条花纹，头戴黑色小帽，腰系灰色布带圆形衣领，圆形衣领", "hairstyle": "光头", "figure": "瘦小", "identity": "侍奉大王的太监", "other": "声音尖细", "from_chapter": [0], "action": "", "expression": ""}, {"name": "左右丞相和上卿", "gender": "男", "age": "中年", "clothes": "高领圆领汉服深蓝色官服，主色调为深蓝色，搭配浅蓝色，绣有山水纹样，头戴乌纱帽，腰系蓝色腰带，佩戴朝珠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "朝廷大臣", "other": "处理国事", "from_chapter": [0], "action": "", "expression": ""}, {"name": "老臣（淳于越）", "gender": "男", "age": "老者", "clothes": "高领圆领汉服褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带圆形衣领，圆形衣领", "hairstyle": "白色束发", "figure": "清瘦", "identity": "朝廷大臣", "other": "迂腐守旧", "from_chapter": [0], "action": "", "expression": ""}, {"name": "邓山", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配红色，有虎纹图案，头戴黑色毡帽，腰系黑色皮腰带，佩刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "侍卫", "other": "听从公子华指令", "from_chapter": [0], "action": "", "expression": ""}, {"name": "侍卫们", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配银色，有简单的几何花纹，头戴黑色头盔，腰系黑色腰带，佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "宫廷侍卫", "other": "执行命令", "from_chapter": [0], "action": "", "expression": ""}, {"name": "秋香和秋碟", "gender": "女", "age": "少女", "clothes": "高领圆领汉服浅绿色丫鬟服饰，主色调为浅绿色，搭配白色，有蝴蝶纹样，头戴绿色发带，腰系绿色丝带圆形衣领，圆形衣领", "hairstyle": "黑色双马尾", "figure": "娇小", "identity": "公子华的随身侍女", "other": "机灵乖巧", "from_chapter": [0], "action": "", "expression": ""}], "scene": "室内", "image_prompt": "中景，室内，一位着急的中年女子穿着高领圆领汉服深粉色宫装，主色调为深粉色，搭配淡黄色，有花卉纹样，头戴金簪发饰，颈戴珍珠项链，圆形衣领，黑色发髻，一把拉起一位一脸懵懂地坐起来、正在揉眼睛的青年男子；青年男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753194435_20250722_222716.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_835d7a24-13a4-40fc-ad22-bc50d9037469_1.25x_20250722_193620.wav", "audio_duration": 16.248, "video_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/videos/video_1753194467_20250722_222748.mp4", "story_board_id": 1, "subtitle": [{"subtitle_id": 1, "text": "清晨的阳光刚洒进宫墙，", "timestamp": "00:00:00,200 --> 00:00:01,960", "duration": 1.76, "char_count": 11, "start_time_s": 0.2, "end_time_s": 1.96, "words": [{"attribute": {"event": "speech"}, "end_time": 360, "start_time": 200, "text": "清"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 360, "text": "晨"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 480, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 600, "text": "阳"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 720, "text": "光"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 920, "text": "刚"}, {"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 1120, "text": "洒"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1280, "text": "进"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1400, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1600, "text": "墙"}], "keyword": "清晨"}, {"subtitle_id": 2, "text": "一缕尘风掀动帘子，", "timestamp": "00:00:01,960 --> 00:00:03,480", "duration": 1.52, "char_count": 9, "start_time_s": 1.96, "end_time_s": 3.48, "words": [{"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1960, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2120, "text": "缕"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2280, "text": "尘"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2480, "text": "风"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2680, "text": "掀"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2840, "text": "动"}, {"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 3000, "text": "帘"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3160, "text": "子"}], "keyword": "尘风"}, {"subtitle_id": 3, "text": "龚子华被阿房女叫醒，", "timestamp": "00:00:03,480 --> 00:00:05,260", "duration": 1.78, "char_count": 10, "start_time_s": 3.48, "end_time_s": 5.26, "words": [{"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3480, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3640, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3760, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3960, "text": "被"}, {"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4080, "text": "阿"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4240, "text": "房"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4440, "text": "女"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4560, "text": "叫"}, {"attribute": {"event": "speech"}, "end_time": 5260, "start_time": 4800, "text": "醒"}], "keyword": "龚子华"}, {"subtitle_id": 4, "text": "她揉了揉眼睛，", "timestamp": "00:00:05,360 --> 00:00:06,360", "duration": 1.0, "char_count": 7, "start_time_s": 5.36, "end_time_s": 6.36, "words": [{"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5360, "text": "她"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5520, "text": "揉"}, {"attribute": {"event": "speech"}, "end_time": 5760, "start_time": 5680, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5760, "text": "揉"}, {"attribute": {"event": "speech"}, "end_time": 6080, "start_time": 5880, "text": "眼"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6080, "text": "睛"}], "keyword": "眼睛"}, {"subtitle_id": 5, "text": "一脸懵懂的坐起来，", "timestamp": "00:00:06,360 --> 00:00:07,680", "duration": 1.32, "char_count": 9, "start_time_s": 6.36, "end_time_s": 7.68, "words": [{"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6360, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6520, "text": "脸"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6640, "text": "懵"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6880, "text": "懂"}, {"attribute": {"event": "speech"}, "end_time": 7120, "start_time": 7000, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 7120, "text": "坐"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7280, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7400, "text": "来"}], "keyword": "坐起"}, {"subtitle_id": 6, "text": "嘴里嘟囔着这大清早谁呀这么着急？", "timestamp": "00:00:07,680 --> 00:00:10,980", "duration": 3.3, "char_count": 16, "start_time_s": 7.68, "end_time_s": 10.98, "words": [{"attribute": {"event": "speech"}, "end_time": 7840, "start_time": 7680, "text": "嘴"}, {"attribute": {"event": "speech"}, "end_time": 8000, "start_time": 7840, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 8160, "start_time": 8000, "text": "嘟"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8160, "text": "囔"}, {"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8320, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8640, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8800, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 9120, "start_time": 8960, "text": "清"}, {"attribute": {"event": "speech"}, "end_time": 9320, "start_time": 9160, "text": "早"}, {"attribute": {"event": "speech"}, "end_time": 9520, "start_time": 9400, "text": "谁"}, {"attribute": {"event": "speech"}, "end_time": 9720, "start_time": 9520, "text": "呀"}, {"attribute": {"event": "speech"}, "end_time": 10120, "start_time": 9920, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 10280, "start_time": 10120, "text": "么"}, {"attribute": {"event": "speech"}, "end_time": 10400, "start_time": 10280, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 10980, "start_time": 10480, "text": "急"}], "keyword": "大清早"}, {"subtitle_id": 7, "text": "阿房女一把拉起她快点", "timestamp": "00:00:11,160 --> 00:00:12,640", "duration": 1.48, "char_count": 10, "start_time_s": 11.16, "end_time_s": 12.64, "words": [{"attribute": {"event": "speech"}, "end_time": 11200, "start_time": 11160, "text": "阿"}, {"attribute": {"event": "speech"}, "end_time": 11400, "start_time": 11280, "text": "房"}, {"attribute": {"event": "speech"}, "end_time": 11480, "start_time": 11400, "text": "女"}, {"attribute": {"event": "speech"}, "end_time": 11600, "start_time": 11480, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 11720, "start_time": 11600, "text": "把"}, {"attribute": {"event": "speech"}, "end_time": 11840, "start_time": 11720, "text": "拉"}, {"attribute": {"event": "speech"}, "end_time": 11960, "start_time": 11840, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 12160, "start_time": 11960, "text": "她"}, {"attribute": {"event": "speech"}, "end_time": 12520, "start_time": 12360, "text": "快"}, {"attribute": {"event": "speech"}, "end_time": 12640, "start_time": 12520, "text": "点"}], "keyword": "阿房女"}, {"subtitle_id": 8, "text": "快点，早朝要开始了，", "timestamp": "00:00:12,640 --> 00:00:14,200", "duration": 1.56, "char_count": 10, "start_time_s": 12.64, "end_time_s": 14.2, "words": [{"attribute": {"event": "speech"}, "end_time": 12840, "start_time": 12640, "text": "快"}, {"attribute": {"event": "speech"}, "end_time": 13000, "start_time": 12840, "text": "点"}, {"attribute": {"event": "speech"}, "end_time": 13360, "start_time": 13200, "text": "早"}, {"attribute": {"event": "speech"}, "end_time": 13520, "start_time": 13360, "text": "朝"}, {"attribute": {"event": "speech"}, "end_time": 13680, "start_time": 13520, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 13840, "start_time": 13680, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 14000, "start_time": 13840, "text": "始"}, {"attribute": {"event": "speech"}, "end_time": 14200, "start_time": 14000, "text": "了"}], "keyword": "早朝"}, {"subtitle_id": 9, "text": "你再不起来可就迟到了！", "timestamp": "00:00:14,200 --> 00:00:15,980", "duration": 1.78, "char_count": 11, "start_time_s": 14.2, "end_time_s": 15.98, "words": [{"attribute": {"event": "speech"}, "end_time": 14360, "start_time": 14200, "text": "你"}, {"attribute": {"event": "speech"}, "end_time": 14480, "start_time": 14360, "text": "再"}, {"attribute": {"event": "speech"}, "end_time": 14600, "start_time": 14480, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 14680, "start_time": 14600, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 14840, "start_time": 14680, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 15040, "start_time": 14880, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 15120, "start_time": 15040, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 15320, "start_time": 15120, "text": "迟"}, {"attribute": {"event": "speech"}, "end_time": 15480, "start_time": 15320, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 15980, "start_time": 15480, "text": "了"}], "keyword": "迟到"}], "keywords": ["清晨", "尘风", "龚子华", "眼睛", "坐起", "大清早", "阿房女", "早朝", "迟到"]}, {"chapter": 4, "story_board": "”她一边帮他整理衣领，一边检查他的打扮，确认没问题后才让他出门。公子华刚走到勤政殿门口，就看到一群大臣陆续进来，彼此打着招呼，气氛热闹得很。他站到靠前的位置，等着早朝开始。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": [0], "action": "走到勤政殿门口，看到大臣后站到靠前的位置等待早朝开始", "expression": "懵懂、平静"}, {"name": "阿房女（夏阿房）", "gender": "女", "age": "中年", "clothes": "高领圆领汉服深粉色宫装，主色调为深粉色，搭配淡黄色，有花卉纹样，头戴金簪发饰，颈戴珍珠项链圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "端庄", "identity": "公子华的母亲", "other": "温柔关怀", "from_chapter": [0], "action": "帮公子华整理衣领，检查他的打扮，然后让他出门", "expression": "着急"}, {"name": "赢政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服黑色龙袍，主色调为黑色，搭配金黄色，绣有五爪金龙纹，头戴冕旒，腰系黄色龙纹腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发头戴冕旒", "figure": "威严", "identity": "大秦大王", "other": "沉稳睿智", "from_chapter": [0], "action": "", "expression": ""}, {"name": "太监", "gender": "男", "age": "中年", "clothes": "高领圆领汉服灰色太监服饰，主色调为灰色，搭配白色，有简单的线条花纹，头戴黑色小帽，腰系灰色布带圆形衣领，圆形衣领", "hairstyle": "光头", "figure": "瘦小", "identity": "侍奉大王的太监", "other": "声音尖细", "from_chapter": [0], "action": "", "expression": ""}, {"name": "左右丞相和上卿", "gender": "男", "age": "中年", "clothes": "高领圆领汉服深蓝色官服，主色调为深蓝色，搭配浅蓝色，绣有山水纹样，头戴乌纱帽，腰系蓝色腰带，佩戴朝珠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "朝廷大臣", "other": "处理国事", "from_chapter": [0], "action": "", "expression": ""}, {"name": "老臣（淳于越）", "gender": "男", "age": "老者", "clothes": "高领圆领汉服褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带圆形衣领，圆形衣领", "hairstyle": "白色束发", "figure": "清瘦", "identity": "朝廷大臣", "other": "迂腐守旧", "from_chapter": [0], "action": "", "expression": ""}, {"name": "邓山", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配红色，有虎纹图案，头戴黑色毡帽，腰系黑色皮腰带，佩刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "侍卫", "other": "听从公子华指令", "from_chapter": [0], "action": "", "expression": ""}, {"name": "侍卫们", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配银色，有简单的几何花纹，头戴黑色头盔，腰系黑色腰带，佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "宫廷侍卫", "other": "执行命令", "from_chapter": [0], "action": "", "expression": ""}, {"name": "秋香和秋碟", "gender": "女", "age": "少女", "clothes": "高领圆领汉服浅绿色丫鬟服饰，主色调为浅绿色，搭配白色，有蝴蝶纹样，头戴绿色发带，腰系绿色丝带圆形衣领，圆形衣领", "hairstyle": "黑色双马尾", "figure": "娇小", "identity": "公子华的随身侍女", "other": "机灵乖巧", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，皇宫大殿内，一位端庄温柔且带着着急神情的中年女子帮青年男子整理衣领、检查打扮，之后男子挺拔地走到勤政殿门口靠前位置平静等待早朝开始，男子脸上带着懵懂的神情；中年女子穿着高领圆领汉服深粉色宫装，主色调为深粉色，搭配淡黄色，有花卉纹样，头戴金簪发饰，颈戴珍珠项链，梳着黑色发髻，有着圆形衣领；青年男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，梳着黑色束发，有着圆形衣领, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753194746_20250722_223227.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_26eb7ed7-636e-4c24-b384-171b32d1e310_1.25x_20250722_193550.wav", "audio_duration": 14.749333, "video_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/videos/video_1753195639_20250722_224720.mp4", "story_board_id": 2, "subtitle": [{"subtitle_id": 1, "text": "他一边帮他整理衣领，", "timestamp": "00:00:00,200 --> 00:00:01,600", "duration": 1.4, "char_count": 10, "start_time_s": 0.2, "end_time_s": 1.6, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 360, "start_time": 280, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 360, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 600, "text": "帮"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 760, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 920, "text": "整"}, {"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 1040, "text": "理"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1160, "text": "衣"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1320, "text": "领"}], "keyword": "整理衣领"}, {"subtitle_id": 2, "text": "一边检查他的打扮，", "timestamp": "00:00:01,600 --> 00:00:03,000", "duration": 1.4, "char_count": 9, "start_time_s": 1.6, "end_time_s": 3.0, "words": [{"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1600, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1800, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1960, "text": "检"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2120, "text": "查"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2280, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2400, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2480, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2640, "text": "扮"}], "keyword": "检查打扮"}, {"subtitle_id": 3, "text": "确认没问题后，", "timestamp": "00:00:03,000 --> 00:00:03,960", "duration": 0.96, "char_count": 7, "start_time_s": 3.0, "end_time_s": 3.96, "words": [{"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3000, "text": "确"}, {"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3200, "text": "认"}, {"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3280, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3440, "text": "问"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3560, "text": "题"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3720, "text": "后"}], "keyword": "确认问题"}, {"subtitle_id": 4, "text": "才让他出门。", "timestamp": "00:00:03,960 --> 00:00:05,020", "duration": 1.06, "char_count": 6, "start_time_s": 3.96, "end_time_s": 5.02, "words": [{"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 3960, "text": "才"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4160, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 4440, "start_time": 4320, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4440, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 5020, "start_time": 4600, "text": "门"}], "keyword": "让他出门"}, {"subtitle_id": 5, "text": "公子华冈走到勤政殿门口，", "timestamp": "00:00:05,160 --> 00:00:07,280", "duration": 2.12, "char_count": 12, "start_time_s": 5.16, "end_time_s": 7.28, "words": [{"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5160, "text": "公"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5360, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5480, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5640, "text": "冈"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5960, "text": "走"}, {"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 6120, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6280, "text": "勤"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6480, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6600, "text": "殿"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6760, "text": "门"}, {"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 6920, "text": "口"}], "keyword": "公子华冈"}, {"subtitle_id": 6, "text": "就看到一群大臣陆续进来，", "timestamp": "00:00:07,280 --> 00:00:09,080", "duration": 1.8, "char_count": 12, "start_time_s": 7.28, "end_time_s": 9.08, "words": [{"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7280, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7480, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7600, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 7840, "start_time": 7720, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7840, "text": "群"}, {"attribute": {"event": "speech"}, "end_time": 8160, "start_time": 7960, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8200, "text": "臣"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8360, "text": "陆"}, {"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8560, "text": "续"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8680, "text": "进"}, {"attribute": {"event": "speech"}, "end_time": 9080, "start_time": 8800, "text": "来"}], "keyword": "勤政殿门"}, {"subtitle_id": 7, "text": "彼此打着招呼，", "timestamp": "00:00:09,080 --> 00:00:10,200", "duration": 1.12, "char_count": 7, "start_time_s": 9.08, "end_time_s": 10.2, "words": [{"attribute": {"event": "speech"}, "end_time": 9280, "start_time": 9080, "text": "彼"}, {"attribute": {"event": "speech"}, "end_time": 9400, "start_time": 9280, "text": "此"}, {"attribute": {"event": "speech"}, "end_time": 9600, "start_time": 9400, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 9720, "start_time": 9600, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 9880, "start_time": 9720, "text": "招"}, {"attribute": {"event": "speech"}, "end_time": 10200, "start_time": 9880, "text": "呼"}], "keyword": "大臣打招"}, {"subtitle_id": 8, "text": "气氛热闹得很。", "timestamp": "00:00:10,200 --> 00:00:11,420", "duration": 1.22, "char_count": 7, "start_time_s": 10.2, "end_time_s": 11.42, "words": [{"attribute": {"event": "speech"}, "end_time": 10400, "start_time": 10200, "text": "气"}, {"attribute": {"event": "speech"}, "end_time": 10480, "start_time": 10400, "text": "氛"}, {"attribute": {"event": "speech"}, "end_time": 10720, "start_time": 10520, "text": "热"}, {"attribute": {"event": "speech"}, "end_time": 10880, "start_time": 10760, "text": "闹"}, {"attribute": {"event": "speech"}, "end_time": 11000, "start_time": 10880, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 11420, "start_time": 11000, "text": "很"}], "keyword": "气氛热闹"}, {"subtitle_id": 9, "text": "他站到靠前的位置，", "timestamp": "00:00:11,560 --> 00:00:13,040", "duration": 1.48, "char_count": 9, "start_time_s": 11.56, "end_time_s": 13.04, "words": [{"attribute": {"event": "speech"}, "end_time": 11720, "start_time": 11560, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 11880, "start_time": 11720, "text": "站"}, {"attribute": {"event": "speech"}, "end_time": 12040, "start_time": 11880, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 12240, "start_time": 12040, "text": "靠"}, {"attribute": {"event": "speech"}, "end_time": 12440, "start_time": 12280, "text": "前"}, {"attribute": {"event": "speech"}, "end_time": 12520, "start_time": 12440, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 12720, "start_time": 12520, "text": "位"}, {"attribute": {"event": "speech"}, "end_time": 13040, "start_time": 12720, "text": "置"}], "keyword": "靠前位置"}, {"subtitle_id": 10, "text": "等着早朝开始。", "timestamp": "00:00:13,040 --> 00:00:14,420", "duration": 1.38, "char_count": 7, "start_time_s": 13.04, "end_time_s": 14.42, "words": [{"attribute": {"event": "speech"}, "end_time": 13240, "start_time": 13040, "text": "等"}, {"attribute": {"event": "speech"}, "end_time": 13360, "start_time": 13240, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 13560, "start_time": 13360, "text": "早"}, {"attribute": {"event": "speech"}, "end_time": 13760, "start_time": 13600, "text": "朝"}, {"attribute": {"event": "speech"}, "end_time": 13920, "start_time": 13760, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 14420, "start_time": 13920, "text": "始"}], "keyword": "早朝开始"}], "keywords": ["整理衣领", "检查打扮", "确认问题", "让他出门", "公子华冈", "勤政殿门", "大臣打招", "气氛热闹", "靠前位置", "早朝开始"]}, {"chapter": 4, "story_board": "突然，一个太监高声喊道：“大王驾到！”所有人立刻跪下，齐声喊：“大王万岁，大秦万年！”赢政缓缓走进殿中，语气平静地说：“众爱卿平身。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": [0], "action": "和众人一起跪下，齐声呼喊", "expression": "恭敬"}, {"name": "阿房女（夏阿房）", "gender": "女", "age": "中年", "clothes": "高领圆领汉服深粉色宫装，主色调为深粉色，搭配淡黄色，有花卉纹样，头戴金簪发饰，颈戴珍珠项链圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "端庄", "identity": "公子华的母亲", "other": "温柔关怀", "from_chapter": [0], "action": "帮公子华整理衣领，检查他的打扮，然后让他出门", "expression": "着急"}, {"name": "赢政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服黑色龙袍，主色调为黑色，搭配金黄色，绣有五爪金龙纹，头戴冕旒，腰系黄色龙纹腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发头戴冕旒", "figure": "威严", "identity": "大秦大王", "other": "沉稳睿智", "from_chapter": [0], "action": "缓缓走进殿中，抬手示意", "expression": "平静"}, {"name": "太监", "gender": "男", "age": "中年", "clothes": "高领圆领汉服灰色太监服饰，主色调为灰色，搭配白色，有简单的线条花纹，头戴黑色小帽，腰系灰色布带圆形衣领，圆形衣领", "hairstyle": "光头", "figure": "瘦小", "identity": "侍奉大王的太监", "other": "声音尖细", "from_chapter": [0], "action": "站在大殿一侧，高声呼喊", "expression": "严肃"}, {"name": "左右丞相和上卿", "gender": "男", "age": "中年", "clothes": "高领圆领汉服深蓝色官服，主色调为深蓝色，搭配浅蓝色，绣有山水纹样，头戴乌纱帽，腰系蓝色腰带，佩戴朝珠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "朝廷大臣", "other": "处理国事", "from_chapter": [0], "action": "", "expression": ""}, {"name": "老臣（淳于越）", "gender": "男", "age": "老者", "clothes": "高领圆领汉服褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带圆形衣领，圆形衣领", "hairstyle": "白色束发", "figure": "清瘦", "identity": "朝廷大臣", "other": "迂腐守旧", "from_chapter": [0], "action": "", "expression": ""}, {"name": "邓山", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配红色，有虎纹图案，头戴黑色毡帽，腰系黑色皮腰带，佩刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "侍卫", "other": "听从公子华指令", "from_chapter": [0], "action": "", "expression": ""}, {"name": "侍卫们", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配银色，有简单的几何花纹，头戴黑色头盔，腰系黑色腰带，佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "宫廷侍卫", "other": "执行命令", "from_chapter": [0], "action": "", "expression": ""}, {"name": "秋香和秋碟", "gender": "女", "age": "少女", "clothes": "高领圆领汉服浅绿色丫鬟服饰，主色调为浅绿色，搭配白色，有蝴蝶纹样，头戴绿色发带，腰系绿色丝带圆形衣领，圆形衣领", "hairstyle": "黑色双马尾", "figure": "娇小", "identity": "公子华的随身侍女", "other": "机灵乖巧", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿中，严肃地高声呼喊着、站在大殿一侧的中年太监，穿着高领圆领汉服灰色太监服饰，主色调为灰色，搭配白色，有简单的线条花纹，头戴黑色小帽，腰系灰色布带，光头，身形瘦小；平静地缓缓走进殿中、抬手示意的中年男子，穿着高领圆领汉服黑色龙袍，主色调为黑色，搭配金黄色，绣有五爪金龙纹，头戴冕旒，腰系黄色龙纹腰带，黑色束发，威严且沉稳睿智；恭敬地和众人一起跪下、齐声呼喊的青年男子，穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，黑色束发，身姿挺拔, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753195683_20250722_224804.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_c53127a3-c824-4e33-af5f-053ed51d2606_1.25x_20250722_193537.wav", "audio_duration": 11.794667, "video_url": "", "story_board_id": 3, "subtitle": [{"subtitle_id": 1, "text": "突然，一个太监高声喊道大王驾到！", "timestamp": "00:00:00,200 --> 00:00:03,460", "duration": 3.26, "char_count": 16, "start_time_s": 0.2, "end_time_s": 3.46, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "突"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 640, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 840, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 960, "text": "太"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1160, "text": "监"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1320, "text": "高"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1520, "text": "声"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1680, "text": "喊"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1840, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2280, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2560, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2760, "text": "驾"}, {"attribute": {"event": "speech"}, "end_time": 3460, "start_time": 3000, "text": "到"}], "keyword": "大王"}, {"subtitle_id": 2, "text": "所有人立刻跪下，", "timestamp": "00:00:03,640 --> 00:00:04,960", "duration": 1.32, "char_count": 8, "start_time_s": 3.64, "end_time_s": 4.96, "words": [{"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3640, "text": "所"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3880, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4040, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4160, "text": "立"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4320, "text": "刻"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4480, "text": "跪"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4640, "text": "下"}], "keyword": "众人"}, {"subtitle_id": 3, "text": "齐声喊大王万岁，", "timestamp": "00:00:04,960 --> 00:00:06,640", "duration": 1.68, "char_count": 8, "start_time_s": 4.96, "end_time_s": 6.64, "words": [{"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 4960, "text": "齐"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5120, "text": "声"}, {"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5320, "text": "喊"}, {"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5720, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5960, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6120, "text": "万"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6280, "text": "岁"}], "keyword": "万岁"}, {"subtitle_id": 4, "text": "大秦万年！", "timestamp": "00:00:06,640 --> 00:00:07,780", "duration": 1.14, "char_count": 5, "start_time_s": 6.64, "end_time_s": 7.78, "words": [{"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6640, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6920, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 7120, "text": "万"}, {"attribute": {"event": "speech"}, "end_time": 7780, "start_time": 7280, "text": "年"}], "keyword": "大秦"}, {"subtitle_id": 5, "text": "嬴政缓缓走进殿中，", "timestamp": "00:00:07,920 --> 00:00:09,320", "duration": 1.4, "char_count": 9, "start_time_s": 7.92, "end_time_s": 9.32, "words": [{"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 7920, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 8240, "start_time": 8120, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8280, "text": "缓"}, {"attribute": {"event": "speech"}, "end_time": 8600, "start_time": 8320, "text": "缓"}, {"attribute": {"event": "speech"}, "end_time": 8720, "start_time": 8600, "text": "走"}, {"attribute": {"event": "speech"}, "end_time": 8840, "start_time": 8760, "text": "进"}, {"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8840, "text": "殿"}, {"attribute": {"event": "speech"}, "end_time": 9320, "start_time": 9000, "text": "中"}], "keyword": "嬴政"}, {"subtitle_id": 6, "text": "语气平静地说众爱卿平身！", "timestamp": "00:00:09,320 --> 00:00:11,700", "duration": 2.38, "char_count": 12, "start_time_s": 9.32, "end_time_s": 11.7, "words": [{"attribute": {"event": "speech"}, "end_time": 9520, "start_time": 9320, "text": "语"}, {"attribute": {"event": "speech"}, "end_time": 9640, "start_time": 9520, "text": "气"}, {"attribute": {"event": "speech"}, "end_time": 9840, "start_time": 9640, "text": "平"}, {"attribute": {"event": "speech"}, "end_time": 10000, "start_time": 9840, "text": "静"}, {"attribute": {"event": "speech"}, "end_time": 10080, "start_time": 10000, "text": "地"}, {"attribute": {"event": "speech"}, "end_time": 10240, "start_time": 10080, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 10640, "start_time": 10480, "text": "众"}, {"attribute": {"event": "speech"}, "end_time": 10840, "start_time": 10640, "text": "爱"}, {"attribute": {"event": "speech"}, "end_time": 11040, "start_time": 10880, "text": "卿"}, {"attribute": {"event": "speech"}, "end_time": 11240, "start_time": 11040, "text": "平"}, {"attribute": {"event": "speech"}, "end_time": 11700, "start_time": 11240, "text": "身"}], "keyword": "平身"}], "keywords": ["大王", "众人", "万岁", "大秦", "嬴政", "平身"]}, {"chapter": 4, "story_board": "”\n\n太监接着说：“有事请奏，无事退朝。”左右丞相和上卿纷纷上前汇报国事，事情一件件处理完毕。赢政环视四周，问：“还有没有事要奏？", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": [0], "action": "和众人一起跪下，齐声呼喊", "expression": "恭敬"}, {"name": "阿房女（夏阿房）", "gender": "女", "age": "中年", "clothes": "高领圆领汉服深粉色宫装，主色调为深粉色，搭配淡黄色，有花卉纹样，头戴金簪发饰，颈戴珍珠项链圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "端庄", "identity": "公子华的母亲", "other": "温柔关怀", "from_chapter": [0], "action": "帮公子华整理衣领，检查他的打扮，然后让他出门", "expression": "着急"}, {"name": "赢政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服黑色龙袍，主色调为黑色，搭配金黄色，绣有五爪金龙纹，头戴冕旒，腰系黄色龙纹腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发头戴冕旒", "figure": "威严", "identity": "大秦大王", "other": "沉稳睿智", "from_chapter": [0], "action": "环视四周并询问", "expression": "威严"}, {"name": "太监", "gender": "男", "age": "中年", "clothes": "高领圆领汉服灰色太监服饰，主色调为灰色，搭配白色，有简单的线条花纹，头戴黑色小帽，腰系灰色布带圆形衣领，圆形衣领", "hairstyle": "光头", "figure": "瘦小", "identity": "侍奉大王的太监", "other": "声音尖细", "from_chapter": [0], "action": "站着说话", "expression": "严肃"}, {"name": "左右丞相和上卿", "gender": "男", "age": "中年", "clothes": "高领圆领汉服深蓝色官服，主色调为深蓝色，搭配浅蓝色，绣有山水纹样，头戴乌纱帽，腰系蓝色腰带，佩戴朝珠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "朝廷大臣", "other": "处理国事", "from_chapter": [0], "action": "纷纷上前汇报", "expression": "认真"}, {"name": "老臣（淳于越）", "gender": "男", "age": "老者", "clothes": "高领圆领汉服褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带圆形衣领，圆形衣领", "hairstyle": "白色束发", "figure": "清瘦", "identity": "朝廷大臣", "other": "迂腐守旧", "from_chapter": [0], "action": "", "expression": ""}, {"name": "邓山", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配红色，有虎纹图案，头戴黑色毡帽，腰系黑色皮腰带，佩刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "侍卫", "other": "听从公子华指令", "from_chapter": [0], "action": "", "expression": ""}, {"name": "侍卫们", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配银色，有简单的几何花纹，头戴黑色头盔，腰系黑色腰带，佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "宫廷侍卫", "other": "执行命令", "from_chapter": [0], "action": "", "expression": ""}, {"name": "秋香和秋碟", "gender": "女", "age": "少女", "clothes": "高领圆领汉服浅绿色丫鬟服饰，主色调为浅绿色，搭配白色，有蝴蝶纹样，头戴绿色发带，腰系绿色丝带圆形衣领，圆形衣领", "hairstyle": "黑色双马尾", "figure": "娇小", "identity": "公子华的随身侍女", "other": "机灵乖巧", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，皇宫大殿中，威严沉稳睿智的中年男子头戴冕旒、黑色束发，环视四周，他身着高领圆领的黑色龙袍，主色调为黑色，搭配金黄色，绣有五爪金龙纹，腰系黄色龙纹腰带；旁边一个严肃的中年太监站着，他光头，身着高领圆领的灰色太监服饰，主色调为灰色，搭配白色，有简单的线条花纹，头戴黑色小帽，腰系灰色布带；几个认真的中年男子纷纷上前，他们身着高领圆领的深蓝色官服，主色调为深蓝色，搭配浅蓝色，绣有山水纹样，头戴乌纱帽，腰系蓝色腰带，佩戴朝珠，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://p9-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/202507222248389C0D0B9BE355B5776FA1-7677-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1753282126&x-signature=l7ip%2FF6t9CGlqJkRhLamZ16D7VQ%3D", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_b9ad078f-15c7-4d6e-92a0-8a0ece020f69_1.25x_20250722_193555.wav", "audio_duration": 11.12, "video_url": "", "story_board_id": 4, "subtitle": [{"subtitle_id": 1, "text": "太监接着说有事请奏，", "timestamp": "00:00:00,200 --> 00:00:01,920", "duration": 1.72, "char_count": 10, "start_time_s": 0.2, "end_time_s": 1.92, "words": [{"attribute": {"event": "speech"}, "end_time": 360, "start_time": 200, "text": "太"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 360, "text": "监"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 520, "text": "接"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 640, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 720, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 1080, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1280, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1440, "text": "请"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1640, "text": "奏"}], "keyword": "太监"}, {"subtitle_id": 2, "text": "无事退朝。", "timestamp": "00:00:01,920 --> 00:00:03,020", "duration": 1.1, "char_count": 5, "start_time_s": 1.92, "end_time_s": 3.02, "words": [{"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 1920, "text": "无"}, {"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2200, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 2520, "start_time": 2360, "text": "退"}, {"attribute": {"event": "speech"}, "end_time": 3020, "start_time": 2560, "text": "朝"}], "keyword": "退朝"}, {"subtitle_id": 3, "text": "左右丞相贺尚卿纷纷上前汇报国事。", "timestamp": "00:00:03,200 --> 00:00:05,880", "duration": 2.68, "char_count": 16, "start_time_s": 3.2, "end_time_s": 5.88, "words": [{"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3200, "text": "左"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3360, "text": "右"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3480, "text": "丞"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3640, "text": "相"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3840, "text": "贺"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 3960, "text": "尚"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4160, "text": "卿"}, {"attribute": {"event": "speech"}, "end_time": 4440, "start_time": 4400, "text": "纷"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4440, "text": "纷"}, {"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4720, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4880, "text": "前"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5080, "text": "汇"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5240, "text": "报"}, {"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5360, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5560, "text": "事"}], "keyword": "丞相"}, {"subtitle_id": 4, "text": "事情一件件处理完毕，", "timestamp": "00:00:05,880 --> 00:00:07,740", "duration": 1.86, "char_count": 10, "start_time_s": 5.88, "end_time_s": 7.74, "words": [{"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5880, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 6040, "text": "情"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6120, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6360, "text": "件"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6560, "text": "件"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6800, "text": "处"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6960, "text": "理"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7080, "text": "完"}, {"attribute": {"event": "speech"}, "end_time": 7740, "start_time": 7240, "text": "毕"}], "keyword": "处理"}, {"subtitle_id": 5, "text": "嬴政桓视四周，", "timestamp": "00:00:07,840 --> 00:00:09,000", "duration": 1.16, "char_count": 7, "start_time_s": 7.84, "end_time_s": 9.0, "words": [{"attribute": {"event": "speech"}, "end_time": 8000, "start_time": 7840, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 8160, "start_time": 8040, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8200, "text": "桓"}, {"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8360, "text": "视"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8480, "text": "四"}, {"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8640, "text": "周"}], "keyword": "嬴政"}, {"subtitle_id": 6, "text": "问还有没有事要奏？", "timestamp": "00:00:09,000 --> 00:00:10,740", "duration": 1.74, "char_count": 9, "start_time_s": 9.0, "end_time_s": 10.74, "words": [{"attribute": {"event": "speech"}, "end_time": 9200, "start_time": 9000, "text": "问"}, {"attribute": {"event": "speech"}, "end_time": 9640, "start_time": 9400, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 9760, "start_time": 9640, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 9880, "start_time": 9760, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 10040, "start_time": 9880, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 10120, "start_time": 10040, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 10280, "start_time": 10120, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 10740, "start_time": 10280, "text": "奏"}], "keyword": "奏事"}], "keywords": ["太监", "退朝", "丞相", "处理", "嬴政", "奏事"]}, {"chapter": 4, "story_board": "”这时，一位老臣站了出来，声音洪亮：“启禀大王，听说您让大公子入住东宫，这是真的吗？”\n\n赢政点点头：“没错，是我允许的。”话音刚落，老臣立刻反驳：“这不符合礼制！", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": [0], "action": "和众人一起跪下，齐声呼喊", "expression": "恭敬"}, {"name": "阿房女（夏阿房）", "gender": "女", "age": "中年", "clothes": "高领圆领汉服深粉色宫装，主色调为深粉色，搭配淡黄色，有花卉纹样，头戴金簪发饰，颈戴珍珠项链圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "端庄", "identity": "公子华的母亲", "other": "温柔关怀", "from_chapter": [0], "action": "帮公子华整理衣领，检查他的打扮，然后让他出门", "expression": "着急"}, {"name": "赢政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服黑色龙袍，主色调为黑色，搭配金黄色，绣有五爪金龙纹，头戴冕旒，腰系黄色龙纹腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发头戴冕旒", "figure": "威严", "identity": "大秦大王", "other": "沉稳睿智", "from_chapter": [0], "action": "站在殿中，点头回应老臣", "expression": "平静"}, {"name": "太监", "gender": "男", "age": "中年", "clothes": "高领圆领汉服灰色太监服饰，主色调为灰色，搭配白色，有简单的线条花纹，头戴黑色小帽，腰系灰色布带圆形衣领，圆形衣领", "hairstyle": "光头", "figure": "瘦小", "identity": "侍奉大王的太监", "other": "声音尖细", "from_chapter": [0], "action": "站着说话", "expression": "严肃"}, {"name": "左右丞相和上卿", "gender": "男", "age": "中年", "clothes": "高领圆领汉服深蓝色官服，主色调为深蓝色，搭配浅蓝色，绣有山水纹样，头戴乌纱帽，腰系蓝色腰带，佩戴朝珠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "朝廷大臣", "other": "处理国事", "from_chapter": [0], "action": "纷纷上前汇报", "expression": "认真"}, {"name": "老臣（淳于越）", "gender": "男", "age": "老者", "clothes": "高领圆领汉服褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带圆形衣领，圆形衣领", "hairstyle": "白色束发", "figure": "清瘦", "identity": "朝廷大臣", "other": "迂腐守旧", "from_chapter": [0], "action": "从人群中站出来，面向赢政大声说话，在赢政回应后立刻反驳", "expression": "严肃、强硬"}, {"name": "邓山", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配红色，有虎纹图案，头戴黑色毡帽，腰系黑色皮腰带，佩刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "侍卫", "other": "听从公子华指令", "from_chapter": [0], "action": "", "expression": ""}, {"name": "侍卫们", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配银色，有简单的几何花纹，头戴黑色头盔，腰系黑色腰带，佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "宫廷侍卫", "other": "执行命令", "from_chapter": [0], "action": "", "expression": ""}, {"name": "秋香和秋碟", "gender": "女", "age": "少女", "clothes": "高领圆领汉服浅绿色丫鬟服饰，主色调为浅绿色，搭配白色，有蝴蝶纹样，头戴绿色发带，腰系绿色丝带圆形衣领，圆形衣领", "hairstyle": "黑色双马尾", "figure": "娇小", "identity": "公子华的随身侍女", "other": "机灵乖巧", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，皇宫大殿中，一位严肃强硬、清瘦迂腐的老者从人群中站出，站立着面向对面一位平静沉稳、威严睿智的中年男子大声表达，中年男子站着点头回应后，老者立刻反驳。老者身着主色调为褐色、搭配浅褐色、有古朴回纹的高领圆领汉服褐色官服，头戴黑色方巾，腰系褐色腰带，白色束发；中年男子身着主色调为黑色、搭配金黄色、绣有五爪金龙纹的高领圆领汉服黑色龙袍，头戴冕旒，腰系黄色龙纹腰带，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753195813_20250722_225014.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_777ff33a-9f24-4432-8bd5-12f1bef2ff34_1.25x_20250722_193604.wav", "audio_duration": 13.616, "video_url": "", "story_board_id": 5, "subtitle": [{"subtitle_id": 1, "text": "这时，一位老臣站了出来，", "timestamp": "00:00:00,200 --> 00:00:02,040", "duration": 1.84, "char_count": 12, "start_time_s": 0.2, "end_time_s": 2.04, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 280, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 640, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 800, "text": "位"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 960, "text": "老"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1160, "text": "臣"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1360, "text": "站"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1520, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1600, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1760, "text": "来"}], "keyword": "老臣"}, {"subtitle_id": 2, "text": "声音洪亮启禀大王，", "timestamp": "00:00:02,040 --> 00:00:03,760", "duration": 1.72, "char_count": 9, "start_time_s": 2.04, "end_time_s": 3.76, "words": [{"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2040, "text": "声"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2160, "text": "音"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2320, "text": "洪"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2520, "text": "亮"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2840, "text": "启"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 3000, "text": "禀"}, {"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3120, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3320, "text": "王"}], "keyword": "启禀"}, {"subtitle_id": 3, "text": "听说您让大公子入住东宫，", "timestamp": "00:00:03,760 --> 00:00:05,760", "duration": 2.0, "char_count": 12, "start_time_s": 3.76, "end_time_s": 5.76, "words": [{"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3760, "text": "听"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3960, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4080, "text": "您"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4200, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4320, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4560, "text": "公"}, {"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4720, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4880, "text": "入"}, {"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5040, "text": "住"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5200, "text": "东"}, {"attribute": {"event": "speech"}, "end_time": 5760, "start_time": 5360, "text": "宫"}], "keyword": "大公子"}, {"subtitle_id": 4, "text": "这是真的吗？", "timestamp": "00:00:05,760 --> 00:00:06,820", "duration": 1.06, "char_count": 6, "start_time_s": 5.76, "end_time_s": 6.82, "words": [{"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5760, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5880, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6000, "text": "真"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6200, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6820, "start_time": 6320, "text": "吗"}], "keyword": "真的"}, {"subtitle_id": 5, "text": "嬴政点点头没错，", "timestamp": "00:00:06,880 --> 00:00:08,560", "duration": 1.68, "char_count": 8, "start_time_s": 6.88, "end_time_s": 8.56, "words": [{"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6880, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7080, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 7240, "text": "点"}, {"attribute": {"event": "speech"}, "end_time": 7520, "start_time": 7280, "text": "点"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7520, "text": "头"}, {"attribute": {"event": "speech"}, "end_time": 8160, "start_time": 7920, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 8560, "start_time": 8160, "text": "错"}], "keyword": "嬴政"}, {"subtitle_id": 6, "text": "是我允许的。", "timestamp": "00:00:08,560 --> 00:00:09,740", "duration": 1.18, "char_count": 6, "start_time_s": 8.56, "end_time_s": 9.74, "words": [{"attribute": {"event": "speech"}, "end_time": 8720, "start_time": 8560, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 8840, "start_time": 8720, "text": "我"}, {"attribute": {"event": "speech"}, "end_time": 9040, "start_time": 8840, "text": "允"}, {"attribute": {"event": "speech"}, "end_time": 9240, "start_time": 9080, "text": "许"}, {"attribute": {"event": "speech"}, "end_time": 9740, "start_time": 9240, "text": "的"}], "keyword": "允许"}, {"subtitle_id": 7, "text": "话音刚落，", "timestamp": "00:00:09,880 --> 00:00:10,720", "duration": 0.84, "char_count": 5, "start_time_s": 9.88, "end_time_s": 10.72, "words": [{"attribute": {"event": "speech"}, "end_time": 10120, "start_time": 9880, "text": "话"}, {"attribute": {"event": "speech"}, "end_time": 10240, "start_time": 10120, "text": "音"}, {"attribute": {"event": "speech"}, "end_time": 10400, "start_time": 10240, "text": "刚"}, {"attribute": {"event": "speech"}, "end_time": 10720, "start_time": 10400, "text": "落"}], "keyword": "话音"}, {"subtitle_id": 8, "text": "老臣立刻反驳这不符合理智。", "timestamp": "00:00:10,720 --> 00:00:13,300", "duration": 2.58, "char_count": 13, "start_time_s": 10.72, "end_time_s": 13.3, "words": [{"attribute": {"event": "speech"}, "end_time": 10920, "start_time": 10720, "text": "老"}, {"attribute": {"event": "speech"}, "end_time": 11080, "start_time": 10920, "text": "臣"}, {"attribute": {"event": "speech"}, "end_time": 11240, "start_time": 11080, "text": "立"}, {"attribute": {"event": "speech"}, "end_time": 11400, "start_time": 11240, "text": "刻"}, {"attribute": {"event": "speech"}, "end_time": 11560, "start_time": 11400, "text": "反"}, {"attribute": {"event": "speech"}, "end_time": 11760, "start_time": 11600, "text": "驳"}, {"attribute": {"event": "speech"}, "end_time": 12160, "start_time": 11960, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 12280, "start_time": 12160, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 12480, "start_time": 12280, "text": "符"}, {"attribute": {"event": "speech"}, "end_time": 12640, "start_time": 12480, "text": "合"}, {"attribute": {"event": "speech"}, "end_time": 12800, "start_time": 12640, "text": "理"}, {"attribute": {"event": "speech"}, "end_time": 13300, "start_time": 12800, "text": "智"}], "keyword": "反驳"}], "keywords": ["老臣", "启禀", "大公子", "真的", "嬴政", "允许", "话音", "反驳"]}, {"chapter": 4, "story_board": "东宫是太子住的地方，大公子不是太子，这样会让人不服气。”他的话刚说完，几位大臣也跟着附和，场面顿时紧张起来。公子华却抢先一步开口：“放肆！", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": [0], "action": "抢先开口，手指对方", "expression": "愤怒"}, {"name": "阿房女（夏阿房）", "gender": "女", "age": "中年", "clothes": "高领圆领汉服深粉色宫装，主色调为深粉色，搭配淡黄色，有花卉纹样，头戴金簪发饰，颈戴珍珠项链圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "端庄", "identity": "公子华的母亲", "other": "温柔关怀", "from_chapter": [0], "action": "帮公子华整理衣领，检查他的打扮，然后让他出门", "expression": "着急"}, {"name": "赢政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服黑色龙袍，主色调为黑色，搭配金黄色，绣有五爪金龙纹，头戴冕旒，腰系黄色龙纹腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发头戴冕旒", "figure": "威严", "identity": "大秦大王", "other": "沉稳睿智", "from_chapter": [0], "action": "站在殿中，点头回应老臣", "expression": "平静"}, {"name": "太监", "gender": "男", "age": "中年", "clothes": "高领圆领汉服灰色太监服饰，主色调为灰色，搭配白色，有简单的线条花纹，头戴黑色小帽，腰系灰色布带圆形衣领，圆形衣领", "hairstyle": "光头", "figure": "瘦小", "identity": "侍奉大王的太监", "other": "声音尖细", "from_chapter": [0], "action": "站着说话", "expression": "严肃"}, {"name": "左右丞相和上卿", "gender": "男", "age": "中年", "clothes": "高领圆领汉服深蓝色官服，主色调为深蓝色，搭配浅蓝色，绣有山水纹样，头戴乌纱帽，腰系蓝色腰带，佩戴朝珠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "朝廷大臣", "other": "处理国事", "from_chapter": [0], "action": "纷纷上前汇报", "expression": "认真"}, {"name": "老臣（淳于越）", "gender": "男", "age": "老者", "clothes": "高领圆领汉服褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带圆形衣领，圆形衣领", "hairstyle": "白色束发", "figure": "清瘦", "identity": "朝廷大臣", "other": "迂腐守旧", "from_chapter": [0], "action": "站出来大声说话", "expression": "严肃"}, {"name": "邓山", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配红色，有虎纹图案，头戴黑色毡帽，腰系黑色皮腰带，佩刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "侍卫", "other": "听从公子华指令", "from_chapter": [0], "action": "", "expression": ""}, {"name": "侍卫们", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配银色，有简单的几何花纹，头戴黑色头盔，腰系黑色腰带，佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "宫廷侍卫", "other": "执行命令", "from_chapter": [0], "action": "", "expression": ""}, {"name": "秋香和秋碟", "gender": "女", "age": "少女", "clothes": "高领圆领汉服浅绿色丫鬟服饰，主色调为浅绿色，搭配白色，有蝴蝶纹样，头戴绿色发带，腰系绿色丝带圆形衣领，圆形衣领", "hairstyle": "黑色双马尾", "figure": "娇小", "identity": "公子华的随身侍女", "other": "机灵乖巧", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿中，愤怒的青年抢先开口，手指着对面，身姿挺拔；一旁严肃的老者站出来，神情庄重。抢先开口并手指对方的青年穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发；站出来神情严肃的老者穿着高领圆领汉服褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带，圆形衣领，白色束发，清瘦, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753195915_20250722_225156.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_d398cfc9-2e50-4a19-ab99-a11e41972e93_1.25x_20250722_193614.wav", "audio_duration": 12.024, "video_url": "", "story_board_id": 6, "subtitle": [{"subtitle_id": 1, "text": "东宫是太子住的地方，", "timestamp": "00:00:00,160 --> 00:00:01,680", "duration": 1.52, "char_count": 10, "start_time_s": 0.16, "end_time_s": 1.68, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 160, "text": "东"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 480, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 600, "text": "太"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 840, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 1000, "text": "住"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1120, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1240, "text": "地"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1360, "text": "方"}], "keyword": "东宫"}, {"subtitle_id": 2, "text": "大宫子不是太子，", "timestamp": "00:00:01,680 --> 00:00:02,920", "duration": 1.24, "char_count": 8, "start_time_s": 1.68, "end_time_s": 2.92, "words": [{"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1680, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1920, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2040, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2160, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2320, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2440, "text": "太"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2600, "text": "子"}], "keyword": "大宫子"}, {"subtitle_id": 3, "text": "这样会让人不服气。", "timestamp": "00:00:02,920 --> 00:00:04,420", "duration": 1.5, "char_count": 9, "start_time_s": 2.92, "end_time_s": 4.42, "words": [{"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2920, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3080, "text": "样"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3240, "text": "会"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3360, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3480, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3640, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3760, "text": "服"}, {"attribute": {"event": "speech"}, "end_time": 4420, "start_time": 3920, "text": "气"}], "keyword": "不服气"}, {"subtitle_id": 4, "text": "他的话刚说完，", "timestamp": "00:00:04,520 --> 00:00:05,680", "duration": 1.16, "char_count": 7, "start_time_s": 4.52, "end_time_s": 5.68, "words": [{"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4520, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4720, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4800, "text": "话"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 4960, "text": "刚"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5160, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5280, "text": "完"}], "keyword": "话"}, {"subtitle_id": 5, "text": "几位大臣也跟着附和，", "timestamp": "00:00:05,680 --> 00:00:07,200", "duration": 1.52, "char_count": 10, "start_time_s": 5.68, "end_time_s": 7.2, "words": [{"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5680, "text": "几"}, {"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5800, "text": "位"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5920, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6120, "text": "臣"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6280, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6440, "text": "跟"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6600, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6720, "text": "附"}, {"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 6880, "text": "和"}], "keyword": "大臣"}, {"subtitle_id": 6, "text": "场面顿时紧张起来。", "timestamp": "00:00:07,200 --> 00:00:08,860", "duration": 1.66, "char_count": 9, "start_time_s": 7.2, "end_time_s": 8.86, "words": [{"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7200, "text": "场"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7400, "text": "面"}, {"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7560, "text": "顿"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7760, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7920, "text": "紧"}, {"attribute": {"event": "speech"}, "end_time": 8240, "start_time": 8120, "text": "张"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8280, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 8860, "start_time": 8400, "text": "来"}], "keyword": "场面"}, {"subtitle_id": 7, "text": "宫子华却抢先一步开口放肆！", "timestamp": "00:00:08,920 --> 00:00:11,740", "duration": 2.82, "char_count": 13, "start_time_s": 8.92, "end_time_s": 11.74, "words": [{"attribute": {"event": "speech"}, "end_time": 9120, "start_time": 8920, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 9240, "start_time": 9120, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 9440, "start_time": 9240, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 9600, "start_time": 9480, "text": "却"}, {"attribute": {"event": "speech"}, "end_time": 9800, "start_time": 9600, "text": "抢"}, {"attribute": {"event": "speech"}, "end_time": 9960, "start_time": 9840, "text": "先"}, {"attribute": {"event": "speech"}, "end_time": 10120, "start_time": 9960, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 10200, "start_time": 10120, "text": "步"}, {"attribute": {"event": "speech"}, "end_time": 10440, "start_time": 10200, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 10600, "start_time": 10440, "text": "口"}, {"attribute": {"event": "speech"}, "end_time": 11160, "start_time": 10960, "text": "放"}, {"attribute": {"event": "speech"}, "end_time": 11740, "start_time": 11200, "text": "肆"}], "keyword": "宫子华"}], "keywords": ["东宫", "大宫子", "不服气", "话", "大臣", "场面", "宫子华"]}, {"chapter": 4, "story_board": "父王让我住东宫，难道就不合礼了吗？东宫可以叫东宫，也可以叫西宫、南宫，你们怎么就这么较真？”他语气强硬，直接戳中对方的软肋。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": [0], "action": "站在大殿中，双手叉腰，语气强硬地说话", "expression": "愤怒"}, {"name": "阿房女（夏阿房）", "gender": "女", "age": "中年", "clothes": "高领圆领汉服深粉色宫装，主色调为深粉色，搭配淡黄色，有花卉纹样，头戴金簪发饰，颈戴珍珠项链圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "端庄", "identity": "公子华的母亲", "other": "温柔关怀", "from_chapter": [0], "action": "帮公子华整理衣领，检查他的打扮，然后让他出门", "expression": "着急"}, {"name": "赢政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服黑色龙袍，主色调为黑色，搭配金黄色，绣有五爪金龙纹，头戴冕旒，腰系黄色龙纹腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发头戴冕旒", "figure": "威严", "identity": "大秦大王", "other": "沉稳睿智", "from_chapter": [0], "action": "站在殿中，点头回应老臣", "expression": "平静"}, {"name": "太监", "gender": "男", "age": "中年", "clothes": "高领圆领汉服灰色太监服饰，主色调为灰色，搭配白色，有简单的线条花纹，头戴黑色小帽，腰系灰色布带圆形衣领，圆形衣领", "hairstyle": "光头", "figure": "瘦小", "identity": "侍奉大王的太监", "other": "声音尖细", "from_chapter": [0], "action": "站着说话", "expression": "严肃"}, {"name": "左右丞相和上卿", "gender": "男", "age": "中年", "clothes": "高领圆领汉服深蓝色官服，主色调为深蓝色，搭配浅蓝色，绣有山水纹样，头戴乌纱帽，腰系蓝色腰带，佩戴朝珠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "朝廷大臣", "other": "处理国事", "from_chapter": [0], "action": "纷纷上前汇报", "expression": "认真"}, {"name": "老臣（淳于越）", "gender": "男", "age": "老者", "clothes": "高领圆领汉服褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带圆形衣领，圆形衣领", "hairstyle": "白色束发", "figure": "清瘦", "identity": "朝廷大臣", "other": "迂腐守旧", "from_chapter": [0], "action": "站在一旁，被指责后身体微微颤抖", "expression": "震惊"}, {"name": "邓山", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配红色，有虎纹图案，头戴黑色毡帽，腰系黑色皮腰带，佩刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "侍卫", "other": "听从公子华指令", "from_chapter": [0], "action": "", "expression": ""}, {"name": "侍卫们", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配银色，有简单的几何花纹，头戴黑色头盔，腰系黑色腰带，佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "宫廷侍卫", "other": "执行命令", "from_chapter": [0], "action": "", "expression": ""}, {"name": "秋香和秋碟", "gender": "女", "age": "少女", "clothes": "高领圆领汉服浅绿色丫鬟服饰，主色调为浅绿色，搭配白色，有蝴蝶纹样，头戴绿色发带，腰系绿色丝带圆形衣领，圆形衣领", "hairstyle": "黑色双马尾", "figure": "娇小", "identity": "公子华的随身侍女", "other": "机灵乖巧", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，皇宫大殿中，双手叉腰、愤怒的青年男子，言辞犀利、敢言善辩、语气强硬；旁边被指责后身体微微颤抖、一脸震惊的老者；青年男子身穿高领圆领紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，黑色束发；老者穿着高领圆领褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带，白色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753196042_20250722_225403.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_2a73415f-dec6-4253-b416-de9eecc14fee_1.25x_20250722_193541.wav", "audio_duration": 9.912, "video_url": "", "story_board_id": 7, "subtitle": [{"subtitle_id": 1, "text": "父王让我住东宫难道就不合理了吗？", "timestamp": "00:00:00,200 --> 00:00:03,020", "duration": 2.82, "char_count": 16, "start_time_s": 0.2, "end_time_s": 3.02, "words": [{"attribute": {"event": "speech"}, "end_time": 360, "start_time": 200, "text": "父"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 360, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 480, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 640, "text": "我"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 760, "text": "住"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 880, "text": "东"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1040, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1440, "text": "难"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1640, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1800, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1920, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2080, "text": "合"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2240, "text": "理"}, {"attribute": {"event": "speech"}, "end_time": 2520, "start_time": 2400, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 3020, "start_time": 2520, "text": "吗"}], "keyword": "父王"}, {"subtitle_id": 2, "text": "东宫可以叫东宫，", "timestamp": "00:00:03,080 --> 00:00:04,280", "duration": 1.2, "char_count": 8, "start_time_s": 3.08, "end_time_s": 4.28, "words": [{"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3080, "text": "东"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3280, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3440, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3560, "text": "以"}, {"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3680, "text": "叫"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3800, "text": "东"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 3960, "text": "宫"}], "keyword": "东宫"}, {"subtitle_id": 3, "text": "也可以叫西宫、南宫，", "timestamp": "00:00:04,280 --> 00:00:05,640", "duration": 1.36, "char_count": 10, "start_time_s": 4.28, "end_time_s": 5.64, "words": [{"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4280, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4480, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4600, "text": "以"}, {"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4680, "text": "叫"}, {"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4880, "text": "西"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 5000, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5120, "text": "南"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5280, "text": "宫"}], "keyword": "西宫"}, {"subtitle_id": 4, "text": "你们怎么就这么较真？", "timestamp": "00:00:05,640 --> 00:00:07,100", "duration": 1.46, "char_count": 10, "start_time_s": 5.64, "end_time_s": 7.1, "words": [{"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5640, "text": "你"}, {"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5840, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5920, "text": "怎"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 6040, "text": "么"}, {"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 6120, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6240, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6360, "text": "么"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6440, "text": "较"}, {"attribute": {"event": "speech"}, "end_time": 7100, "start_time": 6640, "text": "真"}], "keyword": "南宫"}, {"subtitle_id": 5, "text": "他语气强硬，", "timestamp": "00:00:07,200 --> 00:00:08,160", "duration": 0.96, "char_count": 6, "start_time_s": 7.2, "end_time_s": 8.16, "words": [{"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 7520, "start_time": 7400, "text": "语"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7520, "text": "气"}, {"attribute": {"event": "speech"}, "end_time": 7840, "start_time": 7680, "text": "强"}, {"attribute": {"event": "speech"}, "end_time": 8160, "start_time": 7880, "text": "硬"}], "keyword": "语气"}, {"subtitle_id": 6, "text": "直接戳中对方的软肋。", "timestamp": "00:00:08,160 --> 00:00:09,820", "duration": 1.66, "char_count": 10, "start_time_s": 8.16, "end_time_s": 9.82, "words": [{"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8160, "text": "直"}, {"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8360, "text": "接"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8520, "text": "戳"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8640, "text": "中"}, {"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8800, "text": "对"}, {"attribute": {"event": "speech"}, "end_time": 9080, "start_time": 8960, "text": "方"}, {"attribute": {"event": "speech"}, "end_time": 9200, "start_time": 9080, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 9360, "start_time": 9200, "text": "软"}, {"attribute": {"event": "speech"}, "end_time": 9820, "start_time": 9360, "text": "肋"}], "keyword": "软肋"}], "keywords": ["父王", "东宫", "西宫", "南宫", "语气", "软肋"]}, {"chapter": 4, "story_board": "老臣脸色一变，继续辩解：“公子不是太子，住东宫不合礼制，应该遵循周礼。”公子华冷笑一声：“好啊，那我倒要问问，父王昨天才跟我们四个人说过这事，你怎么知道得这么快？是不是在宫里安插了细作？", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": [0], "action": "冷笑一声后质问对方", "expression": "轻蔑"}, {"name": "阿房女（夏阿房）", "gender": "女", "age": "中年", "clothes": "高领圆领汉服深粉色宫装，主色调为深粉色，搭配淡黄色，有花卉纹样，头戴金簪发饰，颈戴珍珠项链圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "端庄", "identity": "公子华的母亲", "other": "温柔关怀", "from_chapter": [0], "action": "帮公子华整理衣领，检查他的打扮，然后让他出门", "expression": "着急"}, {"name": "赢政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服黑色龙袍，主色调为黑色，搭配金黄色，绣有五爪金龙纹，头戴冕旒，腰系黄色龙纹腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发头戴冕旒", "figure": "威严", "identity": "大秦大王", "other": "沉稳睿智", "from_chapter": [0], "action": "站在殿中，点头回应老臣", "expression": "平静"}, {"name": "太监", "gender": "男", "age": "中年", "clothes": "高领圆领汉服灰色太监服饰，主色调为灰色，搭配白色，有简单的线条花纹，头戴黑色小帽，腰系灰色布带圆形衣领，圆形衣领", "hairstyle": "光头", "figure": "瘦小", "identity": "侍奉大王的太监", "other": "声音尖细", "from_chapter": [0], "action": "站着说话", "expression": "严肃"}, {"name": "左右丞相和上卿", "gender": "男", "age": "中年", "clothes": "高领圆领汉服深蓝色官服，主色调为深蓝色，搭配浅蓝色，绣有山水纹样，头戴乌纱帽，腰系蓝色腰带，佩戴朝珠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "朝廷大臣", "other": "处理国事", "from_chapter": [0], "action": "纷纷上前汇报", "expression": "认真"}, {"name": "老臣（淳于越）", "gender": "男", "age": "老者", "clothes": "高领圆领汉服褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带圆形衣领，圆形衣领", "hairstyle": "白色束发", "figure": "清瘦", "identity": "朝廷大臣", "other": "迂腐守旧", "from_chapter": [0], "action": "脸色一变后继续开口说话", "expression": "着急"}, {"name": "邓山", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配红色，有虎纹图案，头戴黑色毡帽，腰系黑色皮腰带，佩刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "侍卫", "other": "听从公子华指令", "from_chapter": [0], "action": "", "expression": ""}, {"name": "侍卫们", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配银色，有简单的几何花纹，头戴黑色头盔，腰系黑色腰带，佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "宫廷侍卫", "other": "执行命令", "from_chapter": [0], "action": "", "expression": ""}, {"name": "秋香和秋碟", "gender": "女", "age": "少女", "clothes": "高领圆领汉服浅绿色丫鬟服饰，主色调为浅绿色，搭配白色，有蝴蝶纹样，头戴绿色发带，腰系绿色丝带圆形衣领，圆形衣领", "hairstyle": "黑色双马尾", "figure": "娇小", "identity": "公子华的随身侍女", "other": "机灵乖巧", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，皇宫大殿的背景下，一个冷笑一声、满脸轻蔑的青年男子质问着一个脸色一变后继续保持着急神情的老者。青年男子挺拔，言辞犀利，敢言善辩，身着高领圆领的紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带并佩戴玉佩，圆形衣领，黑色束发；老者清瘦，迂腐守旧，穿着高领圆领的褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带，圆形衣领，白色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://p9-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/2025072222565276551927905FB9793F23-8851-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1753282619&x-signature=sjNBREocchypqE60RV8InvWrtn0%3D", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_aa3ff7ff-5588-4a2a-af13-01964a45d622_1.25x_20250722_193545.wav", "audio_duration": 14.941333, "video_url": "", "story_board_id": 8, "subtitle": [{"subtitle_id": 1, "text": "老臣脸色一变，", "timestamp": "00:00:00,160 --> 00:00:01,240", "duration": 1.08, "char_count": 7, "start_time_s": 0.16, "end_time_s": 1.24, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 160, "text": "老"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "臣"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 440, "text": "脸"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 600, "text": "色"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 760, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 880, "text": "变"}], "keyword": "老臣"}, {"subtitle_id": 2, "text": "继续辩解公子不是太子，", "timestamp": "00:00:01,240 --> 00:00:03,200", "duration": 1.96, "char_count": 11, "start_time_s": 1.24, "end_time_s": 3.2, "words": [{"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1240, "text": "继"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1440, "text": "续"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1560, "text": "辩"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1760, "text": "解"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2120, "text": "公"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2280, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2440, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2600, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2720, "text": "太"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 2880, "text": "子"}], "keyword": "公子"}, {"subtitle_id": 3, "text": "主东宫，不合礼制，", "timestamp": "00:00:03,200 --> 00:00:04,440", "duration": 1.24, "char_count": 9, "start_time_s": 3.2, "end_time_s": 4.44, "words": [{"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3200, "text": "主"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3360, "text": "东"}, {"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3560, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3680, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3840, "text": "合"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 3960, "text": "礼"}, {"attribute": {"event": "speech"}, "end_time": 4440, "start_time": 4120, "text": "制"}], "keyword": "东宫"}, {"subtitle_id": 4, "text": "应该遵循周礼。", "timestamp": "00:00:04,440 --> 00:00:05,740", "duration": 1.3, "char_count": 7, "start_time_s": 4.44, "end_time_s": 5.74, "words": [{"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4440, "text": "应"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4600, "text": "该"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4760, "text": "遵"}, {"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 4920, "text": "循"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5120, "text": "周"}, {"attribute": {"event": "speech"}, "end_time": 5740, "start_time": 5280, "text": "礼"}], "keyword": "周礼"}, {"subtitle_id": 5, "text": "公子华冷笑一声好啊，", "timestamp": "00:00:05,840 --> 00:00:07,720", "duration": 1.88, "char_count": 10, "start_time_s": 5.84, "end_time_s": 7.72, "words": [{"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5840, "text": "公"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6080, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6200, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6360, "text": "冷"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6520, "text": "笑"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6720, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6800, "text": "声"}, {"attribute": {"event": "speech"}, "end_time": 7360, "start_time": 7160, "text": "好"}, {"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7360, "text": "啊"}], "keyword": "公子华"}, {"subtitle_id": 6, "text": "那我倒要问问父王，", "timestamp": "00:00:07,720 --> 00:00:09,080", "duration": 1.36, "char_count": 9, "start_time_s": 7.72, "end_time_s": 9.08, "words": [{"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7720, "text": "那"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7920, "text": "我"}, {"attribute": {"event": "speech"}, "end_time": 8160, "start_time": 8040, "text": "倒"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8160, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8320, "text": "问"}, {"attribute": {"event": "speech"}, "end_time": 8600, "start_time": 8360, "text": "问"}, {"attribute": {"event": "speech"}, "end_time": 8920, "start_time": 8800, "text": "父"}, {"attribute": {"event": "speech"}, "end_time": 9080, "start_time": 8920, "text": "王"}], "keyword": "父王"}, {"subtitle_id": 7, "text": "昨天才跟我们四个人说过这事，", "timestamp": "00:00:09,080 --> 00:00:10,920", "duration": 1.84, "char_count": 14, "start_time_s": 9.08, "end_time_s": 10.92, "words": [{"attribute": {"event": "speech"}, "end_time": 9240, "start_time": 9080, "text": "昨"}, {"attribute": {"event": "speech"}, "end_time": 9400, "start_time": 9240, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 9560, "start_time": 9400, "text": "才"}, {"attribute": {"event": "speech"}, "end_time": 9680, "start_time": 9560, "text": "跟"}, {"attribute": {"event": "speech"}, "end_time": 9800, "start_time": 9680, "text": "我"}, {"attribute": {"event": "speech"}, "end_time": 9880, "start_time": 9800, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 10000, "start_time": 9880, "text": "四"}, {"attribute": {"event": "speech"}, "end_time": 10080, "start_time": 10000, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 10200, "start_time": 10080, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 10360, "start_time": 10200, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 10480, "start_time": 10360, "text": "过"}, {"attribute": {"event": "speech"}, "end_time": 10600, "start_time": 10480, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 10920, "start_time": 10600, "text": "事"}], "keyword": "四个人"}, {"subtitle_id": 8, "text": "你怎么知道的这么快，", "timestamp": "00:00:10,920 --> 00:00:12,460", "duration": 1.54, "char_count": 10, "start_time_s": 10.92, "end_time_s": 12.46, "words": [{"attribute": {"event": "speech"}, "end_time": 11160, "start_time": 10920, "text": "你"}, {"attribute": {"event": "speech"}, "end_time": 11240, "start_time": 11160, "text": "怎"}, {"attribute": {"event": "speech"}, "end_time": 11360, "start_time": 11240, "text": "么"}, {"attribute": {"event": "speech"}, "end_time": 11480, "start_time": 11360, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 11600, "start_time": 11480, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 11720, "start_time": 11600, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 11840, "start_time": 11720, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 11960, "start_time": 11840, "text": "么"}, {"attribute": {"event": "speech"}, "end_time": 12460, "start_time": 11960, "text": "快"}], "keyword": "事"}, {"subtitle_id": 9, "text": "是不是在宫里安插了细座？", "timestamp": "00:00:12,640 --> 00:00:14,580", "duration": 1.94, "char_count": 12, "start_time_s": 12.64, "end_time_s": 14.58, "words": [{"attribute": {"event": "speech"}, "end_time": 12800, "start_time": 12640, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 12880, "start_time": 12800, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 13000, "start_time": 12880, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 13120, "start_time": 13000, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 13280, "start_time": 13120, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 13440, "start_time": 13280, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 13600, "start_time": 13440, "text": "安"}, {"attribute": {"event": "speech"}, "end_time": 13840, "start_time": 13680, "text": "插"}, {"attribute": {"event": "speech"}, "end_time": 13920, "start_time": 13840, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 14120, "start_time": 13920, "text": "细"}, {"attribute": {"event": "speech"}, "end_time": 14580, "start_time": 14120, "text": "座"}], "keyword": "细作"}], "keywords": ["老臣", "公子", "东宫", "周礼", "公子华", "父王", "四个人", "事", "细作"]}, {"chapter": 4, "story_board": "”\n\n这话一出，整个大殿都安静了下来。邓山赶紧跪下表忠心，老臣也被吓得不敢说话。公子华步步紧逼：“你到底在宫里安插了多少人？", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": [0], "action": "向前逼近，质问对方", "expression": "愤怒且严肃"}, {"name": "阿房女（夏阿房）", "gender": "女", "age": "中年", "clothes": "高领圆领汉服深粉色宫装，主色调为深粉色，搭配淡黄色，有花卉纹样，头戴金簪发饰，颈戴珍珠项链圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "端庄", "identity": "公子华的母亲", "other": "温柔关怀", "from_chapter": [0], "action": "帮公子华整理衣领，检查他的打扮，然后让他出门", "expression": "着急"}, {"name": "赢政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服黑色龙袍，主色调为黑色，搭配金黄色，绣有五爪金龙纹，头戴冕旒，腰系黄色龙纹腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发头戴冕旒", "figure": "威严", "identity": "大秦大王", "other": "沉稳睿智", "from_chapter": [0], "action": "站在殿中，点头回应老臣", "expression": "平静"}, {"name": "太监", "gender": "男", "age": "中年", "clothes": "高领圆领汉服灰色太监服饰，主色调为灰色，搭配白色，有简单的线条花纹，头戴黑色小帽，腰系灰色布带圆形衣领，圆形衣领", "hairstyle": "光头", "figure": "瘦小", "identity": "侍奉大王的太监", "other": "声音尖细", "from_chapter": [0], "action": "站着说话", "expression": "严肃"}, {"name": "左右丞相和上卿", "gender": "男", "age": "中年", "clothes": "高领圆领汉服深蓝色官服，主色调为深蓝色，搭配浅蓝色，绣有山水纹样，头戴乌纱帽，腰系蓝色腰带，佩戴朝珠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "朝廷大臣", "other": "处理国事", "from_chapter": [0], "action": "纷纷上前汇报", "expression": "认真"}, {"name": "老臣（淳于越）", "gender": "男", "age": "老者", "clothes": "高领圆领汉服褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带圆形衣领，圆形衣领", "hairstyle": "白色束发", "figure": "清瘦", "identity": "朝廷大臣", "other": "迂腐守旧", "from_chapter": [0], "action": "呆立原地，不敢说话", "expression": "惊恐"}, {"name": "邓山", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配红色，有虎纹图案，头戴黑色毡帽，腰系黑色皮腰带，佩刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "侍卫", "other": "听从公子华指令", "from_chapter": [0], "action": "迅速跪下", "expression": "慌张且急切"}, {"name": "侍卫们", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配银色，有简单的几何花纹，头戴黑色头盔，腰系黑色腰带，佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "宫廷侍卫", "other": "执行命令", "from_chapter": [0], "action": "", "expression": ""}, {"name": "秋香和秋碟", "gender": "女", "age": "少女", "clothes": "高领圆领汉服浅绿色丫鬟服饰，主色调为浅绿色，搭配白色，有蝴蝶纹样，头戴绿色发带，腰系绿色丝带圆形衣领，圆形衣领", "hairstyle": "黑色双马尾", "figure": "娇小", "identity": "公子华的随身侍女", "other": "机灵乖巧", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿中，一位愤怒且严肃、向前逼近的青年男子，穿着高领圆领紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发；旁边一个慌张且急切、迅速跪下的青年男子，穿着高领圆领黑色侍卫服饰，主色调为黑色，搭配红色，有虎纹图案，头戴黑色毡帽，腰系黑色皮腰带，佩刀，圆形衣领，黑色束发；不远处呆立原地、惊恐的老者，穿着高领圆领褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带，圆形衣领，白色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753196477_20250722_230117.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_fc8b8bdf-2cf2-4a0c-82e8-f6b9aa96549d_1.25x_20250722_193559.wav", "audio_duration": 9.968, "video_url": "", "story_board_id": 9, "subtitle": [{"subtitle_id": 1, "text": "这话一出，", "timestamp": "00:00:00,200 --> 00:00:01,000", "duration": 0.8, "char_count": 5, "start_time_s": 0.2, "end_time_s": 1.0, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 200, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 320, "text": "话"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 480, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 600, "text": "出"}], "keyword": "这话"}, {"subtitle_id": 2, "text": "整个大殿都安静了下来。", "timestamp": "00:00:01,000 --> 00:00:02,820", "duration": 1.82, "char_count": 11, "start_time_s": 1.0, "end_time_s": 2.82, "words": [{"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 1000, "text": "整"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1160, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1320, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1520, "text": "殿"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1680, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1800, "text": "安"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2000, "text": "静"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2160, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2240, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 2820, "start_time": 2360, "text": "来"}], "keyword": "大殿"}, {"subtitle_id": 3, "text": "邓山赶紧跪下表忠心，", "timestamp": "00:00:02,880 --> 00:00:04,480", "duration": 1.6, "char_count": 10, "start_time_s": 2.88, "end_time_s": 4.48, "words": [{"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2880, "text": "邓"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3080, "text": "山"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3240, "text": "赶"}, {"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3400, "text": "紧"}, {"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3560, "text": "跪"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3720, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3840, "text": "表"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 4000, "text": "忠"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4120, "text": "心"}], "keyword": "邓山"}, {"subtitle_id": 4, "text": "老陈也被吓得不敢说话。", "timestamp": "00:00:04,480 --> 00:00:06,140", "duration": 1.66, "char_count": 11, "start_time_s": 4.48, "end_time_s": 6.14, "words": [{"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4480, "text": "老"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4680, "text": "陈"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4800, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4920, "text": "被"}, {"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5040, "text": "吓"}, {"attribute": {"event": "speech"}, "end_time": 5320, "start_time": 5200, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5320, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5400, "text": "敢"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5520, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 6140, "start_time": 5640, "text": "话"}], "keyword": "老陈"}, {"subtitle_id": 5, "text": "龚子华步步紧逼", "timestamp": "00:00:06,240 --> 00:00:07,400", "duration": 1.16, "char_count": 7, "start_time_s": 6.24, "end_time_s": 7.4, "words": [{"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6240, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6400, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6560, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6720, "text": "步"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6760, "text": "步"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6960, "text": "紧"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7120, "text": "逼"}], "keyword": "龚子华"}, {"subtitle_id": 6, "text": "你到底在宫里安插了多少人？", "timestamp": "00:00:07,400 --> 00:00:09,660", "duration": 2.26, "char_count": 13, "start_time_s": 7.4, "end_time_s": 9.66, "words": [{"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7400, "text": "你"}, {"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7600, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7800, "text": "底"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7920, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8040, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8200, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8360, "text": "安"}, {"attribute": {"event": "speech"}, "end_time": 8760, "start_time": 8560, "text": "插"}, {"attribute": {"event": "speech"}, "end_time": 8840, "start_time": 8760, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8840, "text": "多"}, {"attribute": {"event": "speech"}, "end_time": 9160, "start_time": 9040, "text": "少"}, {"attribute": {"event": "speech"}, "end_time": 9660, "start_time": 9160, "text": "人"}], "keyword": "人"}], "keywords": ["这话", "大殿", "邓山", "老陈", "龚子华", "人"]}, {"chapter": 4, "story_board": "我真是佩服你，连这种事都能打听出来。”老臣结结巴巴地说：“我只是听闻宫女打扫东宫，又见公子出现在朝堂……”\n\n公子华根本不给他解释的机会，直接怒斥：“无耻老贼！天下人都恨不得吃你的肉！", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": [0], "action": "双手叉腰，向前一步逼近老臣", "expression": "愤怒"}, {"name": "阿房女（夏阿房）", "gender": "女", "age": "中年", "clothes": "高领圆领汉服深粉色宫装，主色调为深粉色，搭配淡黄色，有花卉纹样，头戴金簪发饰，颈戴珍珠项链圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "端庄", "identity": "公子华的母亲", "other": "温柔关怀", "from_chapter": [0], "action": "帮公子华整理衣领，检查他的打扮，然后让他出门", "expression": "着急"}, {"name": "赢政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服黑色龙袍，主色调为黑色，搭配金黄色，绣有五爪金龙纹，头戴冕旒，腰系黄色龙纹腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发头戴冕旒", "figure": "威严", "identity": "大秦大王", "other": "沉稳睿智", "from_chapter": [0], "action": "站在殿中，点头回应老臣", "expression": "平静"}, {"name": "太监", "gender": "男", "age": "中年", "clothes": "高领圆领汉服灰色太监服饰，主色调为灰色，搭配白色，有简单的线条花纹，头戴黑色小帽，腰系灰色布带圆形衣领，圆形衣领", "hairstyle": "光头", "figure": "瘦小", "identity": "侍奉大王的太监", "other": "声音尖细", "from_chapter": [0], "action": "站着说话", "expression": "严肃"}, {"name": "左右丞相和上卿", "gender": "男", "age": "中年", "clothes": "高领圆领汉服深蓝色官服，主色调为深蓝色，搭配浅蓝色，绣有山水纹样，头戴乌纱帽，腰系蓝色腰带，佩戴朝珠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "朝廷大臣", "other": "处理国事", "from_chapter": [0], "action": "纷纷上前汇报", "expression": "认真"}, {"name": "老臣（淳于越）", "gender": "男", "age": "老者", "clothes": "高领圆领汉服褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带圆形衣领，圆形衣领", "hairstyle": "白色束发", "figure": "清瘦", "identity": "朝廷大臣", "other": "迂腐守旧", "from_chapter": [0], "action": "身体微微颤抖，低着头，双手摊开", "expression": "慌张"}, {"name": "邓山", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配红色，有虎纹图案，头戴黑色毡帽，腰系黑色皮腰带，佩刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "侍卫", "other": "听从公子华指令", "from_chapter": [0], "action": "迅速跪下", "expression": "慌张且急切"}, {"name": "侍卫们", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配银色，有简单的几何花纹，头戴黑色头盔，腰系黑色腰带，佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "宫廷侍卫", "other": "执行命令", "from_chapter": [0], "action": "", "expression": ""}, {"name": "秋香和秋碟", "gender": "女", "age": "少女", "clothes": "高领圆领汉服浅绿色丫鬟服饰，主色调为浅绿色，搭配白色，有蝴蝶纹样，头戴绿色发带，腰系绿色丝带圆形衣领，圆形衣领", "hairstyle": "黑色双马尾", "figure": "娇小", "identity": "公子华的随身侍女", "other": "机灵乖巧", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿里，一位愤怒的青年男子双手叉腰，向前一步逼近一位老者，老者身体微微颤抖，低着头，双手摊开，显得十分慌张。青年男子身着高领圆领的紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，黑色束发；老者穿着高领圆领的褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带，白色束发。动漫分镜插图风格, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753196721_20250722_230521.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_3e0162dd-a34d-4a22-bd3b-c52f30d7cd82_1.25x_20250722_193609.wav", "audio_duration": 14.346667, "video_url": "", "story_board_id": 10, "subtitle": [{"subtitle_id": 1, "text": "我真是佩服你，", "timestamp": "00:00:00,200 --> 00:00:01,200", "duration": 1.0, "char_count": 7, "start_time_s": 0.2, "end_time_s": 1.2, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 200, "text": "我"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "真"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 440, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 600, "text": "佩"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 800, "text": "服"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 920, "text": "你"}], "keyword": "你"}, {"subtitle_id": 2, "text": "连这种事都能打听出来。", "timestamp": "00:00:01,200 --> 00:00:03,220", "duration": 2.02, "char_count": 11, "start_time_s": 1.2, "end_time_s": 3.22, "words": [{"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1200, "text": "连"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1400, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1560, "text": "种"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1720, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2000, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2200, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2320, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2520, "text": "听"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2640, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 3220, "start_time": 2760, "text": "来"}], "keyword": "事"}, {"subtitle_id": 3, "text": "老陈结结巴巴的说", "timestamp": "00:00:03,320 --> 00:00:04,640", "duration": 1.32, "char_count": 8, "start_time_s": 3.32, "end_time_s": 4.64, "words": [{"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3320, "text": "老"}, {"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3520, "text": "陈"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3720, "text": "结"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3760, "text": "结"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3960, "text": "巴"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4000, "text": "巴"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4200, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4320, "text": "说"}], "keyword": "老陈"}, {"subtitle_id": 4, "text": "我只是听闻。", "timestamp": "00:00:04,640 --> 00:00:05,400", "duration": 0.76, "char_count": 6, "start_time_s": 4.64, "end_time_s": 5.4, "words": [{"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4640, "text": "我"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4880, "text": "只"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5040, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5160, "text": "听"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5280, "text": "闻"}], "keyword": "听闻"}, {"subtitle_id": 5, "text": "宫女打扫东宫，", "timestamp": "00:00:05,400 --> 00:00:06,520", "duration": 1.12, "char_count": 7, "start_time_s": 5.4, "end_time_s": 6.52, "words": [{"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5400, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5560, "text": "女"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5720, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5880, "text": "扫"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6040, "text": "东"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6200, "text": "宫"}], "keyword": "宫女"}, {"subtitle_id": 6, "text": "又见公子出现在朝堂，", "timestamp": "00:00:06,520 --> 00:00:08,120", "duration": 1.6, "char_count": 10, "start_time_s": 6.52, "end_time_s": 8.12, "words": [{"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6520, "text": "又"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6720, "text": "见"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6880, "text": "公"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7040, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7320, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7480, "text": "现"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7600, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7760, "text": "朝"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 7960, "text": "堂"}], "keyword": "公子"}, {"subtitle_id": 7, "text": "公子华根本不给他解释的机会，", "timestamp": "00:00:08,120 --> 00:00:10,200", "duration": 2.08, "char_count": 14, "start_time_s": 8.12, "end_time_s": 10.2, "words": [{"attribute": {"event": "speech"}, "end_time": 8240, "start_time": 8120, "text": "公"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8240, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 8600, "start_time": 8400, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8640, "text": "根"}, {"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8800, "text": "本"}, {"attribute": {"event": "speech"}, "end_time": 9080, "start_time": 9000, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 9200, "start_time": 9080, "text": "给"}, {"attribute": {"event": "speech"}, "end_time": 9320, "start_time": 9200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 9520, "start_time": 9360, "text": "解"}, {"attribute": {"event": "speech"}, "end_time": 9640, "start_time": 9520, "text": "释"}, {"attribute": {"event": "speech"}, "end_time": 9760, "start_time": 9640, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 9880, "start_time": 9760, "text": "机"}, {"attribute": {"event": "speech"}, "end_time": 10200, "start_time": 9880, "text": "会"}], "keyword": "公子华"}, {"subtitle_id": 8, "text": "直接怒斥无耻老贼，", "timestamp": "00:00:10,200 --> 00:00:12,180", "duration": 1.98, "char_count": 9, "start_time_s": 10.2, "end_time_s": 12.18, "words": [{"attribute": {"event": "speech"}, "end_time": 10440, "start_time": 10200, "text": "直"}, {"attribute": {"event": "speech"}, "end_time": 10600, "start_time": 10480, "text": "接"}, {"attribute": {"event": "speech"}, "end_time": 10760, "start_time": 10600, "text": "怒"}, {"attribute": {"event": "speech"}, "end_time": 10960, "start_time": 10800, "text": "斥"}, {"attribute": {"event": "speech"}, "end_time": 11360, "start_time": 11080, "text": "无"}, {"attribute": {"event": "speech"}, "end_time": 11520, "start_time": 11360, "text": "耻"}, {"attribute": {"event": "speech"}, "end_time": 11680, "start_time": 11520, "text": "老"}, {"attribute": {"event": "speech"}, "end_time": 12180, "start_time": 11720, "text": "贼"}], "keyword": "怒斥"}, {"subtitle_id": 9, "text": "天下人都恨不得吃你的肉！", "timestamp": "00:00:12,320 --> 00:00:14,220", "duration": 1.9, "char_count": 12, "start_time_s": 12.32, "end_time_s": 14.22, "words": [{"attribute": {"event": "speech"}, "end_time": 12560, "start_time": 12320, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 12680, "start_time": 12560, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 12840, "start_time": 12680, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 12960, "start_time": 12840, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 13120, "start_time": 12960, "text": "恨"}, {"attribute": {"event": "speech"}, "end_time": 13240, "start_time": 13120, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 13360, "start_time": 13240, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 13520, "start_time": 13360, "text": "吃"}, {"attribute": {"event": "speech"}, "end_time": 13680, "start_time": 13520, "text": "你"}, {"attribute": {"event": "speech"}, "end_time": 13760, "start_time": 13680, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 14220, "start_time": 13760, "text": "肉"}], "keyword": "天下人"}], "keywords": ["你", "事", "老陈", "听闻", "宫女", "公子", "公子华", "怒斥", "天下人"]}, {"chapter": 4, "story_board": "你不过是个满口仁义道德的书呆子，整天空谈理论，有什么用？国家有难的时候，你拿什么来帮忙？”他说完，老臣的脸色越来越难看，最后竟然一口血吐出来，晕了过去。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": [0], "action": "手指老臣，大声怒斥", "expression": "愤怒"}, {"name": "阿房女（夏阿房）", "gender": "女", "age": "中年", "clothes": "高领圆领汉服深粉色宫装，主色调为深粉色，搭配淡黄色，有花卉纹样，头戴金簪发饰，颈戴珍珠项链圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "端庄", "identity": "公子华的母亲", "other": "温柔关怀", "from_chapter": [0], "action": "帮公子华整理衣领，检查他的打扮，然后让他出门", "expression": "着急"}, {"name": "赢政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服黑色龙袍，主色调为黑色，搭配金黄色，绣有五爪金龙纹，头戴冕旒，腰系黄色龙纹腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发头戴冕旒", "figure": "威严", "identity": "大秦大王", "other": "沉稳睿智", "from_chapter": [0], "action": "站在殿中，点头回应老臣", "expression": "平静"}, {"name": "太监", "gender": "男", "age": "中年", "clothes": "高领圆领汉服灰色太监服饰，主色调为灰色，搭配白色，有简单的线条花纹，头戴黑色小帽，腰系灰色布带圆形衣领，圆形衣领", "hairstyle": "光头", "figure": "瘦小", "identity": "侍奉大王的太监", "other": "声音尖细", "from_chapter": [0], "action": "站着说话", "expression": "严肃"}, {"name": "左右丞相和上卿", "gender": "男", "age": "中年", "clothes": "高领圆领汉服深蓝色官服，主色调为深蓝色，搭配浅蓝色，绣有山水纹样，头戴乌纱帽，腰系蓝色腰带，佩戴朝珠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "朝廷大臣", "other": "处理国事", "from_chapter": [0], "action": "纷纷上前汇报", "expression": "认真"}, {"name": "老臣（淳于越）", "gender": "男", "age": "老者", "clothes": "高领圆领汉服褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带圆形衣领，圆形衣领", "hairstyle": "白色束发", "figure": "清瘦", "identity": "朝廷大臣", "other": "迂腐守旧", "from_chapter": [0], "action": "身体摇晃，最后倒地", "expression": "惊恐、痛苦"}, {"name": "邓山", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配红色，有虎纹图案，头戴黑色毡帽，腰系黑色皮腰带，佩刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "侍卫", "other": "听从公子华指令", "from_chapter": [0], "action": "迅速跪下", "expression": "慌张且急切"}, {"name": "侍卫们", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配银色，有简单的几何花纹，头戴黑色头盔，腰系黑色腰带，佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "宫廷侍卫", "other": "执行命令", "from_chapter": [0], "action": "", "expression": ""}, {"name": "秋香和秋碟", "gender": "女", "age": "少女", "clothes": "高领圆领汉服浅绿色丫鬟服饰，主色调为浅绿色，搭配白色，有蝴蝶纹样，头戴绿色发带，腰系绿色丝带圆形衣领，圆形衣领", "hairstyle": "黑色双马尾", "figure": "娇小", "identity": "公子华的随身侍女", "other": "机灵乖巧", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿里，一个愤怒地手指着对方大声怒斥的青年男子，他身着高领圆领的紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带并佩戴玉佩，圆形衣领，黑色束发，身姿挺拔；旁边是一个身体摇晃、满脸惊恐痛苦最后倒地的老者，他穿着高领圆领的褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带，圆形衣领，白色束发，身形清瘦，动漫分镜插图风格, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753197038_20250722_231039.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_94013142-599e-4d97-844a-c6d6efa3d2fd_1.25x_20250722_193625.wav", "audio_duration": 12.290667, "video_url": "", "story_board_id": 11, "subtitle": [{"subtitle_id": 1, "text": "你不过是个满口仁义道德的书呆子，", "timestamp": "00:00:00,160 --> 00:00:02,360", "duration": 2.2, "char_count": 16, "start_time_s": 0.16, "end_time_s": 2.36, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "你"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 400, "text": "过"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 520, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 640, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 720, "text": "满"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 920, "text": "口"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1080, "text": "仁"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1200, "text": "义"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1320, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1480, "text": "德"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1600, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1680, "text": "书"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1840, "text": "呆"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2000, "text": "子"}], "keyword": "书呆子"}, {"subtitle_id": 2, "text": "整天空谈理论有什么用？", "timestamp": "00:00:02,360 --> 00:00:04,380", "duration": 2.02, "char_count": 11, "start_time_s": 2.36, "end_time_s": 4.38, "words": [{"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2360, "text": "整"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2560, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2720, "text": "空"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2880, "text": "谈"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3040, "text": "理"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3200, "text": "论"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3520, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3720, "text": "什"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3840, "text": "么"}, {"attribute": {"event": "speech"}, "end_time": 4380, "start_time": 3920, "text": "用"}], "keyword": "理论"}, {"subtitle_id": 3, "text": "国家有难的时候，", "timestamp": "00:00:04,560 --> 00:00:05,800", "duration": 1.24, "char_count": 8, "start_time_s": 4.56, "end_time_s": 5.8, "words": [{"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4560, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4760, "text": "家"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4920, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5080, "text": "难"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5200, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5360, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5440, "text": "候"}], "keyword": "国家"}, {"subtitle_id": 4, "text": "你拿什么来帮忙？", "timestamp": "00:00:05,800 --> 00:00:07,180", "duration": 1.38, "char_count": 8, "start_time_s": 5.8, "end_time_s": 7.18, "words": [{"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5800, "text": "你"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6000, "text": "拿"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6240, "text": "什"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6320, "text": "么"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6400, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6560, "text": "帮"}, {"attribute": {"event": "speech"}, "end_time": 7180, "start_time": 6680, "text": "忙"}], "keyword": "帮忙"}, {"subtitle_id": 5, "text": "他说完，", "timestamp": "00:00:07,320 --> 00:00:07,920", "duration": 0.6, "char_count": 4, "start_time_s": 7.32, "end_time_s": 7.92, "words": [{"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7320, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7480, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7600, "text": "完"}], "keyword": "他"}, {"subtitle_id": 6, "text": "老陈的脸色越来越难看，", "timestamp": "00:00:07,920 --> 00:00:09,680", "duration": 1.76, "char_count": 11, "start_time_s": 7.92, "end_time_s": 9.68, "words": [{"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7920, "text": "老"}, {"attribute": {"event": "speech"}, "end_time": 8240, "start_time": 8120, "text": "陈"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8240, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8320, "text": "脸"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8520, "text": "色"}, {"attribute": {"event": "speech"}, "end_time": 8840, "start_time": 8640, "text": "越"}, {"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8840, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 9120, "start_time": 9000, "text": "越"}, {"attribute": {"event": "speech"}, "end_time": 9280, "start_time": 9120, "text": "难"}, {"attribute": {"event": "speech"}, "end_time": 9680, "start_time": 9280, "text": "看"}], "keyword": "老陈"}, {"subtitle_id": 7, "text": "最后竟然一口血吐出来，", "timestamp": "00:00:09,680 --> 00:00:11,240", "duration": 1.56, "char_count": 11, "start_time_s": 9.68, "end_time_s": 11.24, "words": [{"attribute": {"event": "speech"}, "end_time": 9840, "start_time": 9680, "text": "最"}, {"attribute": {"event": "speech"}, "end_time": 9960, "start_time": 9840, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 10120, "start_time": 9960, "text": "竟"}, {"attribute": {"event": "speech"}, "end_time": 10200, "start_time": 10120, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 10400, "start_time": 10200, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 10560, "start_time": 10400, "text": "口"}, {"attribute": {"event": "speech"}, "end_time": 10720, "start_time": 10560, "text": "血"}, {"attribute": {"event": "speech"}, "end_time": 10880, "start_time": 10760, "text": "吐"}, {"attribute": {"event": "speech"}, "end_time": 11000, "start_time": 10880, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 11240, "start_time": 11000, "text": "来"}], "keyword": "血"}, {"subtitle_id": 8, "text": "晕了过去。", "timestamp": "00:00:11,240 --> 00:00:12,260", "duration": 1.02, "char_count": 5, "start_time_s": 11.24, "end_time_s": 12.26, "words": [{"attribute": {"event": "speech"}, "end_time": 11440, "start_time": 11240, "text": "晕"}, {"attribute": {"event": "speech"}, "end_time": 11600, "start_time": 11440, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 11760, "start_time": 11600, "text": "过"}, {"attribute": {"event": "speech"}, "end_time": 12260, "start_time": 11760, "text": "去"}], "keyword": "晕倒"}], "keywords": ["书呆子", "理论", "国家", "帮忙", "他", "老陈", "血", "晕倒"]}, {"chapter": 4, "story_board": "公子华走过去，掐住老臣的人中，大声喊：“邓山，去提桶冷水来！”邓山立刻跑出去，很快端来一桶水，泼在老臣脸上。老臣慢慢醒来，满脸惊恐。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": [0], "action": "走到老臣身边，掐住老臣的人中，大声呼喊", "expression": "愤怒"}, {"name": "阿房女（夏阿房）", "gender": "女", "age": "中年", "clothes": "高领圆领汉服深粉色宫装，主色调为深粉色，搭配淡黄色，有花卉纹样，头戴金簪发饰，颈戴珍珠项链圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "端庄", "identity": "公子华的母亲", "other": "温柔关怀", "from_chapter": [0], "action": "帮公子华整理衣领，检查他的打扮，然后让他出门", "expression": "着急"}, {"name": "赢政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服黑色龙袍，主色调为黑色，搭配金黄色，绣有五爪金龙纹，头戴冕旒，腰系黄色龙纹腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发头戴冕旒", "figure": "威严", "identity": "大秦大王", "other": "沉稳睿智", "from_chapter": [0], "action": "站在殿中，点头回应老臣", "expression": "平静"}, {"name": "太监", "gender": "男", "age": "中年", "clothes": "高领圆领汉服灰色太监服饰，主色调为灰色，搭配白色，有简单的线条花纹，头戴黑色小帽，腰系灰色布带圆形衣领，圆形衣领", "hairstyle": "光头", "figure": "瘦小", "identity": "侍奉大王的太监", "other": "声音尖细", "from_chapter": [0], "action": "站着说话", "expression": "严肃"}, {"name": "左右丞相和上卿", "gender": "男", "age": "中年", "clothes": "高领圆领汉服深蓝色官服，主色调为深蓝色，搭配浅蓝色，绣有山水纹样，头戴乌纱帽，腰系蓝色腰带，佩戴朝珠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "朝廷大臣", "other": "处理国事", "from_chapter": [0], "action": "纷纷上前汇报", "expression": "认真"}, {"name": "老臣（淳于越）", "gender": "男", "age": "老者", "clothes": "高领圆领汉服褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带圆形衣领，圆形衣领", "hairstyle": "白色束发", "figure": "清瘦", "identity": "朝廷大臣", "other": "迂腐守旧", "from_chapter": [0], "action": "慢慢醒来", "expression": "惊恐"}, {"name": "邓山", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配红色，有虎纹图案，头戴黑色毡帽，腰系黑色皮腰带，佩刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "侍卫", "other": "听从公子华指令", "from_chapter": [0], "action": "快速跑出去，端来一桶水泼在老臣脸上", "expression": "紧张"}, {"name": "侍卫们", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配银色，有简单的几何花纹，头戴黑色头盔，腰系黑色腰带，佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "宫廷侍卫", "other": "执行命令", "from_chapter": [0], "action": "", "expression": ""}, {"name": "秋香和秋碟", "gender": "女", "age": "少女", "clothes": "高领圆领汉服浅绿色丫鬟服饰，主色调为浅绿色，搭配白色，有蝴蝶纹样，头戴绿色发带，腰系绿色丝带圆形衣领，圆形衣领", "hairstyle": "黑色双马尾", "figure": "娇小", "identity": "公子华的随身侍女", "other": "机灵乖巧", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿里，一位愤怒的青年男子走到一位惊恐的老者身边，掐住他的人中大声呼喊，神情紧张的青年男子快速跑出去端来一桶水泼在老者脸上，老者慢慢醒来。愤怒呼喊的青年男子身着高领圆领紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发；神情紧张的青年男子身着高领圆领黑色侍卫服饰，主色调为黑色，搭配红色，有虎纹图案，头戴黑色毡帽，腰系黑色皮腰带，佩刀，圆形衣领，黑色束发；惊恐的老者身着高领圆领褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带，圆形衣领，白色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753197594_20250722_231954.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_c12c2449-bb12-4205-b766-a4ab73829df5_1.25x_20250722_193643.wav", "audio_duration": 11.64, "video_url": "", "story_board_id": 12, "subtitle": [{"subtitle_id": 1, "text": "龚子华走过去，", "timestamp": "00:00:00,160 --> 00:00:01,200", "duration": 1.04, "char_count": 7, "start_time_s": 0.16, "end_time_s": 1.2, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 160, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 440, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 640, "text": "走"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 760, "text": "过"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 880, "text": "去"}], "keyword": "龚子华"}, {"subtitle_id": 2, "text": "掐住老陈的人中，", "timestamp": "00:00:01,200 --> 00:00:02,360", "duration": 1.16, "char_count": 8, "start_time_s": 1.2, "end_time_s": 2.36, "words": [{"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1200, "text": "掐"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1400, "text": "住"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1520, "text": "老"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1720, "text": "陈"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1840, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1920, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2080, "text": "中"}], "keyword": "人中"}, {"subtitle_id": 3, "text": "大声喊邓山，", "timestamp": "00:00:02,360 --> 00:00:03,760", "duration": 1.4, "char_count": 6, "start_time_s": 2.36, "end_time_s": 3.76, "words": [{"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2360, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2600, "text": "声"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2760, "text": "喊"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3200, "text": "邓"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3440, "text": "山"}], "keyword": "邓山"}, {"subtitle_id": 4, "text": "去提桶冷水来！", "timestamp": "00:00:03,760 --> 00:00:05,260", "duration": 1.5, "char_count": 7, "start_time_s": 3.76, "end_time_s": 5.26, "words": [{"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3760, "text": "去"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 3920, "text": "提"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4160, "text": "桶"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4360, "text": "冷"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4600, "text": "水"}, {"attribute": {"event": "speech"}, "end_time": 5260, "start_time": 4760, "text": "来"}], "keyword": "冷水"}, {"subtitle_id": 5, "text": "邓山立刻跑出去，", "timestamp": "00:00:05,360 --> 00:00:06,680", "duration": 1.32, "char_count": 8, "start_time_s": 5.36, "end_time_s": 6.68, "words": [{"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5360, "text": "邓"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5600, "text": "山"}, {"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5720, "text": "立"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5920, "text": "刻"}, {"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 6040, "text": "跑"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6240, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6360, "text": "去"}], "keyword": "邓山"}, {"subtitle_id": 6, "text": "很快端来一桶水，", "timestamp": "00:00:06,680 --> 00:00:07,880", "duration": 1.2, "char_count": 8, "start_time_s": 6.68, "end_time_s": 7.88, "words": [{"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6680, "text": "很"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6880, "text": "快"}, {"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 7040, "text": "端"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7200, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7320, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7400, "text": "桶"}, {"attribute": {"event": "speech"}, "end_time": 7880, "start_time": 7600, "text": "水"}], "keyword": "水桶"}, {"subtitle_id": 7, "text": "泼在老陈脸上。", "timestamp": "00:00:07,880 --> 00:00:09,180", "duration": 1.3, "char_count": 7, "start_time_s": 7.88, "end_time_s": 9.18, "words": [{"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7880, "text": "泼"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8080, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8200, "text": "老"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8400, "text": "陈"}, {"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8520, "text": "脸"}, {"attribute": {"event": "speech"}, "end_time": 9180, "start_time": 8720, "text": "上"}], "keyword": "泼水"}, {"subtitle_id": 8, "text": "老陈慢慢醒来，", "timestamp": "00:00:09,280 --> 00:00:10,520", "duration": 1.24, "char_count": 7, "start_time_s": 9.28, "end_time_s": 10.52, "words": [{"attribute": {"event": "speech"}, "end_time": 9480, "start_time": 9280, "text": "老"}, {"attribute": {"event": "speech"}, "end_time": 9640, "start_time": 9520, "text": "陈"}, {"attribute": {"event": "speech"}, "end_time": 9720, "start_time": 9680, "text": "慢"}, {"attribute": {"event": "speech"}, "end_time": 10000, "start_time": 9720, "text": "慢"}, {"attribute": {"event": "speech"}, "end_time": 10200, "start_time": 10040, "text": "醒"}, {"attribute": {"event": "speech"}, "end_time": 10520, "start_time": 10200, "text": "来"}], "keyword": "老陈"}, {"subtitle_id": 9, "text": "满脸惊恐。", "timestamp": "00:00:10,520 --> 00:00:11,540", "duration": 1.02, "char_count": 5, "start_time_s": 10.52, "end_time_s": 11.54, "words": [{"attribute": {"event": "speech"}, "end_time": 10720, "start_time": 10520, "text": "满"}, {"attribute": {"event": "speech"}, "end_time": 10880, "start_time": 10720, "text": "脸"}, {"attribute": {"event": "speech"}, "end_time": 11040, "start_time": 10920, "text": "惊"}, {"attribute": {"event": "speech"}, "end_time": 11540, "start_time": 11080, "text": "恐"}], "keyword": "惊恐"}], "keywords": ["龚子华", "人中", "邓山", "冷水", "邓山", "水桶", "泼水", "老陈", "惊恐"]}, {"chapter": 4, "story_board": "公子华看着他，冷冷地说：“刚才你晕过去，我一时感慨，写了一首诗送给你。”\n\n他念出诗来：“鲁叟谈五经，白发死章句……”这首诗一出，老臣再次口吐鲜血，再次晕倒。众人看得目瞪口呆，没想到公子华骂人居然能骂到这个份上，既文雅又狠毒，简直比刀还厉害。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": [0], "action": "看着老臣，念出诗句", "expression": "冷漠"}, {"name": "阿房女（夏阿房）", "gender": "女", "age": "中年", "clothes": "高领圆领汉服深粉色宫装，主色调为深粉色，搭配淡黄色，有花卉纹样，头戴金簪发饰，颈戴珍珠项链圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "端庄", "identity": "公子华的母亲", "other": "温柔关怀", "from_chapter": [0], "action": "帮公子华整理衣领，检查他的打扮，然后让他出门", "expression": "着急"}, {"name": "赢政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服黑色龙袍，主色调为黑色，搭配金黄色，绣有五爪金龙纹，头戴冕旒，腰系黄色龙纹腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发头戴冕旒", "figure": "威严", "identity": "大秦大王", "other": "沉稳睿智", "from_chapter": [0], "action": "站在殿中，点头回应老臣", "expression": "平静"}, {"name": "太监", "gender": "男", "age": "中年", "clothes": "高领圆领汉服灰色太监服饰，主色调为灰色，搭配白色，有简单的线条花纹，头戴黑色小帽，腰系灰色布带圆形衣领，圆形衣领", "hairstyle": "光头", "figure": "瘦小", "identity": "侍奉大王的太监", "other": "声音尖细", "from_chapter": [0], "action": "站着说话", "expression": "严肃"}, {"name": "左右丞相和上卿", "gender": "男", "age": "中年", "clothes": "高领圆领汉服深蓝色官服，主色调为深蓝色，搭配浅蓝色，绣有山水纹样，头戴乌纱帽，腰系蓝色腰带，佩戴朝珠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "朝廷大臣", "other": "处理国事", "from_chapter": [0], "action": "纷纷上前汇报", "expression": "认真"}, {"name": "老臣（淳于越）", "gender": "男", "age": "老者", "clothes": "高领圆领汉服褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带圆形衣领，圆形衣领", "hairstyle": "白色束发", "figure": "清瘦", "identity": "朝廷大臣", "other": "迂腐守旧", "from_chapter": [0], "action": "口吐鲜血后晕倒", "expression": "惊恐"}, {"name": "邓山", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配红色，有虎纹图案，头戴黑色毡帽，腰系黑色皮腰带，佩刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "侍卫", "other": "听从公子华指令", "from_chapter": [0], "action": "快速跑出去，端来一桶水泼在老臣脸上", "expression": "紧张"}, {"name": "侍卫们", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配银色，有简单的几何花纹，头戴黑色头盔，腰系黑色腰带，佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "宫廷侍卫", "other": "执行命令", "from_chapter": [0], "action": "", "expression": ""}, {"name": "秋香和秋碟", "gender": "女", "age": "少女", "clothes": "高领圆领汉服浅绿色丫鬟服饰，主色调为浅绿色，搭配白色，有蝴蝶纹样，头戴绿色发带，腰系绿色丝带圆形衣领，圆形衣领", "hairstyle": "黑色双马尾", "figure": "娇小", "identity": "公子华的随身侍女", "other": "机灵乖巧", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，皇宫大殿背景下，一位冷漠的青年男子挺拔站立着看着一位清瘦的老者念出诗句，该老者惊恐地口吐灰色血液后晕倒在地。青年男子身着高领圆领紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发；老者身着高领圆领褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带，圆形衣领，白色束发。动漫分镜插图风格, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753198009_20250722_232650.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_b5cdae76-70f1-462b-85f2-01dd4aa5ecce_1.25x_20250722_193657.wav", "audio_duration": 19.456, "video_url": "", "story_board_id": 13, "subtitle": [{"subtitle_id": 1, "text": "龚子华看着他，", "timestamp": "00:00:00,160 --> 00:00:01,200", "duration": 1.04, "char_count": 7, "start_time_s": 0.16, "end_time_s": 1.2, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 440, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 560, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 760, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 840, "text": "他"}], "keyword": "龚子华"}, {"subtitle_id": 2, "text": "冷冷的说刚才你晕过去，", "timestamp": "00:00:01,200 --> 00:00:03,000", "duration": 1.8, "char_count": 11, "start_time_s": 1.2, "end_time_s": 3.0, "words": [{"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1200, "text": "冷"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1240, "text": "冷"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1480, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1600, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1960, "text": "刚"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2120, "text": "才"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2280, "text": "你"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2400, "text": "晕"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2560, "text": "过"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2760, "text": "去"}], "keyword": "晕过去"}, {"subtitle_id": 3, "text": "我一时感慨，", "timestamp": "00:00:03,000 --> 00:00:03,920", "duration": 0.92, "char_count": 6, "start_time_s": 3.0, "end_time_s": 3.92, "words": [{"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3000, "text": "我"}, {"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3200, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3320, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3400, "text": "感"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3600, "text": "慨"}], "keyword": "感慨"}, {"subtitle_id": 4, "text": "写了一首诗送给你。", "timestamp": "00:00:03,920 --> 00:00:05,340", "duration": 1.42, "char_count": 9, "start_time_s": 3.92, "end_time_s": 5.34, "words": [{"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3920, "text": "写"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4080, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4160, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4240, "text": "首"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4400, "text": "诗"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4600, "text": "送"}, {"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4720, "text": "给"}, {"attribute": {"event": "speech"}, "end_time": 5340, "start_time": 4840, "text": "你"}], "keyword": "诗"}, {"subtitle_id": 5, "text": "他念出诗来鲁叟谈五经，", "timestamp": "00:00:05,400 --> 00:00:07,400", "duration": 2.0, "char_count": 11, "start_time_s": 5.4, "end_time_s": 7.4, "words": [{"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5400, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5560, "text": "念"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5680, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5840, "text": "诗"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 5960, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6360, "text": "鲁"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6560, "text": "叟"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6760, "text": "谈"}, {"attribute": {"event": "speech"}, "end_time": 7120, "start_time": 6920, "text": "五"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7120, "text": "经"}], "keyword": "诗"}, {"subtitle_id": 6, "text": "白发死章句。", "timestamp": "00:00:07,400 --> 00:00:08,280", "duration": 0.88, "char_count": 6, "start_time_s": 7.4, "end_time_s": 8.28, "words": [{"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7400, "text": "白"}, {"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7640, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7800, "text": "死"}, {"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7960, "text": "章"}, {"attribute": {"event": "speech"}, "end_time": 8280, "start_time": 8120, "text": "句"}], "keyword": "章句"}, {"subtitle_id": 7, "text": "这首诗一出，", "timestamp": "00:00:08,280 --> 00:00:09,120", "duration": 0.84, "char_count": 6, "start_time_s": 8.28, "end_time_s": 9.12, "words": [{"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8280, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 8560, "start_time": 8400, "text": "首"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8560, "text": "诗"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8640, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 9120, "start_time": 8800, "text": "出"}], "keyword": "诗"}, {"subtitle_id": 8, "text": "老陈再次口吐鲜血，", "timestamp": "00:00:09,120 --> 00:00:10,680", "duration": 1.56, "char_count": 9, "start_time_s": 9.12, "end_time_s": 10.68, "words": [{"attribute": {"event": "speech"}, "end_time": 9280, "start_time": 9120, "text": "老"}, {"attribute": {"event": "speech"}, "end_time": 9480, "start_time": 9320, "text": "陈"}, {"attribute": {"event": "speech"}, "end_time": 9720, "start_time": 9480, "text": "再"}, {"attribute": {"event": "speech"}, "end_time": 9840, "start_time": 9720, "text": "次"}, {"attribute": {"event": "speech"}, "end_time": 10000, "start_time": 9840, "text": "口"}, {"attribute": {"event": "speech"}, "end_time": 10160, "start_time": 10000, "text": "吐"}, {"attribute": {"event": "speech"}, "end_time": 10360, "start_time": 10200, "text": "鲜"}, {"attribute": {"event": "speech"}, "end_time": 10680, "start_time": 10360, "text": "血"}], "keyword": "老陈"}, {"subtitle_id": 9, "text": "再次晕倒。", "timestamp": "00:00:10,680 --> 00:00:11,780", "duration": 1.1, "char_count": 5, "start_time_s": 10.68, "end_time_s": 11.78, "words": [{"attribute": {"event": "speech"}, "end_time": 10920, "start_time": 10680, "text": "再"}, {"attribute": {"event": "speech"}, "end_time": 11120, "start_time": 10960, "text": "次"}, {"attribute": {"event": "speech"}, "end_time": 11280, "start_time": 11160, "text": "晕"}, {"attribute": {"event": "speech"}, "end_time": 11780, "start_time": 11320, "text": "倒"}], "keyword": "晕倒"}, {"subtitle_id": 10, "text": "众人看得目瞪口呆，", "timestamp": "00:00:11,920 --> 00:00:13,480", "duration": 1.56, "char_count": 9, "start_time_s": 11.92, "end_time_s": 13.48, "words": [{"attribute": {"event": "speech"}, "end_time": 12080, "start_time": 11920, "text": "众"}, {"attribute": {"event": "speech"}, "end_time": 12280, "start_time": 12080, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 12600, "start_time": 12360, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 12680, "start_time": 12600, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 12800, "start_time": 12680, "text": "目"}, {"attribute": {"event": "speech"}, "end_time": 12920, "start_time": 12800, "text": "瞪"}, {"attribute": {"event": "speech"}, "end_time": 13120, "start_time": 12920, "text": "口"}, {"attribute": {"event": "speech"}, "end_time": 13480, "start_time": 13120, "text": "呆"}], "keyword": "众人"}, {"subtitle_id": 11, "text": "没想到", "timestamp": "00:00:13,480 --> 00:00:13,960", "duration": 0.48, "char_count": 3, "start_time_s": 13.48, "end_time_s": 13.96, "words": [{"attribute": {"event": "speech"}, "end_time": 13680, "start_time": 13480, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 13800, "start_time": 13680, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 13960, "start_time": 13800, "text": "到"}], "keyword": "想不到"}, {"subtitle_id": 12, "text": "龚子华骂人居然能骂到这个份上，", "timestamp": "00:00:13,960 --> 00:00:16,240", "duration": 2.28, "char_count": 15, "start_time_s": 13.96, "end_time_s": 16.24, "words": [{"attribute": {"event": "speech"}, "end_time": 14080, "start_time": 13960, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 14200, "start_time": 14080, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 14360, "start_time": 14200, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 14480, "start_time": 14360, "text": "骂"}, {"attribute": {"event": "speech"}, "end_time": 14680, "start_time": 14480, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 14920, "start_time": 14800, "text": "居"}, {"attribute": {"event": "speech"}, "end_time": 15040, "start_time": 14920, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 15160, "start_time": 15040, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 15320, "start_time": 15160, "text": "骂"}, {"attribute": {"event": "speech"}, "end_time": 15480, "start_time": 15320, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 15600, "start_time": 15480, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 15720, "start_time": 15600, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 15840, "start_time": 15720, "text": "份"}, {"attribute": {"event": "speech"}, "end_time": 16240, "start_time": 15880, "text": "上"}], "keyword": "龚子华"}, {"subtitle_id": 13, "text": "既文雅又狠毒，", "timestamp": "00:00:16,240 --> 00:00:17,480", "duration": 1.24, "char_count": 7, "start_time_s": 16.24, "end_time_s": 17.48, "words": [{"attribute": {"event": "speech"}, "end_time": 16400, "start_time": 16240, "text": "既"}, {"attribute": {"event": "speech"}, "end_time": 16560, "start_time": 16400, "text": "文"}, {"attribute": {"event": "speech"}, "end_time": 16760, "start_time": 16560, "text": "雅"}, {"attribute": {"event": "speech"}, "end_time": 16920, "start_time": 16760, "text": "又"}, {"attribute": {"event": "speech"}, "end_time": 17080, "start_time": 16920, "text": "狠"}, {"attribute": {"event": "speech"}, "end_time": 17480, "start_time": 17080, "text": "毒"}], "keyword": "文雅"}, {"subtitle_id": 14, "text": "简直比刀还厉害！", "timestamp": "00:00:17,480 --> 00:00:19,100", "duration": 1.62, "char_count": 8, "start_time_s": 17.48, "end_time_s": 19.1, "words": [{"attribute": {"event": "speech"}, "end_time": 17640, "start_time": 17480, "text": "简"}, {"attribute": {"event": "speech"}, "end_time": 17800, "start_time": 17640, "text": "直"}, {"attribute": {"event": "speech"}, "end_time": 17960, "start_time": 17800, "text": "比"}, {"attribute": {"event": "speech"}, "end_time": 18160, "start_time": 17960, "text": "刀"}, {"attribute": {"event": "speech"}, "end_time": 18480, "start_time": 18280, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 18640, "start_time": 18480, "text": "厉"}, {"attribute": {"event": "speech"}, "end_time": 19100, "start_time": 18640, "text": "害"}], "keyword": "刀"}], "keywords": ["龚子华", "晕过去", "感慨", "诗", "诗", "章句", "诗", "老陈", "晕倒", "众人", "想不到", "龚子华", "文雅", "刀"]}, {"chapter": 4, "story_board": "公子华拍了拍手：“赶紧把他抬去太医院，不然他就死了。”侍卫们连忙抬人离开。公子华还不忘补充一句：“告诉他，亚圣能吐两丈，他才吐这么点，给亚圣丢脸了。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": [0], "action": "拍了拍手，对着侍卫说话，补充话语", "expression": "神情淡然"}, {"name": "阿房女（夏阿房）", "gender": "女", "age": "中年", "clothes": "高领圆领汉服深粉色宫装，主色调为深粉色，搭配淡黄色，有花卉纹样，头戴金簪发饰，颈戴珍珠项链圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "端庄", "identity": "公子华的母亲", "other": "温柔关怀", "from_chapter": [0], "action": "帮公子华整理衣领，检查他的打扮，然后让他出门", "expression": "着急"}, {"name": "赢政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服黑色龙袍，主色调为黑色，搭配金黄色，绣有五爪金龙纹，头戴冕旒，腰系黄色龙纹腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发头戴冕旒", "figure": "威严", "identity": "大秦大王", "other": "沉稳睿智", "from_chapter": [0], "action": "站在殿中，点头回应老臣", "expression": "平静"}, {"name": "太监", "gender": "男", "age": "中年", "clothes": "高领圆领汉服灰色太监服饰，主色调为灰色，搭配白色，有简单的线条花纹，头戴黑色小帽，腰系灰色布带圆形衣领，圆形衣领", "hairstyle": "光头", "figure": "瘦小", "identity": "侍奉大王的太监", "other": "声音尖细", "from_chapter": [0], "action": "站着说话", "expression": "严肃"}, {"name": "左右丞相和上卿", "gender": "男", "age": "中年", "clothes": "高领圆领汉服深蓝色官服，主色调为深蓝色，搭配浅蓝色，绣有山水纹样，头戴乌纱帽，腰系蓝色腰带，佩戴朝珠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "朝廷大臣", "other": "处理国事", "from_chapter": [0], "action": "纷纷上前汇报", "expression": "认真"}, {"name": "老臣（淳于越）", "gender": "男", "age": "老者", "clothes": "高领圆领汉服褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带圆形衣领，圆形衣领", "hairstyle": "白色束发", "figure": "清瘦", "identity": "朝廷大臣", "other": "迂腐守旧", "from_chapter": [0], "action": "口吐鲜血后晕倒", "expression": "惊恐"}, {"name": "邓山", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配红色，有虎纹图案，头戴黑色毡帽，腰系黑色皮腰带，佩刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "侍卫", "other": "听从公子华指令", "from_chapter": [0], "action": "快速跑出去，端来一桶水泼在老臣脸上", "expression": "紧张"}, {"name": "侍卫们", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配银色，有简单的几何花纹，头戴黑色头盔，腰系黑色腰带，佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "宫廷侍卫", "other": "执行命令", "from_chapter": [0], "action": "连忙抬人离开", "expression": "紧张"}, {"name": "秋香和秋碟", "gender": "女", "age": "少女", "clothes": "高领圆领汉服浅绿色丫鬟服饰，主色调为浅绿色，搭配白色，有蝴蝶纹样，头戴绿色发带，腰系绿色丝带圆形衣领，圆形衣领", "hairstyle": "黑色双马尾", "figure": "娇小", "identity": "公子华的随身侍女", "other": "机灵乖巧", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿中，神情淡然、拍着手的青年男子站在前面，几个紧张的青年男子连忙抬人离开。站着的青年男子挺拔，身着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发；抬人的青年男子壮实，穿着高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配银色，有简单的几何花纹，头戴黑色头盔，腰系黑色腰带，佩剑，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753198398_20250722_233319.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_0f6abda8-6d81-4ec0-9958-b024e51e6881_1.25x_20250722_193648.wav", "audio_duration": 12.005333, "video_url": "", "story_board_id": 14, "subtitle": [{"subtitle_id": 1, "text": "宫子华拍了拍手，", "timestamp": "00:00:00,160 --> 00:00:01,320", "duration": 1.16, "char_count": 8, "start_time_s": 0.16, "end_time_s": 1.32, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 400, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 560, "text": "拍"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 680, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 760, "text": "拍"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 920, "text": "手"}], "keyword": "宫子华"}, {"subtitle_id": 2, "text": "赶紧把他抬去太医院，", "timestamp": "00:00:01,320 --> 00:00:02,680", "duration": 1.36, "char_count": 10, "start_time_s": 1.32, "end_time_s": 2.68, "words": [{"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1320, "text": "赶"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1480, "text": "紧"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1640, "text": "把"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1760, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1880, "text": "抬"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 2000, "text": "去"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2120, "text": "太"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2280, "text": "医"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2400, "text": "院"}], "keyword": "太医院"}, {"subtitle_id": 3, "text": "不然他就死了。", "timestamp": "00:00:02,680 --> 00:00:04,020", "duration": 1.34, "char_count": 7, "start_time_s": 2.68, "end_time_s": 4.02, "words": [{"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2680, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2840, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 2960, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3120, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3240, "text": "死"}, {"attribute": {"event": "speech"}, "end_time": 4020, "start_time": 3480, "text": "了"}], "keyword": "他"}, {"subtitle_id": 4, "text": "侍卫们连忙抬人离开，", "timestamp": "00:00:04,120 --> 00:00:05,740", "duration": 1.62, "char_count": 10, "start_time_s": 4.12, "end_time_s": 5.74, "words": [{"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4120, "text": "侍"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4280, "text": "卫"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4400, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4520, "text": "连"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4640, "text": "忙"}, {"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4800, "text": "抬"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 5000, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5120, "text": "离"}, {"attribute": {"event": "speech"}, "end_time": 5740, "start_time": 5240, "text": "开"}], "keyword": "侍卫"}, {"subtitle_id": 5, "text": "宫子华还不忘补充一句，", "timestamp": "00:00:05,880 --> 00:00:07,640", "duration": 1.76, "char_count": 11, "start_time_s": 5.88, "end_time_s": 7.64, "words": [{"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5880, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6040, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6200, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6360, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6520, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6640, "text": "忘"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6800, "text": "补"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6960, "text": "充"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7080, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 7640, "start_time": 7240, "text": "句"}], "keyword": "补充"}, {"subtitle_id": 6, "text": "告诉他亚圣能吐两仗，", "timestamp": "00:00:07,640 --> 00:00:09,400", "duration": 1.76, "char_count": 10, "start_time_s": 7.64, "end_time_s": 9.4, "words": [{"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7640, "text": "告"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7800, "text": "诉"}, {"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7920, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8280, "text": "亚"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8520, "text": "圣"}, {"attribute": {"event": "speech"}, "end_time": 8760, "start_time": 8640, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 8880, "start_time": 8760, "text": "吐"}, {"attribute": {"event": "speech"}, "end_time": 9080, "start_time": 8920, "text": "两"}, {"attribute": {"event": "speech"}, "end_time": 9400, "start_time": 9120, "text": "仗"}], "keyword": "亚圣"}, {"subtitle_id": 7, "text": "他才吐这么点，", "timestamp": "00:00:09,400 --> 00:00:10,320", "duration": 0.92, "char_count": 7, "start_time_s": 9.4, "end_time_s": 10.32, "words": [{"attribute": {"event": "speech"}, "end_time": 9560, "start_time": 9400, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 9680, "start_time": 9560, "text": "才"}, {"attribute": {"event": "speech"}, "end_time": 9800, "start_time": 9680, "text": "吐"}, {"attribute": {"event": "speech"}, "end_time": 9960, "start_time": 9840, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 10080, "start_time": 9960, "text": "么"}, {"attribute": {"event": "speech"}, "end_time": 10320, "start_time": 10080, "text": "点"}], "keyword": "吐"}, {"subtitle_id": 8, "text": "给亚圣丢脸了。", "timestamp": "00:00:10,320 --> 00:00:11,700", "duration": 1.38, "char_count": 7, "start_time_s": 10.32, "end_time_s": 11.7, "words": [{"attribute": {"event": "speech"}, "end_time": 10520, "start_time": 10320, "text": "给"}, {"attribute": {"event": "speech"}, "end_time": 10720, "start_time": 10520, "text": "亚"}, {"attribute": {"event": "speech"}, "end_time": 10840, "start_time": 10720, "text": "圣"}, {"attribute": {"event": "speech"}, "end_time": 11040, "start_time": 10880, "text": "丢"}, {"attribute": {"event": "speech"}, "end_time": 11200, "start_time": 11040, "text": "脸"}, {"attribute": {"event": "speech"}, "end_time": 11700, "start_time": 11200, "text": "了"}], "keyword": "亚圣"}], "keywords": ["宫子华", "太医院", "他", "侍卫", "补充", "亚圣", "吐", "亚圣"]}, {"chapter": 4, "story_board": "”\n\n赢政看着这一切，松了一口气，对公子华说：“今天表现不错，有没有怨恨父王没帮你？”公子华摇头：“没有，君王怎么能亲自下场呢？淳于越不过是楚系的马前卒，父王对付他，简直是太看得起他了。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": [0], "action": "面对赢政摇头并说话", "expression": "平静"}, {"name": "阿房女（夏阿房）", "gender": "女", "age": "中年", "clothes": "高领圆领汉服深粉色宫装，主色调为深粉色，搭配淡黄色，有花卉纹样，头戴金簪发饰，颈戴珍珠项链圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "端庄", "identity": "公子华的母亲", "other": "温柔关怀", "from_chapter": [0], "action": "帮公子华整理衣领，检查他的打扮，然后让他出门", "expression": "着急"}, {"name": "赢政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服黑色龙袍，主色调为黑色，搭配金黄色，绣有五爪金龙纹，头戴冕旒，腰系黄色龙纹腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发头戴冕旒", "figure": "威严", "identity": "大秦大王", "other": "沉稳睿智", "from_chapter": [0], "action": "看着眼前场景，松了一口气，面向公子华说话", "expression": "欣慰"}, {"name": "太监", "gender": "男", "age": "中年", "clothes": "高领圆领汉服灰色太监服饰，主色调为灰色，搭配白色，有简单的线条花纹，头戴黑色小帽，腰系灰色布带圆形衣领，圆形衣领", "hairstyle": "光头", "figure": "瘦小", "identity": "侍奉大王的太监", "other": "声音尖细", "from_chapter": [0], "action": "站着说话", "expression": "严肃"}, {"name": "左右丞相和上卿", "gender": "男", "age": "中年", "clothes": "高领圆领汉服深蓝色官服，主色调为深蓝色，搭配浅蓝色，绣有山水纹样，头戴乌纱帽，腰系蓝色腰带，佩戴朝珠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "朝廷大臣", "other": "处理国事", "from_chapter": [0], "action": "纷纷上前汇报", "expression": "认真"}, {"name": "老臣（淳于越）", "gender": "男", "age": "老者", "clothes": "高领圆领汉服褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带圆形衣领，圆形衣领", "hairstyle": "白色束发", "figure": "清瘦", "identity": "朝廷大臣", "other": "迂腐守旧", "from_chapter": [0], "action": "口吐鲜血后晕倒", "expression": "惊恐"}, {"name": "邓山", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配红色，有虎纹图案，头戴黑色毡帽，腰系黑色皮腰带，佩刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "侍卫", "other": "听从公子华指令", "from_chapter": [0], "action": "快速跑出去，端来一桶水泼在老臣脸上", "expression": "紧张"}, {"name": "侍卫们", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配银色，有简单的几何花纹，头戴黑色头盔，腰系黑色腰带，佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "宫廷侍卫", "other": "执行命令", "from_chapter": [0], "action": "连忙抬人离开", "expression": "紧张"}, {"name": "秋香和秋碟", "gender": "女", "age": "少女", "clothes": "高领圆领汉服浅绿色丫鬟服饰，主色调为浅绿色，搭配白色，有蝴蝶纹样，头戴绿色发带，腰系绿色丝带圆形衣领，圆形衣领", "hairstyle": "黑色双马尾", "figure": "娇小", "identity": "公子华的随身侍女", "other": "机灵乖巧", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，中年男子站在皇宫大殿中，欣慰地松了一口气，看着眼前场景，威严、沉稳睿智；青年男子面对中年男子平静地摇头，挺拔且言辞犀利。中年男子穿着高领圆领汉服黑色龙袍，主色调为黑色，搭配金黄色，绣有五爪金龙纹，头戴冕旒，腰系黄色龙纹腰带，圆形衣领，黑色束发；青年男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753198815_20250722_234015.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_ba127a0b-d8a8-46e4-ae95-e334c7b525f4_1.25x_20250722_193702.wav", "audio_duration": 14.805333, "video_url": "", "story_board_id": 15, "subtitle": [{"subtitle_id": 1, "text": "嬴政看着这一切，", "timestamp": "00:00:00,200 --> 00:00:01,320", "duration": 1.12, "char_count": 8, "start_time_s": 0.2, "end_time_s": 1.32, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 440, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 640, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 720, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 840, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 960, "text": "切"}], "keyword": "嬴政"}, {"subtitle_id": 2, "text": "松了一口气，", "timestamp": "00:00:01,320 --> 00:00:02,160", "duration": 0.84, "char_count": 6, "start_time_s": 1.32, "end_time_s": 2.16, "words": [{"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1320, "text": "松"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1440, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1520, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1640, "text": "口"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 1800, "text": "气"}], "keyword": "松开"}, {"subtitle_id": 3, "text": "对公子华说今天表现不错，", "timestamp": "00:00:02,160 --> 00:00:04,120", "duration": 1.96, "char_count": 12, "start_time_s": 2.16, "end_time_s": 4.12, "words": [{"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2160, "text": "对"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2320, "text": "公"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2440, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2560, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2720, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3080, "text": "今"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3200, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3360, "text": "表"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3560, "text": "现"}, {"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3640, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 3800, "text": "错"}], "keyword": "公子华"}, {"subtitle_id": 4, "text": "有没有怨恨父王没帮你？", "timestamp": "00:00:04,120 --> 00:00:06,020", "duration": 1.9, "char_count": 11, "start_time_s": 4.12, "end_time_s": 6.02, "words": [{"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4120, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4320, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4400, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4520, "text": "怨"}, {"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4720, "text": "恨"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4880, "text": "父"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5040, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 5320, "start_time": 5160, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5320, "text": "帮"}, {"attribute": {"event": "speech"}, "end_time": 6020, "start_time": 5480, "text": "你"}], "keyword": "怨恨"}, {"subtitle_id": 5, "text": "公子华摇头没有，", "timestamp": "00:00:06,120 --> 00:00:07,720", "duration": 1.6, "char_count": 8, "start_time_s": 6.12, "end_time_s": 7.72, "words": [{"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6120, "text": "公"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6320, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6440, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6560, "text": "摇"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6720, "text": "头"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7120, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7320, "text": "有"}], "keyword": "摇头"}, {"subtitle_id": 6, "text": "君王怎么能亲自下场呢？", "timestamp": "00:00:07,720 --> 00:00:09,660", "duration": 1.94, "char_count": 11, "start_time_s": 7.72, "end_time_s": 9.66, "words": [{"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7720, "text": "君"}, {"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7920, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8080, "text": "怎"}, {"attribute": {"event": "speech"}, "end_time": 8280, "start_time": 8200, "text": "么"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8280, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8440, "text": "亲"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8640, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8800, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 9160, "start_time": 9000, "text": "场"}, {"attribute": {"event": "speech"}, "end_time": 9660, "start_time": 9160, "text": "呢"}], "keyword": "君王"}, {"subtitle_id": 7, "text": "淳于越不过是楚系的马前卒，", "timestamp": "00:00:09,760 --> 00:00:11,760", "duration": 2.0, "char_count": 13, "start_time_s": 9.76, "end_time_s": 11.76, "words": [{"attribute": {"event": "speech"}, "end_time": 9920, "start_time": 9760, "text": "淳"}, {"attribute": {"event": "speech"}, "end_time": 10040, "start_time": 9920, "text": "于"}, {"attribute": {"event": "speech"}, "end_time": 10160, "start_time": 10040, "text": "越"}, {"attribute": {"event": "speech"}, "end_time": 10280, "start_time": 10160, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 10400, "start_time": 10280, "text": "过"}, {"attribute": {"event": "speech"}, "end_time": 10560, "start_time": 10400, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 10720, "start_time": 10560, "text": "楚"}, {"attribute": {"event": "speech"}, "end_time": 10880, "start_time": 10760, "text": "系"}, {"attribute": {"event": "speech"}, "end_time": 11000, "start_time": 10880, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 11160, "start_time": 11000, "text": "马"}, {"attribute": {"event": "speech"}, "end_time": 11320, "start_time": 11160, "text": "前"}, {"attribute": {"event": "speech"}, "end_time": 11760, "start_time": 11360, "text": "卒"}], "keyword": "淳于越"}, {"subtitle_id": 8, "text": "父王对付他简直是太看得起他了。", "timestamp": "00:00:11,760 --> 00:00:14,500", "duration": 2.74, "char_count": 15, "start_time_s": 11.76, "end_time_s": 14.5, "words": [{"attribute": {"event": "speech"}, "end_time": 11920, "start_time": 11760, "text": "父"}, {"attribute": {"event": "speech"}, "end_time": 12040, "start_time": 11920, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 12160, "start_time": 12040, "text": "对"}, {"attribute": {"event": "speech"}, "end_time": 12240, "start_time": 12160, "text": "付"}, {"attribute": {"event": "speech"}, "end_time": 12440, "start_time": 12240, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 12840, "start_time": 12640, "text": "简"}, {"attribute": {"event": "speech"}, "end_time": 12960, "start_time": 12840, "text": "直"}, {"attribute": {"event": "speech"}, "end_time": 13120, "start_time": 12960, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 13400, "start_time": 13120, "text": "太"}, {"attribute": {"event": "speech"}, "end_time": 13640, "start_time": 13440, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 13720, "start_time": 13640, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 13880, "start_time": 13720, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 14040, "start_time": 13880, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 14500, "start_time": 14040, "text": "了"}], "keyword": "父王"}], "keywords": ["嬴政", "松开", "公子华", "怨恨", "摇头", "君王", "淳于越", "父王"]}, {"chapter": 4, "story_board": "”\n\n赢政点头：“你能明白就好，君王最忌讳亲自参与，只要稳坐钓鱼台，当好裁判就行。”公子华应了一声，陪赢政吃完早餐，便回凤仪殿收拾东西。夏阿房见他回来，关心地问：“今天早朝怎么样？", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": [0], "action": "回应赢政，陪吃早餐后回凤仪殿", "expression": "平静"}, {"name": "阿房女（夏阿房）", "gender": "女", "age": "中年", "clothes": "高领圆领汉服深粉色宫装，主色调为深粉色，搭配淡黄色，有花卉纹样，头戴金簪发饰，颈戴珍珠项链圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "端庄", "identity": "公子华的母亲", "other": "温柔关怀", "from_chapter": [0], "action": "看到公子华回来，上前询问", "expression": "关心"}, {"name": "赢政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服黑色龙袍，主色调为黑色，搭配金黄色，绣有五爪金龙纹，头戴冕旒，腰系黄色龙纹腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发头戴冕旒", "figure": "威严", "identity": "大秦大王", "other": "沉稳睿智", "from_chapter": [0], "action": "点头说话", "expression": "欣慰"}, {"name": "太监", "gender": "男", "age": "中年", "clothes": "高领圆领汉服灰色太监服饰，主色调为灰色，搭配白色，有简单的线条花纹，头戴黑色小帽，腰系灰色布带圆形衣领，圆形衣领", "hairstyle": "光头", "figure": "瘦小", "identity": "侍奉大王的太监", "other": "声音尖细", "from_chapter": [0], "action": "站着说话", "expression": "严肃"}, {"name": "左右丞相和上卿", "gender": "男", "age": "中年", "clothes": "高领圆领汉服深蓝色官服，主色调为深蓝色，搭配浅蓝色，绣有山水纹样，头戴乌纱帽，腰系蓝色腰带，佩戴朝珠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "朝廷大臣", "other": "处理国事", "from_chapter": [0], "action": "纷纷上前汇报", "expression": "认真"}, {"name": "老臣（淳于越）", "gender": "男", "age": "老者", "clothes": "高领圆领汉服褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带圆形衣领，圆形衣领", "hairstyle": "白色束发", "figure": "清瘦", "identity": "朝廷大臣", "other": "迂腐守旧", "from_chapter": [0], "action": "口吐鲜血后晕倒", "expression": "惊恐"}, {"name": "邓山", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配红色，有虎纹图案，头戴黑色毡帽，腰系黑色皮腰带，佩刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "侍卫", "other": "听从公子华指令", "from_chapter": [0], "action": "快速跑出去，端来一桶水泼在老臣脸上", "expression": "紧张"}, {"name": "侍卫们", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配银色，有简单的几何花纹，头戴黑色头盔，腰系黑色腰带，佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "宫廷侍卫", "other": "执行命令", "from_chapter": [0], "action": "连忙抬人离开", "expression": "紧张"}, {"name": "秋香和秋碟", "gender": "女", "age": "少女", "clothes": "高领圆领汉服浅绿色丫鬟服饰，主色调为浅绿色，搭配白色，有蝴蝶纹样，头戴绿色发带，腰系绿色丝带圆形衣领，圆形衣领", "hairstyle": "黑色双马尾", "figure": "娇小", "identity": "公子华的随身侍女", "other": "机灵乖巧", "from_chapter": [0], "action": "", "expression": ""}], "scene": "室内", "image_prompt": "中景，室内场景，一位点头、表情欣慰且威严沉稳睿智的中年男子，身着主色调为黑色、搭配金黄色、绣有五爪金龙纹的高领圆领汉服黑色龙袍，头戴冕旒，腰系黄色龙纹腰带，圆形衣领，黑色束发；旁边一位表情平静、挺拔且言辞犀利的青年男子，身着主色调为紫色、搭配白色、绣有云纹的高领圆领汉服紫色锦袍，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发；不远处一位表情关心、端庄温柔关怀的中年女子，看到青年男子回来，向前走去，身着主色调为深粉色、搭配淡黄色、有花卉纹样的高领圆领汉服深粉色宫装，头戴金簪发饰，颈戴珍珠项链，圆形衣领，黑色发髻, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753199216_20250722_234658.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_73c7966e-5697-427e-9ed8-df9e8dbec2a2_1.25x_20250722_193634.wav", "audio_duration": 14.824, "video_url": "", "story_board_id": 16, "subtitle": [{"subtitle_id": 1, "text": "嬴政点头你能明白就好，", "timestamp": "00:00:00,200 --> 00:00:02,120", "duration": 1.92, "char_count": 11, "start_time_s": 0.2, "end_time_s": 2.12, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 440, "text": "点"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 600, "text": "头"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1040, "text": "你"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1240, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1360, "text": "明"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1480, "text": "白"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1640, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1760, "text": "好"}], "keyword": "嬴政"}, {"subtitle_id": 2, "text": "君王最忌讳亲自参与，", "timestamp": "00:00:02,120 --> 00:00:03,800", "duration": 1.68, "char_count": 10, "start_time_s": 2.12, "end_time_s": 3.8, "words": [{"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2120, "text": "君"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2280, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2400, "text": "最"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2600, "text": "忌"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2720, "text": "讳"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 2960, "text": "亲"}, {"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3120, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3280, "text": "参"}, {"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3480, "text": "与"}], "keyword": "君王"}, {"subtitle_id": 3, "text": "只要稳坐钓鱼台，", "timestamp": "00:00:03,800 --> 00:00:05,040", "duration": 1.24, "char_count": 8, "start_time_s": 3.8, "end_time_s": 5.04, "words": [{"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3800, "text": "只"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3920, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4040, "text": "稳"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4240, "text": "坐"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4400, "text": "钓"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4520, "text": "鱼"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4640, "text": "台"}], "keyword": "钓鱼台"}, {"subtitle_id": 4, "text": "当好裁判就行。", "timestamp": "00:00:05,040 --> 00:00:06,460", "duration": 1.42, "char_count": 7, "start_time_s": 5.04, "end_time_s": 6.46, "words": [{"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5040, "text": "当"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5200, "text": "好"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5400, "text": "裁"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5640, "text": "判"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5800, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 6460, "start_time": 5960, "text": "行"}], "keyword": "裁判"}, {"subtitle_id": 5, "text": "龚子华应了一声。", "timestamp": "00:00:06,560 --> 00:00:07,760", "duration": 1.2, "char_count": 8, "start_time_s": 6.56, "end_time_s": 7.76, "words": [{"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6560, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6760, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6880, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 7080, "text": "应"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7200, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7320, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7440, "text": "声"}], "keyword": "龚子华"}, {"subtitle_id": 6, "text": "裴嬴政吃完早餐，", "timestamp": "00:00:07,760 --> 00:00:09,080", "duration": 1.32, "char_count": 8, "start_time_s": 7.76, "end_time_s": 9.08, "words": [{"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7760, "text": "裴"}, {"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7960, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 8280, "start_time": 8080, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8280, "text": "吃"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8400, "text": "完"}, {"attribute": {"event": "speech"}, "end_time": 8720, "start_time": 8560, "text": "早"}, {"attribute": {"event": "speech"}, "end_time": 9080, "start_time": 8720, "text": "餐"}], "keyword": "裴嬴政"}, {"subtitle_id": 7, "text": "便回凤仪殿收拾东西。", "timestamp": "00:00:09,080 --> 00:00:10,860", "duration": 1.78, "char_count": 10, "start_time_s": 9.08, "end_time_s": 10.86, "words": [{"attribute": {"event": "speech"}, "end_time": 9240, "start_time": 9080, "text": "便"}, {"attribute": {"event": "speech"}, "end_time": 9440, "start_time": 9280, "text": "回"}, {"attribute": {"event": "speech"}, "end_time": 9600, "start_time": 9440, "text": "凤"}, {"attribute": {"event": "speech"}, "end_time": 9760, "start_time": 9640, "text": "仪"}, {"attribute": {"event": "speech"}, "end_time": 9920, "start_time": 9800, "text": "殿"}, {"attribute": {"event": "speech"}, "end_time": 10120, "start_time": 9960, "text": "收"}, {"attribute": {"event": "speech"}, "end_time": 10240, "start_time": 10120, "text": "拾"}, {"attribute": {"event": "speech"}, "end_time": 10400, "start_time": 10240, "text": "东"}, {"attribute": {"event": "speech"}, "end_time": 10860, "start_time": 10400, "text": "西"}], "keyword": "凤仪殿"}, {"subtitle_id": 8, "text": "夏阿庞见他回来，", "timestamp": "00:00:11,000 --> 00:00:12,280", "duration": 1.28, "char_count": 8, "start_time_s": 11.0, "end_time_s": 12.28, "words": [{"attribute": {"event": "speech"}, "end_time": 11200, "start_time": 11000, "text": "夏"}, {"attribute": {"event": "speech"}, "end_time": 11320, "start_time": 11200, "text": "阿"}, {"attribute": {"event": "speech"}, "end_time": 11520, "start_time": 11360, "text": "庞"}, {"attribute": {"event": "speech"}, "end_time": 11720, "start_time": 11560, "text": "见"}, {"attribute": {"event": "speech"}, "end_time": 11840, "start_time": 11720, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 12000, "start_time": 11840, "text": "回"}, {"attribute": {"event": "speech"}, "end_time": 12280, "start_time": 12000, "text": "来"}], "keyword": "夏阿庞"}, {"subtitle_id": 9, "text": "关心的问今天早朝怎么样？", "timestamp": "00:00:12,280 --> 00:00:14,500", "duration": 2.22, "char_count": 12, "start_time_s": 12.28, "end_time_s": 14.5, "words": [{"attribute": {"event": "speech"}, "end_time": 12480, "start_time": 12280, "text": "关"}, {"attribute": {"event": "speech"}, "end_time": 12600, "start_time": 12480, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 12720, "start_time": 12600, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 12840, "start_time": 12720, "text": "问"}, {"attribute": {"event": "speech"}, "end_time": 13200, "start_time": 13040, "text": "今"}, {"attribute": {"event": "speech"}, "end_time": 13360, "start_time": 13200, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 13560, "start_time": 13360, "text": "早"}, {"attribute": {"event": "speech"}, "end_time": 13800, "start_time": 13600, "text": "朝"}, {"attribute": {"event": "speech"}, "end_time": 13960, "start_time": 13840, "text": "怎"}, {"attribute": {"event": "speech"}, "end_time": 14040, "start_time": 13960, "text": "么"}, {"attribute": {"event": "speech"}, "end_time": 14500, "start_time": 14040, "text": "样"}], "keyword": "早朝"}], "keywords": ["嬴政", "君王", "钓鱼台", "裁判", "龚子华", "裴嬴政", "凤仪殿", "夏阿庞", "早朝"]}, {"chapter": 4, "story_board": "”公子华叹了口气：“没什么收获，就是困。”夏阿房赶紧提醒：“千万别跟父王这么说，小心被罚。”公子华苦笑：“已经罚了一个月俸禄了。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": [0], "action": "叹气，苦笑", "expression": "疲惫、无奈"}, {"name": "阿房女（夏阿房）", "gender": "女", "age": "中年", "clothes": "高领圆领汉服深粉色宫装，主色调为深粉色，搭配淡黄色，有花卉纹样，头戴金簪发饰，颈戴珍珠项链圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "端庄", "identity": "公子华的母亲", "other": "温柔关怀", "from_chapter": [0], "action": "赶紧提醒", "expression": "担忧"}, {"name": "赢政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服黑色龙袍，主色调为黑色，搭配金黄色，绣有五爪金龙纹，头戴冕旒，腰系黄色龙纹腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发头戴冕旒", "figure": "威严", "identity": "大秦大王", "other": "沉稳睿智", "from_chapter": [0], "action": "点头说话", "expression": "欣慰"}, {"name": "太监", "gender": "男", "age": "中年", "clothes": "高领圆领汉服灰色太监服饰，主色调为灰色，搭配白色，有简单的线条花纹，头戴黑色小帽，腰系灰色布带圆形衣领，圆形衣领", "hairstyle": "光头", "figure": "瘦小", "identity": "侍奉大王的太监", "other": "声音尖细", "from_chapter": [0], "action": "站着说话", "expression": "严肃"}, {"name": "左右丞相和上卿", "gender": "男", "age": "中年", "clothes": "高领圆领汉服深蓝色官服，主色调为深蓝色，搭配浅蓝色，绣有山水纹样，头戴乌纱帽，腰系蓝色腰带，佩戴朝珠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "朝廷大臣", "other": "处理国事", "from_chapter": [0], "action": "纷纷上前汇报", "expression": "认真"}, {"name": "老臣（淳于越）", "gender": "男", "age": "老者", "clothes": "高领圆领汉服褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带圆形衣领，圆形衣领", "hairstyle": "白色束发", "figure": "清瘦", "identity": "朝廷大臣", "other": "迂腐守旧", "from_chapter": [0], "action": "口吐鲜血后晕倒", "expression": "惊恐"}, {"name": "邓山", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配红色，有虎纹图案，头戴黑色毡帽，腰系黑色皮腰带，佩刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "侍卫", "other": "听从公子华指令", "from_chapter": [0], "action": "快速跑出去，端来一桶水泼在老臣脸上", "expression": "紧张"}, {"name": "侍卫们", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配银色，有简单的几何花纹，头戴黑色头盔，腰系黑色腰带，佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "宫廷侍卫", "other": "执行命令", "from_chapter": [0], "action": "连忙抬人离开", "expression": "紧张"}, {"name": "秋香和秋碟", "gender": "女", "age": "少女", "clothes": "高领圆领汉服浅绿色丫鬟服饰，主色调为浅绿色，搭配白色，有蝴蝶纹样，头戴绿色发带，腰系绿色丝带圆形衣领，圆形衣领", "hairstyle": "黑色双马尾", "figure": "娇小", "identity": "公子华的随身侍女", "other": "机灵乖巧", "from_chapter": [0], "action": "", "expression": ""}], "scene": "室内", "image_prompt": "中景，室内，一个叹气苦笑、满脸疲惫无奈的青年男子，身穿高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发；旁边一个满脸担忧、赶紧做出提醒动作的中年女子，身穿高领圆领汉服深粉色宫装，主色调为深粉色，搭配淡黄色，有花卉纹样，头戴金簪发饰，颈戴珍珠项链，圆形衣领，黑色发髻, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753199626_20250722_235346.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_ef81aa46-e9c2-4612-9cd3-b4dbaefcf1c1_1.25x_20250722_193652.wav", "audio_duration": 10.389333, "video_url": "", "story_board_id": 17, "subtitle": [{"subtitle_id": 1, "text": "龚子华叹了口气，", "timestamp": "00:00:00,160 --> 00:00:01,360", "duration": 1.2, "char_count": 8, "start_time_s": 0.16, "end_time_s": 1.36, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 440, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 640, "text": "叹"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 760, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 840, "text": "口"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1000, "text": "气"}], "keyword": "龚子华"}, {"subtitle_id": 2, "text": "没什么收获，", "timestamp": "00:00:01,360 --> 00:00:02,360", "duration": 1.0, "char_count": 6, "start_time_s": 1.36, "end_time_s": 2.36, "words": [{"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1360, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1600, "text": "什"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1720, "text": "么"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1840, "text": "收"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2000, "text": "获"}], "keyword": "收获"}, {"subtitle_id": 3, "text": "就是困。夏颚旁赶紧提醒", "timestamp": "00:00:02,360 --> 00:00:04,800", "duration": 2.44, "char_count": 11, "start_time_s": 2.36, "end_time_s": 4.8, "words": [{"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2360, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2560, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2720, "text": "困"}, {"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3400, "text": "夏"}, {"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3600, "text": "颚"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3720, "text": "旁"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3880, "text": "赶"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4040, "text": "紧"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4200, "text": "提"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4400, "text": "醒"}], "keyword": "夏颚旁"}, {"subtitle_id": 4, "text": "千万别跟父王这么说，", "timestamp": "00:00:04,800 --> 00:00:06,120", "duration": 1.32, "char_count": 10, "start_time_s": 4.8, "end_time_s": 6.12, "words": [{"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4800, "text": "千"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5000, "text": "万"}, {"attribute": {"event": "speech"}, "end_time": 5320, "start_time": 5160, "text": "别"}, {"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5320, "text": "跟"}, {"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5440, "text": "父"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5560, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5680, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5800, "text": "么"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5880, "text": "说"}], "keyword": "父王"}, {"subtitle_id": 5, "text": "小心被罚。", "timestamp": "00:00:06,120 --> 00:00:07,100", "duration": 0.98, "char_count": 5, "start_time_s": 6.12, "end_time_s": 7.1, "words": [{"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6120, "text": "小"}, {"attribute": {"event": "speech"}, "end_time": 6480, "start_time": 6360, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6480, "text": "被"}, {"attribute": {"event": "speech"}, "end_time": 7100, "start_time": 6640, "text": "罚"}], "keyword": "罚"}, {"subtitle_id": 6, "text": "龚子华苦笑，", "timestamp": "00:00:07,240 --> 00:00:08,320", "duration": 1.08, "char_count": 6, "start_time_s": 7.24, "end_time_s": 8.32, "words": [{"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7240, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7440, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7600, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7720, "text": "苦"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 7960, "text": "笑"}], "keyword": "龚子华"}, {"subtitle_id": 7, "text": "已经罚了一个月俸禄了。", "timestamp": "00:00:08,320 --> 00:00:10,100", "duration": 1.78, "char_count": 11, "start_time_s": 8.32, "end_time_s": 10.1, "words": [{"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8320, "text": "已"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8520, "text": "经"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8640, "text": "罚"}, {"attribute": {"event": "speech"}, "end_time": 8880, "start_time": 8800, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8880, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 9160, "start_time": 9000, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 9280, "start_time": 9160, "text": "月"}, {"attribute": {"event": "speech"}, "end_time": 9480, "start_time": 9280, "text": "俸"}, {"attribute": {"event": "speech"}, "end_time": 9600, "start_time": 9480, "text": "禄"}, {"attribute": {"event": "speech"}, "end_time": 10100, "start_time": 9600, "text": "了"}], "keyword": "俸禄"}], "keywords": ["龚子华", "收获", "夏颚旁", "父王", "罚", "龚子华", "俸禄"]}, {"chapter": 4, "story_board": "”\n\n夏阿房惊讶：“怎么回事？”公子华简单交代：“淳于越弹劾我住东宫，我说了几句话，他受不了晕过去了。”夏阿房无奈：“没事，娘给你钱。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": [0], "action": "站着简单讲述事情经过", "expression": "平静"}, {"name": "阿房女（夏阿房）", "gender": "女", "age": "中年", "clothes": "高领圆领汉服深粉色宫装，主色调为深粉色，搭配淡黄色，有花卉纹样，头戴金簪发饰，颈戴珍珠项链圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "端庄", "identity": "公子华的母亲", "other": "温柔关怀", "from_chapter": [0], "action": "摆了摆手，说话安慰", "expression": "无奈"}, {"name": "赢政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服黑色龙袍，主色调为黑色，搭配金黄色，绣有五爪金龙纹，头戴冕旒，腰系黄色龙纹腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发头戴冕旒", "figure": "威严", "identity": "大秦大王", "other": "沉稳睿智", "from_chapter": [0], "action": "点头说话", "expression": "欣慰"}, {"name": "太监", "gender": "男", "age": "中年", "clothes": "高领圆领汉服灰色太监服饰，主色调为灰色，搭配白色，有简单的线条花纹，头戴黑色小帽，腰系灰色布带圆形衣领，圆形衣领", "hairstyle": "光头", "figure": "瘦小", "identity": "侍奉大王的太监", "other": "声音尖细", "from_chapter": [0], "action": "站着说话", "expression": "严肃"}, {"name": "左右丞相和上卿", "gender": "男", "age": "中年", "clothes": "高领圆领汉服深蓝色官服，主色调为深蓝色，搭配浅蓝色，绣有山水纹样，头戴乌纱帽，腰系蓝色腰带，佩戴朝珠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "朝廷大臣", "other": "处理国事", "from_chapter": [0], "action": "纷纷上前汇报", "expression": "认真"}, {"name": "老臣（淳于越）", "gender": "男", "age": "老者", "clothes": "高领圆领汉服褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带圆形衣领，圆形衣领", "hairstyle": "白色束发", "figure": "清瘦", "identity": "朝廷大臣", "other": "迂腐守旧", "from_chapter": [0], "action": "口吐鲜血后晕倒", "expression": "惊恐"}, {"name": "邓山", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配红色，有虎纹图案，头戴黑色毡帽，腰系黑色皮腰带，佩刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "侍卫", "other": "听从公子华指令", "from_chapter": [0], "action": "快速跑出去，端来一桶水泼在老臣脸上", "expression": "紧张"}, {"name": "侍卫们", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配银色，有简单的几何花纹，头戴黑色头盔，腰系黑色腰带，佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "宫廷侍卫", "other": "执行命令", "from_chapter": [0], "action": "连忙抬人离开", "expression": "紧张"}, {"name": "秋香和秋碟", "gender": "女", "age": "少女", "clothes": "高领圆领汉服浅绿色丫鬟服饰，主色调为浅绿色，搭配白色，有蝴蝶纹样，头戴绿色发带，腰系绿色丝带圆形衣领，圆形衣领", "hairstyle": "黑色双马尾", "figure": "娇小", "identity": "公子华的随身侍女", "other": "机灵乖巧", "from_chapter": [0], "action": "", "expression": ""}], "scene": "室内", "image_prompt": "中景，室内，身体微微前倾、脸上带着惊讶表情的中年女子，身着高领圆领、主色调为深粉色并搭配淡黄色、有花卉纹样的汉服深粉色宫装，头戴金簪发饰，颈戴珍珠项链，梳着黑色发髻；旁边站着平静讲述事情经过的青年男子，身着高领圆领、主色调为紫色并搭配白色、绣有云纹的汉服紫色锦袍，腰间束黑色腰带，佩戴玉佩，束着黑色头发；随后该中年女子摆了摆手，脸上露出无奈神情, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753200049_20250723_000050.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_47ef9a06-2fd9-4cab-8c4f-cdcb17c9cc10_1.25x_20250722_193630.wav", "audio_duration": 10.965333, "video_url": "", "story_board_id": 18, "subtitle": [{"subtitle_id": 1, "text": "夏阿旁惊讶怎么回事？", "timestamp": "00:00:00,240 --> 00:00:02,380", "duration": 2.14, "char_count": 10, "start_time_s": 0.24, "end_time_s": 2.38, "words": [{"attribute": {"event": "speech"}, "end_time": 400, "start_time": 240, "text": "夏"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 400, "text": "阿"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 600, "text": "旁"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 800, "text": "惊"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 920, "text": "讶"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1400, "text": "怎"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1560, "text": "么"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1720, "text": "回"}, {"attribute": {"event": "speech"}, "end_time": 2380, "start_time": 1880, "text": "事"}], "keyword": "夏阿旁"}, {"subtitle_id": 2, "text": "公子华简单交代淳于", "timestamp": "00:00:02,480 --> 00:00:04,080", "duration": 1.6, "char_count": 9, "start_time_s": 2.48, "end_time_s": 4.08, "words": [{"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2480, "text": "公"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2640, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2800, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 2960, "text": "简"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3120, "text": "单"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3240, "text": "交"}, {"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3400, "text": "代"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3840, "text": "淳"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3960, "text": "于"}], "keyword": "公子华"}, {"subtitle_id": 3, "text": "月潭和我住东宫。", "timestamp": "00:00:04,080 --> 00:00:05,280", "duration": 1.2, "char_count": 8, "start_time_s": 4.08, "end_time_s": 5.28, "words": [{"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4080, "text": "月"}, {"attribute": {"event": "speech"}, "end_time": 4440, "start_time": 4240, "text": "潭"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4440, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4640, "text": "我"}, {"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4760, "text": "住"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4880, "text": "东"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5040, "text": "宫"}], "keyword": "月潭"}, {"subtitle_id": 4, "text": "我说了几句话，", "timestamp": "00:00:05,280 --> 00:00:06,320", "duration": 1.04, "char_count": 7, "start_time_s": 5.28, "end_time_s": 6.32, "words": [{"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5280, "text": "我"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5520, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5600, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5720, "text": "几"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5840, "text": "句"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 5960, "text": "话"}], "keyword": "几句话"}, {"subtitle_id": 5, "text": "他受不了，", "timestamp": "00:00:06,320 --> 00:00:06,960", "duration": 0.64, "char_count": 5, "start_time_s": 6.32, "end_time_s": 6.96, "words": [{"attribute": {"event": "speech"}, "end_time": 6480, "start_time": 6320, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6520, "text": "受"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6680, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6800, "text": "了"}], "keyword": "受不了"}, {"subtitle_id": 6, "text": "晕过去了。", "timestamp": "00:00:06,960 --> 00:00:07,860", "duration": 0.9, "char_count": 5, "start_time_s": 6.96, "end_time_s": 7.86, "words": [{"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6960, "text": "晕"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7120, "text": "过"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7240, "text": "去"}, {"attribute": {"event": "speech"}, "end_time": 7860, "start_time": 7400, "text": "了"}], "keyword": "晕过去"}, {"subtitle_id": 7, "text": "夏阿旁无奈没事，", "timestamp": "00:00:08,000 --> 00:00:09,600", "duration": 1.6, "char_count": 8, "start_time_s": 8.0, "end_time_s": 9.6, "words": [{"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8000, "text": "夏"}, {"attribute": {"event": "speech"}, "end_time": 8280, "start_time": 8200, "text": "阿"}, {"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8320, "text": "旁"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8480, "text": "无"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8640, "text": "奈"}, {"attribute": {"event": "speech"}, "end_time": 9240, "start_time": 8960, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 9600, "start_time": 9240, "text": "事"}], "keyword": "夏阿旁"}, {"subtitle_id": 8, "text": "娘给你钱。", "timestamp": "00:00:09,600 --> 00:00:10,620", "duration": 1.02, "char_count": 5, "start_time_s": 9.6, "end_time_s": 10.62, "words": [{"attribute": {"event": "speech"}, "end_time": 9800, "start_time": 9600, "text": "娘"}, {"attribute": {"event": "speech"}, "end_time": 9960, "start_time": 9840, "text": "给"}, {"attribute": {"event": "speech"}, "end_time": 10120, "start_time": 9960, "text": "你"}, {"attribute": {"event": "speech"}, "end_time": 10620, "start_time": 10120, "text": "钱"}], "keyword": "娘"}], "keywords": ["夏阿旁", "公子华", "月潭", "几句话", "受不了", "晕过去", "夏阿旁", "娘"]}, {"chapter": 4, "story_board": "”公子华感激地说：“谢谢母后。”\n\n最后，夏阿房把公子华送到东宫，唠叨了半天，还送了随身侍女秋香和秋碟。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": [0], "action": "站着，眼睛看着对方，微微鞠躬", "expression": "感激"}, {"name": "阿房女（夏阿房）", "gender": "女", "age": "中年", "clothes": "高领圆领汉服深粉色宫装，主色调为深粉色，搭配淡黄色，有花卉纹样，头戴金簪发饰，颈戴珍珠项链圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "端庄", "identity": "公子华的母亲", "other": "温柔关怀", "from_chapter": [0], "action": "拉着公子华的手，一边说着话一边送他到东宫", "expression": "关切"}, {"name": "赢政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服黑色龙袍，主色调为黑色，搭配金黄色，绣有五爪金龙纹，头戴冕旒，腰系黄色龙纹腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发头戴冕旒", "figure": "威严", "identity": "大秦大王", "other": "沉稳睿智", "from_chapter": [0], "action": "点头说话", "expression": "欣慰"}, {"name": "太监", "gender": "男", "age": "中年", "clothes": "高领圆领汉服灰色太监服饰，主色调为灰色，搭配白色，有简单的线条花纹，头戴黑色小帽，腰系灰色布带圆形衣领，圆形衣领", "hairstyle": "光头", "figure": "瘦小", "identity": "侍奉大王的太监", "other": "声音尖细", "from_chapter": [0], "action": "站着说话", "expression": "严肃"}, {"name": "左右丞相和上卿", "gender": "男", "age": "中年", "clothes": "高领圆领汉服深蓝色官服，主色调为深蓝色，搭配浅蓝色，绣有山水纹样，头戴乌纱帽，腰系蓝色腰带，佩戴朝珠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "朝廷大臣", "other": "处理国事", "from_chapter": [0], "action": "纷纷上前汇报", "expression": "认真"}, {"name": "老臣（淳于越）", "gender": "男", "age": "老者", "clothes": "高领圆领汉服褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带圆形衣领，圆形衣领", "hairstyle": "白色束发", "figure": "清瘦", "identity": "朝廷大臣", "other": "迂腐守旧", "from_chapter": [0], "action": "口吐鲜血后晕倒", "expression": "惊恐"}, {"name": "邓山", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配红色，有虎纹图案，头戴黑色毡帽，腰系黑色皮腰带，佩刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "侍卫", "other": "听从公子华指令", "from_chapter": [0], "action": "快速跑出去，端来一桶水泼在老臣脸上", "expression": "紧张"}, {"name": "侍卫们", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配银色，有简单的几何花纹，头戴黑色头盔，腰系黑色腰带，佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "宫廷侍卫", "other": "执行命令", "from_chapter": [0], "action": "连忙抬人离开", "expression": "紧张"}, {"name": "秋香和秋碟", "gender": "女", "age": "少女", "clothes": "高领圆领汉服浅绿色丫鬟服饰，主色调为浅绿色，搭配白色，有蝴蝶纹样，头戴绿色发带，腰系绿色丝带圆形衣领，圆形衣领", "hairstyle": "黑色双马尾", "figure": "娇小", "identity": "公子华的随身侍女", "other": "机灵乖巧", "from_chapter": [0], "action": "", "expression": ""}], "scene": "室内", "image_prompt": "中景，室内，一位拉着对方手、满脸关切、端庄温柔的中年女子正送对方到东宫，旁边一位微微鞠躬、眼神感激、挺拔且眼睛看着对方的青年男子站着。中年女子穿着高领圆领汉服深粉色宫装，主色调为深粉色，搭配淡黄色，有花卉纹样，头戴金簪发饰，颈戴珍珠项链，梳着黑色发髻，圆形衣领；青年男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，黑色束发，圆形衣领, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753200520_20250723_000841.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_75905cf5-b9b1-4b45-ae10-fb6f08eee14e_1.25x_20250722_193639.wav", "audio_duration": 8.512, "video_url": "", "story_board_id": 19, "subtitle": [{"subtitle_id": 1, "text": "龚子华感激地说谢谢母后。", "timestamp": "00:00:00,160 --> 00:00:02,420", "duration": 2.26, "char_count": 12, "start_time_s": 0.16, "end_time_s": 2.42, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 160, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 440, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 600, "text": "感"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 800, "text": "激"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 920, "text": "地"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1000, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1440, "text": "谢"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1760, "text": "谢"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1800, "text": "母"}, {"attribute": {"event": "speech"}, "end_time": 2420, "start_time": 1960, "text": "后"}], "keyword": "龚子华"}, {"subtitle_id": 2, "text": "最后，夏颚旁把龚子华送到东宫，", "timestamp": "00:00:02,560 --> 00:00:05,000", "duration": 2.44, "char_count": 15, "start_time_s": 2.56, "end_time_s": 5.0, "words": [{"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2560, "text": "最"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2720, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3200, "text": "夏"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3360, "text": "颚"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3520, "text": "旁"}, {"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3640, "text": "把"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3800, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3920, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4080, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4240, "text": "送"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4360, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4520, "text": "东"}, {"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4640, "text": "宫"}], "keyword": "东宫"}, {"subtitle_id": 3, "text": "劳叨了半天，", "timestamp": "00:00:05,000 --> 00:00:05,880", "duration": 0.88, "char_count": 6, "start_time_s": 5.0, "end_time_s": 5.88, "words": [{"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5000, "text": "劳"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5160, "text": "叨"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5280, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5360, "text": "半"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5520, "text": "天"}], "keyword": "劳叨"}, {"subtitle_id": 4, "text": "还送了随身侍女秋香和秋蝶。", "timestamp": "00:00:05,880 --> 00:00:08,100", "duration": 2.22, "char_count": 13, "start_time_s": 5.88, "end_time_s": 8.1, "words": [{"attribute": {"event": "speech"}, "end_time": 6080, "start_time": 5880, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 6080, "text": "送"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6240, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6320, "text": "随"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6520, "text": "身"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6680, "text": "侍"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6840, "text": "女"}, {"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 7000, "text": "秋"}, {"attribute": {"event": "speech"}, "end_time": 7360, "start_time": 7200, "text": "香"}, {"attribute": {"event": "speech"}, "end_time": 7520, "start_time": 7360, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7520, "text": "秋"}, {"attribute": {"event": "speech"}, "end_time": 8100, "start_time": 7680, "text": "蝶"}], "keyword": "秋香秋蝶"}], "keywords": ["龚子华", "东宫", "劳叨", "秋香秋蝶"]}, {"chapter": 4, "story_board": "临走前叮嘱：“有事找娘。”公子华点点头，目送母亲离开，心中暗暗下定决心：从今以后，他不仅要守住自己的位置，还要让那些想打压他的人付出代价。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": [0], "action": "站在原地，点头回应，目光看着阿房女离开的方向", "expression": "坚定"}, {"name": "阿房女（夏阿房）", "gender": "女", "age": "中年", "clothes": "高领圆领汉服深粉色宫装，主色调为深粉色，搭配淡黄色，有花卉纹样，头戴金簪发饰，颈戴珍珠项链圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "端庄", "identity": "公子华的母亲", "other": "温柔关怀", "from_chapter": [0], "action": "站在公子华面前，拉着公子华的手，眼神关切", "expression": "担忧"}, {"name": "赢政", "gender": "男", "age": "中年", "clothes": "高领圆领汉服黑色龙袍，主色调为黑色，搭配金黄色，绣有五爪金龙纹，头戴冕旒，腰系黄色龙纹腰带圆形衣领，圆形衣领", "hairstyle": "黑色束发头戴冕旒", "figure": "威严", "identity": "大秦大王", "other": "沉稳睿智", "from_chapter": [0], "action": "点头说话", "expression": "欣慰"}, {"name": "太监", "gender": "男", "age": "中年", "clothes": "高领圆领汉服灰色太监服饰，主色调为灰色，搭配白色，有简单的线条花纹，头戴黑色小帽，腰系灰色布带圆形衣领，圆形衣领", "hairstyle": "光头", "figure": "瘦小", "identity": "侍奉大王的太监", "other": "声音尖细", "from_chapter": [0], "action": "站着说话", "expression": "严肃"}, {"name": "左右丞相和上卿", "gender": "男", "age": "中年", "clothes": "高领圆领汉服深蓝色官服，主色调为深蓝色，搭配浅蓝色，绣有山水纹样，头戴乌纱帽，腰系蓝色腰带，佩戴朝珠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "朝廷大臣", "other": "处理国事", "from_chapter": [0], "action": "纷纷上前汇报", "expression": "认真"}, {"name": "老臣（淳于越）", "gender": "男", "age": "老者", "clothes": "高领圆领汉服褐色官服，主色调为褐色，搭配浅褐色，有古朴的回纹，头戴黑色方巾，腰系褐色腰带圆形衣领，圆形衣领", "hairstyle": "白色束发", "figure": "清瘦", "identity": "朝廷大臣", "other": "迂腐守旧", "from_chapter": [0], "action": "口吐鲜血后晕倒", "expression": "惊恐"}, {"name": "邓山", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配红色，有虎纹图案，头戴黑色毡帽，腰系黑色皮腰带，佩刀圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "侍卫", "other": "听从公子华指令", "from_chapter": [0], "action": "快速跑出去，端来一桶水泼在老臣脸上", "expression": "紧张"}, {"name": "侍卫们", "gender": "男", "age": "青年", "clothes": "高领圆领汉服黑色侍卫服饰，主色调为黑色，搭配银色，有简单的几何花纹，头戴黑色头盔，腰系黑色腰带，佩剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "宫廷侍卫", "other": "执行命令", "from_chapter": [0], "action": "连忙抬人离开", "expression": "紧张"}, {"name": "秋香和秋碟", "gender": "女", "age": "少女", "clothes": "高领圆领汉服浅绿色丫鬟服饰，主色调为浅绿色，搭配白色，有蝴蝶纹样，头戴绿色发带，腰系绿色丝带圆形衣领，圆形衣领", "hairstyle": "黑色双马尾", "figure": "娇小", "identity": "公子华的随身侍女", "other": "机灵乖巧", "from_chapter": [0], "action": "", "expression": ""}], "scene": "室内", "image_prompt": "中景，室内，一位端庄温柔、眼神关切担忧的中年女子拉着一位青年男子的手，而青年男子挺拔地站在原地点头回应，目光看向女子离开的方向，神情坚定。中年女子穿着高领圆领汉服深粉色宫装，主色调为深粉色，搭配淡黄色，有花卉纹样，头戴金簪发饰，颈戴珍珠项链，梳着黑色发髻，身着圆形衣领的服饰；青年男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，束着黑色头发，身着圆形衣领的服饰, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753200887_20250723_001447.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_4e3c0c3b-f051-4415-9038-333b5c292aa9_1.25x_20250722_193834.wav", "audio_duration": 11.541333, "video_url": "", "story_board_id": 20, "subtitle": [{"subtitle_id": 1, "text": "临走前叮嘱有事找娘。", "timestamp": "00:00:00,200 --> 00:00:02,300", "duration": 2.1, "char_count": 10, "start_time_s": 0.2, "end_time_s": 2.3, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 200, "text": "临"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 320, "text": "走"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 520, "text": "前"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 720, "text": "叮"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 880, "text": "嘱"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1200, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1480, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1640, "text": "找"}, {"attribute": {"event": "speech"}, "end_time": 2300, "start_time": 1840, "text": "娘"}], "keyword": "娘"}, {"subtitle_id": 2, "text": "龚子华点点头，", "timestamp": "00:00:02,480 --> 00:00:03,600", "duration": 1.12, "char_count": 7, "start_time_s": 2.48, "end_time_s": 3.6, "words": [{"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2480, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2640, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2760, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2920, "text": "点"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 2960, "text": "点"}, {"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3200, "text": "头"}], "keyword": "龚子华"}, {"subtitle_id": 3, "text": "目送母亲离开，", "timestamp": "00:00:03,600 --> 00:00:04,680", "duration": 1.08, "char_count": 7, "start_time_s": 3.6, "end_time_s": 4.68, "words": [{"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3600, "text": "目"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3760, "text": "送"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3880, "text": "母"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4040, "text": "亲"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4160, "text": "离"}, {"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4320, "text": "开"}], "keyword": "母亲"}, {"subtitle_id": 4, "text": "心中暗暗下定决心，", "timestamp": "00:00:04,680 --> 00:00:06,200", "duration": 1.52, "char_count": 9, "start_time_s": 4.68, "end_time_s": 6.2, "words": [{"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4680, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4840, "text": "中"}, {"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4960, "text": "暗"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5000, "text": "暗"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5280, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5480, "text": "定"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5640, "text": "决"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 5800, "text": "心"}], "keyword": "决心"}, {"subtitle_id": 5, "text": "从今以后，", "timestamp": "00:00:06,200 --> 00:00:06,880", "duration": 0.68, "char_count": 5, "start_time_s": 6.2, "end_time_s": 6.88, "words": [{"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6200, "text": "从"}, {"attribute": {"event": "speech"}, "end_time": 6480, "start_time": 6360, "text": "今"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6480, "text": "以"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6600, "text": "后"}], "keyword": "以后"}, {"subtitle_id": 6, "text": "他不仅要守住自己的位置，", "timestamp": "00:00:06,880 --> 00:00:08,840", "duration": 1.96, "char_count": 12, "start_time_s": 6.88, "end_time_s": 8.84, "words": [{"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6880, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 7040, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7200, "text": "仅"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7320, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7600, "text": "守"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7800, "text": "住"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 7960, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 8240, "start_time": 8120, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8240, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8360, "text": "位"}, {"attribute": {"event": "speech"}, "end_time": 8840, "start_time": 8520, "text": "置"}], "keyword": "位置"}, {"subtitle_id": 7, "text": "还要让那些想打压他的人付出代价。", "timestamp": "00:00:08,840 --> 00:00:11,460", "duration": 2.62, "char_count": 16, "start_time_s": 8.84, "end_time_s": 11.46, "words": [{"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8840, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 9120, "start_time": 9000, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 9240, "start_time": 9120, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 9400, "start_time": 9240, "text": "那"}, {"attribute": {"event": "speech"}, "end_time": 9560, "start_time": 9400, "text": "些"}, {"attribute": {"event": "speech"}, "end_time": 9720, "start_time": 9560, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 9880, "start_time": 9720, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 10040, "start_time": 9880, "text": "压"}, {"attribute": {"event": "speech"}, "end_time": 10200, "start_time": 10040, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 10280, "start_time": 10200, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 10440, "start_time": 10280, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 10680, "start_time": 10480, "text": "付"}, {"attribute": {"event": "speech"}, "end_time": 10800, "start_time": 10680, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 11000, "start_time": 10800, "text": "代"}, {"attribute": {"event": "speech"}, "end_time": 11460, "start_time": 11000, "text": "价"}], "keyword": "打压"}], "keywords": ["娘", "龚子华", "母亲", "决心", "以后", "位置", "打压"]}]