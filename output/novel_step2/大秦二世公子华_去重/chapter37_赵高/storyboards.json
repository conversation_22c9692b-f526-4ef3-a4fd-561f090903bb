[{"chapter": 37, "story_board": "深夜，公子华正躺在床上翻来覆去睡不着，突然“咚咚咚”的声音从地底传来，像是有人在敲墙。他以为是自己做梦，可那声音太真实了，连床板都在震动。他猛地坐起来，贴着墙壁仔细听，确认不是幻觉。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "先是在床上翻来覆去，然后猛地坐起来，贴着墙壁仔细听", "expression": "疑惑、警惕"}, {"name": "护卫队长", "gender": "男", "age": "中年", "clothes": "高领秦代战甲，主色调为黑色，搭配红色和金色，甲片上有虎纹浮雕，头戴黑色兜鍪，腰系黑色革带，配有长剑圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "魁梧", "identity": "东宫护卫队长", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "郎中令尹成", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服长袍，主色调为褐色，搭配浅黄色和深棕色，衣服上有几何纹样，头戴黑色进贤冠，腰束褐色腰带，挂有印章圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "适中", "identity": "郎中令", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "顿弱", "gender": "男", "age": "中年", "clothes": "高领圆领浅蓝色秦国大臣朝服，主色调为浅蓝色，搭配淡蓝色和白色，袍身有波浪纹图案，腰间束蓝色丝带，佩戴朝珠，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "偏胖", "identity": "秦国大臣", "other": "能言善辩", "from_chapter": 13, "action": "", "expression": ""}, {"name": "赵高", "gender": "男", "age": "中年", "clothes": "高领圆领秦代囚服，主色调为白色，搭配灰色，衣服上有囚字标记，头发凌乱，无配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发凌乱", "figure": "瘦削", "identity": "阴谋刺杀者", "other": "阴险狡诈", "from_chapter": [0], "action": "", "expression": ""}, {"name": "秦王政（赢政）", "gender": "男", "age": "中年", "clothes": "高领圆领秦代龙袍，主色调为黄色，搭配金色和红色，袍身绣有五爪金龙图案，头戴冕旒，腰束黄色玉带，佩有玉玺圆形衣领，圆形衣领", "hairstyle": "黑色长发束起头戴冕旒", "figure": "高大", "identity": "秦国国王", "other": "有威严", "from_chapter": [0], "action": "", "expression": ""}, {"name": "相里腾", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服，主色调为褐色，搭配深灰色，有几何纹图案，腰系黑色革带，挂有铜牌圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "为公子华送工匠的人", "other": "办事利落", "from_chapter": 16, "action": "", "expression": ""}, {"name": "崔永", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服短衣，主色调为灰色，搭配白色，无明显花纹，系黑色腰带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精瘦", "identity": "公子华的下属", "other": "做事利落", "from_chapter": 7, "action": "", "expression": ""}, {"name": "医家张琰", "gender": "男", "age": "老年", "clothes": "高领圆领秦代医服长袍，主色调为青色，搭配淡蓝色和白色，衣身有草药图案，头戴青色方巾，腰系青色腰带，配有药囊圆形衣领，圆形衣领", "hairstyle": "白色长发束起", "figure": "清瘦", "identity": "医家", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "淳于越", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为深绿色，搭配白色和黑色，有方格花纹，腰间束布腰带，头戴黑色方巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "秦国大臣", "other": "", "from_chapter": 19, "action": "", "expression": ""}], "scene": "室内", "image_prompt": "中景，室内，一个先是在床上翻来覆去然后猛地坐起来并贴着墙壁仔细听的青年男子，脸上带着疑惑、警惕的表情，他穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，有着圆形衣领，黑色束发，身姿挺拔, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://p9-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/20250722222805CA4A4BD61B56907594B6-5212-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1753280894&x-signature=2dhExub0GFxRzM4EK3twY%2FFzPY0%3D", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_fb35a766-776f-4f9b-a751-7e89f8ee45d6_1.25x_20250722_195425.wav", "audio_duration": 14.656, "video_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/videos/video_1753194687_20250722_223128.mp4", "story_board_id": 1, "subtitle": [{"subtitle_id": 1, "text": "深夜，", "timestamp": "00:00:00,240 --> 00:00:00,760", "duration": 0.52, "char_count": 3, "start_time_s": 0.24, "end_time_s": 0.76, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 240, "text": "深"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 320, "text": "夜"}], "keyword": "深夜"}, {"subtitle_id": 2, "text": "龚子华正躺在床上翻来覆去睡不着。", "timestamp": "00:00:00,760 --> 00:00:03,080", "duration": 2.32, "char_count": 16, "start_time_s": 0.76, "end_time_s": 3.08, "words": [{"attribute": {"event": "speech"}, "end_time": 920, "start_time": 760, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 920, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1040, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1240, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1400, "text": "躺"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1560, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1680, "text": "床"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1840, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 2000, "text": "翻"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2120, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2280, "text": "覆"}, {"attribute": {"event": "speech"}, "end_time": 2520, "start_time": 2400, "text": "去"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2520, "text": "睡"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2640, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2760, "text": "着"}], "keyword": "龚子华"}, {"subtitle_id": 3, "text": "突然，咚咚咚的声音从地底传来，", "timestamp": "00:00:03,080 --> 00:00:05,120", "duration": 2.04, "char_count": 15, "start_time_s": 3.08, "end_time_s": 5.12, "words": [{"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3080, "text": "突"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3240, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3400, "text": "咚"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3440, "text": "咚"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3480, "text": "咚"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3760, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3920, "text": "声"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4000, "text": "音"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4160, "text": "从"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4320, "text": "地"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4520, "text": "底"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4640, "text": "传"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 4800, "text": "来"}], "keyword": "咚咚咚"}, {"subtitle_id": 4, "text": "像是有人在敲墙。", "timestamp": "00:00:05,120 --> 00:00:06,500", "duration": 1.38, "char_count": 8, "start_time_s": 5.12, "end_time_s": 6.5, "words": [{"attribute": {"event": "speech"}, "end_time": 5320, "start_time": 5120, "text": "像"}, {"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5320, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5440, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5560, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5720, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5840, "text": "敲"}, {"attribute": {"event": "speech"}, "end_time": 6500, "start_time": 6040, "text": "墙"}], "keyword": "敲墙"}, {"subtitle_id": 5, "text": "他以为是自己做梦，", "timestamp": "00:00:06,680 --> 00:00:07,960", "duration": 1.28, "char_count": 9, "start_time_s": 6.68, "end_time_s": 7.96, "words": [{"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6680, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6840, "text": "以"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6960, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 7080, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 7360, "start_time": 7200, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7360, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 7640, "start_time": 7480, "text": "做"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7640, "text": "梦"}], "keyword": "做梦"}, {"subtitle_id": 6, "text": "可那声音太真实了，", "timestamp": "00:00:07,960 --> 00:00:09,240", "duration": 1.28, "char_count": 9, "start_time_s": 7.96, "end_time_s": 9.24, "words": [{"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7960, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8080, "text": "那"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8200, "text": "声"}, {"attribute": {"event": "speech"}, "end_time": 8440, "start_time": 8320, "text": "音"}, {"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8440, "text": "太"}, {"attribute": {"event": "speech"}, "end_time": 8880, "start_time": 8720, "text": "真"}, {"attribute": {"event": "speech"}, "end_time": 9040, "start_time": 8880, "text": "实"}, {"attribute": {"event": "speech"}, "end_time": 9240, "start_time": 9040, "text": "了"}], "keyword": "声音"}, {"subtitle_id": 7, "text": "连床板都在震动。", "timestamp": "00:00:09,240 --> 00:00:10,860", "duration": 1.62, "char_count": 8, "start_time_s": 9.24, "end_time_s": 10.86, "words": [{"attribute": {"event": "speech"}, "end_time": 9440, "start_time": 9240, "text": "连"}, {"attribute": {"event": "speech"}, "end_time": 9640, "start_time": 9480, "text": "床"}, {"attribute": {"event": "speech"}, "end_time": 9920, "start_time": 9720, "text": "板"}, {"attribute": {"event": "speech"}, "end_time": 10080, "start_time": 9920, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 10240, "start_time": 10080, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 10400, "start_time": 10240, "text": "震"}, {"attribute": {"event": "speech"}, "end_time": 10860, "start_time": 10400, "text": "动"}], "keyword": "床板"}, {"subtitle_id": 8, "text": "他猛地坐起来，", "timestamp": "00:00:10,960 --> 00:00:11,880", "duration": 0.92, "char_count": 7, "start_time_s": 10.96, "end_time_s": 11.88, "words": [{"attribute": {"event": "speech"}, "end_time": 11160, "start_time": 10960, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 11280, "start_time": 11160, "text": "猛"}, {"attribute": {"event": "speech"}, "end_time": 11400, "start_time": 11280, "text": "地"}, {"attribute": {"event": "speech"}, "end_time": 11560, "start_time": 11400, "text": "坐"}, {"attribute": {"event": "speech"}, "end_time": 11640, "start_time": 11560, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 11880, "start_time": 11640, "text": "来"}], "keyword": "坐起"}, {"subtitle_id": 9, "text": "贴着墙壁仔细听，", "timestamp": "00:00:11,880 --> 00:00:13,280", "duration": 1.4, "char_count": 8, "start_time_s": 11.88, "end_time_s": 13.28, "words": [{"attribute": {"event": "speech"}, "end_time": 12080, "start_time": 11880, "text": "贴"}, {"attribute": {"event": "speech"}, "end_time": 12200, "start_time": 12080, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 12360, "start_time": 12200, "text": "墙"}, {"attribute": {"event": "speech"}, "end_time": 12520, "start_time": 12400, "text": "壁"}, {"attribute": {"event": "speech"}, "end_time": 12720, "start_time": 12560, "text": "仔"}, {"attribute": {"event": "speech"}, "end_time": 12840, "start_time": 12720, "text": "细"}, {"attribute": {"event": "speech"}, "end_time": 13280, "start_time": 12840, "text": "听"}], "keyword": "墙壁"}, {"subtitle_id": 10, "text": "确认不是幻觉。", "timestamp": "00:00:13,280 --> 00:00:14,580", "duration": 1.3, "char_count": 7, "start_time_s": 13.28, "end_time_s": 14.58, "words": [{"attribute": {"event": "speech"}, "end_time": 13480, "start_time": 13280, "text": "确"}, {"attribute": {"event": "speech"}, "end_time": 13600, "start_time": 13480, "text": "认"}, {"attribute": {"event": "speech"}, "end_time": 13800, "start_time": 13600, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 13920, "start_time": 13800, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 14080, "start_time": 13920, "text": "幻"}, {"attribute": {"event": "speech"}, "end_time": 14580, "start_time": 14080, "text": "觉"}], "keyword": "幻觉"}], "keywords": ["深夜", "龚子华", "咚咚咚", "敲墙", "做梦", "声音", "床板", "坐起", "墙壁", "幻觉"]}, {"chapter": 37, "story_board": "这声音是从寝殿的书桌底下传来的。他立刻叫来守夜的护卫，让他们悄悄进屋蹲守，准备抓个现行。公子华自己则回到床上，盯着天花板发愣：到底是谁？", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "先是听声音，然后叫来守夜护卫并安排他们蹲守，之后回到床上盯着天花板", "expression": "疑惑、警惕"}, {"name": "护卫队长", "gender": "男", "age": "中年", "clothes": "高领秦代战甲，主色调为黑色，搭配红色和金色，甲片上有虎纹浮雕，头戴黑色兜鍪，腰系黑色革带，配有长剑圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "魁梧", "identity": "东宫护卫队长", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "郎中令尹成", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服长袍，主色调为褐色，搭配浅黄色和深棕色，衣服上有几何纹样，头戴黑色进贤冠，腰束褐色腰带，挂有印章圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "适中", "identity": "郎中令", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "顿弱", "gender": "男", "age": "中年", "clothes": "高领圆领浅蓝色秦国大臣朝服，主色调为浅蓝色，搭配淡蓝色和白色，袍身有波浪纹图案，腰间束蓝色丝带，佩戴朝珠，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "偏胖", "identity": "秦国大臣", "other": "能言善辩", "from_chapter": 13, "action": "", "expression": ""}, {"name": "赵高", "gender": "男", "age": "中年", "clothes": "高领圆领秦代囚服，主色调为白色，搭配灰色，衣服上有囚字标记，头发凌乱，无配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发凌乱", "figure": "瘦削", "identity": "阴谋刺杀者", "other": "阴险狡诈", "from_chapter": [0], "action": "", "expression": ""}, {"name": "秦王政（赢政）", "gender": "男", "age": "中年", "clothes": "高领圆领秦代龙袍，主色调为黄色，搭配金色和红色，袍身绣有五爪金龙图案，头戴冕旒，腰束黄色玉带，佩有玉玺圆形衣领，圆形衣领", "hairstyle": "黑色长发束起头戴冕旒", "figure": "高大", "identity": "秦国国王", "other": "有威严", "from_chapter": [0], "action": "", "expression": ""}, {"name": "相里腾", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服，主色调为褐色，搭配深灰色，有几何纹图案，腰系黑色革带，挂有铜牌圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "为公子华送工匠的人", "other": "办事利落", "from_chapter": 16, "action": "", "expression": ""}, {"name": "崔永", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服短衣，主色调为灰色，搭配白色，无明显花纹，系黑色腰带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精瘦", "identity": "公子华的下属", "other": "做事利落", "from_chapter": 7, "action": "", "expression": ""}, {"name": "医家张琰", "gender": "男", "age": "老年", "clothes": "高领圆领秦代医服长袍，主色调为青色，搭配淡蓝色和白色，衣身有草药图案，头戴青色方巾，腰系青色腰带，配有药囊圆形衣领，圆形衣领", "hairstyle": "白色长发束起", "figure": "清瘦", "identity": "医家", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "淳于越", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为深绿色，搭配白色和黑色，有方格花纹，腰间束布腰带，头戴黑色方巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "秦国大臣", "other": "", "from_chapter": 19, "action": "", "expression": ""}], "scene": "古代宫殿", "image_prompt": "中景，在古代宫殿中，先是侧耳倾听声音，接着招手叫来守夜护卫安排他们蹲守，之后回到床上盯着天花板，脸上带着疑惑、警惕神情的青年男子，他身着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，有着圆形衣领，黑色束发，身姿挺拔, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753194796_20250722_223316.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_85eb3f13-cbf1-4000-b8f2-ef950e9e6cb4_1.25x_20250722_195421.wav", "audio_duration": 11.986667, "video_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/videos/video_1753195874_20250722_225116.mp4", "story_board_id": 2, "subtitle": [{"subtitle_id": 1, "text": "这声音是从寝殿的书桌底下传来的，", "timestamp": "00:00:00,200 --> 00:00:03,020", "duration": 2.82, "char_count": 16, "start_time_s": 0.2, "end_time_s": 3.02, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 200, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "声"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 440, "text": "音"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 720, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 920, "text": "从"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1120, "text": "寝"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1320, "text": "殿"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1480, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1600, "text": "书"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1760, "text": "桌"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1880, "text": "底"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2040, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2240, "text": "传"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2400, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 3020, "start_time": 2560, "text": "的"}], "keyword": "寝殿"}, {"subtitle_id": 2, "text": "他立刻叫来守夜的护卫，", "timestamp": "00:00:03,080 --> 00:00:04,640", "duration": 1.56, "char_count": 11, "start_time_s": 3.08, "end_time_s": 4.64, "words": [{"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3080, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3280, "text": "立"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3440, "text": "刻"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3560, "text": "叫"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3720, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3880, "text": "守"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4040, "text": "夜"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4160, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4280, "text": "护"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4400, "text": "卫"}], "keyword": "护卫"}, {"subtitle_id": 3, "text": "让他们悄悄进屋蹲守，", "timestamp": "00:00:04,640 --> 00:00:06,200", "duration": 1.56, "char_count": 10, "start_time_s": 4.64, "end_time_s": 6.2, "words": [{"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4640, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4800, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 4960, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 5080, "text": "悄"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5120, "text": "悄"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5480, "text": "进"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5600, "text": "屋"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5720, "text": "蹲"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 5920, "text": "守"}], "keyword": "屋"}, {"subtitle_id": 4, "text": "准备抓个现行。", "timestamp": "00:00:06,200 --> 00:00:07,420", "duration": 1.22, "char_count": 7, "start_time_s": 6.2, "end_time_s": 7.42, "words": [{"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6200, "text": "准"}, {"attribute": {"event": "speech"}, "end_time": 6480, "start_time": 6360, "text": "备"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6480, "text": "抓"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6640, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6760, "text": "现"}, {"attribute": {"event": "speech"}, "end_time": 7420, "start_time": 6920, "text": "行"}], "keyword": "抓现行"}, {"subtitle_id": 5, "text": "龚子华自己则回到床上，", "timestamp": "00:00:07,520 --> 00:00:09,240", "duration": 1.72, "char_count": 11, "start_time_s": 7.52, "end_time_s": 9.24, "words": [{"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7520, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 7840, "start_time": 7720, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 8000, "start_time": 7840, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 8160, "start_time": 8040, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 8280, "start_time": 8160, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8280, "text": "则"}, {"attribute": {"event": "speech"}, "end_time": 8560, "start_time": 8400, "text": "回"}, {"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8560, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 8920, "start_time": 8720, "text": "床"}, {"attribute": {"event": "speech"}, "end_time": 9240, "start_time": 8960, "text": "上"}], "keyword": "龚子华"}, {"subtitle_id": 6, "text": "盯着天花板发愣", "timestamp": "00:00:09,240 --> 00:00:10,600", "duration": 1.36, "char_count": 7, "start_time_s": 9.24, "end_time_s": 10.6, "words": [{"attribute": {"event": "speech"}, "end_time": 9400, "start_time": 9240, "text": "盯"}, {"attribute": {"event": "speech"}, "end_time": 9520, "start_time": 9400, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 9720, "start_time": 9520, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 9880, "start_time": 9720, "text": "花"}, {"attribute": {"event": "speech"}, "end_time": 10040, "start_time": 9880, "text": "板"}, {"attribute": {"event": "speech"}, "end_time": 10200, "start_time": 10040, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 10600, "start_time": 10240, "text": "愣"}], "keyword": "天花板"}, {"subtitle_id": 7, "text": "到底是谁？", "timestamp": "00:00:10,600 --> 00:00:11,660", "duration": 1.06, "char_count": 5, "start_time_s": 10.6, "end_time_s": 11.66, "words": [{"attribute": {"event": "speech"}, "end_time": 10840, "start_time": 10600, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 11000, "start_time": 10840, "text": "底"}, {"attribute": {"event": "speech"}, "end_time": 11160, "start_time": 11000, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 11660, "start_time": 11160, "text": "谁"}], "keyword": "谁"}], "keywords": ["寝殿", "护卫", "屋", "抓现行", "龚子华", "天花板", "谁"]}, {"chapter": 37, "story_board": "怎么敢在东宫底下挖地道？而且还是直接挖到他的寝殿！", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "坐在床上，盯着天花板，双手摊开", "expression": "震惊、疑惑"}, {"name": "护卫队长", "gender": "男", "age": "中年", "clothes": "高领秦代战甲，主色调为黑色，搭配红色和金色，甲片上有虎纹浮雕，头戴黑色兜鍪，腰系黑色革带，配有长剑圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "魁梧", "identity": "东宫护卫队长", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "郎中令尹成", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服长袍，主色调为褐色，搭配浅黄色和深棕色，衣服上有几何纹样，头戴黑色进贤冠，腰束褐色腰带，挂有印章圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "适中", "identity": "郎中令", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "顿弱", "gender": "男", "age": "中年", "clothes": "高领圆领浅蓝色秦国大臣朝服，主色调为浅蓝色，搭配淡蓝色和白色，袍身有波浪纹图案，腰间束蓝色丝带，佩戴朝珠，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "偏胖", "identity": "秦国大臣", "other": "能言善辩", "from_chapter": 13, "action": "", "expression": ""}, {"name": "赵高", "gender": "男", "age": "中年", "clothes": "高领圆领秦代囚服，主色调为白色，搭配灰色，衣服上有囚字标记，头发凌乱，无配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发凌乱", "figure": "瘦削", "identity": "阴谋刺杀者", "other": "阴险狡诈", "from_chapter": [0], "action": "", "expression": ""}, {"name": "秦王政（赢政）", "gender": "男", "age": "中年", "clothes": "高领圆领秦代龙袍，主色调为黄色，搭配金色和红色，袍身绣有五爪金龙图案，头戴冕旒，腰束黄色玉带，佩有玉玺圆形衣领，圆形衣领", "hairstyle": "黑色长发束起头戴冕旒", "figure": "高大", "identity": "秦国国王", "other": "有威严", "from_chapter": [0], "action": "", "expression": ""}, {"name": "相里腾", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服，主色调为褐色，搭配深灰色，有几何纹图案，腰系黑色革带，挂有铜牌圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "为公子华送工匠的人", "other": "办事利落", "from_chapter": 16, "action": "", "expression": ""}, {"name": "崔永", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服短衣，主色调为灰色，搭配白色，无明显花纹，系黑色腰带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精瘦", "identity": "公子华的下属", "other": "做事利落", "from_chapter": 7, "action": "", "expression": ""}, {"name": "医家张琰", "gender": "男", "age": "老年", "clothes": "高领圆领秦代医服长袍，主色调为青色，搭配淡蓝色和白色，衣身有草药图案，头戴青色方巾，腰系青色腰带，配有药囊圆形衣领，圆形衣领", "hairstyle": "白色长发束起", "figure": "清瘦", "identity": "医家", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "淳于越", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为深绿色，搭配白色和黑色，有方格花纹，腰间束布腰带，头戴黑色方巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "秦国大臣", "other": "", "from_chapter": 19, "action": "", "expression": ""}], "scene": "古代宫殿", "image_prompt": "中景，坐在床上的青年男子，双手摊开，盯着天花板，脸上带着震惊、疑惑的表情。男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发，整体形象挺拔。背景是古代宫殿。, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753196010_20250722_225330.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_724b3766-e940-455f-89a9-719ed0fbdd2f_1.25x_20250722_195434.wav", "audio_duration": 4.629333, "video_url": "", "story_board_id": 3, "subtitle": [{"subtitle_id": 1, "text": "怎么敢在东宫底下挖地道，", "timestamp": "00:00:00,160 --> 00:00:01,980", "duration": 1.82, "char_count": 12, "start_time_s": 0.16, "end_time_s": 1.98, "words": [{"attribute": {"event": "speech"}, "end_time": 240, "start_time": 160, "text": "怎"}, {"attribute": {"event": "speech"}, "end_time": 320, "start_time": 240, "text": "么"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 320, "text": "敢"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 480, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 640, "text": "东"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 760, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 920, "text": "底"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1080, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1240, "text": "挖"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1400, "text": "地"}, {"attribute": {"event": "speech"}, "end_time": 1980, "start_time": 1520, "text": "道"}], "keyword": "东宫"}, {"subtitle_id": 2, "text": "而且还是直接挖到他的寝殿！", "timestamp": "00:00:02,160 --> 00:00:04,300", "duration": 2.14, "char_count": 13, "start_time_s": 2.16, "end_time_s": 4.3, "words": [{"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2160, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2360, "text": "且"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2480, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2640, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2760, "text": "直"}, {"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2960, "text": "接"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3080, "text": "挖"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3240, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3400, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3520, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3640, "text": "寝"}, {"attribute": {"event": "speech"}, "end_time": 4300, "start_time": 3800, "text": "殿"}], "keyword": "寝殿"}], "keywords": ["东宫", "寝殿"]}, {"chapter": 37, "story_board": "这可不是一天两天能完成的事，对方一定有备而来。两刻钟后，地板砖被挪开的声音响起，紧接着两个人从洞里爬出来，蹑手蹑脚地往公子华的床边走。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "躺在床上，眼睛盯着天花板发愣", "expression": "疑惑、沉思"}, {"name": "护卫队长", "gender": "男", "age": "中年", "clothes": "高领秦代战甲，主色调为黑色，搭配红色和金色，甲片上有虎纹浮雕，头戴黑色兜鍪，腰系黑色革带，配有长剑圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "魁梧", "identity": "东宫护卫队长", "other": "", "from_chapter": [0], "action": "躲在暗处等待指令", "expression": "警惕"}, {"name": "郎中令尹成", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服长袍，主色调为褐色，搭配浅黄色和深棕色，衣服上有几何纹样，头戴黑色进贤冠，腰束褐色腰带，挂有印章圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "适中", "identity": "郎中令", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "顿弱", "gender": "男", "age": "中年", "clothes": "高领圆领浅蓝色秦国大臣朝服，主色调为浅蓝色，搭配淡蓝色和白色，袍身有波浪纹图案，腰间束蓝色丝带，佩戴朝珠，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "偏胖", "identity": "秦国大臣", "other": "能言善辩", "from_chapter": 13, "action": "", "expression": ""}, {"name": "赵高", "gender": "男", "age": "中年", "clothes": "高领圆领秦代囚服，主色调为白色，搭配灰色，衣服上有囚字标记，头发凌乱，无配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发凌乱", "figure": "瘦削", "identity": "阴谋刺杀者", "other": "阴险狡诈", "from_chapter": [0], "action": "", "expression": ""}, {"name": "秦王政（赢政）", "gender": "男", "age": "中年", "clothes": "高领圆领秦代龙袍，主色调为黄色，搭配金色和红色，袍身绣有五爪金龙图案，头戴冕旒，腰束黄色玉带，佩有玉玺圆形衣领，圆形衣领", "hairstyle": "黑色长发束起头戴冕旒", "figure": "高大", "identity": "秦国国王", "other": "有威严", "from_chapter": [0], "action": "", "expression": ""}, {"name": "相里腾", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服，主色调为褐色，搭配深灰色，有几何纹图案，腰系黑色革带，挂有铜牌圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "为公子华送工匠的人", "other": "办事利落", "from_chapter": 16, "action": "", "expression": ""}, {"name": "崔永", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服短衣，主色调为灰色，搭配白色，无明显花纹，系黑色腰带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精瘦", "identity": "公子华的下属", "other": "做事利落", "from_chapter": 7, "action": "", "expression": ""}, {"name": "医家张琰", "gender": "男", "age": "老年", "clothes": "高领圆领秦代医服长袍，主色调为青色，搭配淡蓝色和白色，衣身有草药图案，头戴青色方巾，腰系青色腰带，配有药囊圆形衣领，圆形衣领", "hairstyle": "白色长发束起", "figure": "清瘦", "identity": "医家", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "淳于越", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为深绿色，搭配白色和黑色，有方格花纹，腰间束布腰带，头戴黑色方巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "秦国大臣", "other": "", "from_chapter": 19, "action": "", "expression": ""}], "scene": "古代宫殿", "image_prompt": "中景，躺在床上眼睛盯着天花板发愣，带着疑惑、沉思神情的青年男子为主，旁边暗处躲着一个警惕地等待指令的中年男子，背景是古代宫殿。青年男子挺拔，穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发；中年男子魁梧，穿着高领秦代战甲，主色调为黑色，搭配红色和金色，甲片上有虎纹浮雕，头戴黑色兜鍪，腰系黑色革带，配有长剑，圆形衣领，黑色短发束起, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753196176_20250722_225617.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_2e3c1b9d-708d-4106-b1b2-71e1c6abb82e_1.25x_20250722_195523.wav", "audio_duration": 10.928, "video_url": "", "story_board_id": 4, "subtitle": [{"subtitle_id": 1, "text": "这可不是一天两天能完成的事，", "timestamp": "00:00:00,160 --> 00:00:02,120", "duration": 1.96, "char_count": 14, "start_time_s": 0.16, "end_time_s": 2.12, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 400, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 560, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 640, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 760, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 920, "text": "两"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1080, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1240, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1320, "text": "完"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1480, "text": "成"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1640, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1720, "text": "事"}], "keyword": "事"}, {"subtitle_id": 2, "text": "对方一定有备而来。", "timestamp": "00:00:02,120 --> 00:00:03,580", "duration": 1.46, "char_count": 9, "start_time_s": 2.12, "end_time_s": 3.58, "words": [{"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2120, "text": "对"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2280, "text": "方"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2440, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2600, "text": "定"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2720, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2880, "text": "备"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 3000, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 3580, "start_time": 3120, "text": "来"}], "keyword": "对方"}, {"subtitle_id": 3, "text": "两刻钟后，", "timestamp": "00:00:03,680 --> 00:00:04,440", "duration": 0.76, "char_count": 5, "start_time_s": 3.68, "end_time_s": 4.44, "words": [{"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3680, "text": "两"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3840, "text": "刻"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3920, "text": "钟"}, {"attribute": {"event": "speech"}, "end_time": 4440, "start_time": 4040, "text": "后"}], "keyword": "两刻钟"}, {"subtitle_id": 4, "text": "地板砖被挪开的声音响起，", "timestamp": "00:00:04,440 --> 00:00:06,360", "duration": 1.92, "char_count": 12, "start_time_s": 4.44, "end_time_s": 6.36, "words": [{"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4440, "text": "地"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4640, "text": "板"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4800, "text": "砖"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 4960, "text": "被"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5120, "text": "挪"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5320, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5480, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5560, "text": "声"}, {"attribute": {"event": "speech"}, "end_time": 5760, "start_time": 5680, "text": "音"}, {"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5760, "text": "响"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6000, "text": "起"}], "keyword": "地板砖"}, {"subtitle_id": 5, "text": "紧接着，两个人从洞里爬出来，", "timestamp": "00:00:06,360 --> 00:00:08,400", "duration": 2.04, "char_count": 14, "start_time_s": 6.36, "end_time_s": 8.4, "words": [{"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6360, "text": "紧"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6560, "text": "接"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6720, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6920, "text": "两"}, {"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 7080, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7200, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7320, "text": "从"}, {"attribute": {"event": "speech"}, "end_time": 7640, "start_time": 7480, "text": "洞"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7640, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7760, "text": "爬"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 8000, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8120, "text": "来"}], "keyword": "两人"}, {"subtitle_id": 6, "text": "蹑手蹑脚的往龚子华的床边走。", "timestamp": "00:00:08,400 --> 00:00:10,580", "duration": 2.18, "char_count": 14, "start_time_s": 8.4, "end_time_s": 10.58, "words": [{"attribute": {"event": "speech"}, "end_time": 8600, "start_time": 8400, "text": "蹑"}, {"attribute": {"event": "speech"}, "end_time": 8720, "start_time": 8600, "text": "手"}, {"attribute": {"event": "speech"}, "end_time": 8880, "start_time": 8760, "text": "蹑"}, {"attribute": {"event": "speech"}, "end_time": 9040, "start_time": 8880, "text": "脚"}, {"attribute": {"event": "speech"}, "end_time": 9120, "start_time": 9040, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 9240, "start_time": 9120, "text": "往"}, {"attribute": {"event": "speech"}, "end_time": 9400, "start_time": 9280, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 9560, "start_time": 9400, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 9680, "start_time": 9560, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 9800, "start_time": 9680, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 9960, "start_time": 9800, "text": "床"}, {"attribute": {"event": "speech"}, "end_time": 10120, "start_time": 9960, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 10580, "start_time": 10160, "text": "走"}], "keyword": "龚子华"}], "keywords": ["事", "对方", "两刻钟", "地板砖", "两人", "龚子华"]}, {"chapter": 37, "story_board": "就在他们靠近时，护卫队长一声令下，众人点燃灯火，瞬间将两人制服。公子华亲自检查他们的嘴里有没有毒药，确认安全后，下令把人带到后院绑起来审问。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "亲自检查两个刺客嘴里是否有毒药，确认安全后下令把人带到后院绑起来审问", "expression": "严肃"}, {"name": "护卫队长", "gender": "男", "age": "中年", "clothes": "高领秦代战甲，主色调为黑色，搭配红色和金色，甲片上有虎纹浮雕，头戴黑色兜鍪，腰系黑色革带，配有长剑圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "魁梧", "identity": "东宫护卫队长", "other": "", "from_chapter": [0], "action": "一声令下", "expression": "威严"}, {"name": "郎中令尹成", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服长袍，主色调为褐色，搭配浅黄色和深棕色，衣服上有几何纹样，头戴黑色进贤冠，腰束褐色腰带，挂有印章圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "适中", "identity": "郎中令", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "顿弱", "gender": "男", "age": "中年", "clothes": "高领圆领浅蓝色秦国大臣朝服，主色调为浅蓝色，搭配淡蓝色和白色，袍身有波浪纹图案，腰间束蓝色丝带，佩戴朝珠，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "偏胖", "identity": "秦国大臣", "other": "能言善辩", "from_chapter": 13, "action": "", "expression": ""}, {"name": "赵高", "gender": "男", "age": "中年", "clothes": "高领圆领秦代囚服，主色调为白色，搭配灰色，衣服上有囚字标记，头发凌乱，无配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发凌乱", "figure": "瘦削", "identity": "阴谋刺杀者", "other": "阴险狡诈", "from_chapter": [0], "action": "", "expression": ""}, {"name": "秦王政（赢政）", "gender": "男", "age": "中年", "clothes": "高领圆领秦代龙袍，主色调为黄色，搭配金色和红色，袍身绣有五爪金龙图案，头戴冕旒，腰束黄色玉带，佩有玉玺圆形衣领，圆形衣领", "hairstyle": "黑色长发束起头戴冕旒", "figure": "高大", "identity": "秦国国王", "other": "有威严", "from_chapter": [0], "action": "", "expression": ""}, {"name": "相里腾", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服，主色调为褐色，搭配深灰色，有几何纹图案，腰系黑色革带，挂有铜牌圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "为公子华送工匠的人", "other": "办事利落", "from_chapter": 16, "action": "", "expression": ""}, {"name": "崔永", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服短衣，主色调为灰色，搭配白色，无明显花纹，系黑色腰带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精瘦", "identity": "公子华的下属", "other": "做事利落", "from_chapter": 7, "action": "", "expression": ""}, {"name": "医家张琰", "gender": "男", "age": "老年", "clothes": "高领圆领秦代医服长袍，主色调为青色，搭配淡蓝色和白色，衣身有草药图案，头戴青色方巾，腰系青色腰带，配有药囊圆形衣领，圆形衣领", "hairstyle": "白色长发束起", "figure": "清瘦", "identity": "医家", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "淳于越", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为深绿色，搭配白色和黑色，有方格花纹，腰间束布腰带，头戴黑色方巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "秦国大臣", "other": "", "from_chapter": 19, "action": "", "expression": ""}], "scene": "古代宫殿", "image_prompt": "中景，在古代宫殿中，一个严肃的青年男子亲自检查两个刺客嘴里是否有毒药，确认安全后下令把人带到后院绑起来，他身姿挺拔；旁边一个威严的中年男子一声令下，魁梧的他站在一旁。青年男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，黑色束发；中年男子穿着高领秦代战甲，主色调为黑色，搭配红色和金色，甲片上有虎纹浮雕，头戴黑色兜鍪，腰系黑色革带，配有长剑，黑色短发束起, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753196413_20250722_230013.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_d24faa3a-7670-4ae4-82e4-d8f611868e2a_1.25x_20250722_195558.wav", "audio_duration": 11.658667, "video_url": "", "story_board_id": 5, "subtitle": [{"subtitle_id": 1, "text": "就在他们靠近时，", "timestamp": "00:00:00,200 --> 00:00:01,360", "duration": 1.16, "char_count": 8, "start_time_s": 0.2, "end_time_s": 1.36, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 200, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 440, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 560, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 640, "text": "靠"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 840, "text": "近"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 960, "text": "时"}], "keyword": "他们"}, {"subtitle_id": 2, "text": "护卫队长一声令下，", "timestamp": "00:00:01,360 --> 00:00:02,720", "duration": 1.36, "char_count": 9, "start_time_s": 1.36, "end_time_s": 2.72, "words": [{"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1360, "text": "护"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1480, "text": "卫"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1640, "text": "队"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1800, "text": "长"}, {"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1920, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2080, "text": "声"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2200, "text": "令"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2360, "text": "下"}], "keyword": "护卫队长"}, {"subtitle_id": 3, "text": "众人点燃灯火，", "timestamp": "00:00:02,720 --> 00:00:03,920", "duration": 1.2, "char_count": 7, "start_time_s": 2.72, "end_time_s": 3.92, "words": [{"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2720, "text": "众"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2880, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3000, "text": "点"}, {"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3200, "text": "燃"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3320, "text": "灯"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3520, "text": "火"}], "keyword": "灯火"}, {"subtitle_id": 4, "text": "瞬间将两人制服。", "timestamp": "00:00:03,920 --> 00:00:05,340", "duration": 1.42, "char_count": 8, "start_time_s": 3.92, "end_time_s": 5.34, "words": [{"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3920, "text": "瞬"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4120, "text": "间"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4280, "text": "将"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4400, "text": "两"}, {"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4560, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4680, "text": "制"}, {"attribute": {"event": "speech"}, "end_time": 5340, "start_time": 4880, "text": "服"}], "keyword": "制服"}, {"subtitle_id": 5, "text": "龚子华亲自检查", "timestamp": "00:00:05,520 --> 00:00:06,720", "duration": 1.2, "char_count": 7, "start_time_s": 5.52, "end_time_s": 6.72, "words": [{"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5520, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5720, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5840, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6120, "text": "亲"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6280, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6400, "text": "检"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6560, "text": "查"}], "keyword": "龚子华"}, {"subtitle_id": 6, "text": "他们的嘴里有没有毒药，", "timestamp": "00:00:06,720 --> 00:00:08,240", "duration": 1.52, "char_count": 11, "start_time_s": 6.72, "end_time_s": 8.24, "words": [{"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6720, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6880, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6920, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 7040, "text": "嘴"}, {"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 7160, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7280, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 7520, "start_time": 7440, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 7640, "start_time": 7520, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7640, "text": "毒"}, {"attribute": {"event": "speech"}, "end_time": 8240, "start_time": 7800, "text": "药"}], "keyword": "毒药"}, {"subtitle_id": 7, "text": "确认安全后，", "timestamp": "00:00:08,240 --> 00:00:09,200", "duration": 0.96, "char_count": 6, "start_time_s": 8.24, "end_time_s": 9.2, "words": [{"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8240, "text": "确"}, {"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8400, "text": "认"}, {"attribute": {"event": "speech"}, "end_time": 8600, "start_time": 8480, "text": "安"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8600, "text": "全"}, {"attribute": {"event": "speech"}, "end_time": 9200, "start_time": 8800, "text": "后"}], "keyword": "安全"}, {"subtitle_id": 8, "text": "下令把人带到后院绑起来审问。", "timestamp": "00:00:09,200 --> 00:00:11,340", "duration": 2.14, "char_count": 14, "start_time_s": 9.2, "end_time_s": 11.34, "words": [{"attribute": {"event": "speech"}, "end_time": 9360, "start_time": 9200, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 9480, "start_time": 9360, "text": "令"}, {"attribute": {"event": "speech"}, "end_time": 9600, "start_time": 9480, "text": "把"}, {"attribute": {"event": "speech"}, "end_time": 9720, "start_time": 9600, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 9840, "start_time": 9720, "text": "带"}, {"attribute": {"event": "speech"}, "end_time": 9960, "start_time": 9840, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 10080, "start_time": 9960, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 10240, "start_time": 10120, "text": "院"}, {"attribute": {"event": "speech"}, "end_time": 10440, "start_time": 10280, "text": "绑"}, {"attribute": {"event": "speech"}, "end_time": 10600, "start_time": 10480, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 10720, "start_time": 10600, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 10920, "start_time": 10720, "text": "审"}, {"attribute": {"event": "speech"}, "end_time": 11340, "start_time": 10920, "text": "问"}], "keyword": "后院"}], "keywords": ["他们", "护卫队长", "灯火", "制服", "龚子华", "毒药", "安全", "后院"]}, {"chapter": 37, "story_board": "与此同时，郎中令尹成和顿弱接到消息，立刻赶到东宫。经过严刑拷问，刺客供出了一个名字——赵高。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "亲自检查两个刺客嘴里是否有毒药，确认安全后下令把人带到后院绑起来审问", "expression": "严肃"}, {"name": "护卫队长", "gender": "男", "age": "中年", "clothes": "高领秦代战甲，主色调为黑色，搭配红色和金色，甲片上有虎纹浮雕，头戴黑色兜鍪，腰系黑色革带，配有长剑圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "魁梧", "identity": "东宫护卫队长", "other": "", "from_chapter": [0], "action": "一声令下", "expression": "威严"}, {"name": "郎中令尹成", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服长袍，主色调为褐色，搭配浅黄色和深棕色，衣服上有几何纹样，头戴黑色进贤冠，腰束褐色腰带，挂有印章圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "适中", "identity": "郎中令", "other": "", "from_chapter": [0], "action": "接到消息后，快速赶到东宫，参与对刺客的严刑拷问，听到刺客供出赵高的名字后震惊站立", "expression": "震惊"}, {"name": "顿弱", "gender": "男", "age": "中年", "clothes": "高领圆领浅蓝色秦国大臣朝服，主色调为浅蓝色，搭配淡蓝色和白色，袍身有波浪纹图案，腰间束蓝色丝带，佩戴朝珠，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "偏胖", "identity": "秦国大臣", "other": "能言善辩", "from_chapter": 13, "action": "接到消息后，与尹成一同快速赶到东宫，参与拷问刺客，听到赵高名字后一脸惊愕", "expression": "惊愕"}, {"name": "赵高", "gender": "男", "age": "中年", "clothes": "高领圆领秦代囚服，主色调为白色，搭配灰色，衣服上有囚字标记，头发凌乱，无配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发凌乱", "figure": "瘦削", "identity": "阴谋刺杀者", "other": "阴险狡诈", "from_chapter": [0], "action": "", "expression": ""}, {"name": "秦王政（赢政）", "gender": "男", "age": "中年", "clothes": "高领圆领秦代龙袍，主色调为黄色，搭配金色和红色，袍身绣有五爪金龙图案，头戴冕旒，腰束黄色玉带，佩有玉玺圆形衣领，圆形衣领", "hairstyle": "黑色长发束起头戴冕旒", "figure": "高大", "identity": "秦国国王", "other": "有威严", "from_chapter": [0], "action": "", "expression": ""}, {"name": "相里腾", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服，主色调为褐色，搭配深灰色，有几何纹图案，腰系黑色革带，挂有铜牌圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "为公子华送工匠的人", "other": "办事利落", "from_chapter": 16, "action": "", "expression": ""}, {"name": "崔永", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服短衣，主色调为灰色，搭配白色，无明显花纹，系黑色腰带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精瘦", "identity": "公子华的下属", "other": "做事利落", "from_chapter": 7, "action": "", "expression": ""}, {"name": "医家张琰", "gender": "男", "age": "老年", "clothes": "高领圆领秦代医服长袍，主色调为青色，搭配淡蓝色和白色，衣身有草药图案，头戴青色方巾，腰系青色腰带，配有药囊圆形衣领，圆形衣领", "hairstyle": "白色长发束起", "figure": "清瘦", "identity": "医家", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "淳于越", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为深绿色，搭配白色和黑色，有方格花纹，腰间束布腰带，头戴黑色方巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "秦国大臣", "other": "", "from_chapter": 19, "action": "", "expression": ""}], "scene": "古代宫殿", "image_prompt": "中景，在古代宫殿中，两个中年男子震惊站立、一脸惊愕。其中一个身材适中、黑色长发束起的中年男子穿着高领圆领秦代官服长袍，主色调为褐色，搭配浅黄色和深棕色，衣服有几何纹样，头戴黑色进贤冠，腰束褐色腰带并挂有印章，圆形衣领；另一个偏胖、黑色束发的中年男子穿着高领圆领浅蓝色秦国大臣朝服，主色调为浅蓝色，搭配淡蓝色和白色，袍身有波浪纹图案，腰间束蓝色丝带，佩戴朝珠，头戴黑色官帽，圆形衣领, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753196639_20250722_230359.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_73502772-ef75-4e6d-a523-48084f5cb4f8_1.25x_20250722_195532.wav", "audio_duration": 7.645333, "video_url": "", "story_board_id": 6, "subtitle": [{"subtitle_id": 1, "text": "与此同时，", "timestamp": "00:00:00,160 --> 00:00:00,920", "duration": 0.76, "char_count": 5, "start_time_s": 0.16, "end_time_s": 0.92, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "与"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "此"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 400, "text": "同"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 520, "text": "时"}], "keyword": "同时"}, {"subtitle_id": 2, "text": "郎中令尹成和顿若接到消息，", "timestamp": "00:00:00,920 --> 00:00:02,960", "duration": 2.04, "char_count": 13, "start_time_s": 0.92, "end_time_s": 2.96, "words": [{"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 920, "text": "郎"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1120, "text": "中"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1240, "text": "令"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1400, "text": "尹"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1560, "text": "成"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1720, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1880, "text": "顿"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2080, "text": "若"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2240, "text": "接"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2360, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2480, "text": "消"}, {"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2640, "text": "息"}], "keyword": "尹成"}, {"subtitle_id": 3, "text": "立刻赶到东宫。", "timestamp": "00:00:02,960 --> 00:00:04,220", "duration": 1.26, "char_count": 7, "start_time_s": 2.96, "end_time_s": 4.22, "words": [{"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 2960, "text": "立"}, {"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3160, "text": "刻"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3320, "text": "赶"}, {"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3480, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3600, "text": "东"}, {"attribute": {"event": "speech"}, "end_time": 4220, "start_time": 3760, "text": "宫"}], "keyword": "东宫"}, {"subtitle_id": 4, "text": "经过严刑拷问，", "timestamp": "00:00:04,400 --> 00:00:05,520", "duration": 1.12, "char_count": 7, "start_time_s": 4.4, "end_time_s": 5.52, "words": [{"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4400, "text": "经"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4560, "text": "过"}, {"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4720, "text": "严"}, {"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4880, "text": "刑"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5000, "text": "拷"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5200, "text": "问"}], "keyword": "拷问"}, {"subtitle_id": 5, "text": "刺客共出了一个名字赵高。", "timestamp": "00:00:05,520 --> 00:00:07,580", "duration": 2.06, "char_count": 12, "start_time_s": 5.52, "end_time_s": 7.58, "words": [{"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5520, "text": "刺"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5720, "text": "客"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5880, "text": "共"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6040, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6200, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6280, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6360, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6440, "text": "名"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6640, "text": "字"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6880, "text": "赵"}, {"attribute": {"event": "speech"}, "end_time": 7580, "start_time": 7080, "text": "高"}], "keyword": "赵高"}], "keywords": ["同时", "尹成", "东宫", "拷问", "赵高"]}, {"chapter": 37, "story_board": "这个消息让两人震惊不已，赵高可是大王小时候的玩伴，怎么会对大公子下手？公子华听到这个名字，冷笑一声。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "双手抱胸", "expression": "冷笑"}, {"name": "护卫队长", "gender": "男", "age": "中年", "clothes": "高领秦代战甲，主色调为黑色，搭配红色和金色，甲片上有虎纹浮雕，头戴黑色兜鍪，腰系黑色革带，配有长剑圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "魁梧", "identity": "东宫护卫队长", "other": "", "from_chapter": [0], "action": "一声令下", "expression": "威严"}, {"name": "郎中令尹成", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服长袍，主色调为褐色，搭配浅黄色和深棕色，衣服上有几何纹样，头戴黑色进贤冠，腰束褐色腰带，挂有印章圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "适中", "identity": "郎中令", "other": "", "from_chapter": [0], "action": "站着，身体微微前倾", "expression": "震惊"}, {"name": "顿弱", "gender": "男", "age": "中年", "clothes": "高领圆领浅蓝色秦国大臣朝服，主色调为浅蓝色，搭配淡蓝色和白色，袍身有波浪纹图案，腰间束蓝色丝带，佩戴朝珠，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "偏胖", "identity": "秦国大臣", "other": "能言善辩", "from_chapter": 13, "action": "站着，瞪大双眼", "expression": "震惊"}, {"name": "赵高", "gender": "男", "age": "中年", "clothes": "高领圆领秦代囚服，主色调为白色，搭配灰色，衣服上有囚字标记，头发凌乱，无配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发凌乱", "figure": "瘦削", "identity": "阴谋刺杀者", "other": "阴险狡诈", "from_chapter": [0], "action": "", "expression": ""}, {"name": "秦王政（赢政）", "gender": "男", "age": "中年", "clothes": "高领圆领秦代龙袍，主色调为黄色，搭配金色和红色，袍身绣有五爪金龙图案，头戴冕旒，腰束黄色玉带，佩有玉玺圆形衣领，圆形衣领", "hairstyle": "黑色长发束起头戴冕旒", "figure": "高大", "identity": "秦国国王", "other": "有威严", "from_chapter": [0], "action": "", "expression": ""}, {"name": "相里腾", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服，主色调为褐色，搭配深灰色，有几何纹图案，腰系黑色革带，挂有铜牌圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "为公子华送工匠的人", "other": "办事利落", "from_chapter": 16, "action": "", "expression": ""}, {"name": "崔永", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服短衣，主色调为灰色，搭配白色，无明显花纹，系黑色腰带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精瘦", "identity": "公子华的下属", "other": "做事利落", "from_chapter": 7, "action": "", "expression": ""}, {"name": "医家张琰", "gender": "男", "age": "老年", "clothes": "高领圆领秦代医服长袍，主色调为青色，搭配淡蓝色和白色，衣身有草药图案，头戴青色方巾，腰系青色腰带，配有药囊圆形衣领，圆形衣领", "hairstyle": "白色长发束起", "figure": "清瘦", "identity": "医家", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "淳于越", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为深绿色，搭配白色和黑色，有方格花纹，腰间束布腰带，头戴黑色方巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "秦国大臣", "other": "", "from_chapter": 19, "action": "", "expression": ""}], "scene": "古代宫殿", "image_prompt": "中景，古代宫殿的背景下，一位双手抱胸冷笑的青年男子，他穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发，身姿挺拔；旁边站着两位震惊的中年男子，其中一位身体微微前倾，他穿着高领圆领秦代官服长袍，主色调为褐色，搭配浅黄色和深棕色，衣服上有几何纹样，头戴黑色进贤冠，腰束褐色腰带，挂有印章，圆形衣领，黑色长发束起；另一位瞪大双眼，体型偏胖，穿着高领圆领浅蓝色秦国大臣朝服，主色调为浅蓝色，搭配淡蓝色和白色，袍身有波浪纹图案，腰间束蓝色丝带，佩戴朝珠，头戴黑色官帽，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753196935_20250722_230856.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_4fd4eb23-7561-46f4-a8de-68c2d0d7e823_1.25x_20250722_195536.wav", "audio_duration": 8.469333, "video_url": "", "story_board_id": 7, "subtitle": [{"subtitle_id": 1, "text": "这个消息让两人震惊不已，", "timestamp": "00:00:00,160 --> 00:00:02,080", "duration": 1.92, "char_count": 12, "start_time_s": 0.16, "end_time_s": 2.08, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 400, "text": "消"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 560, "text": "息"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 800, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 960, "text": "两"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1120, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1240, "text": "震"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1480, "text": "惊"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1600, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1760, "text": "已"}], "keyword": "消息"}, {"subtitle_id": 2, "text": "赵高可是大王小时候的玩伴，", "timestamp": "00:00:02,080 --> 00:00:04,040", "duration": 1.96, "char_count": 13, "start_time_s": 2.08, "end_time_s": 4.04, "words": [{"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2080, "text": "赵"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2280, "text": "高"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2440, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2600, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2680, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2840, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 2960, "text": "小"}, {"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3120, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3280, "text": "候"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3360, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3480, "text": "玩"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3640, "text": "伴"}], "keyword": "赵高"}, {"subtitle_id": 3, "text": "怎么会对大公子下手，", "timestamp": "00:00:04,040 --> 00:00:05,620", "duration": 1.58, "char_count": 10, "start_time_s": 4.04, "end_time_s": 5.62, "words": [{"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4040, "text": "怎"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4200, "text": "么"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4280, "text": "会"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4400, "text": "对"}, {"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4480, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4680, "text": "公"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4840, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 4960, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 5620, "start_time": 5160, "text": "手"}], "keyword": "大公子"}, {"subtitle_id": 4, "text": "公子华听到这个名字冷笑一声。", "timestamp": "00:00:05,760 --> 00:00:08,380", "duration": 2.62, "char_count": 14, "start_time_s": 5.76, "end_time_s": 8.38, "words": [{"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5760, "text": "公"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5960, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6120, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 6480, "start_time": 6320, "text": "听"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6480, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6640, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6720, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6840, "text": "名"}, {"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 7000, "text": "字"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7400, "text": "冷"}, {"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7640, "text": "笑"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7800, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 8380, "start_time": 7960, "text": "声"}], "keyword": "公子华"}], "keywords": ["消息", "赵高", "大公子", "公子华"]}, {"chapter": 37, "story_board": "他早就想除掉赵高，因为此人阴险狡诈，历史上曾多次陷害忠良，甚至参与过杀害赢政子嗣的勾当。现在赵高竟然主动送上门，正好让他一网打尽。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "双手抱臂，微微仰头", "expression": "冷笑、坚定"}, {"name": "护卫队长", "gender": "男", "age": "中年", "clothes": "高领秦代战甲，主色调为黑色，搭配红色和金色，甲片上有虎纹浮雕，头戴黑色兜鍪，腰系黑色革带，配有长剑圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "魁梧", "identity": "东宫护卫队长", "other": "", "from_chapter": [0], "action": "一声令下", "expression": "威严"}, {"name": "郎中令尹成", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服长袍，主色调为褐色，搭配浅黄色和深棕色，衣服上有几何纹样，头戴黑色进贤冠，腰束褐色腰带，挂有印章圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "适中", "identity": "郎中令", "other": "", "from_chapter": [0], "action": "站着，身体微微前倾", "expression": "震惊"}, {"name": "顿弱", "gender": "男", "age": "中年", "clothes": "高领圆领浅蓝色秦国大臣朝服，主色调为浅蓝色，搭配淡蓝色和白色，袍身有波浪纹图案，腰间束蓝色丝带，佩戴朝珠，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "偏胖", "identity": "秦国大臣", "other": "能言善辩", "from_chapter": 13, "action": "站着，瞪大双眼", "expression": "震惊"}, {"name": "赵高", "gender": "男", "age": "中年", "clothes": "高领圆领秦代囚服，主色调为白色，搭配灰色，衣服上有囚字标记，头发凌乱，无配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发凌乱", "figure": "瘦削", "identity": "阴谋刺杀者", "other": "阴险狡诈", "from_chapter": [0], "action": "", "expression": ""}, {"name": "秦王政（赢政）", "gender": "男", "age": "中年", "clothes": "高领圆领秦代龙袍，主色调为黄色，搭配金色和红色，袍身绣有五爪金龙图案，头戴冕旒，腰束黄色玉带，佩有玉玺圆形衣领，圆形衣领", "hairstyle": "黑色长发束起头戴冕旒", "figure": "高大", "identity": "秦国国王", "other": "有威严", "from_chapter": [0], "action": "", "expression": ""}, {"name": "相里腾", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服，主色调为褐色，搭配深灰色，有几何纹图案，腰系黑色革带，挂有铜牌圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "为公子华送工匠的人", "other": "办事利落", "from_chapter": 16, "action": "", "expression": ""}, {"name": "崔永", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服短衣，主色调为灰色，搭配白色，无明显花纹，系黑色腰带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精瘦", "identity": "公子华的下属", "other": "做事利落", "from_chapter": 7, "action": "", "expression": ""}, {"name": "医家张琰", "gender": "男", "age": "老年", "clothes": "高领圆领秦代医服长袍，主色调为青色，搭配淡蓝色和白色，衣身有草药图案，头戴青色方巾，腰系青色腰带，配有药囊圆形衣领，圆形衣领", "hairstyle": "白色长发束起", "figure": "清瘦", "identity": "医家", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "淳于越", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为深绿色，搭配白色和黑色，有方格花纹，腰间束布腰带，头戴黑色方巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "秦国大臣", "other": "", "from_chapter": 19, "action": "", "expression": ""}], "scene": "古代宫殿", "image_prompt": "中景，双手抱臂、微微仰头冷笑且表情坚定的青年男子站在古代宫殿中，该男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753197480_20250722_231801.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_065a7490-418a-4b7e-8f3e-9309b92a46f9_1.25x_20250722_195552.wav", "audio_duration": 10.792, "video_url": "", "story_board_id": 8, "subtitle": [{"subtitle_id": 1, "text": "他早就想除掉赵高，", "timestamp": "00:00:00,160 --> 00:00:01,480", "duration": 1.32, "char_count": 9, "start_time_s": 0.16, "end_time_s": 1.48, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "早"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 440, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 600, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 760, "text": "除"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 880, "text": "掉"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 1000, "text": "赵"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1120, "text": "高"}], "keyword": "赵高"}, {"subtitle_id": 2, "text": "因为此人阴险狡诈，", "timestamp": "00:00:01,480 --> 00:00:02,800", "duration": 1.32, "char_count": 9, "start_time_s": 1.48, "end_time_s": 2.8, "words": [{"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1480, "text": "因"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1600, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1720, "text": "此"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1880, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2000, "text": "阴"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2160, "text": "险"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2320, "text": "狡"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2480, "text": "诈"}], "keyword": "赵高"}, {"subtitle_id": 3, "text": "历史上曾多次陷害忠良，", "timestamp": "00:00:02,800 --> 00:00:04,520", "duration": 1.72, "char_count": 11, "start_time_s": 2.8, "end_time_s": 4.52, "words": [{"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2800, "text": "历"}, {"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2960, "text": "史"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3080, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3240, "text": "曾"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3400, "text": "多"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3600, "text": "次"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3720, "text": "陷"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3880, "text": "害"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4040, "text": "忠"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4200, "text": "良"}], "keyword": "忠良"}, {"subtitle_id": 4, "text": "甚至参与过杀害嬴政子嗣的勾当。", "timestamp": "00:00:04,520 --> 00:00:06,940", "duration": 2.42, "char_count": 15, "start_time_s": 4.52, "end_time_s": 6.94, "words": [{"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4520, "text": "甚"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4680, "text": "至"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4800, "text": "参"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4960, "text": "与"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5040, "text": "过"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5200, "text": "杀"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5400, "text": "害"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5560, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5760, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 6080, "start_time": 5920, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 6080, "text": "嗣"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6240, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6480, "start_time": 6320, "text": "勾"}, {"attribute": {"event": "speech"}, "end_time": 6940, "start_time": 6480, "text": "当"}], "keyword": "嬴政子嗣"}, {"subtitle_id": 5, "text": "现在赵高竟然主动送上门，", "timestamp": "00:00:07,080 --> 00:00:09,000", "duration": 1.92, "char_count": 12, "start_time_s": 7.08, "end_time_s": 9.0, "words": [{"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7080, "text": "现"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7240, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7400, "text": "赵"}, {"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7560, "text": "高"}, {"attribute": {"event": "speech"}, "end_time": 7880, "start_time": 7760, "text": "竟"}, {"attribute": {"event": "speech"}, "end_time": 8000, "start_time": 7880, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8040, "text": "主"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8240, "text": "动"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8360, "text": "送"}, {"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8520, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8680, "text": "门"}], "keyword": "赵高"}, {"subtitle_id": 6, "text": "正好让他一网打尽。", "timestamp": "00:00:09,000 --> 00:00:10,500", "duration": 1.5, "char_count": 9, "start_time_s": 9.0, "end_time_s": 10.5, "words": [{"attribute": {"event": "speech"}, "end_time": 9160, "start_time": 9000, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 9320, "start_time": 9200, "text": "好"}, {"attribute": {"event": "speech"}, "end_time": 9480, "start_time": 9320, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 9560, "start_time": 9480, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 9720, "start_time": 9560, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 9840, "start_time": 9720, "text": "网"}, {"attribute": {"event": "speech"}, "end_time": 10040, "start_time": 9840, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 10500, "start_time": 10080, "text": "尽"}], "keyword": "一网打尽"}], "keywords": ["赵高", "赵高", "忠良", "嬴政子嗣", "赵高", "一网打尽"]}, {"chapter": 37, "story_board": "他立刻下令，调动手下，把赵高的三族以及与他关系密切的人全部抓起来，同时叮嘱不要通知秦王政。两人领命而去，开始行动。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "双手抱臂，表情严肃地下达命令", "expression": "冷静果断"}, {"name": "护卫队长", "gender": "男", "age": "中年", "clothes": "高领秦代战甲，主色调为黑色，搭配红色和金色，甲片上有虎纹浮雕，头戴黑色兜鍪，腰系黑色革带，配有长剑圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "魁梧", "identity": "东宫护卫队长", "other": "", "from_chapter": [0], "action": "一声令下", "expression": "威严"}, {"name": "郎中令尹成", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服长袍，主色调为褐色，搭配浅黄色和深棕色，衣服上有几何纹样，头戴黑色进贤冠，腰束褐色腰带，挂有印章圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "适中", "identity": "郎中令", "other": "", "from_chapter": [0], "action": "抱拳领命，转身准备行动", "expression": "恭敬"}, {"name": "顿弱", "gender": "男", "age": "中年", "clothes": "高领圆领浅蓝色秦国大臣朝服，主色调为浅蓝色，搭配淡蓝色和白色，袍身有波浪纹图案，腰间束蓝色丝带，佩戴朝珠，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "偏胖", "identity": "秦国大臣", "other": "能言善辩", "from_chapter": 13, "action": "抱拳领命，转身准备行动", "expression": "严肃"}, {"name": "赵高", "gender": "男", "age": "中年", "clothes": "高领圆领秦代囚服，主色调为白色，搭配灰色，衣服上有囚字标记，头发凌乱，无配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发凌乱", "figure": "瘦削", "identity": "阴谋刺杀者", "other": "阴险狡诈", "from_chapter": [0], "action": "", "expression": ""}, {"name": "秦王政（赢政）", "gender": "男", "age": "中年", "clothes": "高领圆领秦代龙袍，主色调为黄色，搭配金色和红色，袍身绣有五爪金龙图案，头戴冕旒，腰束黄色玉带，佩有玉玺圆形衣领，圆形衣领", "hairstyle": "黑色长发束起头戴冕旒", "figure": "高大", "identity": "秦国国王", "other": "有威严", "from_chapter": [0], "action": "", "expression": ""}, {"name": "相里腾", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服，主色调为褐色，搭配深灰色，有几何纹图案，腰系黑色革带，挂有铜牌圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "为公子华送工匠的人", "other": "办事利落", "from_chapter": 16, "action": "", "expression": ""}, {"name": "崔永", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服短衣，主色调为灰色，搭配白色，无明显花纹，系黑色腰带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精瘦", "identity": "公子华的下属", "other": "做事利落", "from_chapter": 7, "action": "", "expression": ""}, {"name": "医家张琰", "gender": "男", "age": "老年", "clothes": "高领圆领秦代医服长袍，主色调为青色，搭配淡蓝色和白色，衣身有草药图案，头戴青色方巾，腰系青色腰带，配有药囊圆形衣领，圆形衣领", "hairstyle": "白色长发束起", "figure": "清瘦", "identity": "医家", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "淳于越", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为深绿色，搭配白色和黑色，有方格花纹，腰间束布腰带，头戴黑色方巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "秦国大臣", "other": "", "from_chapter": 19, "action": "", "expression": ""}], "scene": "古代宫殿", "image_prompt": "中景，在古代宫殿里，双手抱臂、表情严肃地下达命令、冷静果断的青年男子，挺拔身姿。旁边有两个抱拳领命后转身准备行动的中年男子，其中一人恭敬，另一人严肃。青年男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，黑色束发；其中一个中年男子穿着高领圆领秦代官服长袍，主色调为褐色，搭配浅黄色和深棕色，衣服上有几何纹样，头戴黑色进贤冠，腰束褐色腰带，挂有印章，黑色长发束起；另一个中年男子穿着高领圆领浅蓝色秦国大臣朝服，主色调为浅蓝色，搭配淡蓝色和白色，袍身有波浪纹图案，腰间束蓝色丝带，佩戴朝珠，头戴黑色官帽，黑色束发，体型偏胖, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753197904_20250722_232505.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_81197cce-032f-42b2-a35e-987efbdbac2d_1.25x_20250722_195544.wav", "audio_duration": 10.048, "video_url": "", "story_board_id": 9, "subtitle": [{"subtitle_id": 1, "text": "他立刻下令调动手下，", "timestamp": "00:00:00,160 --> 00:00:01,800", "duration": 1.64, "char_count": 10, "start_time_s": 0.16, "end_time_s": 1.8, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "立"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 400, "text": "刻"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 520, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 680, "text": "令"}, {"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 960, "text": "调"}, {"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 1160, "text": "动"}, {"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1280, "text": "手"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1440, "text": "下"}], "keyword": "他"}, {"subtitle_id": 2, "text": "把赵、高的三族", "timestamp": "00:00:01,800 --> 00:00:02,800", "duration": 1.0, "char_count": 7, "start_time_s": 1.8, "end_time_s": 2.8, "words": [{"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1800, "text": "把"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1920, "text": "赵"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2120, "text": "高"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2280, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2520, "start_time": 2360, "text": "三"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2560, "text": "族"}], "keyword": "赵高"}, {"subtitle_id": 3, "text": "以及与他关系密切的人全部抓起来，", "timestamp": "00:00:02,800 --> 00:00:05,200", "duration": 2.4, "char_count": 16, "start_time_s": 2.8, "end_time_s": 5.2, "words": [{"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2800, "text": "以"}, {"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2960, "text": "及"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3080, "text": "与"}, {"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3320, "text": "关"}, {"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3480, "text": "系"}, {"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3600, "text": "密"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3800, "text": "切"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3920, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4000, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4280, "text": "全"}, {"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4520, "text": "部"}, {"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4680, "text": "抓"}, {"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4880, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5000, "text": "来"}], "keyword": "三族"}, {"subtitle_id": 4, "text": "同时叮嘱不要通知秦王正。", "timestamp": "00:00:05,200 --> 00:00:07,380", "duration": 2.18, "char_count": 12, "start_time_s": 5.2, "end_time_s": 7.38, "words": [{"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5200, "text": "同"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5440, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5600, "text": "叮"}, {"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5760, "text": "嘱"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5960, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6120, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6280, "text": "通"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6440, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6600, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6760, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 7380, "start_time": 6920, "text": "正"}], "keyword": "秦王"}, {"subtitle_id": 5, "text": "两人领命而去，", "timestamp": "00:00:07,440 --> 00:00:08,640", "duration": 1.2, "char_count": 7, "start_time_s": 7.44, "end_time_s": 8.64, "words": [{"attribute": {"event": "speech"}, "end_time": 7640, "start_time": 7440, "text": "两"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7640, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7760, "text": "领"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 7960, "text": "命"}, {"attribute": {"event": "speech"}, "end_time": 8280, "start_time": 8120, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8280, "text": "去"}], "keyword": "两人"}, {"subtitle_id": 6, "text": "开始行动。", "timestamp": "00:00:08,640 --> 00:00:09,740", "duration": 1.1, "char_count": 5, "start_time_s": 8.64, "end_time_s": 9.74, "words": [{"attribute": {"event": "speech"}, "end_time": 8880, "start_time": 8640, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 9040, "start_time": 8880, "text": "始"}, {"attribute": {"event": "speech"}, "end_time": 9240, "start_time": 9080, "text": "行"}, {"attribute": {"event": "speech"}, "end_time": 9740, "start_time": 9280, "text": "动"}], "keyword": "行动"}], "keywords": ["他", "赵高", "三族", "秦王", "两人", "行动"]}, {"chapter": 37, "story_board": "天刚蒙蒙亮，抓捕行动已经结束。尹成和顿弱带回了大量证据，包括赵高与各国往来的信件和大量钱粮。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "坐在椅子上，伸手接过证据查看", "expression": "冷静"}, {"name": "护卫队长", "gender": "男", "age": "中年", "clothes": "高领秦代战甲，主色调为黑色，搭配红色和金色，甲片上有虎纹浮雕，头戴黑色兜鍪，腰系黑色革带，配有长剑圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "魁梧", "identity": "东宫护卫队长", "other": "", "from_chapter": [0], "action": "一声令下", "expression": "威严"}, {"name": "郎中令尹成", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服长袍，主色调为褐色，搭配浅黄色和深棕色，衣服上有几何纹样，头戴黑色进贤冠，腰束褐色腰带，挂有印章圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "适中", "identity": "郎中令", "other": "", "from_chapter": [0], "action": "拿着信件和钱粮，站在公子华面前", "expression": "严肃"}, {"name": "顿弱", "gender": "男", "age": "中年", "clothes": "高领圆领浅蓝色秦国大臣朝服，主色调为浅蓝色，搭配淡蓝色和白色，袍身有波浪纹图案，腰间束蓝色丝带，佩戴朝珠，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "偏胖", "identity": "秦国大臣", "other": "能言善辩", "from_chapter": 13, "action": "抱着信件和钱粮，站在公子华面前", "expression": "认真"}, {"name": "赵高", "gender": "男", "age": "中年", "clothes": "高领圆领秦代囚服，主色调为白色，搭配灰色，衣服上有囚字标记，头发凌乱，无配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发凌乱", "figure": "瘦削", "identity": "阴谋刺杀者", "other": "阴险狡诈", "from_chapter": [0], "action": "", "expression": ""}, {"name": "秦王政（赢政）", "gender": "男", "age": "中年", "clothes": "高领圆领秦代龙袍，主色调为黄色，搭配金色和红色，袍身绣有五爪金龙图案，头戴冕旒，腰束黄色玉带，佩有玉玺圆形衣领，圆形衣领", "hairstyle": "黑色长发束起头戴冕旒", "figure": "高大", "identity": "秦国国王", "other": "有威严", "from_chapter": [0], "action": "", "expression": ""}, {"name": "相里腾", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服，主色调为褐色，搭配深灰色，有几何纹图案，腰系黑色革带，挂有铜牌圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "为公子华送工匠的人", "other": "办事利落", "from_chapter": 16, "action": "", "expression": ""}, {"name": "崔永", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服短衣，主色调为灰色，搭配白色，无明显花纹，系黑色腰带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精瘦", "identity": "公子华的下属", "other": "做事利落", "from_chapter": 7, "action": "", "expression": ""}, {"name": "医家张琰", "gender": "男", "age": "老年", "clothes": "高领圆领秦代医服长袍，主色调为青色，搭配淡蓝色和白色，衣身有草药图案，头戴青色方巾，腰系青色腰带，配有药囊圆形衣领，圆形衣领", "hairstyle": "白色长发束起", "figure": "清瘦", "identity": "医家", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "淳于越", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为深绿色，搭配白色和黑色，有方格花纹，腰间束布腰带，头戴黑色方巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "秦国大臣", "other": "", "from_chapter": 19, "action": "", "expression": ""}], "scene": "古代宫殿", "image_prompt": "中景，古代宫殿中，一位青年男子坐在椅子上，冷静地伸手接过证据查看，他穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发，身姿挺拔；两位中年男子站在他面前，一位表情严肃地拿着信件和钱粮，另一位认真地抱着信件和钱粮，前者穿着高领圆领秦代官服长袍，主色调为褐色，搭配浅黄色和深棕色，衣服上有几何纹样，头戴黑色进贤冠，腰束褐色腰带，挂有印章，圆形衣领，黑色长发束起；后者穿着高领圆领浅蓝色秦国大臣朝服，主色调为浅蓝色，搭配淡蓝色和白色，袍身有波浪纹图案，腰间束蓝色丝带，佩戴朝珠，头戴黑色官帽，圆形衣领，黑色束发，体型偏胖，动漫分镜插图风格, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753198286_20250722_233126.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_09d50341-60f7-4203-8d70-adfd9214fe1b_1.25x_20250722_195548.wav", "audio_duration": 8.530667, "video_url": "", "story_board_id": 10, "subtitle": [{"subtitle_id": 1, "text": "天刚蒙蒙亮，", "timestamp": "00:00:00,200 --> 00:00:01,120", "duration": 0.92, "char_count": 6, "start_time_s": 0.2, "end_time_s": 1.12, "words": [{"attribute": {"event": "speech"}, "end_time": 360, "start_time": 200, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 360, "text": "刚"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 520, "text": "蒙"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 560, "text": "蒙"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 800, "text": "亮"}], "keyword": "天亮"}, {"subtitle_id": 2, "text": "抓捕行动已经结束。", "timestamp": "00:00:01,120 --> 00:00:02,700", "duration": 1.58, "char_count": 9, "start_time_s": 1.12, "end_time_s": 2.7, "words": [{"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 1120, "text": "抓"}, {"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1320, "text": "捕"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1440, "text": "行"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1600, "text": "动"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1800, "text": "已"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1920, "text": "经"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2040, "text": "结"}, {"attribute": {"event": "speech"}, "end_time": 2700, "start_time": 2240, "text": "束"}], "keyword": "行动"}, {"subtitle_id": 3, "text": "尹承、何顿若带回了大量证据，", "timestamp": "00:00:02,840 --> 00:00:05,040", "duration": 2.2, "char_count": 14, "start_time_s": 2.84, "end_time_s": 5.04, "words": [{"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2840, "text": "尹"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3040, "text": "承"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3200, "text": "何"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3360, "text": "顿"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3480, "text": "若"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3720, "text": "带"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3920, "text": "回"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4080, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4200, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4400, "text": "量"}, {"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4520, "text": "证"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4680, "text": "据"}], "keyword": "尹承"}, {"subtitle_id": 4, "text": "包括赵高与各国往来的信件", "timestamp": "00:00:05,040 --> 00:00:07,040", "duration": 2.0, "char_count": 12, "start_time_s": 5.04, "end_time_s": 7.04, "words": [{"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5040, "text": "包"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5200, "text": "括"}, {"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5400, "text": "赵"}, {"attribute": {"event": "speech"}, "end_time": 5760, "start_time": 5600, "text": "高"}, {"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5760, "text": "与"}, {"attribute": {"event": "speech"}, "end_time": 6080, "start_time": 5920, "text": "各"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6080, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6200, "text": "往"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6360, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6520, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6600, "text": "信"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6800, "text": "件"}], "keyword": "信件"}, {"subtitle_id": 5, "text": "和大量钱粮。", "timestamp": "00:00:07,040 --> 00:00:08,180", "duration": 1.14, "char_count": 6, "start_time_s": 7.04, "end_time_s": 8.18, "words": [{"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 7040, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 7360, "start_time": 7160, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 7520, "start_time": 7400, "text": "量"}, {"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7560, "text": "钱"}, {"attribute": {"event": "speech"}, "end_time": 8180, "start_time": 7720, "text": "粮"}], "keyword": "钱粮"}], "keywords": ["天亮", "行动", "尹承", "信件", "钱粮"]}, {"chapter": 37, "story_board": "他们知道事情严重，专门设立了监牢关押赵高案的涉案人员。赵高被押入狱中，拍着门大喊要见秦王政，但没人理会。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "坐在椅子上，伸手接过证据查看", "expression": "冷静"}, {"name": "护卫队长", "gender": "男", "age": "中年", "clothes": "高领秦代战甲，主色调为黑色，搭配红色和金色，甲片上有虎纹浮雕，头戴黑色兜鍪，腰系黑色革带，配有长剑圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "魁梧", "identity": "东宫护卫队长", "other": "", "from_chapter": [0], "action": "一声令下", "expression": "威严"}, {"name": "郎中令尹成", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服长袍，主色调为褐色，搭配浅黄色和深棕色，衣服上有几何纹样，头戴黑色进贤冠，腰束褐色腰带，挂有印章圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "适中", "identity": "郎中令", "other": "", "from_chapter": [0], "action": "设立监牢关押涉案人员", "expression": "严肃"}, {"name": "顿弱", "gender": "男", "age": "中年", "clothes": "高领圆领浅蓝色秦国大臣朝服，主色调为浅蓝色，搭配淡蓝色和白色，袍身有波浪纹图案，腰间束蓝色丝带，佩戴朝珠，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "偏胖", "identity": "秦国大臣", "other": "能言善辩", "from_chapter": 13, "action": "设立监牢关押涉案人员", "expression": "严肃"}, {"name": "赵高", "gender": "男", "age": "中年", "clothes": "高领圆领秦代囚服，主色调为白色，搭配灰色，衣服上有囚字标记，头发凌乱，无配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发凌乱", "figure": "瘦削", "identity": "阴谋刺杀者", "other": "阴险狡诈", "from_chapter": [0], "action": "被押入狱中后拍着门", "expression": "焦急"}, {"name": "秦王政（赢政）", "gender": "男", "age": "中年", "clothes": "高领圆领秦代龙袍，主色调为黄色，搭配金色和红色，袍身绣有五爪金龙图案，头戴冕旒，腰束黄色玉带，佩有玉玺圆形衣领，圆形衣领", "hairstyle": "黑色长发束起头戴冕旒", "figure": "高大", "identity": "秦国国王", "other": "有威严", "from_chapter": [0], "action": "", "expression": ""}, {"name": "相里腾", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服，主色调为褐色，搭配深灰色，有几何纹图案，腰系黑色革带，挂有铜牌圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "为公子华送工匠的人", "other": "办事利落", "from_chapter": 16, "action": "", "expression": ""}, {"name": "崔永", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服短衣，主色调为灰色，搭配白色，无明显花纹，系黑色腰带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精瘦", "identity": "公子华的下属", "other": "做事利落", "from_chapter": 7, "action": "", "expression": ""}, {"name": "医家张琰", "gender": "男", "age": "老年", "clothes": "高领圆领秦代医服长袍，主色调为青色，搭配淡蓝色和白色，衣身有草药图案，头戴青色方巾，腰系青色腰带，配有药囊圆形衣领，圆形衣领", "hairstyle": "白色长发束起", "figure": "清瘦", "identity": "医家", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "淳于越", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为深绿色，搭配白色和黑色，有方格花纹，腰间束布腰带，头戴黑色方巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "秦国大臣", "other": "", "from_chapter": 19, "action": "", "expression": ""}], "scene": "古代监狱", "image_prompt": "中景，在古代监狱中，一个瘦削、头发凌乱的中年男子焦急地拍着牢门，脸上带着阴险狡诈的神情；旁边有两个中年男子，神情严肃地站着。拍门的中年男子穿着主色调为白色、搭配灰色的高领圆领秦代囚服，衣服上有囚字标记，无配饰，圆形衣领，黑色短发凌乱；另外两个中年男子，一个穿着主色调为褐色、搭配浅黄色和深棕色的高领圆领秦代官服长袍，衣服上有几何纹样，头戴黑色进贤冠，腰束褐色腰带，挂有印章，圆形衣领，黑色长发束起；另一个穿着主色调为浅蓝色、搭配淡蓝色和白色的高领圆领秦国大臣朝服，袍身有波浪纹图案，腰间束蓝色丝带，佩戴朝珠，头戴黑色官帽，圆形衣领，黑色束发，体型偏胖, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753198697_20250722_233818.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_e02b7335-a967-482e-90bb-0941655b4c1d_1.25x_20250722_195540.wav", "audio_duration": 8.376, "video_url": "", "story_board_id": 11, "subtitle": [{"subtitle_id": 1, "text": "他们知道事情严重，", "timestamp": "00:00:00,160 --> 00:00:01,400", "duration": 1.24, "char_count": 9, "start_time_s": 0.16, "end_time_s": 1.4, "words": [{"attribute": {"event": "speech"}, "end_time": 240, "start_time": 160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 320, "start_time": 240, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 440, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 640, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 760, "text": "情"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 840, "text": "严"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1000, "text": "重"}], "keyword": "他们"}, {"subtitle_id": 2, "text": "专门设立了监牢，", "timestamp": "00:00:01,400 --> 00:00:02,400", "duration": 1.0, "char_count": 8, "start_time_s": 1.4, "end_time_s": 2.4, "words": [{"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1400, "text": "专"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1560, "text": "门"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1720, "text": "设"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1880, "text": "立"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1960, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2120, "text": "监"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2240, "text": "牢"}], "keyword": "监牢"}, {"subtitle_id": 3, "text": "关押赵高案的涉案人员。", "timestamp": "00:00:02,400 --> 00:00:04,140", "duration": 1.74, "char_count": 11, "start_time_s": 2.4, "end_time_s": 4.14, "words": [{"attribute": {"event": "speech"}, "end_time": 2520, "start_time": 2400, "text": "关"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2520, "text": "押"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2680, "text": "赵"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2840, "text": "高"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 3000, "text": "案"}, {"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3120, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3320, "text": "涉"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3440, "text": "案"}, {"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3560, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 4140, "start_time": 3680, "text": "员"}], "keyword": "赵高"}, {"subtitle_id": 4, "text": "赵高被押入狱中，", "timestamp": "00:00:04,320 --> 00:00:05,520", "duration": 1.2, "char_count": 8, "start_time_s": 4.32, "end_time_s": 5.52, "words": [{"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4320, "text": "赵"}, {"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4480, "text": "高"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4680, "text": "被"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4800, "text": "押"}, {"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 4960, "text": "入"}, {"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5080, "text": "狱"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5200, "text": "中"}], "keyword": "赵高"}, {"subtitle_id": 5, "text": "拍着门大喊要见秦王政，", "timestamp": "00:00:05,520 --> 00:00:07,200", "duration": 1.68, "char_count": 11, "start_time_s": 5.52, "end_time_s": 7.2, "words": [{"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5520, "text": "拍"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5720, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5800, "text": "门"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5920, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 6120, "text": "喊"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6240, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6400, "text": "见"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6560, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6720, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 6880, "text": "政"}], "keyword": "秦王政"}, {"subtitle_id": 6, "text": "但没人理会。", "timestamp": "00:00:07,200 --> 00:00:08,340", "duration": 1.14, "char_count": 6, "start_time_s": 7.2, "end_time_s": 8.34, "words": [{"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7200, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 7520, "start_time": 7360, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7520, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 7840, "start_time": 7680, "text": "理"}, {"attribute": {"event": "speech"}, "end_time": 8340, "start_time": 7840, "text": "会"}], "keyword": "无人"}], "keywords": ["他们", "监牢", "赵高", "赵高", "秦王政", "无人"]}, {"chapter": 37, "story_board": "他只能眼睁睁看着自己的势力被瓦解。随后，三人一起用早饭。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "与他人一起吃早饭", "expression": "平静"}, {"name": "护卫队长", "gender": "男", "age": "中年", "clothes": "高领秦代战甲，主色调为黑色，搭配红色和金色，甲片上有虎纹浮雕，头戴黑色兜鍪，腰系黑色革带，配有长剑圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "魁梧", "identity": "东宫护卫队长", "other": "", "from_chapter": [0], "action": "一声令下", "expression": "威严"}, {"name": "郎中令尹成", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服长袍，主色调为褐色，搭配浅黄色和深棕色，衣服上有几何纹样，头戴黑色进贤冠，腰束褐色腰带，挂有印章圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "适中", "identity": "郎中令", "other": "", "from_chapter": [0], "action": "与他人一起吃早饭", "expression": "严肃"}, {"name": "顿弱", "gender": "男", "age": "中年", "clothes": "高领圆领浅蓝色秦国大臣朝服，主色调为浅蓝色，搭配淡蓝色和白色，袍身有波浪纹图案，腰间束蓝色丝带，佩戴朝珠，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "偏胖", "identity": "秦国大臣", "other": "能言善辩", "from_chapter": 13, "action": "设立监牢关押涉案人员", "expression": "严肃"}, {"name": "赵高", "gender": "男", "age": "中年", "clothes": "高领圆领秦代囚服，主色调为白色，搭配灰色，衣服上有囚字标记，头发凌乱，无配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发凌乱", "figure": "瘦削", "identity": "阴谋刺杀者", "other": "阴险狡诈", "from_chapter": [0], "action": "被押入狱中，拍着门大喊，眼睁睁看着", "expression": "愤怒、绝望"}, {"name": "秦王政（赢政）", "gender": "男", "age": "中年", "clothes": "高领圆领秦代龙袍，主色调为黄色，搭配金色和红色，袍身绣有五爪金龙图案，头戴冕旒，腰束黄色玉带，佩有玉玺圆形衣领，圆形衣领", "hairstyle": "黑色长发束起头戴冕旒", "figure": "高大", "identity": "秦国国王", "other": "有威严", "from_chapter": [0], "action": "", "expression": ""}, {"name": "相里腾", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服，主色调为褐色，搭配深灰色，有几何纹图案，腰系黑色革带，挂有铜牌圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "为公子华送工匠的人", "other": "办事利落", "from_chapter": 16, "action": "", "expression": ""}, {"name": "崔永", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服短衣，主色调为灰色，搭配白色，无明显花纹，系黑色腰带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精瘦", "identity": "公子华的下属", "other": "做事利落", "from_chapter": 7, "action": "", "expression": ""}, {"name": "医家张琰", "gender": "男", "age": "老年", "clothes": "高领圆领秦代医服长袍，主色调为青色，搭配淡蓝色和白色，衣身有草药图案，头戴青色方巾，腰系青色腰带，配有药囊圆形衣领，圆形衣领", "hairstyle": "白色长发束起", "figure": "清瘦", "identity": "医家", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "淳于越", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为深绿色，搭配白色和黑色，有方格花纹，腰间束布腰带，头戴黑色方巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "秦国大臣", "other": "", "from_chapter": 19, "action": "", "expression": ""}], "scene": "古代宫殿", "image_prompt": "中景，在古代宫殿的场景中，一个愤怒绝望的中年男人被押入狱中，正拍着门，眼睁睁地看着外面，此人头发凌乱，无配饰，圆形衣领，黑色短发凌乱，身形瘦削，穿着高领圆领秦代囚服，主色调为白色，搭配灰色，衣服上有囚字标记；不远处，一个青年男子和一个中年男子正在平静且严肃地吃早饭，青年男子挺拔，黑色束发，身着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领；中年男子适中身材，黑色长发束起，头戴黑色进贤冠，腰束褐色腰带，挂有印章，穿着高领圆领秦代官服长袍，主色调为褐色，搭配浅黄色和深棕色，衣服上有几何纹样，圆形衣领, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753199121_20250722_234522.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_6900e1f2-73c9-4cf1-a78b-de25a95924d0_1.25x_20250722_195528.wav", "audio_duration": 5.032, "video_url": "", "story_board_id": 12, "subtitle": [{"subtitle_id": 1, "text": "他只能眼睁睁看着", "timestamp": "00:00:00,160 --> 00:00:01,320", "duration": 1.16, "char_count": 8, "start_time_s": 0.16, "end_time_s": 1.32, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "只"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 400, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 560, "text": "眼"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 760, "text": "睁"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 800, "text": "睁"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1000, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1200, "text": "着"}], "keyword": "他"}, {"subtitle_id": 2, "text": "自己的势力被瓦解。", "timestamp": "00:00:01,320 --> 00:00:02,740", "duration": 1.42, "char_count": 9, "start_time_s": 1.32, "end_time_s": 2.74, "words": [{"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1320, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1440, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1560, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1720, "text": "势"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1840, "text": "力"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1960, "text": "被"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2120, "text": "瓦"}, {"attribute": {"event": "speech"}, "end_time": 2740, "start_time": 2280, "text": "解"}], "keyword": "势力"}, {"subtitle_id": 3, "text": "随后，三人一起用早饭。", "timestamp": "00:00:02,920 --> 00:00:04,940", "duration": 2.02, "char_count": 11, "start_time_s": 2.92, "end_time_s": 4.94, "words": [{"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2920, "text": "随"}, {"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3080, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3520, "text": "三"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3680, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3840, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4000, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4160, "text": "用"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4320, "text": "早"}, {"attribute": {"event": "speech"}, "end_time": 4940, "start_time": 4480, "text": "饭"}], "keyword": "早饭"}], "keywords": ["他", "势力", "早饭"]}, {"chapter": 37, "story_board": "赢政匆匆赶来，关心儿子是否安好。公子华表示没事，并劝父亲不必动怒。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站在原地，对着父亲摆手", "expression": "平静"}, {"name": "护卫队长", "gender": "男", "age": "中年", "clothes": "高领秦代战甲，主色调为黑色，搭配红色和金色，甲片上有虎纹浮雕，头戴黑色兜鍪，腰系黑色革带，配有长剑圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "魁梧", "identity": "东宫护卫队长", "other": "", "from_chapter": [0], "action": "一声令下", "expression": "威严"}, {"name": "郎中令尹成", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服长袍，主色调为褐色，搭配浅黄色和深棕色，衣服上有几何纹样，头戴黑色进贤冠，腰束褐色腰带，挂有印章圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "适中", "identity": "郎中令", "other": "", "from_chapter": [0], "action": "与他人一起吃早饭", "expression": "严肃"}, {"name": "顿弱", "gender": "男", "age": "中年", "clothes": "高领圆领浅蓝色秦国大臣朝服，主色调为浅蓝色，搭配淡蓝色和白色，袍身有波浪纹图案，腰间束蓝色丝带，佩戴朝珠，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "偏胖", "identity": "秦国大臣", "other": "能言善辩", "from_chapter": 13, "action": "设立监牢关押涉案人员", "expression": "严肃"}, {"name": "赵高", "gender": "男", "age": "中年", "clothes": "高领圆领秦代囚服，主色调为白色，搭配灰色，衣服上有囚字标记，头发凌乱，无配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发凌乱", "figure": "瘦削", "identity": "阴谋刺杀者", "other": "阴险狡诈", "from_chapter": [0], "action": "被押入狱中，拍着门大喊，眼睁睁看着", "expression": "愤怒、绝望"}, {"name": "秦王政（赢政）", "gender": "男", "age": "中年", "clothes": "高领圆领秦代龙袍，主色调为黄色，搭配金色和红色，袍身绣有五爪金龙图案，头戴冕旒，腰束黄色玉带，佩有玉玺圆形衣领，圆形衣领", "hairstyle": "黑色长发束起头戴冕旒", "figure": "高大", "identity": "秦国国王", "other": "有威严", "from_chapter": [0], "action": "匆匆赶来，站在公子华面前", "expression": "焦急"}, {"name": "相里腾", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服，主色调为褐色，搭配深灰色，有几何纹图案，腰系黑色革带，挂有铜牌圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "为公子华送工匠的人", "other": "办事利落", "from_chapter": 16, "action": "", "expression": ""}, {"name": "崔永", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服短衣，主色调为灰色，搭配白色，无明显花纹，系黑色腰带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精瘦", "identity": "公子华的下属", "other": "做事利落", "from_chapter": 7, "action": "", "expression": ""}, {"name": "医家张琰", "gender": "男", "age": "老年", "clothes": "高领圆领秦代医服长袍，主色调为青色，搭配淡蓝色和白色，衣身有草药图案，头戴青色方巾，腰系青色腰带，配有药囊圆形衣领，圆形衣领", "hairstyle": "白色长发束起", "figure": "清瘦", "identity": "医家", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "淳于越", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为深绿色，搭配白色和黑色，有方格花纹，腰间束布腰带，头戴黑色方巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "秦国大臣", "other": "", "from_chapter": 19, "action": "", "expression": ""}], "scene": "古代宫殿", "image_prompt": "中景，在古代宫殿中，匆匆赶来、神情焦急的中年男子站在青年男子面前，青年男子站在原地对着中年男子摆手，表情平静。中年男子高大威严，身着高领圆领秦代龙袍，主色调为黄色，搭配金色和红色，袍身绣有五爪金龙图案，头戴冕旒，腰束黄色玉带，佩有玉玺，圆形衣领，黑色长发束起；青年男子挺拔，言辞犀利，身着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753199517_20250722_235158.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_87a86632-cff0-4001-b8e6-609232ed091a_1.25x_20250722_195519.wav", "audio_duration": 6.165333, "video_url": "", "story_board_id": 13, "subtitle": [{"subtitle_id": 1, "text": "嬴政匆匆赶来，", "timestamp": "00:00:00,200 --> 00:00:01,320", "duration": 1.12, "char_count": 7, "start_time_s": 0.2, "end_time_s": 1.32, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 200, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 360, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 560, "text": "匆"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 600, "text": "匆"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 880, "text": "赶"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1000, "text": "来"}], "keyword": "嬴政"}, {"subtitle_id": 2, "text": "关心儿子是否安好，", "timestamp": "00:00:01,320 --> 00:00:02,940", "duration": 1.62, "char_count": 9, "start_time_s": 1.32, "end_time_s": 2.94, "words": [{"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1320, "text": "关"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1520, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1640, "text": "儿"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1800, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1920, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2120, "text": "否"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2240, "text": "安"}, {"attribute": {"event": "speech"}, "end_time": 2940, "start_time": 2440, "text": "好"}], "keyword": "儿子"}, {"subtitle_id": 3, "text": "龚子华表示没事，", "timestamp": "00:00:03,040 --> 00:00:04,240", "duration": 1.2, "char_count": 8, "start_time_s": 3.04, "end_time_s": 4.24, "words": [{"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3040, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3240, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3360, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3520, "text": "表"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3680, "text": "示"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3760, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 3920, "text": "事"}], "keyword": "龚子华"}, {"subtitle_id": 4, "text": "并劝父亲不必动怒。", "timestamp": "00:00:04,240 --> 00:00:05,860", "duration": 1.62, "char_count": 9, "start_time_s": 4.24, "end_time_s": 5.86, "words": [{"attribute": {"event": "speech"}, "end_time": 4440, "start_time": 4240, "text": "并"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4480, "text": "劝"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4640, "text": "父"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4800, "text": "亲"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 4920, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5120, "text": "必"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5240, "text": "动"}, {"attribute": {"event": "speech"}, "end_time": 5860, "start_time": 5400, "text": "怒"}], "keyword": "动怒"}], "keywords": ["嬴政", "儿子", "龚子华", "动怒"]}, {"chapter": 37, "story_board": "赢政感慨儿子临危不乱，像极了自己年轻时的样子。饭后，赢政询问密道的来历。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "坐在桌旁回应父亲", "expression": "平静"}, {"name": "护卫队长", "gender": "男", "age": "中年", "clothes": "高领秦代战甲，主色调为黑色，搭配红色和金色，甲片上有虎纹浮雕，头戴黑色兜鍪，腰系黑色革带，配有长剑圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "魁梧", "identity": "东宫护卫队长", "other": "", "from_chapter": [0], "action": "一声令下", "expression": "威严"}, {"name": "郎中令尹成", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服长袍，主色调为褐色，搭配浅黄色和深棕色，衣服上有几何纹样，头戴黑色进贤冠，腰束褐色腰带，挂有印章圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "适中", "identity": "郎中令", "other": "", "from_chapter": [0], "action": "与他人一起吃早饭", "expression": "严肃"}, {"name": "顿弱", "gender": "男", "age": "中年", "clothes": "高领圆领浅蓝色秦国大臣朝服，主色调为浅蓝色，搭配淡蓝色和白色，袍身有波浪纹图案，腰间束蓝色丝带，佩戴朝珠，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "偏胖", "identity": "秦国大臣", "other": "能言善辩", "from_chapter": 13, "action": "设立监牢关押涉案人员", "expression": "严肃"}, {"name": "赵高", "gender": "男", "age": "中年", "clothes": "高领圆领秦代囚服，主色调为白色，搭配灰色，衣服上有囚字标记，头发凌乱，无配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发凌乱", "figure": "瘦削", "identity": "阴谋刺杀者", "other": "阴险狡诈", "from_chapter": [0], "action": "被押入狱中，拍着门大喊，眼睁睁看着", "expression": "愤怒、绝望"}, {"name": "秦王政（赢政）", "gender": "男", "age": "中年", "clothes": "高领圆领秦代龙袍，主色调为黄色，搭配金色和红色，袍身绣有五爪金龙图案，头戴冕旒，腰束黄色玉带，佩有玉玺圆形衣领，圆形衣领", "hairstyle": "黑色长发束起头戴冕旒", "figure": "高大", "identity": "秦国国王", "other": "有威严", "from_chapter": [0], "action": "坐在桌旁，一脸感慨地看着公子华，随后询问情况", "expression": "欣慰"}, {"name": "相里腾", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服，主色调为褐色，搭配深灰色，有几何纹图案，腰系黑色革带，挂有铜牌圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "为公子华送工匠的人", "other": "办事利落", "from_chapter": 16, "action": "", "expression": ""}, {"name": "崔永", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服短衣，主色调为灰色，搭配白色，无明显花纹，系黑色腰带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精瘦", "identity": "公子华的下属", "other": "做事利落", "from_chapter": 7, "action": "", "expression": ""}, {"name": "医家张琰", "gender": "男", "age": "老年", "clothes": "高领圆领秦代医服长袍，主色调为青色，搭配淡蓝色和白色，衣身有草药图案，头戴青色方巾，腰系青色腰带，配有药囊圆形衣领，圆形衣领", "hairstyle": "白色长发束起", "figure": "清瘦", "identity": "医家", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "淳于越", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为深绿色，搭配白色和黑色，有方格花纹，腰间束布腰带，头戴黑色方巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "秦国大臣", "other": "", "from_chapter": 19, "action": "", "expression": ""}], "scene": "古代宫殿", "image_prompt": "中景，在古代宫殿里，坐在桌旁、一脸欣慰且感慨的中年男子看着对面的青年男子，青年男子表情平静地坐着。中年男子高大威严，头戴冕旒，黑色长发束起，身着高领圆领秦代龙袍，主色调为黄色，搭配金色和红色，袍身绣有五爪金龙图案，腰束黄色玉带，佩有玉玺，有圆形衣领；青年男子挺拔，黑色束发，身着高领圆领紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，有圆形衣领, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753199928_20250722_235849.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_70c37f20-878b-4e2d-b3ed-b130cd12fc6d_1.25x_20250722_195602.wav", "audio_duration": 6.493333, "video_url": "", "story_board_id": 14, "subtitle": [{"subtitle_id": 1, "text": "嬴政感慨儿子临危不乱，", "timestamp": "00:00:00,200 --> 00:00:02,000", "duration": 1.8, "char_count": 11, "start_time_s": 0.2, "end_time_s": 2.0, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 440, "text": "感"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 640, "text": "慨"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 920, "text": "儿"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1120, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1240, "text": "临"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1360, "text": "危"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1520, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1640, "text": "乱"}], "keyword": "嬴政"}, {"subtitle_id": 2, "text": "像极了自己年轻时的样子。", "timestamp": "00:00:02,000 --> 00:00:04,020", "duration": 2.02, "char_count": 12, "start_time_s": 2.0, "end_time_s": 4.02, "words": [{"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2000, "text": "像"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2200, "text": "极"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2360, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2440, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2600, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2720, "text": "年"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 2920, "text": "轻"}, {"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3120, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3280, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3400, "text": "样"}, {"attribute": {"event": "speech"}, "end_time": 4020, "start_time": 3560, "text": "子"}], "keyword": "自己"}, {"subtitle_id": 3, "text": "饭后，嬴政询问密道的来历。", "timestamp": "00:00:04,120 --> 00:00:06,420", "duration": 2.3, "char_count": 13, "start_time_s": 4.12, "end_time_s": 6.42, "words": [{"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4120, "text": "饭"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4320, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4720, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 4920, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5120, "text": "询"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5240, "text": "问"}, {"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5400, "text": "密"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5560, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5720, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5840, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 6420, "start_time": 6000, "text": "历"}], "keyword": "密道"}], "keywords": ["嬴政", "自己", "密道"]}, {"chapter": 37, "story_board": "公子华告诉父亲，这是秦昭襄王时期修建的，当年宣太后摄政时，为了应对突发情况而建。后来秦王政从邯郸回来时也发现了这条密道，赵高正是那时候知道了它的存在。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站在父亲面前，神情认真地讲述", "expression": "严肃"}, {"name": "护卫队长", "gender": "男", "age": "中年", "clothes": "高领秦代战甲，主色调为黑色，搭配红色和金色，甲片上有虎纹浮雕，头戴黑色兜鍪，腰系黑色革带，配有长剑圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "魁梧", "identity": "东宫护卫队长", "other": "", "from_chapter": [0], "action": "一声令下", "expression": "威严"}, {"name": "郎中令尹成", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服长袍，主色调为褐色，搭配浅黄色和深棕色，衣服上有几何纹样，头戴黑色进贤冠，腰束褐色腰带，挂有印章圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "适中", "identity": "郎中令", "other": "", "from_chapter": [0], "action": "与他人一起吃早饭", "expression": "严肃"}, {"name": "顿弱", "gender": "男", "age": "中年", "clothes": "高领圆领浅蓝色秦国大臣朝服，主色调为浅蓝色，搭配淡蓝色和白色，袍身有波浪纹图案，腰间束蓝色丝带，佩戴朝珠，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "偏胖", "identity": "秦国大臣", "other": "能言善辩", "from_chapter": 13, "action": "设立监牢关押涉案人员", "expression": "严肃"}, {"name": "赵高", "gender": "男", "age": "中年", "clothes": "高领圆领秦代囚服，主色调为白色，搭配灰色，衣服上有囚字标记，头发凌乱，无配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发凌乱", "figure": "瘦削", "identity": "阴谋刺杀者", "other": "阴险狡诈", "from_chapter": [0], "action": "被押入狱中，拍着门大喊，眼睁睁看着", "expression": "愤怒、绝望"}, {"name": "秦王政（赢政）", "gender": "男", "age": "中年", "clothes": "高领圆领秦代龙袍，主色调为黄色，搭配金色和红色，袍身绣有五爪金龙图案，头戴冕旒，腰束黄色玉带，佩有玉玺圆形衣领，圆形衣领", "hairstyle": "黑色长发束起头戴冕旒", "figure": "高大", "identity": "秦国国王", "other": "有威严", "from_chapter": [0], "action": "站着倾听，微微点头", "expression": "沉思"}, {"name": "相里腾", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服，主色调为褐色，搭配深灰色，有几何纹图案，腰系黑色革带，挂有铜牌圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "为公子华送工匠的人", "other": "办事利落", "from_chapter": 16, "action": "", "expression": ""}, {"name": "崔永", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服短衣，主色调为灰色，搭配白色，无明显花纹，系黑色腰带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精瘦", "identity": "公子华的下属", "other": "做事利落", "from_chapter": 7, "action": "", "expression": ""}, {"name": "医家张琰", "gender": "男", "age": "老年", "clothes": "高领圆领秦代医服长袍，主色调为青色，搭配淡蓝色和白色，衣身有草药图案，头戴青色方巾，腰系青色腰带，配有药囊圆形衣领，圆形衣领", "hairstyle": "白色长发束起", "figure": "清瘦", "identity": "医家", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "淳于越", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为深绿色，搭配白色和黑色，有方格花纹，腰间束布腰带，头戴黑色方巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "秦国大臣", "other": "", "from_chapter": 19, "action": "", "expression": ""}], "scene": "古代宫殿", "image_prompt": "中景，站在中年男子面前神情认真且严肃地讲述着的青年男子，穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发；一旁站着倾听并微微点头、处于沉思状态的中年男子，穿着高领圆领秦代龙袍，主色调为黄色，搭配金色和红色，袍身绣有五爪金龙图案，头戴冕旒，腰束黄色玉带，佩有玉玺，圆形衣领，黑色长发束起头戴冕旒，两人身后是古代宫殿, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753200411_20250723_000652.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_e79cbecc-9675-4aa4-87ef-1e7c8aabe575_1.25x_20250722_195606.wav", "audio_duration": 12.773333, "video_url": "", "story_board_id": 15, "subtitle": [{"subtitle_id": 1, "text": "龚子华告诉父亲，", "timestamp": "00:00:00,160 --> 00:00:01,360", "duration": 1.2, "char_count": 8, "start_time_s": 0.16, "end_time_s": 1.36, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 400, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 560, "text": "告"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 720, "text": "诉"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 840, "text": "父"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1000, "text": "亲"}], "keyword": "龚子华"}, {"subtitle_id": 2, "text": "这是秦昭襄王时期修建的，", "timestamp": "00:00:01,360 --> 00:00:03,360", "duration": 2.0, "char_count": 12, "start_time_s": 1.36, "end_time_s": 3.36, "words": [{"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1360, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1520, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1680, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1840, "text": "昭"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2000, "text": "襄"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2160, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2320, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2440, "text": "期"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2640, "text": "修"}, {"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2800, "text": "建"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 2960, "text": "的"}], "keyword": "秦昭襄王"}, {"subtitle_id": 3, "text": "当年宣太后摄政时，", "timestamp": "00:00:03,360 --> 00:00:04,840", "duration": 1.48, "char_count": 9, "start_time_s": 3.36, "end_time_s": 4.84, "words": [{"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3360, "text": "当"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3520, "text": "年"}, {"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3640, "text": "宣"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3840, "text": "太"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4000, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4160, "text": "摄"}, {"attribute": {"event": "speech"}, "end_time": 4440, "start_time": 4320, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4440, "text": "时"}], "keyword": "宣太后"}, {"subtitle_id": 4, "text": "为了应对突发情况而建。", "timestamp": "00:00:04,840 --> 00:00:06,860", "duration": 2.02, "char_count": 11, "start_time_s": 4.84, "end_time_s": 6.86, "words": [{"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4840, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 5040, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5120, "text": "应"}, {"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5280, "text": "对"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5440, "text": "突"}, {"attribute": {"event": "speech"}, "end_time": 5760, "start_time": 5600, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5760, "text": "情"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5960, "text": "况"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6160, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 6860, "start_time": 6400, "text": "建"}], "keyword": "密道"}, {"subtitle_id": 5, "text": "后来秦王政从邯郸回来时", "timestamp": "00:00:06,960 --> 00:00:08,640", "duration": 1.68, "char_count": 11, "start_time_s": 6.96, "end_time_s": 8.64, "words": [{"attribute": {"event": "speech"}, "end_time": 7120, "start_time": 6960, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7120, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 7360, "start_time": 7240, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 7520, "start_time": 7360, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7520, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7680, "text": "从"}, {"attribute": {"event": "speech"}, "end_time": 8000, "start_time": 7840, "text": "邯"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 8000, "text": "郸"}, {"attribute": {"event": "speech"}, "end_time": 8280, "start_time": 8120, "text": "回"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8280, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8400, "text": "时"}], "keyword": "秦王政"}, {"subtitle_id": 6, "text": "也发现了这条密道，", "timestamp": "00:00:08,640 --> 00:00:10,000", "duration": 1.36, "char_count": 9, "start_time_s": 8.64, "end_time_s": 10.0, "words": [{"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8640, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8800, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 9120, "start_time": 9000, "text": "现"}, {"attribute": {"event": "speech"}, "end_time": 9200, "start_time": 9120, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 9320, "start_time": 9200, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 9480, "start_time": 9320, "text": "条"}, {"attribute": {"event": "speech"}, "end_time": 9600, "start_time": 9480, "text": "密"}, {"attribute": {"event": "speech"}, "end_time": 10000, "start_time": 9600, "text": "道"}], "keyword": "密道"}, {"subtitle_id": 7, "text": "赵高正是那时候知道了他的存在。", "timestamp": "00:00:10,000 --> 00:00:12,420", "duration": 2.42, "char_count": 15, "start_time_s": 10.0, "end_time_s": 12.42, "words": [{"attribute": {"event": "speech"}, "end_time": 10160, "start_time": 10000, "text": "赵"}, {"attribute": {"event": "speech"}, "end_time": 10360, "start_time": 10160, "text": "高"}, {"attribute": {"event": "speech"}, "end_time": 10480, "start_time": 10360, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 10600, "start_time": 10480, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 10760, "start_time": 10600, "text": "那"}, {"attribute": {"event": "speech"}, "end_time": 10880, "start_time": 10760, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 11040, "start_time": 10880, "text": "候"}, {"attribute": {"event": "speech"}, "end_time": 11320, "start_time": 11120, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 11440, "start_time": 11320, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 11560, "start_time": 11440, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 11680, "start_time": 11560, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 11800, "start_time": 11680, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 12000, "start_time": 11800, "text": "存"}, {"attribute": {"event": "speech"}, "end_time": 12420, "start_time": 12000, "text": "在"}], "keyword": "赵高"}], "keywords": ["龚子华", "秦昭襄王", "宣太后", "密道", "秦王政", "密道", "赵高"]}, {"chapter": 37, "story_board": "赢政点头，表示会派人加强防守。公子华则开始着手处理赵高的案件，他命令相里腾打造特制的针管和小刀，又让崔永购买水银、羊皮，还安排侍卫在渭水河畔放蜂蚁，为接下来的行动做准备。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "开始安排事务，命令相里腾打造特制针管和小刀，让崔永购买水银、羊皮，安排侍卫在渭水河畔放蜂蚁", "expression": "沉稳"}, {"name": "护卫队长", "gender": "男", "age": "中年", "clothes": "高领秦代战甲，主色调为黑色，搭配红色和金色，甲片上有虎纹浮雕，头戴黑色兜鍪，腰系黑色革带，配有长剑圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "魁梧", "identity": "东宫护卫队长", "other": "", "from_chapter": [0], "action": "一声令下", "expression": "威严"}, {"name": "郎中令尹成", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服长袍，主色调为褐色，搭配浅黄色和深棕色，衣服上有几何纹样，头戴黑色进贤冠，腰束褐色腰带，挂有印章圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "适中", "identity": "郎中令", "other": "", "from_chapter": [0], "action": "与他人一起吃早饭", "expression": "严肃"}, {"name": "顿弱", "gender": "男", "age": "中年", "clothes": "高领圆领浅蓝色秦国大臣朝服，主色调为浅蓝色，搭配淡蓝色和白色，袍身有波浪纹图案，腰间束蓝色丝带，佩戴朝珠，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "偏胖", "identity": "秦国大臣", "other": "能言善辩", "from_chapter": 13, "action": "设立监牢关押涉案人员", "expression": "严肃"}, {"name": "赵高", "gender": "男", "age": "中年", "clothes": "高领圆领秦代囚服，主色调为白色，搭配灰色，衣服上有囚字标记，头发凌乱，无配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发凌乱", "figure": "瘦削", "identity": "阴谋刺杀者", "other": "阴险狡诈", "from_chapter": [0], "action": "被押入狱中，拍着门大喊，眼睁睁看着", "expression": "愤怒、绝望"}, {"name": "秦王政（赢政）", "gender": "男", "age": "中年", "clothes": "高领圆领秦代龙袍，主色调为黄色，搭配金色和红色，袍身绣有五爪金龙图案，头戴冕旒，腰束黄色玉带，佩有玉玺圆形衣领，圆形衣领", "hairstyle": "黑色长发束起头戴冕旒", "figure": "高大", "identity": "秦国国王", "other": "有威严", "from_chapter": [0], "action": "点头", "expression": "严肃"}, {"name": "相里腾", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服，主色调为褐色，搭配深灰色，有几何纹图案，腰系黑色革带，挂有铜牌圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "为公子华送工匠的人", "other": "办事利落", "from_chapter": 16, "action": "聆听公子华的命令", "expression": "认真"}, {"name": "崔永", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服短衣，主色调为灰色，搭配白色，无明显花纹，系黑色腰带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精瘦", "identity": "公子华的下属", "other": "做事利落", "from_chapter": 7, "action": "", "expression": ""}, {"name": "医家张琰", "gender": "男", "age": "老年", "clothes": "高领圆领秦代医服长袍，主色调为青色，搭配淡蓝色和白色，衣身有草药图案，头戴青色方巾，腰系青色腰带，配有药囊圆形衣领，圆形衣领", "hairstyle": "白色长发束起", "figure": "清瘦", "identity": "医家", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "淳于越", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为深绿色，搭配白色和黑色，有方格花纹，腰间束布腰带，头戴黑色方巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "秦国大臣", "other": "", "from_chapter": 19, "action": "", "expression": ""}], "scene": "古代宫殿", "image_prompt": "中景，在古代宫殿中，一位严肃的中年男子点头站着，旁边一位挺拔的青年男子沉稳地开始安排事务，还有一位壮实的中年男子认真聆听。站着点头的中年男子身材高大，有威严，头戴冕旒，黑色长发束起，身穿高领圆领秦代龙袍，主色调为黄色，搭配金色和红色，袍身绣有五爪金龙图案，腰束黄色玉带，佩有玉玺，圆形衣领；安排事务的青年男子黑色束发，身穿高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领；聆听命令的中年男子黑色束发，身穿高领圆领秦代官服，主色调为褐色，搭配深灰色，有几何纹图案，腰系黑色革带，挂有铜牌，圆形衣领, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753200741_20250723_001221.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_76cce9a8-de65-411d-a525-6f4818c0a9c1_1.25x_20250722_195620.wav", "audio_duration": 13.789333, "video_url": "", "story_board_id": 16, "subtitle": [{"subtitle_id": 1, "text": "嬴政点头表示会派人加强防守。", "timestamp": "00:00:00,200 --> 00:00:02,780", "duration": 2.58, "char_count": 14, "start_time_s": 0.2, "end_time_s": 2.78, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 200, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 320, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 480, "text": "点"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 640, "text": "头"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1040, "text": "表"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1200, "text": "示"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1320, "text": "会"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1480, "text": "派"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1680, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1800, "text": "加"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 2000, "text": "强"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2120, "text": "防"}, {"attribute": {"event": "speech"}, "end_time": 2780, "start_time": 2320, "text": "守"}], "keyword": "嬴政"}, {"subtitle_id": 2, "text": "龚子华则开始着手处理赵高的案件，", "timestamp": "00:00:02,880 --> 00:00:05,280", "duration": 2.4, "char_count": 16, "start_time_s": 2.88, "end_time_s": 5.28, "words": [{"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2880, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3040, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3200, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3360, "text": "则"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3480, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3640, "text": "始"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3760, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3920, "text": "手"}, {"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4080, "text": "处"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4240, "text": "理"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4360, "text": "赵"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4520, "text": "高"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4720, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4800, "text": "案"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 4960, "text": "件"}], "keyword": "龚子华"}, {"subtitle_id": 3, "text": "他命令", "timestamp": "00:00:05,280 --> 00:00:05,640", "duration": 0.36, "char_count": 3, "start_time_s": 5.28, "end_time_s": 5.64, "words": [{"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5280, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5440, "text": "命"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5520, "text": "令"}], "keyword": "命令"}, {"subtitle_id": 4, "text": "向李腾打造特制的针管和小刀，", "timestamp": "00:00:05,640 --> 00:00:08,040", "duration": 2.4, "char_count": 14, "start_time_s": 5.64, "end_time_s": 8.04, "words": [{"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5640, "text": "向"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5800, "text": "李"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5960, "text": "腾"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6200, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6400, "text": "造"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6560, "text": "特"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6760, "text": "制"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6880, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7120, "start_time": 7000, "text": "针"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7160, "text": "管"}, {"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7320, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 7640, "start_time": 7440, "text": "小"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7640, "text": "刀"}], "keyword": "针管"}, {"subtitle_id": 5, "text": "又让崔永购买水银羊皮，", "timestamp": "00:00:08,040 --> 00:00:09,720", "duration": 1.68, "char_count": 11, "start_time_s": 8.04, "end_time_s": 9.72, "words": [{"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8040, "text": "又"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8200, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8360, "text": "崔"}, {"attribute": {"event": "speech"}, "end_time": 8600, "start_time": 8480, "text": "永"}, {"attribute": {"event": "speech"}, "end_time": 8760, "start_time": 8600, "text": "购"}, {"attribute": {"event": "speech"}, "end_time": 8920, "start_time": 8760, "text": "买"}, {"attribute": {"event": "speech"}, "end_time": 9040, "start_time": 8920, "text": "水"}, {"attribute": {"event": "speech"}, "end_time": 9160, "start_time": 9080, "text": "银"}, {"attribute": {"event": "speech"}, "end_time": 9320, "start_time": 9160, "text": "羊"}, {"attribute": {"event": "speech"}, "end_time": 9720, "start_time": 9360, "text": "皮"}], "keyword": "水银羊皮"}, {"subtitle_id": 6, "text": "还安排侍卫在渭水河畔放风椅，", "timestamp": "00:00:09,720 --> 00:00:11,880", "duration": 2.16, "char_count": 14, "start_time_s": 9.72, "end_time_s": 11.88, "words": [{"attribute": {"event": "speech"}, "end_time": 9880, "start_time": 9720, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 10000, "start_time": 9880, "text": "安"}, {"attribute": {"event": "speech"}, "end_time": 10160, "start_time": 10000, "text": "排"}, {"attribute": {"event": "speech"}, "end_time": 10320, "start_time": 10200, "text": "侍"}, {"attribute": {"event": "speech"}, "end_time": 10440, "start_time": 10320, "text": "卫"}, {"attribute": {"event": "speech"}, "end_time": 10640, "start_time": 10440, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 10800, "start_time": 10640, "text": "渭"}, {"attribute": {"event": "speech"}, "end_time": 10920, "start_time": 10800, "text": "水"}, {"attribute": {"event": "speech"}, "end_time": 11080, "start_time": 10920, "text": "河"}, {"attribute": {"event": "speech"}, "end_time": 11240, "start_time": 11080, "text": "畔"}, {"attribute": {"event": "speech"}, "end_time": 11440, "start_time": 11280, "text": "放"}, {"attribute": {"event": "speech"}, "end_time": 11600, "start_time": 11440, "text": "风"}, {"attribute": {"event": "speech"}, "end_time": 11880, "start_time": 11600, "text": "椅"}], "keyword": "渭水河畔"}, {"subtitle_id": 7, "text": "为接下来的行动做准备。", "timestamp": "00:00:11,880 --> 00:00:13,700", "duration": 1.82, "char_count": 11, "start_time_s": 11.88, "end_time_s": 13.7, "words": [{"attribute": {"event": "speech"}, "end_time": 12080, "start_time": 11880, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 12280, "start_time": 12120, "text": "接"}, {"attribute": {"event": "speech"}, "end_time": 12440, "start_time": 12280, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 12520, "start_time": 12440, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 12640, "start_time": 12520, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 12760, "start_time": 12640, "text": "行"}, {"attribute": {"event": "speech"}, "end_time": 12920, "start_time": 12760, "text": "动"}, {"attribute": {"event": "speech"}, "end_time": 13080, "start_time": 12920, "text": "做"}, {"attribute": {"event": "speech"}, "end_time": 13240, "start_time": 13080, "text": "准"}, {"attribute": {"event": "speech"}, "end_time": 13700, "start_time": 13240, "text": "备"}], "keyword": "行动"}], "keywords": ["嬴政", "龚子华", "命令", "针管", "水银羊皮", "渭水河畔", "行动"]}, {"chapter": 37, "story_board": "与此同时，咸阳城内议论纷纷。医家张琰与淳于越的冲突刚刚平息，赵高刺杀公子华的消息却再次引发轰动，老秦人对赵高的行为极为愤怒。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "开始安排事务，命令相里腾打造特制针管和小刀，让崔永购买水银、羊皮，安排侍卫在渭水河畔放蜂蚁", "expression": "沉稳"}, {"name": "护卫队长", "gender": "男", "age": "中年", "clothes": "高领秦代战甲，主色调为黑色，搭配红色和金色，甲片上有虎纹浮雕，头戴黑色兜鍪，腰系黑色革带，配有长剑圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "魁梧", "identity": "东宫护卫队长", "other": "", "from_chapter": [0], "action": "一声令下", "expression": "威严"}, {"name": "郎中令尹成", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服长袍，主色调为褐色，搭配浅黄色和深棕色，衣服上有几何纹样，头戴黑色进贤冠，腰束褐色腰带，挂有印章圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "适中", "identity": "郎中令", "other": "", "from_chapter": [0], "action": "与他人一起吃早饭", "expression": "严肃"}, {"name": "顿弱", "gender": "男", "age": "中年", "clothes": "高领圆领浅蓝色秦国大臣朝服，主色调为浅蓝色，搭配淡蓝色和白色，袍身有波浪纹图案，腰间束蓝色丝带，佩戴朝珠，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "偏胖", "identity": "秦国大臣", "other": "能言善辩", "from_chapter": 13, "action": "设立监牢关押涉案人员", "expression": "严肃"}, {"name": "赵高", "gender": "男", "age": "中年", "clothes": "高领圆领秦代囚服，主色调为白色，搭配灰色，衣服上有囚字标记，头发凌乱，无配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发凌乱", "figure": "瘦削", "identity": "阴谋刺杀者", "other": "阴险狡诈", "from_chapter": [0], "action": "被押入狱中，拍着门大喊，眼睁睁看着", "expression": "愤怒、绝望"}, {"name": "秦王政（赢政）", "gender": "男", "age": "中年", "clothes": "高领圆领秦代龙袍，主色调为黄色，搭配金色和红色，袍身绣有五爪金龙图案，头戴冕旒，腰束黄色玉带，佩有玉玺圆形衣领，圆形衣领", "hairstyle": "黑色长发束起头戴冕旒", "figure": "高大", "identity": "秦国国王", "other": "有威严", "from_chapter": [0], "action": "点头", "expression": "严肃"}, {"name": "相里腾", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服，主色调为褐色，搭配深灰色，有几何纹图案，腰系黑色革带，挂有铜牌圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "为公子华送工匠的人", "other": "办事利落", "from_chapter": 16, "action": "聆听公子华的命令", "expression": "认真"}, {"name": "崔永", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服短衣，主色调为灰色，搭配白色，无明显花纹，系黑色腰带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精瘦", "identity": "公子华的下属", "other": "做事利落", "from_chapter": 7, "action": "", "expression": ""}, {"name": "医家张琰", "gender": "男", "age": "老年", "clothes": "高领圆领秦代医服长袍，主色调为青色，搭配淡蓝色和白色，衣身有草药图案，头戴青色方巾，腰系青色腰带，配有药囊圆形衣领，圆形衣领", "hairstyle": "白色长发束起", "figure": "清瘦", "identity": "医家", "other": "", "from_chapter": [0], "action": "站在人群中听议论", "expression": "严肃"}, {"name": "淳于越", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为深绿色，搭配白色和黑色，有方格花纹，腰间束布腰带，头戴黑色方巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "秦国大臣", "other": "", "from_chapter": 19, "action": "站在一旁，微微皱眉听着", "expression": "忧虑"}], "scene": "古代庭院", "image_prompt": "中景，古代庭院里，一位严肃的老年男子站在人群中倾听，一位微微皱眉、神情忧虑的中年男子站在一旁倾听。站在人群中的老年男子清瘦，白色长发束起，头戴青色方巾，身穿高领圆领秦代医服长袍，主色调为青色，搭配淡蓝色和白色，衣身有草药图案，腰系青色腰带，配有药囊，圆形衣领；站在一旁的中年男子黑色束发，头戴黑色方巾，身穿高领圆领汉服长袍，主色调为深绿色，搭配白色和黑色，有方格花纹，腰间束布腰带，圆形衣领, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753201108_20250723_001829.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_1072d240-89aa-46a1-b78f-fca237b9347f_1.25x_20250722_195624.wav", "audio_duration": 10.946667, "video_url": "", "story_board_id": 17, "subtitle": [{"subtitle_id": 1, "text": "与此同时，", "timestamp": "00:00:00,200 --> 00:00:00,960", "duration": 0.76, "char_count": 5, "start_time_s": 0.2, "end_time_s": 0.96, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "与"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "此"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 400, "text": "同"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 520, "text": "时"}], "keyword": "同时"}, {"subtitle_id": 2, "text": "咸阳城内议论纷纷，", "timestamp": "00:00:00,960 --> 00:00:02,580", "duration": 1.62, "char_count": 9, "start_time_s": 0.96, "end_time_s": 2.58, "words": [{"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 960, "text": "咸"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1080, "text": "阳"}, {"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1240, "text": "城"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1440, "text": "内"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1600, "text": "议"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1840, "text": "论"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1960, "text": "纷"}, {"attribute": {"event": "speech"}, "end_time": 2580, "start_time": 2000, "text": "纷"}], "keyword": "咸阳城"}, {"subtitle_id": 3, "text": "一家张衍与淳于月的冲突刚刚平息，", "timestamp": "00:00:02,720 --> 00:00:05,280", "duration": 2.56, "char_count": 16, "start_time_s": 2.72, "end_time_s": 5.28, "words": [{"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2720, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2920, "text": "家"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3080, "text": "张"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3240, "text": "衍"}, {"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3480, "text": "与"}, {"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3680, "text": "淳"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3800, "text": "于"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3920, "text": "月"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4040, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4160, "text": "冲"}, {"attribute": {"event": "speech"}, "end_time": 4440, "start_time": 4280, "text": "突"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4480, "text": "刚"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4520, "text": "刚"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4760, "text": "平"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 4960, "text": "息"}], "keyword": "张衍淳于"}, {"subtitle_id": 4, "text": "赵高刺杀龚子华的消息", "timestamp": "00:00:05,280 --> 00:00:06,920", "duration": 1.64, "char_count": 10, "start_time_s": 5.28, "end_time_s": 6.92, "words": [{"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5280, "text": "赵"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5480, "text": "高"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5680, "text": "刺"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5840, "text": "杀"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5960, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6120, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6280, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6400, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6520, "text": "消"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6680, "text": "息"}], "keyword": "赵高刺杀"}, {"subtitle_id": 5, "text": "却再次引发轰动，", "timestamp": "00:00:06,920 --> 00:00:08,320", "duration": 1.4, "char_count": 8, "start_time_s": 6.92, "end_time_s": 8.32, "words": [{"attribute": {"event": "speech"}, "end_time": 7120, "start_time": 6920, "text": "却"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7120, "text": "再"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7360, "text": "次"}, {"attribute": {"event": "speech"}, "end_time": 7640, "start_time": 7480, "text": "引"}, {"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7680, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7840, "text": "轰"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8000, "text": "动"}], "keyword": "轰动"}, {"subtitle_id": 6, "text": "老情人对赵高的行为极为愤怒。", "timestamp": "00:00:08,320 --> 00:00:10,620", "duration": 2.3, "char_count": 14, "start_time_s": 8.32, "end_time_s": 10.62, "words": [{"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8320, "text": "老"}, {"attribute": {"event": "speech"}, "end_time": 8600, "start_time": 8480, "text": "情"}, {"attribute": {"event": "speech"}, "end_time": 8720, "start_time": 8600, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 8880, "start_time": 8720, "text": "对"}, {"attribute": {"event": "speech"}, "end_time": 9080, "start_time": 8920, "text": "赵"}, {"attribute": {"event": "speech"}, "end_time": 9240, "start_time": 9080, "text": "高"}, {"attribute": {"event": "speech"}, "end_time": 9360, "start_time": 9240, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 9480, "start_time": 9360, "text": "行"}, {"attribute": {"event": "speech"}, "end_time": 9640, "start_time": 9480, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 9840, "start_time": 9680, "text": "极"}, {"attribute": {"event": "speech"}, "end_time": 9960, "start_time": 9840, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 10160, "start_time": 10000, "text": "愤"}, {"attribute": {"event": "speech"}, "end_time": 10620, "start_time": 10160, "text": "怒"}], "keyword": "老情人"}], "keywords": ["同时", "咸阳城", "张衍淳于", "赵高刺杀", "轰动", "老情人"]}, {"chapter": 37, "story_board": "这场暗杀虽然未遂，却让整个朝廷风声鹤唳。公子华不仅成功识破阴谋，还借此机会铲除了赵高及其党羽，进一步巩固了自己的地位。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "坐在宫殿中，双手抱臂", "expression": "自信从容"}, {"name": "护卫队长", "gender": "男", "age": "中年", "clothes": "高领秦代战甲，主色调为黑色，搭配红色和金色，甲片上有虎纹浮雕，头戴黑色兜鍪，腰系黑色革带，配有长剑圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "魁梧", "identity": "东宫护卫队长", "other": "", "from_chapter": [0], "action": "一声令下", "expression": "威严"}, {"name": "郎中令尹成", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服长袍，主色调为褐色，搭配浅黄色和深棕色，衣服上有几何纹样，头戴黑色进贤冠，腰束褐色腰带，挂有印章圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "适中", "identity": "郎中令", "other": "", "from_chapter": [0], "action": "与他人一起吃早饭", "expression": "严肃"}, {"name": "顿弱", "gender": "男", "age": "中年", "clothes": "高领圆领浅蓝色秦国大臣朝服，主色调为浅蓝色，搭配淡蓝色和白色，袍身有波浪纹图案，腰间束蓝色丝带，佩戴朝珠，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "偏胖", "identity": "秦国大臣", "other": "能言善辩", "from_chapter": 13, "action": "设立监牢关押涉案人员", "expression": "严肃"}, {"name": "赵高", "gender": "男", "age": "中年", "clothes": "高领圆领秦代囚服，主色调为白色，搭配灰色，衣服上有囚字标记，头发凌乱，无配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发凌乱", "figure": "瘦削", "identity": "阴谋刺杀者", "other": "阴险狡诈", "from_chapter": [0], "action": "被押入狱中，拍着门大喊，眼睁睁看着", "expression": "愤怒、绝望"}, {"name": "秦王政（赢政）", "gender": "男", "age": "中年", "clothes": "高领圆领秦代龙袍，主色调为黄色，搭配金色和红色，袍身绣有五爪金龙图案，头戴冕旒，腰束黄色玉带，佩有玉玺圆形衣领，圆形衣领", "hairstyle": "黑色长发束起头戴冕旒", "figure": "高大", "identity": "秦国国王", "other": "有威严", "from_chapter": [0], "action": "点头", "expression": "严肃"}, {"name": "相里腾", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服，主色调为褐色，搭配深灰色，有几何纹图案，腰系黑色革带，挂有铜牌圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "为公子华送工匠的人", "other": "办事利落", "from_chapter": 16, "action": "聆听公子华的命令", "expression": "认真"}, {"name": "崔永", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服短衣，主色调为灰色，搭配白色，无明显花纹，系黑色腰带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精瘦", "identity": "公子华的下属", "other": "做事利落", "from_chapter": 7, "action": "", "expression": ""}, {"name": "医家张琰", "gender": "男", "age": "老年", "clothes": "高领圆领秦代医服长袍，主色调为青色，搭配淡蓝色和白色，衣身有草药图案，头戴青色方巾，腰系青色腰带，配有药囊圆形衣领，圆形衣领", "hairstyle": "白色长发束起", "figure": "清瘦", "identity": "医家", "other": "", "from_chapter": [0], "action": "站在人群中听议论", "expression": "严肃"}, {"name": "淳于越", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为深绿色，搭配白色和黑色，有方格花纹，腰间束布腰带，头戴黑色方巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "秦国大臣", "other": "", "from_chapter": 19, "action": "站在一旁，微微皱眉听着", "expression": "忧虑"}], "scene": "古代宫殿", "image_prompt": "中景，坐在古代宫殿中的青年男子，双手抱臂，表情自信从容，他穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，有着圆形衣领，黑色束发，身姿挺拔, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753201414_20250723_002335.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_4d0aa4d1-7492-4ec9-b4a5-4b6d62c88cad_1.25x_20250722_195629.wav", "audio_duration": 10.066667, "video_url": "", "story_board_id": 18, "subtitle": [{"subtitle_id": 1, "text": "这场暗杀虽然未遂，", "timestamp": "00:00:00,200 --> 00:00:01,640", "duration": 1.44, "char_count": 9, "start_time_s": 0.2, "end_time_s": 1.64, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 200, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "场"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 440, "text": "暗"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 640, "text": "杀"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 840, "text": "虽"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 920, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1040, "text": "未"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1240, "text": "遂"}], "keyword": "暗杀"}, {"subtitle_id": 2, "text": "却让整个朝廷风声鹤唳。", "timestamp": "00:00:01,640 --> 00:00:03,660", "duration": 2.02, "char_count": 11, "start_time_s": 1.64, "end_time_s": 3.66, "words": [{"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1640, "text": "却"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1800, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1920, "text": "整"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2120, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2320, "text": "朝"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2520, "text": "廷"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2680, "text": "风"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2840, "text": "声"}, {"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 3000, "text": "鹤"}, {"attribute": {"event": "speech"}, "end_time": 3660, "start_time": 3160, "text": "唳"}], "keyword": "朝廷"}, {"subtitle_id": 3, "text": "龚子华不仅成功识破阴谋，", "timestamp": "00:00:03,720 --> 00:00:05,520", "duration": 1.8, "char_count": 12, "start_time_s": 3.72, "end_time_s": 5.52, "words": [{"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3720, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3880, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4000, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4160, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 4440, "start_time": 4320, "text": "仅"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4440, "text": "成"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4600, "text": "功"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4760, "text": "识"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4920, "text": "破"}, {"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5080, "text": "阴"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5200, "text": "谋"}], "keyword": "龚子华"}, {"subtitle_id": 4, "text": "还借此机会铲除了赵高及其党羽，", "timestamp": "00:00:05,520 --> 00:00:07,960", "duration": 2.44, "char_count": 15, "start_time_s": 5.52, "end_time_s": 7.96, "words": [{"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5520, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5720, "text": "借"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5840, "text": "此"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5960, "text": "机"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6120, "text": "会"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6360, "text": "铲"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6560, "text": "除"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6720, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6800, "text": "赵"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 7000, "text": "高"}, {"attribute": {"event": "speech"}, "end_time": 7360, "start_time": 7200, "text": "及"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7360, "text": "其"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7480, "text": "党"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7640, "text": "羽"}], "keyword": "赵高"}, {"subtitle_id": 5, "text": "进一步巩固了自己的地位。", "timestamp": "00:00:07,960 --> 00:00:09,740", "duration": 1.78, "char_count": 12, "start_time_s": 7.96, "end_time_s": 9.74, "words": [{"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 7960, "text": "进"}, {"attribute": {"event": "speech"}, "end_time": 8240, "start_time": 8120, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8240, "text": "步"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8360, "text": "巩"}, {"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8560, "text": "固"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8680, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8800, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 9080, "start_time": 8960, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 9160, "start_time": 9080, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 9320, "start_time": 9160, "text": "地"}, {"attribute": {"event": "speech"}, "end_time": 9740, "start_time": 9320, "text": "位"}], "keyword": "巩固"}], "keywords": ["暗杀", "朝廷", "龚子华", "赵高", "巩固"]}, {"chapter": 37, "story_board": "他冷静沉稳的表现，也让秦王政对他更加信任。这场惊心动魄的夜晚，最终以公子华的胜利告终。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站着，双手背后，表情平静地安排事务", "expression": "冷静沉稳"}, {"name": "护卫队长", "gender": "男", "age": "中年", "clothes": "高领秦代战甲，主色调为黑色，搭配红色和金色，甲片上有虎纹浮雕，头戴黑色兜鍪，腰系黑色革带，配有长剑圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "魁梧", "identity": "东宫护卫队长", "other": "", "from_chapter": [0], "action": "一声令下", "expression": "威严"}, {"name": "郎中令尹成", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服长袍，主色调为褐色，搭配浅黄色和深棕色，衣服上有几何纹样，头戴黑色进贤冠，腰束褐色腰带，挂有印章圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "适中", "identity": "郎中令", "other": "", "from_chapter": [0], "action": "与他人一起吃早饭", "expression": "严肃"}, {"name": "顿弱", "gender": "男", "age": "中年", "clothes": "高领圆领浅蓝色秦国大臣朝服，主色调为浅蓝色，搭配淡蓝色和白色，袍身有波浪纹图案，腰间束蓝色丝带，佩戴朝珠，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "偏胖", "identity": "秦国大臣", "other": "能言善辩", "from_chapter": 13, "action": "设立监牢关押涉案人员", "expression": "严肃"}, {"name": "赵高", "gender": "男", "age": "中年", "clothes": "高领圆领秦代囚服，主色调为白色，搭配灰色，衣服上有囚字标记，头发凌乱，无配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发凌乱", "figure": "瘦削", "identity": "阴谋刺杀者", "other": "阴险狡诈", "from_chapter": [0], "action": "被押入狱中，拍着门大喊，眼睁睁看着", "expression": "愤怒、绝望"}, {"name": "秦王政（赢政）", "gender": "男", "age": "中年", "clothes": "高领圆领秦代龙袍，主色调为黄色，搭配金色和红色，袍身绣有五爪金龙图案，头戴冕旒，腰束黄色玉带，佩有玉玺圆形衣领，圆形衣领", "hairstyle": "黑色长发束起头戴冕旒", "figure": "高大", "identity": "秦国国王", "other": "有威严", "from_chapter": [0], "action": "走上前，拍拍公子华的肩膀", "expression": "欣慰信任"}, {"name": "相里腾", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服，主色调为褐色，搭配深灰色，有几何纹图案，腰系黑色革带，挂有铜牌圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "为公子华送工匠的人", "other": "办事利落", "from_chapter": 16, "action": "聆听公子华的命令", "expression": "认真"}, {"name": "崔永", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服短衣，主色调为灰色，搭配白色，无明显花纹，系黑色腰带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精瘦", "identity": "公子华的下属", "other": "做事利落", "from_chapter": 7, "action": "", "expression": ""}, {"name": "医家张琰", "gender": "男", "age": "老年", "clothes": "高领圆领秦代医服长袍，主色调为青色，搭配淡蓝色和白色，衣身有草药图案，头戴青色方巾，腰系青色腰带，配有药囊圆形衣领，圆形衣领", "hairstyle": "白色长发束起", "figure": "清瘦", "identity": "医家", "other": "", "from_chapter": [0], "action": "站在人群中听议论", "expression": "严肃"}, {"name": "淳于越", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为深绿色，搭配白色和黑色，有方格花纹，腰间束布腰带，头戴黑色方巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "秦国大臣", "other": "", "from_chapter": 19, "action": "站在一旁，微微皱眉听着", "expression": "忧虑"}], "scene": "古代宫殿", "image_prompt": "中景，古代宫殿背景下，一位中年男子走上前，拍拍年轻男子的肩膀，表情欣慰信任；年轻男子双手背后站着，表情平静地安排事务，冷静沉稳。中年男子高大有威严，穿着高领圆领秦代龙袍，主色调为黄色，搭配金色和红色，袍身绣有五爪金龙图案，头戴冕旒，腰束黄色玉带，佩有玉玺，黑色长发束起；年轻男子挺拔，穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753201704_20250723_002825.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_1d5702ed-91fd-4464-a95b-564937c8dda5_1.25x_20250722_195612.wav", "audio_duration": 7.589333, "video_url": "", "story_board_id": 19, "subtitle": [{"subtitle_id": 1, "text": "他冷静沉稳的表现，", "timestamp": "00:00:00,200 --> 00:00:01,520", "duration": 1.32, "char_count": 9, "start_time_s": 0.2, "end_time_s": 1.52, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "冷"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 440, "text": "静"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 600, "text": "沉"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 800, "text": "稳"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 920, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1000, "text": "表"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1200, "text": "现"}], "keyword": "他"}, {"subtitle_id": 2, "text": "也让秦王政对他更加信任。", "timestamp": "00:00:01,520 --> 00:00:03,700", "duration": 2.18, "char_count": 12, "start_time_s": 1.52, "end_time_s": 3.7, "words": [{"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1520, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1680, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1840, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2000, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2160, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2400, "text": "对"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2560, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2720, "text": "更"}, {"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2920, "text": "加"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3120, "text": "信"}, {"attribute": {"event": "speech"}, "end_time": 3700, "start_time": 3240, "text": "任"}], "keyword": "秦王政"}, {"subtitle_id": 3, "text": "这场惊心动魄的夜晚，", "timestamp": "00:00:03,760 --> 00:00:05,520", "duration": 1.76, "char_count": 10, "start_time_s": 3.76, "end_time_s": 5.52, "words": [{"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3760, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 3960, "text": "场"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4200, "text": "惊"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4360, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4560, "text": "动"}, {"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4720, "text": "魄"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4840, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 4960, "text": "夜"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5120, "text": "晚"}], "keyword": "夜晚"}, {"subtitle_id": 4, "text": "最终以龚子华的胜利告终。", "timestamp": "00:00:05,520 --> 00:00:07,500", "duration": 1.98, "char_count": 12, "start_time_s": 5.52, "end_time_s": 7.5, "words": [{"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5520, "text": "最"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5720, "text": "终"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5840, "text": "以"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5960, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6160, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 6480, "start_time": 6320, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6480, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6640, "text": "胜"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6760, "text": "利"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6880, "text": "告"}, {"attribute": {"event": "speech"}, "end_time": 7500, "start_time": 7080, "text": "终"}], "keyword": "龚子华"}], "keywords": ["他", "秦王政", "夜晚", "龚子华"]}, {"chapter": 37, "story_board": "他不仅保住了性命，还抓住了一个潜在的威胁，为未来埋下了伏笔。而赵高，注定要在历史的舞台上走向毁灭。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "双手抱臂，神情镇定", "expression": "自信"}, {"name": "护卫队长", "gender": "男", "age": "中年", "clothes": "高领秦代战甲，主色调为黑色，搭配红色和金色，甲片上有虎纹浮雕，头戴黑色兜鍪，腰系黑色革带，配有长剑圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "魁梧", "identity": "东宫护卫队长", "other": "", "from_chapter": [0], "action": "一声令下", "expression": "威严"}, {"name": "郎中令尹成", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服长袍，主色调为褐色，搭配浅黄色和深棕色，衣服上有几何纹样，头戴黑色进贤冠，腰束褐色腰带，挂有印章圆形衣领，圆形衣领", "hairstyle": "黑色长发束起", "figure": "适中", "identity": "郎中令", "other": "", "from_chapter": [0], "action": "与他人一起吃早饭", "expression": "严肃"}, {"name": "顿弱", "gender": "男", "age": "中年", "clothes": "高领圆领浅蓝色秦国大臣朝服，主色调为浅蓝色，搭配淡蓝色和白色，袍身有波浪纹图案，腰间束蓝色丝带，佩戴朝珠，头戴黑色官帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "偏胖", "identity": "秦国大臣", "other": "能言善辩", "from_chapter": 13, "action": "设立监牢关押涉案人员", "expression": "严肃"}, {"name": "赵高", "gender": "男", "age": "中年", "clothes": "高领圆领秦代囚服，主色调为白色，搭配灰色，衣服上有囚字标记，头发凌乱，无配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发凌乱", "figure": "瘦削", "identity": "阴谋刺杀者", "other": "阴险狡诈", "from_chapter": [0], "action": "被狱卒押着，不断挣扎", "expression": "绝望"}, {"name": "秦王政（赢政）", "gender": "男", "age": "中年", "clothes": "高领圆领秦代龙袍，主色调为黄色，搭配金色和红色，袍身绣有五爪金龙图案，头戴冕旒，腰束黄色玉带，佩有玉玺圆形衣领，圆形衣领", "hairstyle": "黑色长发束起头戴冕旒", "figure": "高大", "identity": "秦国国王", "other": "有威严", "from_chapter": [0], "action": "走上前，拍拍公子华的肩膀", "expression": "欣慰信任"}, {"name": "相里腾", "gender": "男", "age": "中年", "clothes": "高领圆领秦代官服，主色调为褐色，搭配深灰色，有几何纹图案，腰系黑色革带，挂有铜牌圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "壮实", "identity": "为公子华送工匠的人", "other": "办事利落", "from_chapter": 16, "action": "聆听公子华的命令", "expression": "认真"}, {"name": "崔永", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服短衣，主色调为灰色，搭配白色，无明显花纹，系黑色腰带，头戴黑色布帽圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精瘦", "identity": "公子华的下属", "other": "做事利落", "from_chapter": 7, "action": "", "expression": ""}, {"name": "医家张琰", "gender": "男", "age": "老年", "clothes": "高领圆领秦代医服长袍，主色调为青色，搭配淡蓝色和白色，衣身有草药图案，头戴青色方巾，腰系青色腰带，配有药囊圆形衣领，圆形衣领", "hairstyle": "白色长发束起", "figure": "清瘦", "identity": "医家", "other": "", "from_chapter": [0], "action": "站在人群中听议论", "expression": "严肃"}, {"name": "淳于越", "gender": "男", "age": "中年", "clothes": "高领圆领汉服长袍，主色调为深绿色，搭配白色和黑色，有方格花纹，腰间束布腰带，头戴黑色方巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "正常", "identity": "秦国大臣", "other": "", "from_chapter": 19, "action": "站在一旁，微微皱眉听着", "expression": "忧虑"}], "scene": "古代监狱", "image_prompt": "中景，画面为古代监狱，双手抱臂、神情镇定自信的青年男子站着，旁边被狱卒押着不断挣扎、满脸绝望的中年男子在一旁。青年男子挺拔，身着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，黑色束发；中年男子瘦削，穿着高领圆领秦代囚服，主色调为白色，搭配灰色，衣服上有囚字标记，头发凌乱，无配饰，黑色短发凌乱，动漫分镜插图风格, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753201960_20250723_003241.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_4b4081ba-c49a-4e70-9b64-9e9c9aaf5075_1.25x_20250722_195616.wav", "audio_duration": 8.24, "video_url": "", "story_board_id": 20, "subtitle": [{"subtitle_id": 1, "text": "他不仅保住了性命，", "timestamp": "00:00:00,200 --> 00:00:01,360", "duration": 1.16, "char_count": 9, "start_time_s": 0.2, "end_time_s": 1.36, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 400, "text": "仅"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 520, "text": "保"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 720, "text": "住"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 840, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 920, "text": "性"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1080, "text": "命"}], "keyword": "他"}, {"subtitle_id": 2, "text": "还抓住了一个潜在的威胁，", "timestamp": "00:00:01,360 --> 00:00:03,120", "duration": 1.76, "char_count": 12, "start_time_s": 1.36, "end_time_s": 3.12, "words": [{"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1360, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1560, "text": "抓"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1720, "text": "住"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1800, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1880, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 2000, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2160, "text": "潜"}, {"attribute": {"event": "speech"}, "end_time": 2520, "start_time": 2360, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2520, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2600, "text": "威"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 2760, "text": "胁"}], "keyword": "威胁"}, {"subtitle_id": 3, "text": "为未来埋下了伏笔。", "timestamp": "00:00:03,120 --> 00:00:04,740", "duration": 1.62, "char_count": 9, "start_time_s": 3.12, "end_time_s": 4.74, "words": [{"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3120, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3440, "text": "未"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3480, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3640, "text": "埋"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3880, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 4000, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4080, "text": "伏"}, {"attribute": {"event": "speech"}, "end_time": 4740, "start_time": 4280, "text": "笔"}], "keyword": "伏笔"}, {"subtitle_id": 4, "text": "而赵高，", "timestamp": "00:00:04,840 --> 00:00:05,560", "duration": 0.72, "char_count": 4, "start_time_s": 4.84, "end_time_s": 5.56, "words": [{"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4840, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5040, "text": "赵"}, {"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5200, "text": "高"}], "keyword": "赵高"}, {"subtitle_id": 5, "text": "注定要在历史的舞台上走向毁灭。", "timestamp": "00:00:05,560 --> 00:00:08,180", "duration": 2.62, "char_count": 15, "start_time_s": 5.56, "end_time_s": 8.18, "words": [{"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5560, "text": "注"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5760, "text": "定"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5840, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6000, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6160, "text": "历"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6320, "text": "史"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6440, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6560, "text": "舞"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6680, "text": "台"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6880, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7160, "text": "走"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7360, "text": "向"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7520, "text": "毁"}, {"attribute": {"event": "speech"}, "end_time": 8180, "start_time": 7720, "text": "灭"}], "keyword": "毁灭"}], "keywords": ["他", "威胁", "伏笔", "赵高", "毁灭"]}]