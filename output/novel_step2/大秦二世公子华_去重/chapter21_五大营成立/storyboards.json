[{"chapter": 21, "story_board": "清晨的阳光刚洒进庭院，一声怒喝划破宁静，一个黑衣人突然冲进院子，手中握着一把闪着寒光的短刀。他直奔屋内，嘴里喊着：“公子快逃！”可等他冲到门口时，却发现屋里空无一人。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "", "expression": ""}, {"name": "黑衣人", "gender": "男", "age": "青年", "clothes": "高领圆领劲装，主色调为黑色，搭配深褐色，衣服有暗纹，脚穿黑色靴子，腰系黑色腰带，无过多配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "瘦削敏捷", "identity": "公子华的护卫", "other": "忠诚勇敢", "from_chapter": [0], "action": "手握短刀冲进院子，直奔屋内，冲到门口愣住，转身逃跑", "expression": "惊恐"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "", "expression": ""}, {"name": "龙且", "gender": "男", "age": "青年", "clothes": "高领圆领秦朝黑色古代战甲，配红色腰带，主色调为黑色和红色，战甲上有简单的几何花纹，头戴黑色头盔，腰带挂短刀圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "健壮", "identity": "士兵", "other": "刀法娴熟，动作利落", "from_chapter": 28, "action": "", "expression": ""}, {"name": "英布", "gender": "男", "age": "青年", "clothes": "高领圆领灰色秦代长袍，脚蹬黑色靴子，主色调为灰色，搭配白色，长袍有淡蓝色水纹，头戴灰色毡帽，腰系灰色腰带圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "公子华的心腹", "other": "常低声说话", "from_chapter": 121, "action": "", "expression": ""}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "叔孙通", "gender": "男", "age": "中年", "clothes": "高领圆领蓝色齐国长袍，主色调为蓝色，搭配浅蓝色和白色，衣服上有波浪纹图案，腰间束蓝色丝带，配蓝色玉佩，头戴蓝色束发帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "消瘦", "identity": "公子华安插在齐国的棋子", "other": "深谙人心，擅长挑拨离间", "from_chapter": 91, "action": "", "expression": ""}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": 1, "action": "", "expression": ""}], "scene": "室外", "image_prompt": "中景，一个青年男子手握短刀冲进院子，直奔屋内，冲到门口愣住，随后转身逃跑，脸上露出惊恐的表情，室外场景。该男子穿着高领圆领劲装，主色调为黑色，搭配深褐色，衣服有暗纹，脚穿黑色靴子，腰系黑色腰带，无过多配饰，圆形衣领，留着黑色短发，身形瘦削敏捷, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241076_20250723_112437.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_7ff0ac6c-1f87-4f3f-a810-158d76935dc0_1.25x_20250723_105614.wav", "audio_duration": 14.712, "video_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/videos/video_1753241099_20250723_112500.mp4", "story_board_id": 1, "subtitle": [{"subtitle_id": 1, "text": "清晨的阳光刚洒进庭院，", "timestamp": "00:00:00,200 --> 00:00:02,000", "duration": 1.8, "char_count": 11, "start_time_s": 0.2, "end_time_s": 2.0, "words": [{"attribute": {"event": "speech"}, "end_time": 360, "start_time": 200, "text": "清"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 360, "text": "晨"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 520, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 600, "text": "阳"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 720, "text": "光"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 920, "text": "刚"}, {"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 1160, "text": "洒"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1320, "text": "进"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1400, "text": "庭"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1640, "text": "院"}], "keyword": "清晨"}, {"subtitle_id": 2, "text": "医生怒喝划破宁静。", "timestamp": "00:00:02,000 --> 00:00:03,480", "duration": 1.48, "char_count": 9, "start_time_s": 2.0, "end_time_s": 3.48, "words": [{"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2000, "text": "医"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2160, "text": "生"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2280, "text": "怒"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2440, "text": "喝"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2640, "text": "划"}, {"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2840, "text": "破"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 2960, "text": "宁"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3160, "text": "静"}], "keyword": "医生"}, {"subtitle_id": 3, "text": "一个黑衣人突然冲进院子，", "timestamp": "00:00:03,480 --> 00:00:05,280", "duration": 1.8, "char_count": 12, "start_time_s": 3.48, "end_time_s": 5.28, "words": [{"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3480, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3640, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3760, "text": "黑"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3880, "text": "衣"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4000, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4200, "text": "突"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4360, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4520, "text": "冲"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4680, "text": "进"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4800, "text": "院"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5000, "text": "子"}], "keyword": "黑衣人"}, {"subtitle_id": 4, "text": "手中握着一把闪着寒光的短刀。", "timestamp": "00:00:05,280 --> 00:00:07,660", "duration": 2.38, "char_count": 14, "start_time_s": 5.28, "end_time_s": 7.66, "words": [{"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5280, "text": "手"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5440, "text": "中"}, {"attribute": {"event": "speech"}, "end_time": 5760, "start_time": 5600, "text": "握"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5760, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5880, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6000, "text": "把"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6200, "text": "闪"}, {"attribute": {"event": "speech"}, "end_time": 6480, "start_time": 6360, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6480, "text": "寒"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6720, "text": "光"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6840, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 7000, "text": "短"}, {"attribute": {"event": "speech"}, "end_time": 7660, "start_time": 7160, "text": "刀"}], "keyword": "短刀"}, {"subtitle_id": 5, "text": "他直奔屋内，", "timestamp": "00:00:07,760 --> 00:00:08,720", "duration": 0.96, "char_count": 6, "start_time_s": 7.76, "end_time_s": 8.72, "words": [{"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7760, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 8160, "start_time": 7960, "text": "直"}, {"attribute": {"event": "speech"}, "end_time": 8280, "start_time": 8160, "text": "奔"}, {"attribute": {"event": "speech"}, "end_time": 8440, "start_time": 8320, "text": "屋"}, {"attribute": {"event": "speech"}, "end_time": 8720, "start_time": 8440, "text": "内"}], "keyword": "屋内"}, {"subtitle_id": 6, "text": "嘴里喊着公子，", "timestamp": "00:00:08,720 --> 00:00:10,000", "duration": 1.28, "char_count": 7, "start_time_s": 8.72, "end_time_s": 10.0, "words": [{"attribute": {"event": "speech"}, "end_time": 8920, "start_time": 8720, "text": "嘴"}, {"attribute": {"event": "speech"}, "end_time": 9040, "start_time": 8920, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 9200, "start_time": 9040, "text": "喊"}, {"attribute": {"event": "speech"}, "end_time": 9440, "start_time": 9240, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 9760, "start_time": 9560, "text": "公"}, {"attribute": {"event": "speech"}, "end_time": 10000, "start_time": 9840, "text": "子"}], "keyword": "公子"}, {"subtitle_id": 7, "text": "快逃！可等他冲到门口时，", "timestamp": "00:00:10,000 --> 00:00:12,440", "duration": 2.44, "char_count": 12, "start_time_s": 10.0, "end_time_s": 12.44, "words": [{"attribute": {"event": "speech"}, "end_time": 10200, "start_time": 10000, "text": "快"}, {"attribute": {"event": "speech"}, "end_time": 10400, "start_time": 10200, "text": "逃"}, {"attribute": {"event": "speech"}, "end_time": 11080, "start_time": 10880, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 11200, "start_time": 11080, "text": "等"}, {"attribute": {"event": "speech"}, "end_time": 11320, "start_time": 11200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 11480, "start_time": 11360, "text": "冲"}, {"attribute": {"event": "speech"}, "end_time": 11640, "start_time": 11480, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 11800, "start_time": 11640, "text": "门"}, {"attribute": {"event": "speech"}, "end_time": 12000, "start_time": 11800, "text": "口"}, {"attribute": {"event": "speech"}, "end_time": 12440, "start_time": 12000, "text": "时"}], "keyword": "逃"}, {"subtitle_id": 8, "text": "却发现屋里空无一人。", "timestamp": "00:00:12,440 --> 00:00:14,380", "duration": 1.94, "char_count": 10, "start_time_s": 12.44, "end_time_s": 14.38, "words": [{"attribute": {"event": "speech"}, "end_time": 12600, "start_time": 12440, "text": "却"}, {"attribute": {"event": "speech"}, "end_time": 12800, "start_time": 12600, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 12960, "start_time": 12800, "text": "现"}, {"attribute": {"event": "speech"}, "end_time": 13160, "start_time": 13000, "text": "屋"}, {"attribute": {"event": "speech"}, "end_time": 13320, "start_time": 13160, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 13520, "start_time": 13320, "text": "空"}, {"attribute": {"event": "speech"}, "end_time": 13760, "start_time": 13520, "text": "无"}, {"attribute": {"event": "speech"}, "end_time": 13920, "start_time": 13760, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 14380, "start_time": 13920, "text": "人"}], "keyword": "屋里"}], "keywords": ["清晨", "医生", "黑衣人", "短刀", "屋内", "公子", "逃", "屋里"]}, {"chapter": 21, "story_board": "原来，公子华早已察觉异样，提前离开了。黑衣人愣在原地，脸上写满惊恐，转身就跑，但没跑出几步，就被埋伏已久的护卫一剑刺穿。这是发生在公子华身边的第一个危机，也是他崛起路上的第一道坎。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "提前离开屋子", "expression": "警惕"}, {"name": "黑衣人", "gender": "男", "age": "青年", "clothes": "高领圆领劲装，主色调为黑色，搭配深褐色，衣服有暗纹，脚穿黑色靴子，腰系黑色腰带，无过多配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "瘦削敏捷", "identity": "公子华的护卫", "other": "忠诚勇敢", "from_chapter": [0], "action": "愣在原地后转身逃跑，被护卫一剑刺穿", "expression": "惊恐"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "", "expression": ""}, {"name": "龙且", "gender": "男", "age": "青年", "clothes": "高领圆领秦朝黑色古代战甲，配红色腰带，主色调为黑色和红色，战甲上有简单的几何花纹，头戴黑色头盔，腰带挂短刀圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "健壮", "identity": "士兵", "other": "刀法娴熟，动作利落", "from_chapter": 28, "action": "", "expression": ""}, {"name": "英布", "gender": "男", "age": "青年", "clothes": "高领圆领灰色秦代长袍，脚蹬黑色靴子，主色调为灰色，搭配白色，长袍有淡蓝色水纹，头戴灰色毡帽，腰系灰色腰带圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "公子华的心腹", "other": "常低声说话", "from_chapter": 121, "action": "", "expression": ""}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "叔孙通", "gender": "男", "age": "中年", "clothes": "高领圆领蓝色齐国长袍，主色调为蓝色，搭配浅蓝色和白色，衣服上有波浪纹图案，腰间束蓝色丝带，配蓝色玉佩，头戴蓝色束发帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "消瘦", "identity": "公子华安插在齐国的棋子", "other": "深谙人心，擅长挑拨离间", "from_chapter": 91, "action": "", "expression": ""}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": 1, "action": "", "expression": ""}], "scene": "室外", "image_prompt": "中景，室外，一个警惕的青年男子提前离开屋子，挺拔身姿，神色警觉。不远处一个瘦削敏捷的青年男子先是愣在原地，随后转身逃跑，脸上满是惊恐，被护卫一剑刺穿。离开屋子的青年男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发。逃跑的青年男子穿着高领圆领劲装，主色调为黑色，搭配深褐色，衣服有暗纹，脚穿黑色靴子，腰系黑色腰带，无过多配饰，圆形衣领，黑色短发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241116_20250723_112518.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_1ad39b2e-8fc5-4024-86cf-a10bd85ba81b_1.25x_20250723_105548.wav", "audio_duration": 14.538667, "video_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/videos/video_1753241144_20250723_112545.mp4", "story_board_id": 2, "subtitle": [{"subtitle_id": 1, "text": "原来，龚子华早已察觉异样，", "timestamp": "00:00:00,160 --> 00:00:02,040", "duration": 1.88, "char_count": 13, "start_time_s": 0.16, "end_time_s": 2.04, "words": [{"attribute": {"event": "speech"}, "end_time": 240, "start_time": 160, "text": "原"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 240, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 640, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 800, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 920, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1080, "text": "早"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1240, "text": "已"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1360, "text": "察"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1560, "text": "觉"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1720, "text": "异"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1800, "text": "样"}], "keyword": "龚子华"}, {"subtitle_id": 2, "text": "提前离开了。", "timestamp": "00:00:02,040 --> 00:00:03,340", "duration": 1.3, "char_count": 6, "start_time_s": 2.04, "end_time_s": 3.34, "words": [{"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2040, "text": "提"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2320, "text": "前"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2440, "text": "离"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2640, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 3340, "start_time": 2800, "text": "了"}], "keyword": "离开"}, {"subtitle_id": 3, "text": "黑衣人愣在原地，", "timestamp": "00:00:03,400 --> 00:00:04,640", "duration": 1.24, "char_count": 8, "start_time_s": 3.4, "end_time_s": 4.64, "words": [{"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3400, "text": "黑"}, {"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3600, "text": "衣"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3680, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3840, "text": "愣"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 3960, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4120, "text": "原"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4280, "text": "地"}], "keyword": "黑衣人"}, {"subtitle_id": 4, "text": "脸上写满惊恐，", "timestamp": "00:00:04,640 --> 00:00:05,760", "duration": 1.12, "char_count": 7, "start_time_s": 4.64, "end_time_s": 5.76, "words": [{"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4640, "text": "脸"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4800, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 4960, "text": "写"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5120, "text": "满"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5280, "text": "惊"}, {"attribute": {"event": "speech"}, "end_time": 5760, "start_time": 5440, "text": "恐"}], "keyword": "惊恐"}, {"subtitle_id": 5, "text": "转身就跑，", "timestamp": "00:00:05,760 --> 00:00:06,680", "duration": 0.92, "char_count": 5, "start_time_s": 5.76, "end_time_s": 6.68, "words": [{"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5760, "text": "转"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6000, "text": "身"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6160, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6320, "text": "跑"}], "keyword": "跑"}, {"subtitle_id": 6, "text": "但没跑出几步，", "timestamp": "00:00:06,680 --> 00:00:07,800", "duration": 1.12, "char_count": 7, "start_time_s": 6.68, "end_time_s": 7.8, "words": [{"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6680, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6840, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 7000, "text": "跑"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7160, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7320, "text": "几"}, {"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7480, "text": "步"}], "keyword": "几步"}, {"subtitle_id": 7, "text": "就被埋伏已久的护卫一剑刺穿。", "timestamp": "00:00:07,800 --> 00:00:10,140", "duration": 2.34, "char_count": 14, "start_time_s": 7.8, "end_time_s": 10.14, "words": [{"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7800, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7960, "text": "被"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8080, "text": "埋"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8240, "text": "伏"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8360, "text": "已"}, {"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8520, "text": "久"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8680, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 8920, "start_time": 8800, "text": "护"}, {"attribute": {"event": "speech"}, "end_time": 9120, "start_time": 8920, "text": "卫"}, {"attribute": {"event": "speech"}, "end_time": 9320, "start_time": 9120, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 9440, "start_time": 9320, "text": "剑"}, {"attribute": {"event": "speech"}, "end_time": 9640, "start_time": 9440, "text": "刺"}, {"attribute": {"event": "speech"}, "end_time": 10140, "start_time": 9640, "text": "穿"}], "keyword": "护卫"}, {"subtitle_id": 8, "text": "这", "timestamp": "00:00:10,240 --> 00:00:10,440", "duration": 0.2, "char_count": 1, "start_time_s": 10.24, "end_time_s": 10.44, "words": [{"attribute": {"event": "speech"}, "end_time": 10440, "start_time": 10240, "text": "这"}], "keyword": "这"}, {"subtitle_id": 9, "text": "是发生在龚子华身边的第一个危机，", "timestamp": "00:00:10,440 --> 00:00:12,520", "duration": 2.08, "char_count": 16, "start_time_s": 10.44, "end_time_s": 12.52, "words": [{"attribute": {"event": "speech"}, "end_time": 10520, "start_time": 10440, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 10680, "start_time": 10520, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 10800, "start_time": 10680, "text": "生"}, {"attribute": {"event": "speech"}, "end_time": 10920, "start_time": 10800, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 11080, "start_time": 10960, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 11200, "start_time": 11080, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 11360, "start_time": 11200, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 11480, "start_time": 11360, "text": "身"}, {"attribute": {"event": "speech"}, "end_time": 11640, "start_time": 11480, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 11760, "start_time": 11640, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 11880, "start_time": 11760, "text": "第"}, {"attribute": {"event": "speech"}, "end_time": 11920, "start_time": 11880, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 12040, "start_time": 11920, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 12200, "start_time": 12040, "text": "危"}, {"attribute": {"event": "speech"}, "end_time": 12520, "start_time": 12200, "text": "机"}], "keyword": "危机"}, {"subtitle_id": 10, "text": "也是他崛起路上的第一道坎。", "timestamp": "00:00:12,520 --> 00:00:14,460", "duration": 1.94, "char_count": 13, "start_time_s": 12.52, "end_time_s": 14.46, "words": [{"attribute": {"event": "speech"}, "end_time": 12680, "start_time": 12520, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 12800, "start_time": 12680, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 12920, "start_time": 12800, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 13160, "start_time": 12960, "text": "崛"}, {"attribute": {"event": "speech"}, "end_time": 13280, "start_time": 13160, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 13400, "start_time": 13280, "text": "路"}, {"attribute": {"event": "speech"}, "end_time": 13560, "start_time": 13440, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 13640, "start_time": 13560, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 13760, "start_time": 13640, "text": "第"}, {"attribute": {"event": "speech"}, "end_time": 13840, "start_time": 13760, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 13960, "start_time": 13840, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 14460, "start_time": 13960, "text": "坎"}], "keyword": "起点"}], "keywords": ["龚子华", "离开", "黑衣人", "惊恐", "跑", "几步", "护卫", "这", "危机", "起点"]}, {"chapter": 21, "story_board": "他不是靠权势上位，而是用智慧和胆识一步步打下自己的天下。他不仅聪明，还懂得用人，更知道如何让手下死心塌地跟着他。在一次酒宴上，公子华与乌倮氏谈笑风生，两人聊着茶、聊着盐、聊着未来。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "与乌倮氏坐在酒宴上交谈", "expression": "从容自信"}, {"name": "黑衣人", "gender": "男", "age": "青年", "clothes": "高领圆领劲装，主色调为黑色，搭配深褐色，衣服有暗纹，脚穿黑色靴子，腰系黑色腰带，无过多配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "瘦削敏捷", "identity": "公子华的护卫", "other": "忠诚勇敢", "from_chapter": [0], "action": "愣在原地后转身逃跑，被护卫一剑刺穿", "expression": "惊恐"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "与公子华坐在酒宴上交谈", "expression": "恭敬"}, {"name": "龙且", "gender": "男", "age": "青年", "clothes": "高领圆领秦朝黑色古代战甲，配红色腰带，主色调为黑色和红色，战甲上有简单的几何花纹，头戴黑色头盔，腰带挂短刀圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "健壮", "identity": "士兵", "other": "刀法娴熟，动作利落", "from_chapter": 28, "action": "", "expression": ""}, {"name": "英布", "gender": "男", "age": "青年", "clothes": "高领圆领灰色秦代长袍，脚蹬黑色靴子，主色调为灰色，搭配白色，长袍有淡蓝色水纹，头戴灰色毡帽，腰系灰色腰带圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "公子华的心腹", "other": "常低声说话", "from_chapter": 121, "action": "", "expression": ""}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "叔孙通", "gender": "男", "age": "中年", "clothes": "高领圆领蓝色齐国长袍，主色调为蓝色，搭配浅蓝色和白色，衣服上有波浪纹图案，腰间束蓝色丝带，配蓝色玉佩，头戴蓝色束发帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "消瘦", "identity": "公子华安插在齐国的棋子", "other": "深谙人心，擅长挑拨离间", "from_chapter": 91, "action": "", "expression": ""}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": 1, "action": "", "expression": ""}], "scene": "室内", "image_prompt": "中景，在室内的酒宴上，一位从容自信、挺拔的青年男子与一位恭敬、富态的中年男子相对而坐。青年男子言辞犀利，敢言善辩；中年男子则显得精明。青年男子身着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发。中年男子穿着高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子，圆形衣领，黑色束发。动漫分镜插图风格, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241163_20250723_112603.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_b22eb487-af38-435f-9c50-0fb1e4521f04_1.25x_20250723_105619.wav", "audio_duration": 15.250667, "video_url": "", "story_board_id": 3, "subtitle": [{"subtitle_id": 1, "text": "他不是靠权势上位，", "timestamp": "00:00:00,200 --> 00:00:01,600", "duration": 1.4, "char_count": 9, "start_time_s": 0.2, "end_time_s": 1.6, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 440, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 600, "text": "靠"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 800, "text": "权"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 1000, "text": "势"}, {"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 1120, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1280, "text": "位"}], "keyword": "权势"}, {"subtitle_id": 2, "text": "而是用智慧和胆识", "timestamp": "00:00:01,600 --> 00:00:03,040", "duration": 1.44, "char_count": 8, "start_time_s": 1.6, "end_time_s": 3.04, "words": [{"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1600, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1800, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1920, "text": "用"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2080, "text": "智"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2280, "text": "慧"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2440, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2560, "text": "胆"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2720, "text": "识"}], "keyword": "智慧"}, {"subtitle_id": 3, "text": "一步步打下自己的天下。", "timestamp": "00:00:03,040 --> 00:00:04,860", "duration": 1.82, "char_count": 11, "start_time_s": 3.04, "end_time_s": 4.86, "words": [{"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3040, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3280, "text": "步"}, {"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3320, "text": "步"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3520, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3720, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3840, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 4000, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4120, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4200, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 4860, "start_time": 4400, "text": "下"}], "keyword": "天下"}, {"subtitle_id": 4, "text": "他不仅聪明，", "timestamp": "00:00:04,960 --> 00:00:05,840", "duration": 0.88, "char_count": 6, "start_time_s": 4.96, "end_time_s": 5.84, "words": [{"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 4960, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5080, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5240, "text": "仅"}, {"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5400, "text": "聪"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5560, "text": "明"}], "keyword": "聪明"}, {"subtitle_id": 5, "text": "还懂得用人，", "timestamp": "00:00:05,840 --> 00:00:06,760", "duration": 0.92, "char_count": 6, "start_time_s": 5.84, "end_time_s": 6.76, "words": [{"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5840, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6000, "text": "懂"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6160, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6280, "text": "用"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6400, "text": "人"}], "keyword": "用人"}, {"subtitle_id": 6, "text": "更知道如何让手下死心塌地跟着他。", "timestamp": "00:00:06,760 --> 00:00:09,420", "duration": 2.66, "char_count": 16, "start_time_s": 6.76, "end_time_s": 9.42, "words": [{"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6760, "text": "更"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6960, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 7080, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 7360, "start_time": 7200, "text": "如"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7360, "text": "何"}, {"attribute": {"event": "speech"}, "end_time": 7640, "start_time": 7480, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7640, "text": "手"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7800, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 7960, "text": "死"}, {"attribute": {"event": "speech"}, "end_time": 8280, "start_time": 8120, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8280, "text": "塌"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8480, "text": "地"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8680, "text": "跟"}, {"attribute": {"event": "speech"}, "end_time": 8920, "start_time": 8840, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 9420, "start_time": 8920, "text": "他"}], "keyword": "死心塌地"}, {"subtitle_id": 7, "text": "在一次酒宴上，", "timestamp": "00:00:09,520 --> 00:00:10,720", "duration": 1.2, "char_count": 7, "start_time_s": 9.52, "end_time_s": 10.72, "words": [{"attribute": {"event": "speech"}, "end_time": 9760, "start_time": 9520, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 9880, "start_time": 9760, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 10000, "start_time": 9880, "text": "次"}, {"attribute": {"event": "speech"}, "end_time": 10160, "start_time": 10000, "text": "酒"}, {"attribute": {"event": "speech"}, "end_time": 10320, "start_time": 10200, "text": "宴"}, {"attribute": {"event": "speech"}, "end_time": 10720, "start_time": 10360, "text": "上"}], "keyword": "酒宴"}, {"subtitle_id": 8, "text": "公子华与乌罗氏谈笑风生，", "timestamp": "00:00:10,720 --> 00:00:12,800", "duration": 2.08, "char_count": 12, "start_time_s": 10.72, "end_time_s": 12.8, "words": [{"attribute": {"event": "speech"}, "end_time": 10920, "start_time": 10720, "text": "公"}, {"attribute": {"event": "speech"}, "end_time": 11040, "start_time": 10920, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 11240, "start_time": 11040, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 11360, "start_time": 11240, "text": "与"}, {"attribute": {"event": "speech"}, "end_time": 11520, "start_time": 11360, "text": "乌"}, {"attribute": {"event": "speech"}, "end_time": 11640, "start_time": 11520, "text": "罗"}, {"attribute": {"event": "speech"}, "end_time": 11760, "start_time": 11640, "text": "氏"}, {"attribute": {"event": "speech"}, "end_time": 11960, "start_time": 11800, "text": "谈"}, {"attribute": {"event": "speech"}, "end_time": 12160, "start_time": 12000, "text": "笑"}, {"attribute": {"event": "speech"}, "end_time": 12360, "start_time": 12200, "text": "风"}, {"attribute": {"event": "speech"}, "end_time": 12800, "start_time": 12360, "text": "生"}], "keyword": "公子华"}, {"subtitle_id": 9, "text": "两人聊着茶，", "timestamp": "00:00:12,800 --> 00:00:13,560", "duration": 0.76, "char_count": 6, "start_time_s": 12.8, "end_time_s": 13.56, "words": [{"attribute": {"event": "speech"}, "end_time": 12960, "start_time": 12800, "text": "两"}, {"attribute": {"event": "speech"}, "end_time": 13040, "start_time": 12960, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 13200, "start_time": 13040, "text": "聊"}, {"attribute": {"event": "speech"}, "end_time": 13320, "start_time": 13240, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 13560, "start_time": 13320, "text": "茶"}], "keyword": "茶"}, {"subtitle_id": 10, "text": "聊着盐，聊着未来。", "timestamp": "00:00:13,560 --> 00:00:14,940", "duration": 1.38, "char_count": 9, "start_time_s": 13.56, "end_time_s": 14.94, "words": [{"attribute": {"event": "speech"}, "end_time": 13680, "start_time": 13560, "text": "聊"}, {"attribute": {"event": "speech"}, "end_time": 13800, "start_time": 13720, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 14000, "start_time": 13800, "text": "盐"}, {"attribute": {"event": "speech"}, "end_time": 14200, "start_time": 14000, "text": "聊"}, {"attribute": {"event": "speech"}, "end_time": 14280, "start_time": 14200, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 14440, "start_time": 14280, "text": "未"}, {"attribute": {"event": "speech"}, "end_time": 14940, "start_time": 14440, "text": "来"}], "keyword": "盐"}], "keywords": ["权势", "智慧", "天下", "聪明", "用人", "死心塌地", "酒宴", "公子华", "茶", "盐"]}, {"chapter": 21, "story_board": "乌倮氏是个精明人，他知道公子华不是普通人，所以对他格外恭敬。而公子华也看中了他的能力，让他帮忙采购一种叫“白碟子”的植物种子，这东西在月氏那边很珍贵，能用来做丝绸。乌倮氏答应了，还夸赞公子华的茶好喝，说比他们那边的茶强多了。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "坐在桌前，手指点着桌面与乌倮氏交谈", "expression": "沉稳自信"}, {"name": "黑衣人", "gender": "男", "age": "青年", "clothes": "高领圆领劲装，主色调为黑色，搭配深褐色，衣服有暗纹，脚穿黑色靴子，腰系黑色腰带，无过多配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "瘦削敏捷", "identity": "公子华的护卫", "other": "忠诚勇敢", "from_chapter": [0], "action": "愣在原地后转身逃跑，被护卫一剑刺穿", "expression": "惊恐"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "身体微微前倾，恭敬地回应公子华", "expression": "谦逊"}, {"name": "龙且", "gender": "男", "age": "青年", "clothes": "高领圆领秦朝黑色古代战甲，配红色腰带，主色调为黑色和红色，战甲上有简单的几何花纹，头戴黑色头盔，腰带挂短刀圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "健壮", "identity": "士兵", "other": "刀法娴熟，动作利落", "from_chapter": 28, "action": "", "expression": ""}, {"name": "英布", "gender": "男", "age": "青年", "clothes": "高领圆领灰色秦代长袍，脚蹬黑色靴子，主色调为灰色，搭配白色，长袍有淡蓝色水纹，头戴灰色毡帽，腰系灰色腰带圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "公子华的心腹", "other": "常低声说话", "from_chapter": 121, "action": "", "expression": ""}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "叔孙通", "gender": "男", "age": "中年", "clothes": "高领圆领蓝色齐国长袍，主色调为蓝色，搭配浅蓝色和白色，衣服上有波浪纹图案，腰间束蓝色丝带，配蓝色玉佩，头戴蓝色束发帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "消瘦", "identity": "公子华安插在齐国的棋子", "other": "深谙人心，擅长挑拨离间", "from_chapter": 91, "action": "", "expression": ""}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": 1, "action": "", "expression": ""}], "scene": "室内", "image_prompt": "中景，室内，坐在桌前沉稳自信地用手指点着桌面的青年男子，身体微微前倾谦逊回应的中年男子。青年男子挺拔，穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发；中年男子富态，穿着高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241180_20250723_112620.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_8de9a916-3068-4b99-802b-5918f559f7f6_1.25x_20250723_105606.wav", "audio_duration": 17.784, "video_url": "", "story_board_id": 4, "subtitle": [{"subtitle_id": 1, "text": "乌罗氏是个精明人，", "timestamp": "00:00:00,160 --> 00:00:01,480", "duration": 1.32, "char_count": 9, "start_time_s": 0.16, "end_time_s": 1.48, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "乌"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "罗"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 440, "text": "氏"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 520, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 720, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 840, "text": "精"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 960, "text": "明"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1080, "text": "人"}], "keyword": "乌罗氏"}, {"subtitle_id": 2, "text": "他知道龚子华不是普通人，", "timestamp": "00:00:01,480 --> 00:00:03,240", "duration": 1.76, "char_count": 12, "start_time_s": 1.48, "end_time_s": 3.24, "words": [{"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1480, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1640, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1760, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1920, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2040, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2160, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2320, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2480, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2600, "text": "普"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2760, "text": "通"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 2880, "text": "人"}], "keyword": "龚子华"}, {"subtitle_id": 3, "text": "所以对他格外恭敬。", "timestamp": "00:00:03,240 --> 00:00:04,700", "duration": 1.46, "char_count": 9, "start_time_s": 3.24, "end_time_s": 4.7, "words": [{"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3240, "text": "所"}, {"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3360, "text": "以"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3440, "text": "对"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3560, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3760, "text": "格"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3960, "text": "外"}, {"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4120, "text": "恭"}, {"attribute": {"event": "speech"}, "end_time": 4700, "start_time": 4240, "text": "敬"}], "keyword": "恭敬"}, {"subtitle_id": 4, "text": "而龚子华也看中了他的能力，", "timestamp": "00:00:04,760 --> 00:00:06,680", "duration": 1.92, "char_count": 13, "start_time_s": 4.76, "end_time_s": 6.68, "words": [{"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4760, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 5000, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5120, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5280, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5440, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5600, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5800, "text": "中"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5920, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 6000, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 6120, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6240, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6360, "text": "力"}], "keyword": "龚子华"}, {"subtitle_id": 5, "text": "让他帮忙采购一种", "timestamp": "00:00:06,680 --> 00:00:07,880", "duration": 1.2, "char_count": 8, "start_time_s": 6.68, "end_time_s": 7.88, "words": [{"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6680, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6880, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 7120, "start_time": 7000, "text": "帮"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7120, "text": "忙"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7280, "text": "采"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7480, "text": "购"}, {"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7600, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 7880, "start_time": 7720, "text": "种"}], "keyword": "采购"}, {"subtitle_id": 6, "text": "叫白蝶子的植物种子，", "timestamp": "00:00:07,880 --> 00:00:09,460", "duration": 1.58, "char_count": 10, "start_time_s": 7.88, "end_time_s": 9.46, "words": [{"attribute": {"event": "speech"}, "end_time": 8000, "start_time": 7880, "text": "叫"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8040, "text": "白"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8240, "text": "蝶"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8400, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8520, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8640, "text": "植"}, {"attribute": {"event": "speech"}, "end_time": 8920, "start_time": 8800, "text": "物"}, {"attribute": {"event": "speech"}, "end_time": 9080, "start_time": 8920, "text": "种"}, {"attribute": {"event": "speech"}, "end_time": 9460, "start_time": 9080, "text": "子"}], "keyword": "白蝶子"}, {"subtitle_id": 7, "text": "这东西在月之那边很珍贵，", "timestamp": "00:00:09,480 --> 00:00:11,440", "duration": 1.96, "char_count": 12, "start_time_s": 9.48, "end_time_s": 11.44, "words": [{"attribute": {"event": "speech"}, "end_time": 9640, "start_time": 9480, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 9760, "start_time": 9640, "text": "东"}, {"attribute": {"event": "speech"}, "end_time": 9920, "start_time": 9760, "text": "西"}, {"attribute": {"event": "speech"}, "end_time": 10040, "start_time": 9920, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 10200, "start_time": 10040, "text": "月"}, {"attribute": {"event": "speech"}, "end_time": 10360, "start_time": 10240, "text": "之"}, {"attribute": {"event": "speech"}, "end_time": 10520, "start_time": 10360, "text": "那"}, {"attribute": {"event": "speech"}, "end_time": 10680, "start_time": 10560, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 10840, "start_time": 10680, "text": "很"}, {"attribute": {"event": "speech"}, "end_time": 11040, "start_time": 10880, "text": "珍"}, {"attribute": {"event": "speech"}, "end_time": 11440, "start_time": 11080, "text": "贵"}], "keyword": "月之边"}, {"subtitle_id": 8, "text": "能用来做丝绸。", "timestamp": "00:00:11,440 --> 00:00:12,780", "duration": 1.34, "char_count": 7, "start_time_s": 11.44, "end_time_s": 12.78, "words": [{"attribute": {"event": "speech"}, "end_time": 11600, "start_time": 11440, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 11760, "start_time": 11600, "text": "用"}, {"attribute": {"event": "speech"}, "end_time": 11920, "start_time": 11760, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 12080, "start_time": 11920, "text": "做"}, {"attribute": {"event": "speech"}, "end_time": 12280, "start_time": 12080, "text": "丝"}, {"attribute": {"event": "speech"}, "end_time": 12780, "start_time": 12280, "text": "绸"}], "keyword": "丝绸"}, {"subtitle_id": 9, "text": "乌罗氏答应了，", "timestamp": "00:00:12,920 --> 00:00:13,920", "duration": 1.0, "char_count": 7, "start_time_s": 12.92, "end_time_s": 13.92, "words": [{"attribute": {"event": "speech"}, "end_time": 13120, "start_time": 12920, "text": "乌"}, {"attribute": {"event": "speech"}, "end_time": 13240, "start_time": 13120, "text": "罗"}, {"attribute": {"event": "speech"}, "end_time": 13360, "start_time": 13240, "text": "氏"}, {"attribute": {"event": "speech"}, "end_time": 13520, "start_time": 13360, "text": "答"}, {"attribute": {"event": "speech"}, "end_time": 13640, "start_time": 13560, "text": "应"}, {"attribute": {"event": "speech"}, "end_time": 13920, "start_time": 13640, "text": "了"}], "keyword": "乌罗氏"}, {"subtitle_id": 10, "text": "还夸赞宫子华的茶好喝，", "timestamp": "00:00:13,920 --> 00:00:15,640", "duration": 1.72, "char_count": 11, "start_time_s": 13.92, "end_time_s": 15.64, "words": [{"attribute": {"event": "speech"}, "end_time": 14160, "start_time": 13920, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 14280, "start_time": 14160, "text": "夸"}, {"attribute": {"event": "speech"}, "end_time": 14440, "start_time": 14320, "text": "赞"}, {"attribute": {"event": "speech"}, "end_time": 14600, "start_time": 14440, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 14720, "start_time": 14600, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 14880, "start_time": 14720, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 14960, "start_time": 14880, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 15120, "start_time": 14960, "text": "茶"}, {"attribute": {"event": "speech"}, "end_time": 15320, "start_time": 15120, "text": "好"}, {"attribute": {"event": "speech"}, "end_time": 15640, "start_time": 15320, "text": "喝"}], "keyword": "宫子华"}, {"subtitle_id": 11, "text": "说比他们那边的茶强多了。", "timestamp": "00:00:15,640 --> 00:00:17,500", "duration": 1.86, "char_count": 12, "start_time_s": 15.64, "end_time_s": 17.5, "words": [{"attribute": {"event": "speech"}, "end_time": 15800, "start_time": 15640, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 15920, "start_time": 15800, "text": "比"}, {"attribute": {"event": "speech"}, "end_time": 16080, "start_time": 15920, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 16160, "start_time": 16080, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 16280, "start_time": 16160, "text": "那"}, {"attribute": {"event": "speech"}, "end_time": 16440, "start_time": 16280, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 16560, "start_time": 16440, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 16720, "start_time": 16560, "text": "茶"}, {"attribute": {"event": "speech"}, "end_time": 16880, "start_time": 16720, "text": "强"}, {"attribute": {"event": "speech"}, "end_time": 17040, "start_time": 16880, "text": "多"}, {"attribute": {"event": "speech"}, "end_time": 17500, "start_time": 17040, "text": "了"}], "keyword": "茶"}], "keywords": ["乌罗氏", "龚子华", "恭敬", "龚子华", "采购", "白蝶子", "月之边", "丝绸", "乌罗氏", "宫子华", "茶"]}, {"chapter": 21, "story_board": "从那以后，乌倮氏成了公子华最信任的人之一。他不仅帮公子华办成大事，还帮他打探六国的消息，为公子华的计划提供了不少帮助。与此同时，公子华也在打造自己的军队。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "专注地为打造军队做规划和安排", "expression": "坚定"}, {"name": "黑衣人", "gender": "男", "age": "青年", "clothes": "高领圆领劲装，主色调为黑色，搭配深褐色，衣服有暗纹，脚穿黑色靴子，腰系黑色腰带，无过多配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "瘦削敏捷", "identity": "公子华的护卫", "other": "忠诚勇敢", "from_chapter": [0], "action": "愣在原地后转身逃跑，被护卫一剑刺穿", "expression": "惊恐"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "认真地整理和汇报六国消息", "expression": "沉稳"}, {"name": "龙且", "gender": "男", "age": "青年", "clothes": "高领圆领秦朝黑色古代战甲，配红色腰带，主色调为黑色和红色，战甲上有简单的几何花纹，头戴黑色头盔，腰带挂短刀圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "健壮", "identity": "士兵", "other": "刀法娴熟，动作利落", "from_chapter": 28, "action": "", "expression": ""}, {"name": "英布", "gender": "男", "age": "青年", "clothes": "高领圆领灰色秦代长袍，脚蹬黑色靴子，主色调为灰色，搭配白色，长袍有淡蓝色水纹，头戴灰色毡帽，腰系灰色腰带圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "公子华的心腹", "other": "常低声说话", "from_chapter": 121, "action": "", "expression": ""}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "叔孙通", "gender": "男", "age": "中年", "clothes": "高领圆领蓝色齐国长袍，主色调为蓝色，搭配浅蓝色和白色，衣服上有波浪纹图案，腰间束蓝色丝带，配蓝色玉佩，头戴蓝色束发帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "消瘦", "identity": "公子华安插在齐国的棋子", "other": "深谙人心，擅长挑拨离间", "from_chapter": 91, "action": "", "expression": ""}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": 1, "action": "", "expression": ""}], "scene": "军营", "image_prompt": "中景，在军营中，一位专注且坚定的青年男子正在为打造军队做规划和安排，旁边一位沉稳的中年男子认真地整理和汇报信息。青年男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发，身姿挺拔；中年男子穿着高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子，圆形衣领，黑色束发，模样富态。, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241202_20250723_112644.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_100a495f-55c6-4ff6-b1ee-c12b18e80cf0_1.25x_20250723_105540.wav", "audio_duration": 12.6, "video_url": "", "story_board_id": 5, "subtitle": [{"subtitle_id": 1, "text": "从那以后，", "timestamp": "00:00:00,160 --> 00:00:00,920", "duration": 0.76, "char_count": 5, "start_time_s": 0.16, "end_time_s": 0.92, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 160, "text": "从"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "那"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 440, "text": "以"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 560, "text": "后"}], "keyword": "乌罗氏"}, {"subtitle_id": 2, "text": "乌罗氏成了龚子华最信任的人之一，", "timestamp": "00:00:00,920 --> 00:00:03,500", "duration": 2.58, "char_count": 16, "start_time_s": 0.92, "end_time_s": 3.5, "words": [{"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 920, "text": "乌"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1120, "text": "罗"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1240, "text": "氏"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1400, "text": "成"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1560, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1640, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1800, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1920, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2160, "text": "最"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2400, "text": "信"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2560, "text": "任"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2680, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2800, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2920, "text": "之"}, {"attribute": {"event": "speech"}, "end_time": 3500, "start_time": 3080, "text": "一"}], "keyword": "龚子华"}, {"subtitle_id": 3, "text": "他不仅帮龚子华办成大事，", "timestamp": "00:00:03,640 --> 00:00:05,400", "duration": 1.76, "char_count": 12, "start_time_s": 3.64, "end_time_s": 5.4, "words": [{"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3640, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3800, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3920, "text": "仅"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4040, "text": "帮"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4160, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4320, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4480, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4640, "text": "办"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4760, "text": "成"}, {"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 4920, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5080, "text": "事"}], "keyword": "办成大事"}, {"subtitle_id": 4, "text": "还帮他打探六国的消息，", "timestamp": "00:00:05,400 --> 00:00:07,000", "duration": 1.6, "char_count": 11, "start_time_s": 5.4, "end_time_s": 7.0, "words": [{"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5400, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5560, "text": "帮"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5680, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5840, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 6000, "text": "探"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6120, "text": "六"}, {"attribute": {"event": "speech"}, "end_time": 6480, "start_time": 6320, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6480, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6560, "text": "消"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6720, "text": "息"}], "keyword": "打探消息"}, {"subtitle_id": 5, "text": "为龚子华的计划提供了不少帮助。", "timestamp": "00:00:07,000 --> 00:00:09,500", "duration": 2.5, "char_count": 15, "start_time_s": 7.0, "end_time_s": 9.5, "words": [{"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 7000, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 7360, "start_time": 7200, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 7520, "start_time": 7360, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7520, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7680, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7760, "text": "计"}, {"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7920, "text": "划"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8120, "text": "提"}, {"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8360, "text": "供"}, {"attribute": {"event": "speech"}, "end_time": 8560, "start_time": 8480, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 8720, "start_time": 8560, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 8840, "start_time": 8720, "text": "少"}, {"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8880, "text": "帮"}, {"attribute": {"event": "speech"}, "end_time": 9500, "start_time": 9040, "text": "助"}], "keyword": "计划"}, {"subtitle_id": 6, "text": "与此同时，", "timestamp": "00:00:09,520 --> 00:00:10,360", "duration": 0.84, "char_count": 5, "start_time_s": 9.52, "end_time_s": 10.36, "words": [{"attribute": {"event": "speech"}, "end_time": 9720, "start_time": 9520, "text": "与"}, {"attribute": {"event": "speech"}, "end_time": 9840, "start_time": 9720, "text": "此"}, {"attribute": {"event": "speech"}, "end_time": 9960, "start_time": 9840, "text": "同"}, {"attribute": {"event": "speech"}, "end_time": 10360, "start_time": 9960, "text": "时"}], "keyword": "同时"}, {"subtitle_id": 7, "text": "龚子华也在打造自己的军队。", "timestamp": "00:00:10,360 --> 00:00:12,500", "duration": 2.14, "char_count": 13, "start_time_s": 10.36, "end_time_s": 12.5, "words": [{"attribute": {"event": "speech"}, "end_time": 10520, "start_time": 10360, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 10680, "start_time": 10520, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 10840, "start_time": 10680, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 11000, "start_time": 10840, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 11120, "start_time": 11000, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 11320, "start_time": 11120, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 11480, "start_time": 11320, "text": "造"}, {"attribute": {"event": "speech"}, "end_time": 11640, "start_time": 11480, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 11800, "start_time": 11640, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 11880, "start_time": 11800, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 12040, "start_time": 11880, "text": "军"}, {"attribute": {"event": "speech"}, "end_time": 12500, "start_time": 12040, "text": "队"}], "keyword": "军队"}], "keywords": ["乌罗氏", "龚子华", "办成大事", "打探消息", "计划", "同时", "军队"]}, {"chapter": 21, "story_board": "他把护卫营放在了产业园，而不是东宫，因为他觉得产业更重要。来到军营后，他亲自挑选统领，龙且、英布、王离等人纷纷展现实力，最终各自拿到了自己营的统领之位。公子华对军队要求极高，他告诉众人，只有最强的才能留下，弱者会被淘汰。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站在军营中挑选统领，严肃地对众人讲话", "expression": "威严"}, {"name": "黑衣人", "gender": "男", "age": "青年", "clothes": "高领圆领劲装，主色调为黑色，搭配深褐色，衣服有暗纹，脚穿黑色靴子，腰系黑色腰带，无过多配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "瘦削敏捷", "identity": "公子华的护卫", "other": "忠诚勇敢", "from_chapter": [0], "action": "愣在原地后转身逃跑，被护卫一剑刺穿", "expression": "惊恐"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "认真地整理和汇报六国消息", "expression": "沉稳"}, {"name": "龙且", "gender": "男", "age": "青年", "clothes": "高领圆领秦朝黑色古代战甲，配红色腰带，主色调为黑色和红色，战甲上有简单的几何花纹，头戴黑色头盔，腰带挂短刀圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "健壮", "identity": "士兵", "other": "刀法娴熟，动作利落", "from_chapter": 28, "action": "在军营中展示实力", "expression": "自信"}, {"name": "英布", "gender": "男", "age": "青年", "clothes": "高领圆领灰色秦代长袍，脚蹬黑色靴子，主色调为灰色，搭配白色，长袍有淡蓝色水纹，头戴灰色毡帽，腰系灰色腰带圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "公子华的心腹", "other": "常低声说话", "from_chapter": 121, "action": "在军营中展示实力", "expression": "坚毅"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "叔孙通", "gender": "男", "age": "中年", "clothes": "高领圆领蓝色齐国长袍，主色调为蓝色，搭配浅蓝色和白色，衣服上有波浪纹图案，腰间束蓝色丝带，配蓝色玉佩，头戴蓝色束发帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "消瘦", "identity": "公子华安插在齐国的棋子", "other": "深谙人心，擅长挑拨离间", "from_chapter": 91, "action": "", "expression": ""}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": 1, "action": "", "expression": ""}], "scene": "军营", "image_prompt": "中景，在军营中，一位严肃威严、挺拔站立的青年男子正在挑选统领，旁边有两位青年男子展示实力，其中一位自信满满，动作利落，另一位则坚毅沉着。站着挑选统领的青年男子穿着高领圆领紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发；展示实力且自信的青年男子穿着高领圆领秦朝黑色古代战甲，配红色腰带，主色调为黑色和红色，战甲上有简单的几何花纹，头戴黑色头盔，腰带挂短刀，圆形衣领，黑色短发束起；展示实力且坚毅的青年男子穿着高领圆领灰色秦代长袍，脚蹬黑色靴子，主色调为灰色，搭配白色，长袍有淡蓝色水纹，头戴灰色毡帽，腰系灰色腰带，圆形衣领，黑色短发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241240_20250723_112720.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_420368e1-ddc1-4780-b4cb-b77f29fc0a9e_1.25x_20250723_105557.wav", "audio_duration": 18.304, "video_url": "", "story_board_id": 6, "subtitle": [{"subtitle_id": 1, "text": "他把护卫营放在了产业园，", "timestamp": "00:00:00,160 --> 00:00:01,880", "duration": 1.72, "char_count": 12, "start_time_s": 0.16, "end_time_s": 1.88, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "把"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 400, "text": "护"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 520, "text": "卫"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 680, "text": "营"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 840, "text": "放"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 1000, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1120, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1240, "text": "产"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1400, "text": "业"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1520, "text": "园"}], "keyword": "护卫营"}, {"subtitle_id": 2, "text": "而不是东宫，", "timestamp": "00:00:01,880 --> 00:00:02,880", "duration": 1.0, "char_count": 6, "start_time_s": 1.88, "end_time_s": 2.88, "words": [{"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1880, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2080, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2240, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 2520, "start_time": 2360, "text": "东"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2520, "text": "宫"}], "keyword": "东宫"}, {"subtitle_id": 3, "text": "因为他觉得产业更重要。", "timestamp": "00:00:02,880 --> 00:00:04,780", "duration": 1.9, "char_count": 11, "start_time_s": 2.88, "end_time_s": 4.78, "words": [{"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2880, "text": "因"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 3000, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3120, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3240, "text": "觉"}, {"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3400, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3640, "text": "产"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3840, "text": "业"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4000, "text": "更"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4200, "text": "重"}, {"attribute": {"event": "speech"}, "end_time": 4780, "start_time": 4360, "text": "要"}], "keyword": "产业"}, {"subtitle_id": 4, "text": "来到军营后，", "timestamp": "00:00:04,920 --> 00:00:05,880", "duration": 0.96, "char_count": 6, "start_time_s": 4.92, "end_time_s": 5.88, "words": [{"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 4920, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5080, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5240, "text": "军"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5360, "text": "营"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5480, "text": "后"}], "keyword": "军营"}, {"subtitle_id": 5, "text": "他亲自挑选统领，", "timestamp": "00:00:05,880 --> 00:00:07,280", "duration": 1.4, "char_count": 8, "start_time_s": 5.88, "end_time_s": 7.28, "words": [{"attribute": {"event": "speech"}, "end_time": 6080, "start_time": 5880, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6080, "text": "亲"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6280, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6440, "text": "挑"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6600, "text": "选"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6720, "text": "统"}, {"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 6920, "text": "领"}], "keyword": "统领"}, {"subtitle_id": 6, "text": "龙且、英部、王里等人纷纷展现实力，", "timestamp": "00:00:07,280 --> 00:00:09,900", "duration": 2.62, "char_count": 17, "start_time_s": 7.28, "end_time_s": 9.9, "words": [{"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7280, "text": "龙"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7440, "text": "且"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7600, "text": "英"}, {"attribute": {"event": "speech"}, "end_time": 7880, "start_time": 7760, "text": "部"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7880, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8040, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8200, "text": "等"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8320, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8600, "text": "纷"}, {"attribute": {"event": "speech"}, "end_time": 8920, "start_time": 8640, "text": "纷"}, {"attribute": {"event": "speech"}, "end_time": 9120, "start_time": 8960, "text": "展"}, {"attribute": {"event": "speech"}, "end_time": 9280, "start_time": 9160, "text": "现"}, {"attribute": {"event": "speech"}, "end_time": 9480, "start_time": 9280, "text": "实"}, {"attribute": {"event": "speech"}, "end_time": 9900, "start_time": 9480, "text": "力"}], "keyword": "龙且"}, {"subtitle_id": 7, "text": "最终各自拿到了自己赢的统领之位。", "timestamp": "00:00:09,920 --> 00:00:12,380", "duration": 2.46, "char_count": 16, "start_time_s": 9.92, "end_time_s": 12.38, "words": [{"attribute": {"event": "speech"}, "end_time": 10000, "start_time": 9920, "text": "最"}, {"attribute": {"event": "speech"}, "end_time": 10160, "start_time": 10000, "text": "终"}, {"attribute": {"event": "speech"}, "end_time": 10360, "start_time": 10160, "text": "各"}, {"attribute": {"event": "speech"}, "end_time": 10480, "start_time": 10360, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 10640, "start_time": 10480, "text": "拿"}, {"attribute": {"event": "speech"}, "end_time": 10760, "start_time": 10640, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 10840, "start_time": 10760, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 10960, "start_time": 10840, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 11080, "start_time": 10960, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 11200, "start_time": 11080, "text": "赢"}, {"attribute": {"event": "speech"}, "end_time": 11360, "start_time": 11200, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 11600, "start_time": 11360, "text": "统"}, {"attribute": {"event": "speech"}, "end_time": 11760, "start_time": 11600, "text": "领"}, {"attribute": {"event": "speech"}, "end_time": 11920, "start_time": 11800, "text": "之"}, {"attribute": {"event": "speech"}, "end_time": 12380, "start_time": 11920, "text": "位"}], "keyword": "统领之位"}, {"subtitle_id": 8, "text": "龚子华对军队要求极高，", "timestamp": "00:00:12,440 --> 00:00:14,160", "duration": 1.72, "char_count": 11, "start_time_s": 12.44, "end_time_s": 14.16, "words": [{"attribute": {"event": "speech"}, "end_time": 12640, "start_time": 12440, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 12760, "start_time": 12640, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 12880, "start_time": 12760, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 13040, "start_time": 12920, "text": "对"}, {"attribute": {"event": "speech"}, "end_time": 13200, "start_time": 13040, "text": "军"}, {"attribute": {"event": "speech"}, "end_time": 13320, "start_time": 13200, "text": "队"}, {"attribute": {"event": "speech"}, "end_time": 13520, "start_time": 13360, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 13640, "start_time": 13520, "text": "求"}, {"attribute": {"event": "speech"}, "end_time": 13800, "start_time": 13680, "text": "极"}, {"attribute": {"event": "speech"}, "end_time": 14160, "start_time": 13800, "text": "高"}], "keyword": "龚子华"}, {"subtitle_id": 9, "text": "他告诉众人，", "timestamp": "00:00:14,160 --> 00:00:15,040", "duration": 0.88, "char_count": 6, "start_time_s": 14.16, "end_time_s": 15.04, "words": [{"attribute": {"event": "speech"}, "end_time": 14320, "start_time": 14160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 14480, "start_time": 14320, "text": "告"}, {"attribute": {"event": "speech"}, "end_time": 14600, "start_time": 14480, "text": "诉"}, {"attribute": {"event": "speech"}, "end_time": 14720, "start_time": 14600, "text": "众"}, {"attribute": {"event": "speech"}, "end_time": 15040, "start_time": 14720, "text": "人"}], "keyword": "众人"}, {"subtitle_id": 10, "text": "只有最强的才能留下，", "timestamp": "00:00:15,040 --> 00:00:16,640", "duration": 1.6, "char_count": 10, "start_time_s": 15.04, "end_time_s": 16.64, "words": [{"attribute": {"event": "speech"}, "end_time": 15160, "start_time": 15040, "text": "只"}, {"attribute": {"event": "speech"}, "end_time": 15280, "start_time": 15160, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 15480, "start_time": 15280, "text": "最"}, {"attribute": {"event": "speech"}, "end_time": 15760, "start_time": 15520, "text": "强"}, {"attribute": {"event": "speech"}, "end_time": 15920, "start_time": 15760, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 16080, "start_time": 15920, "text": "才"}, {"attribute": {"event": "speech"}, "end_time": 16160, "start_time": 16080, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 16320, "start_time": 16160, "text": "留"}, {"attribute": {"event": "speech"}, "end_time": 16640, "start_time": 16320, "text": "下"}], "keyword": "最强"}, {"subtitle_id": 11, "text": "弱者会被淘汰。", "timestamp": "00:00:16,640 --> 00:00:17,980", "duration": 1.34, "char_count": 7, "start_time_s": 16.64, "end_time_s": 17.98, "words": [{"attribute": {"event": "speech"}, "end_time": 16800, "start_time": 16640, "text": "弱"}, {"attribute": {"event": "speech"}, "end_time": 17000, "start_time": 16800, "text": "者"}, {"attribute": {"event": "speech"}, "end_time": 17160, "start_time": 17000, "text": "会"}, {"attribute": {"event": "speech"}, "end_time": 17280, "start_time": 17160, "text": "被"}, {"attribute": {"event": "speech"}, "end_time": 17520, "start_time": 17280, "text": "淘"}, {"attribute": {"event": "speech"}, "end_time": 17980, "start_time": 17520, "text": "汰"}], "keyword": "弱者"}], "keywords": ["护卫营", "东宫", "产业", "军营", "统领", "龙且", "统领之位", "龚子华", "众人", "最强", "弱者"]}, {"chapter": 21, "story_board": "他还说，以后军队会扩大到十五万、一百五十万人，要让他们成为历史上最强大的力量。为了激励大家，他规定每月考核，第一名能获得荣誉勋章，连续垫底的直接解散。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站在军营中对着士兵们讲话", "expression": "坚定且自信"}, {"name": "黑衣人", "gender": "男", "age": "青年", "clothes": "高领圆领劲装，主色调为黑色，搭配深褐色，衣服有暗纹，脚穿黑色靴子，腰系黑色腰带，无过多配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "瘦削敏捷", "identity": "公子华的护卫", "other": "忠诚勇敢", "from_chapter": [0], "action": "愣在原地后转身逃跑，被护卫一剑刺穿", "expression": "惊恐"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "认真地整理和汇报六国消息", "expression": "沉稳"}, {"name": "龙且", "gender": "男", "age": "青年", "clothes": "高领圆领秦朝黑色古代战甲，配红色腰带，主色调为黑色和红色，战甲上有简单的几何花纹，头戴黑色头盔，腰带挂短刀圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "健壮", "identity": "士兵", "other": "刀法娴熟，动作利落", "from_chapter": 28, "action": "在军营中展示实力", "expression": "自信"}, {"name": "英布", "gender": "男", "age": "青年", "clothes": "高领圆领灰色秦代长袍，脚蹬黑色靴子，主色调为灰色，搭配白色，长袍有淡蓝色水纹，头戴灰色毡帽，腰系灰色腰带圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "公子华的心腹", "other": "常低声说话", "from_chapter": 121, "action": "在军营中展示实力", "expression": "坚毅"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "叔孙通", "gender": "男", "age": "中年", "clothes": "高领圆领蓝色齐国长袍，主色调为蓝色，搭配浅蓝色和白色，衣服上有波浪纹图案，腰间束蓝色丝带，配蓝色玉佩，头戴蓝色束发帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "消瘦", "identity": "公子华安插在齐国的棋子", "other": "深谙人心，擅长挑拨离间", "from_chapter": 91, "action": "", "expression": ""}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": 1, "action": "", "expression": ""}], "scene": "军营", "image_prompt": "中景，站在军营中的青年男子，表情坚定且自信，身姿挺拔，穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发，敢言善辩, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241266_20250723_112746.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_515c6d89-d8e2-4c65-a290-5865c3116ba4_1.25x_20250723_105610.wav", "audio_duration": 11.869333, "video_url": "", "story_board_id": 7, "subtitle": [{"subtitle_id": 1, "text": "他还说，", "timestamp": "00:00:00,200 --> 00:00:00,760", "duration": 0.56, "char_count": 4, "start_time_s": 0.2, "end_time_s": 0.76, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 360, "start_time": 280, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 360, "text": "说"}], "keyword": "他"}, {"subtitle_id": 2, "text": "以后军队会扩大到15万、150万人，", "timestamp": "00:00:00,760 --> 00:00:03,560", "duration": 2.8, "char_count": 18, "start_time_s": 0.76, "end_time_s": 3.56, "words": [{"attribute": {"event": "speech"}, "end_time": 920, "start_time": 760, "text": "以"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 920, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1040, "text": "军"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1200, "text": "队"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1360, "text": "会"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1560, "text": "扩"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1680, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1840, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2040, "text": "15"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2360, "text": "万"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2480, "text": "150"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3040, "text": "万"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3200, "text": "人"}], "keyword": "军队"}, {"subtitle_id": 3, "text": "要让他们成为历史上最强大的力量。", "timestamp": "00:00:03,560 --> 00:00:06,100", "duration": 2.54, "char_count": 16, "start_time_s": 3.56, "end_time_s": 6.1, "words": [{"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3560, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3680, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3840, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3960, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4040, "text": "成"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4200, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 4440, "start_time": 4320, "text": "历"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4440, "text": "史"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4600, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4760, "text": "最"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5040, "text": "强"}, {"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5240, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5440, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5520, "text": "力"}, {"attribute": {"event": "speech"}, "end_time": 6100, "start_time": 5640, "text": "量"}], "keyword": "力量"}, {"subtitle_id": 4, "text": "为了激励大家，", "timestamp": "00:00:06,200 --> 00:00:07,120", "duration": 0.92, "char_count": 7, "start_time_s": 6.2, "end_time_s": 7.12, "words": [{"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6200, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6360, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6480, "text": "激"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6600, "text": "励"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6680, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 7120, "start_time": 6800, "text": "家"}], "keyword": "激励"}, {"subtitle_id": 5, "text": "他规定", "timestamp": "00:00:07,120 --> 00:00:07,520", "duration": 0.4, "char_count": 3, "start_time_s": 7.12, "end_time_s": 7.52, "words": [{"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 7120, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7280, "text": "规"}, {"attribute": {"event": "speech"}, "end_time": 7520, "start_time": 7400, "text": "定"}], "keyword": "规定"}, {"subtitle_id": 6, "text": "每月考核第一名能获得荣誉勋章，", "timestamp": "00:00:07,520 --> 00:00:09,840", "duration": 2.32, "char_count": 15, "start_time_s": 7.52, "end_time_s": 9.84, "words": [{"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7520, "text": "每"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7680, "text": "月"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7760, "text": "考"}, {"attribute": {"event": "speech"}, "end_time": 8160, "start_time": 7920, "text": "核"}, {"attribute": {"event": "speech"}, "end_time": 8440, "start_time": 8280, "text": "第"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8440, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8520, "text": "名"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8640, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 8920, "start_time": 8800, "text": "获"}, {"attribute": {"event": "speech"}, "end_time": 9080, "start_time": 8920, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 9200, "start_time": 9080, "text": "荣"}, {"attribute": {"event": "speech"}, "end_time": 9320, "start_time": 9200, "text": "誉"}, {"attribute": {"event": "speech"}, "end_time": 9480, "start_time": 9360, "text": "勋"}, {"attribute": {"event": "speech"}, "end_time": 9840, "start_time": 9520, "text": "章"}], "keyword": "荣誉勋章"}, {"subtitle_id": 7, "text": "连续垫底的直接解散。", "timestamp": "00:00:09,840 --> 00:00:11,540", "duration": 1.7, "char_count": 10, "start_time_s": 9.84, "end_time_s": 11.54, "words": [{"attribute": {"event": "speech"}, "end_time": 10000, "start_time": 9840, "text": "连"}, {"attribute": {"event": "speech"}, "end_time": 10160, "start_time": 10040, "text": "续"}, {"attribute": {"event": "speech"}, "end_time": 10320, "start_time": 10160, "text": "垫"}, {"attribute": {"event": "speech"}, "end_time": 10440, "start_time": 10320, "text": "底"}, {"attribute": {"event": "speech"}, "end_time": 10600, "start_time": 10440, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 10760, "start_time": 10600, "text": "直"}, {"attribute": {"event": "speech"}, "end_time": 10920, "start_time": 10760, "text": "接"}, {"attribute": {"event": "speech"}, "end_time": 11080, "start_time": 10920, "text": "解"}, {"attribute": {"event": "speech"}, "end_time": 11540, "start_time": 11080, "text": "散"}], "keyword": "散伙"}], "keywords": ["他", "军队", "力量", "激励", "规定", "荣誉勋章", "散伙"]}, {"chapter": 21, "story_board": "军营里气氛热烈，士兵们开始准备宴会，大家一边喝酒一边比武，场面热闹非凡。公子华看着这一切，心里却有些感慨。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站在一旁看着士兵们喝酒比武", "expression": "感慨"}, {"name": "黑衣人", "gender": "男", "age": "青年", "clothes": "高领圆领劲装，主色调为黑色，搭配深褐色，衣服有暗纹，脚穿黑色靴子，腰系黑色腰带，无过多配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "瘦削敏捷", "identity": "公子华的护卫", "other": "忠诚勇敢", "from_chapter": [0], "action": "愣在原地后转身逃跑，被护卫一剑刺穿", "expression": "惊恐"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "认真地整理和汇报六国消息", "expression": "沉稳"}, {"name": "龙且", "gender": "男", "age": "青年", "clothes": "高领圆领秦朝黑色古代战甲，配红色腰带，主色调为黑色和红色，战甲上有简单的几何花纹，头戴黑色头盔，腰带挂短刀圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "健壮", "identity": "士兵", "other": "刀法娴熟，动作利落", "from_chapter": 28, "action": "在军营中展示实力", "expression": "自信"}, {"name": "英布", "gender": "男", "age": "青年", "clothes": "高领圆领灰色秦代长袍，脚蹬黑色靴子，主色调为灰色，搭配白色，长袍有淡蓝色水纹，头戴灰色毡帽，腰系灰色腰带圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "公子华的心腹", "other": "常低声说话", "from_chapter": 121, "action": "在军营中展示实力", "expression": "坚毅"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "叔孙通", "gender": "男", "age": "中年", "clothes": "高领圆领蓝色齐国长袍，主色调为蓝色，搭配浅蓝色和白色，衣服上有波浪纹图案，腰间束蓝色丝带，配蓝色玉佩，头戴蓝色束发帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "消瘦", "identity": "公子华安插在齐国的棋子", "other": "深谙人心，擅长挑拨离间", "from_chapter": 91, "action": "", "expression": ""}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": 1, "action": "", "expression": ""}], "scene": "军营", "image_prompt": "中景，一个青年男子挺拔地站在军营里，看着士兵们喝酒比武，脸上带着感慨的神情。青年男子身着高领圆领的汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束着黑色腰带并佩戴玉佩，有着圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241293_20250723_112814.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_e7d02ea4-5420-487e-b151-0b7d7edfee13_1.25x_20250723_105553.wav", "audio_duration": 8.741333, "video_url": "", "story_board_id": 8, "subtitle": [{"subtitle_id": 1, "text": "军营里气氛热烈，", "timestamp": "00:00:00,200 --> 00:00:01,360", "duration": 1.16, "char_count": 8, "start_time_s": 0.2, "end_time_s": 1.36, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 200, "text": "军"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "营"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 440, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 640, "text": "气"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 760, "text": "氛"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 880, "text": "热"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1040, "text": "烈"}], "keyword": "军营"}, {"subtitle_id": 2, "text": "士兵们开始准备宴会，", "timestamp": "00:00:01,360 --> 00:00:02,680", "duration": 1.32, "char_count": 10, "start_time_s": 1.36, "end_time_s": 2.68, "words": [{"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1360, "text": "士"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1520, "text": "兵"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1640, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1720, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1880, "text": "始"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 2000, "text": "准"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2120, "text": "备"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2240, "text": "宴"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2360, "text": "会"}], "keyword": "宴会"}, {"subtitle_id": 3, "text": "大家一边喝酒一边比武，", "timestamp": "00:00:02,680 --> 00:00:04,400", "duration": 1.72, "char_count": 11, "start_time_s": 2.68, "end_time_s": 4.4, "words": [{"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2680, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2800, "text": "家"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2920, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 3040, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3160, "text": "喝"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3400, "text": "酒"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3600, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3760, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3880, "text": "比"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4040, "text": "武"}], "keyword": "比武"}, {"subtitle_id": 4, "text": "场面热闹非凡。", "timestamp": "00:00:04,400 --> 00:00:05,740", "duration": 1.34, "char_count": 7, "start_time_s": 4.4, "end_time_s": 5.74, "words": [{"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4400, "text": "场"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4600, "text": "面"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4760, "text": "热"}, {"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 4960, "text": "闹"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5120, "text": "非"}, {"attribute": {"event": "speech"}, "end_time": 5740, "start_time": 5280, "text": "凡"}], "keyword": "场面"}, {"subtitle_id": 5, "text": "龚子华看着这一切，", "timestamp": "00:00:05,880 --> 00:00:07,280", "duration": 1.4, "char_count": 9, "start_time_s": 5.88, "end_time_s": 7.28, "words": [{"attribute": {"event": "speech"}, "end_time": 6080, "start_time": 5880, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6080, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6200, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6360, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6560, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6640, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6800, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 6920, "text": "切"}], "keyword": "龚子华"}, {"subtitle_id": 6, "text": "心里却有些感慨。", "timestamp": "00:00:07,280 --> 00:00:08,620", "duration": 1.34, "char_count": 8, "start_time_s": 7.28, "end_time_s": 8.62, "words": [{"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7280, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7440, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7560, "text": "却"}, {"attribute": {"event": "speech"}, "end_time": 7840, "start_time": 7680, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7840, "text": "些"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 7960, "text": "感"}, {"attribute": {"event": "speech"}, "end_time": 8620, "start_time": 8160, "text": "慨"}], "keyword": "感慨"}], "keywords": ["军营", "宴会", "比武", "场面", "龚子华", "感慨"]}, {"chapter": 21, "story_board": "他说，如果天下太平该多好，可是现在六国争斗不断，秦人必须统一他们，才能迎来真正的和平。酒过三巡，公子华和将士们越聊越开心。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "与将士们围坐喝酒聊天，边说边举起酒杯", "expression": "真诚且期待"}, {"name": "黑衣人", "gender": "男", "age": "青年", "clothes": "高领圆领劲装，主色调为黑色，搭配深褐色，衣服有暗纹，脚穿黑色靴子，腰系黑色腰带，无过多配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "瘦削敏捷", "identity": "公子华的护卫", "other": "忠诚勇敢", "from_chapter": [0], "action": "愣在原地后转身逃跑，被护卫一剑刺穿", "expression": "惊恐"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "认真地整理和汇报六国消息", "expression": "沉稳"}, {"name": "龙且", "gender": "男", "age": "青年", "clothes": "高领圆领秦朝黑色古代战甲，配红色腰带，主色调为黑色和红色，战甲上有简单的几何花纹，头戴黑色头盔，腰带挂短刀圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "健壮", "identity": "士兵", "other": "刀法娴熟，动作利落", "from_chapter": 28, "action": "在军营中展示实力", "expression": "自信"}, {"name": "英布", "gender": "男", "age": "青年", "clothes": "高领圆领灰色秦代长袍，脚蹬黑色靴子，主色调为灰色，搭配白色，长袍有淡蓝色水纹，头戴灰色毡帽，腰系灰色腰带圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "公子华的心腹", "other": "常低声说话", "from_chapter": 121, "action": "在军营中展示实力", "expression": "坚毅"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "叔孙通", "gender": "男", "age": "中年", "clothes": "高领圆领蓝色齐国长袍，主色调为蓝色，搭配浅蓝色和白色，衣服上有波浪纹图案，腰间束蓝色丝带，配蓝色玉佩，头戴蓝色束发帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "消瘦", "identity": "公子华安插在齐国的棋子", "other": "深谙人心，擅长挑拨离间", "from_chapter": 91, "action": "", "expression": ""}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": 1, "action": "", "expression": ""}], "scene": "军营", "image_prompt": "中景，在军营里，一个真诚且期待地边说边举起酒杯的青年男子与一群将士围坐喝酒，男子身姿挺拔，穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241317_20250723_112838.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_0f815be1-253a-40fe-966e-d678db8a2419_1.25x_20250723_105602.wav", "audio_duration": 10.68, "video_url": "", "story_board_id": 9, "subtitle": [{"subtitle_id": 1, "text": "他说如果天下太平该多好，", "timestamp": "00:00:00,200 --> 00:00:02,240", "duration": 2.04, "char_count": 12, "start_time_s": 0.2, "end_time_s": 2.24, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 280, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 680, "text": "如"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 880, "text": "果"}, {"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 1000, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1160, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1320, "text": "太"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1520, "text": "平"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1640, "text": "该"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1760, "text": "多"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 1920, "text": "好"}], "keyword": "天下太平"}, {"subtitle_id": 2, "text": "可是现在六国争斗不断，", "timestamp": "00:00:02,240 --> 00:00:03,880", "duration": 1.64, "char_count": 11, "start_time_s": 2.24, "end_time_s": 3.88, "words": [{"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2240, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2400, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2480, "text": "现"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2640, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2760, "text": "六"}, {"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2960, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3080, "text": "争"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3240, "text": "斗"}, {"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3360, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3520, "text": "断"}], "keyword": "六国争斗"}, {"subtitle_id": 3, "text": "亲人必须统一，", "timestamp": "00:00:03,880 --> 00:00:04,840", "duration": 0.96, "char_count": 7, "start_time_s": 3.88, "end_time_s": 4.84, "words": [{"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3880, "text": "亲"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4040, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4200, "text": "必"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4360, "text": "须"}, {"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4520, "text": "统"}, {"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4680, "text": "一"}], "keyword": "亲人统一"}, {"subtitle_id": 4, "text": "他们才能迎来真正的和平。", "timestamp": "00:00:04,840 --> 00:00:06,980", "duration": 2.14, "char_count": 12, "start_time_s": 4.84, "end_time_s": 6.98, "words": [{"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4840, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 5000, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5240, "text": "才"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5440, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5520, "text": "迎"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5640, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5840, "text": "真"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6080, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6200, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6480, "start_time": 6360, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 6980, "start_time": 6520, "text": "平"}], "keyword": "真正和平"}, {"subtitle_id": 5, "text": "酒过三巡，", "timestamp": "00:00:07,120 --> 00:00:07,960", "duration": 0.84, "char_count": 5, "start_time_s": 7.12, "end_time_s": 7.96, "words": [{"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7120, "text": "酒"}, {"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7320, "text": "过"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7440, "text": "三"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7600, "text": "巡"}], "keyword": "酒过三巡"}, {"subtitle_id": 6, "text": "龚子华和将士们越聊越开心。", "timestamp": "00:00:07,960 --> 00:00:10,340", "duration": 2.38, "char_count": 13, "start_time_s": 7.96, "end_time_s": 10.34, "words": [{"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 7960, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 8280, "start_time": 8120, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 8440, "start_time": 8280, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 8560, "start_time": 8440, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 8720, "start_time": 8560, "text": "将"}, {"attribute": {"event": "speech"}, "end_time": 8880, "start_time": 8760, "text": "士"}, {"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8880, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 9320, "start_time": 9080, "text": "越"}, {"attribute": {"event": "speech"}, "end_time": 9480, "start_time": 9320, "text": "聊"}, {"attribute": {"event": "speech"}, "end_time": 9680, "start_time": 9520, "text": "越"}, {"attribute": {"event": "speech"}, "end_time": 9880, "start_time": 9680, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 10340, "start_time": 9880, "text": "心"}], "keyword": "龚子华"}], "keywords": ["天下太平", "六国争斗", "亲人统一", "真正和平", "酒过三巡", "龚子华"]}, {"chapter": 21, "story_board": "他叮嘱大家，宁愿在训练场上流汗，也不愿在战场上流血。他希望每个人都练好本事，将来为国效力。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "面对将士们说话", "expression": "严肃认真"}, {"name": "黑衣人", "gender": "男", "age": "青年", "clothes": "高领圆领劲装，主色调为黑色，搭配深褐色，衣服有暗纹，脚穿黑色靴子，腰系黑色腰带，无过多配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "瘦削敏捷", "identity": "公子华的护卫", "other": "忠诚勇敢", "from_chapter": [0], "action": "愣在原地后转身逃跑，被护卫一剑刺穿", "expression": "惊恐"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "认真地整理和汇报六国消息", "expression": "沉稳"}, {"name": "龙且", "gender": "男", "age": "青年", "clothes": "高领圆领秦朝黑色古代战甲，配红色腰带，主色调为黑色和红色，战甲上有简单的几何花纹，头戴黑色头盔，腰带挂短刀圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "健壮", "identity": "士兵", "other": "刀法娴熟，动作利落", "from_chapter": 28, "action": "在军营中展示实力", "expression": "自信"}, {"name": "英布", "gender": "男", "age": "青年", "clothes": "高领圆领灰色秦代长袍，脚蹬黑色靴子，主色调为灰色，搭配白色，长袍有淡蓝色水纹，头戴灰色毡帽，腰系灰色腰带圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "公子华的心腹", "other": "常低声说话", "from_chapter": 121, "action": "在军营中展示实力", "expression": "坚毅"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "叔孙通", "gender": "男", "age": "中年", "clothes": "高领圆领蓝色齐国长袍，主色调为蓝色，搭配浅蓝色和白色，衣服上有波浪纹图案，腰间束蓝色丝带，配蓝色玉佩，头戴蓝色束发帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "消瘦", "identity": "公子华安插在齐国的棋子", "other": "深谙人心，擅长挑拨离间", "from_chapter": 91, "action": "", "expression": ""}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": 1, "action": "", "expression": ""}], "scene": "军营", "image_prompt": "中景，一位神情严肃认真、身姿挺拔的青年男子，站在军营中面对将士们。他身着高领圆领的汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束着黑色腰带并佩戴玉佩，黑色束发。周围是军营的环境。, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241337_20250723_112858.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_dec5350f-abaf-4a66-988d-e98304f0ee9f_1.25x_20250723_105545.wav", "audio_duration": 7.818667, "video_url": "", "story_board_id": 10, "subtitle": [{"subtitle_id": 1, "text": "他叮嘱大家宁愿在训练场上流汗，", "timestamp": "00:00:00,160 --> 00:00:02,640", "duration": 2.48, "char_count": 15, "start_time_s": 0.16, "end_time_s": 2.64, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "叮"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 440, "text": "嘱"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 560, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 680, "text": "家"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1080, "text": "宁"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1240, "text": "愿"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1360, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1560, "text": "训"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1680, "text": "练"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1760, "text": "场"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 2000, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2120, "text": "流"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2280, "text": "汗"}], "keyword": "他"}, {"subtitle_id": 2, "text": "也不愿在战场上流血。", "timestamp": "00:00:02,640 --> 00:00:04,500", "duration": 1.86, "char_count": 10, "start_time_s": 2.64, "end_time_s": 4.5, "words": [{"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2640, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2800, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 3000, "text": "愿"}, {"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3120, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3280, "text": "战"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3520, "text": "场"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3720, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3840, "text": "流"}, {"attribute": {"event": "speech"}, "end_time": 4500, "start_time": 4000, "text": "血"}], "keyword": "战场"}, {"subtitle_id": 3, "text": "他希望每个人都练好本事，", "timestamp": "00:00:04,600 --> 00:00:06,400", "duration": 1.8, "char_count": 12, "start_time_s": 4.6, "end_time_s": 6.4, "words": [{"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4600, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4800, "text": "希"}, {"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4920, "text": "望"}, {"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5000, "text": "每"}, {"attribute": {"event": "speech"}, "end_time": 5320, "start_time": 5200, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5320, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5440, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5560, "text": "练"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5720, "text": "好"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5840, "text": "本"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6000, "text": "事"}], "keyword": "本事"}, {"subtitle_id": 4, "text": "将来为国效力。", "timestamp": "00:00:06,400 --> 00:00:07,740", "duration": 1.34, "char_count": 7, "start_time_s": 6.4, "end_time_s": 7.74, "words": [{"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6400, "text": "将"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6600, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6720, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6960, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 7120, "text": "效"}, {"attribute": {"event": "speech"}, "end_time": 7740, "start_time": 7280, "text": "力"}], "keyword": "国效力"}], "keywords": ["他", "战场", "本事", "国效力"]}, {"chapter": 21, "story_board": "第二天醒来，公子华已经记不清昨晚发生了什么，只记得自己喝了很多酒。他简单洗漱后，便去了产业园，查看各个工厂的运作情况。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "简单洗漱后前往产业园查看各个工厂的运作情况", "expression": "略显疲惫"}, {"name": "黑衣人", "gender": "男", "age": "青年", "clothes": "高领圆领劲装，主色调为黑色，搭配深褐色，衣服有暗纹，脚穿黑色靴子，腰系黑色腰带，无过多配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "瘦削敏捷", "identity": "公子华的护卫", "other": "忠诚勇敢", "from_chapter": [0], "action": "愣在原地后转身逃跑，被护卫一剑刺穿", "expression": "惊恐"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "认真地整理和汇报六国消息", "expression": "沉稳"}, {"name": "龙且", "gender": "男", "age": "青年", "clothes": "高领圆领秦朝黑色古代战甲，配红色腰带，主色调为黑色和红色，战甲上有简单的几何花纹，头戴黑色头盔，腰带挂短刀圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "健壮", "identity": "士兵", "other": "刀法娴熟，动作利落", "from_chapter": 28, "action": "在军营中展示实力", "expression": "自信"}, {"name": "英布", "gender": "男", "age": "青年", "clothes": "高领圆领灰色秦代长袍，脚蹬黑色靴子，主色调为灰色，搭配白色，长袍有淡蓝色水纹，头戴灰色毡帽，腰系灰色腰带圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "公子华的心腹", "other": "常低声说话", "from_chapter": 121, "action": "在军营中展示实力", "expression": "坚毅"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "叔孙通", "gender": "男", "age": "中年", "clothes": "高领圆领蓝色齐国长袍，主色调为蓝色，搭配浅蓝色和白色，衣服上有波浪纹图案，腰间束蓝色丝带，配蓝色玉佩，头戴蓝色束发帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "消瘦", "identity": "公子华安插在齐国的棋子", "other": "深谙人心，擅长挑拨离间", "from_chapter": 91, "action": "", "expression": ""}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": 1, "action": "", "expression": ""}], "scene": "产业园", "image_prompt": "中景，略显疲惫的青年男子挺拔地走在产业园里查看工厂运作情况，他穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241360_20250723_112921.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_3f41f296-980b-4565-b658-96cf9fb06a30_1.25x_20250723_105630.wav", "audio_duration": 9.602667, "video_url": "", "story_board_id": 11, "subtitle": [{"subtitle_id": 1, "text": "第二天醒来，", "timestamp": "00:00:00,160 --> 00:00:00,920", "duration": 0.76, "char_count": 6, "start_time_s": 0.16, "end_time_s": 0.92, "words": [{"attribute": {"event": "speech"}, "end_time": 240, "start_time": 160, "text": "第"}, {"attribute": {"event": "speech"}, "end_time": 320, "start_time": 240, "text": "二"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 480, "text": "醒"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 600, "text": "来"}], "keyword": "第二天"}, {"subtitle_id": 2, "text": "龚子华已经记不清昨晚发生了什么，", "timestamp": "00:00:00,920 --> 00:00:03,280", "duration": 2.36, "char_count": 16, "start_time_s": 0.92, "end_time_s": 3.28, "words": [{"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 920, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1080, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1200, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1360, "text": "已"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1480, "text": "经"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1640, "text": "记"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1760, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1880, "text": "清"}, {"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2200, "text": "昨"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2360, "text": "晚"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2480, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2640, "text": "生"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2760, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2880, "text": "什"}, {"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3000, "text": "么"}], "keyword": "龚子华"}, {"subtitle_id": 3, "text": "只记得自己喝了很多酒。", "timestamp": "00:00:03,280 --> 00:00:04,980", "duration": 1.7, "char_count": 11, "start_time_s": 3.28, "end_time_s": 4.98, "words": [{"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3280, "text": "只"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3440, "text": "记"}, {"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3560, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3680, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3800, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3920, "text": "喝"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4080, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4160, "text": "很"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4320, "text": "多"}, {"attribute": {"event": "speech"}, "end_time": 4980, "start_time": 4480, "text": "酒"}], "keyword": "酒"}, {"subtitle_id": 4, "text": "他简单洗漱后，", "timestamp": "00:00:05,040 --> 00:00:06,200", "duration": 1.16, "char_count": 7, "start_time_s": 5.04, "end_time_s": 6.2, "words": [{"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5040, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5240, "text": "简"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5360, "text": "单"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5520, "text": "洗"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5680, "text": "漱"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 5840, "text": "后"}], "keyword": "洗漱"}, {"subtitle_id": 5, "text": "便去了产业园，", "timestamp": "00:00:06,200 --> 00:00:07,400", "duration": 1.2, "char_count": 7, "start_time_s": 6.2, "end_time_s": 7.4, "words": [{"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6200, "text": "便"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6400, "text": "去"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6520, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6640, "text": "产"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6800, "text": "业"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 6920, "text": "园"}], "keyword": "产业园"}, {"subtitle_id": 6, "text": "查看各个工厂的运作情况。", "timestamp": "00:00:07,400 --> 00:00:09,500", "duration": 2.1, "char_count": 12, "start_time_s": 7.4, "end_time_s": 9.5, "words": [{"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7400, "text": "查"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7560, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7760, "text": "各"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7920, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8040, "text": "工"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8240, "text": "厂"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8400, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8520, "text": "运"}, {"attribute": {"event": "speech"}, "end_time": 8840, "start_time": 8720, "text": "作"}, {"attribute": {"event": "speech"}, "end_time": 9040, "start_time": 8880, "text": "情"}, {"attribute": {"event": "speech"}, "end_time": 9500, "start_time": 9040, "text": "况"}], "keyword": "工厂"}], "keywords": ["第二天", "龚子华", "酒", "洗漱", "产业园", "工厂"]}, {"chapter": 21, "story_board": "他特别关注制盐厂，因为盐是军队的生命线。他制定了严格的流程，从原料处理到蒸发干燥，每一步都安排得井井有条。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "在制盐厂内仔细查看各个环节，认真制定流程，安排工作", "expression": "专注、严肃"}, {"name": "黑衣人", "gender": "男", "age": "青年", "clothes": "高领圆领劲装，主色调为黑色，搭配深褐色，衣服有暗纹，脚穿黑色靴子，腰系黑色腰带，无过多配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "瘦削敏捷", "identity": "公子华的护卫", "other": "忠诚勇敢", "from_chapter": [0], "action": "愣在原地后转身逃跑，被护卫一剑刺穿", "expression": "惊恐"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "认真地整理和汇报六国消息", "expression": "沉稳"}, {"name": "龙且", "gender": "男", "age": "青年", "clothes": "高领圆领秦朝黑色古代战甲，配红色腰带，主色调为黑色和红色，战甲上有简单的几何花纹，头戴黑色头盔，腰带挂短刀圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "健壮", "identity": "士兵", "other": "刀法娴熟，动作利落", "from_chapter": 28, "action": "在军营中展示实力", "expression": "自信"}, {"name": "英布", "gender": "男", "age": "青年", "clothes": "高领圆领灰色秦代长袍，脚蹬黑色靴子，主色调为灰色，搭配白色，长袍有淡蓝色水纹，头戴灰色毡帽，腰系灰色腰带圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "公子华的心腹", "other": "常低声说话", "from_chapter": 121, "action": "在军营中展示实力", "expression": "坚毅"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "叔孙通", "gender": "男", "age": "中年", "clothes": "高领圆领蓝色齐国长袍，主色调为蓝色，搭配浅蓝色和白色，衣服上有波浪纹图案，腰间束蓝色丝带，配蓝色玉佩，头戴蓝色束发帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "消瘦", "identity": "公子华安插在齐国的棋子", "other": "深谙人心，擅长挑拨离间", "from_chapter": 91, "action": "", "expression": ""}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": 1, "action": "", "expression": ""}], "scene": "产业园", "image_prompt": "中景，一个专注且严肃的青年男子在制盐厂内仔细查看各个环节，认真制定流程并安排工作，他穿着高领圆领的紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束着黑色腰带，佩戴玉佩，圆形衣领，黑色束发，整体形象挺拔，背景是制盐厂产业园, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241384_20250723_112945.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_81705b93-54b0-4677-a7f3-641d56fc839f_1.25x_20250723_105633.wav", "audio_duration": 8.778667, "video_url": "", "story_board_id": 12, "subtitle": [{"subtitle_id": 1, "text": "他特别关注制盐厂，", "timestamp": "00:00:00,160 --> 00:00:01,440", "duration": 1.28, "char_count": 9, "start_time_s": 0.16, "end_time_s": 1.44, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "特"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 440, "text": "别"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 560, "text": "关"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 680, "text": "注"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 800, "text": "制"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 920, "text": "盐"}, {"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1080, "text": "厂"}], "keyword": "制盐厂"}, {"subtitle_id": 2, "text": "因为盐是军队的生命线。", "timestamp": "00:00:01,440 --> 00:00:03,180", "duration": 1.74, "char_count": 11, "start_time_s": 1.44, "end_time_s": 3.18, "words": [{"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1440, "text": "因"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1600, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1680, "text": "盐"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1880, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2040, "text": "军"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2200, "text": "队"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2360, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2440, "text": "生"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2560, "text": "命"}, {"attribute": {"event": "speech"}, "end_time": 3180, "start_time": 2680, "text": "线"}], "keyword": "军队"}, {"subtitle_id": 3, "text": "他制定了严格的流程，", "timestamp": "00:00:03,240 --> 00:00:04,800", "duration": 1.56, "char_count": 10, "start_time_s": 3.24, "end_time_s": 4.8, "words": [{"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3240, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3440, "text": "制"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3600, "text": "定"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3720, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3840, "text": "严"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 4000, "text": "格"}, {"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4120, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4240, "text": "流"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4360, "text": "程"}], "keyword": "流程"}, {"subtitle_id": 4, "text": "从原料处理到蒸发干燥，", "timestamp": "00:00:04,800 --> 00:00:06,680", "duration": 1.88, "char_count": 11, "start_time_s": 4.8, "end_time_s": 6.68, "words": [{"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4800, "text": "从"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 4960, "text": "原"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5120, "text": "料"}, {"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5280, "text": "处"}, {"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5440, "text": "理"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5560, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5800, "text": "蒸"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6000, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6160, "text": "干"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6320, "text": "燥"}], "keyword": "原料处理"}, {"subtitle_id": 5, "text": "每一步都安排得井井有条。", "timestamp": "00:00:06,680 --> 00:00:08,660", "duration": 1.98, "char_count": 12, "start_time_s": 6.68, "end_time_s": 8.66, "words": [{"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6680, "text": "每"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6880, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 7000, "text": "步"}, {"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 7160, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7280, "text": "安"}, {"attribute": {"event": "speech"}, "end_time": 7640, "start_time": 7440, "text": "排"}, {"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7640, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 7880, "start_time": 7720, "text": "井"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7920, "text": "井"}, {"attribute": {"event": "speech"}, "end_time": 8160, "start_time": 8040, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 8660, "start_time": 8160, "text": "条"}], "keyword": "蒸发干燥"}], "keywords": ["制盐厂", "军队", "流程", "原料处理", "蒸发干燥"]}, {"chapter": 21, "story_board": "他还特别强调保密，不允许员工互相交流，举报有奖，防止泄密。当天，制盐厂就产出了500斤盐，公子华拿走200斤，剩下的分给了股东。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "在制盐厂对员工强调保密事宜，拿走200斤盐并将剩下的盐分给股东", "expression": "严肃认真"}, {"name": "黑衣人", "gender": "男", "age": "青年", "clothes": "高领圆领劲装，主色调为黑色，搭配深褐色，衣服有暗纹，脚穿黑色靴子，腰系黑色腰带，无过多配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "瘦削敏捷", "identity": "公子华的护卫", "other": "忠诚勇敢", "from_chapter": [0], "action": "愣在原地后转身逃跑，被护卫一剑刺穿", "expression": "惊恐"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "认真地整理和汇报六国消息", "expression": "沉稳"}, {"name": "龙且", "gender": "男", "age": "青年", "clothes": "高领圆领秦朝黑色古代战甲，配红色腰带，主色调为黑色和红色，战甲上有简单的几何花纹，头戴黑色头盔，腰带挂短刀圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "健壮", "identity": "士兵", "other": "刀法娴熟，动作利落", "from_chapter": 28, "action": "在军营中展示实力", "expression": "自信"}, {"name": "英布", "gender": "男", "age": "青年", "clothes": "高领圆领灰色秦代长袍，脚蹬黑色靴子，主色调为灰色，搭配白色，长袍有淡蓝色水纹，头戴灰色毡帽，腰系灰色腰带圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "公子华的心腹", "other": "常低声说话", "from_chapter": 121, "action": "在军营中展示实力", "expression": "坚毅"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "叔孙通", "gender": "男", "age": "中年", "clothes": "高领圆领蓝色齐国长袍，主色调为蓝色，搭配浅蓝色和白色，衣服上有波浪纹图案，腰间束蓝色丝带，配蓝色玉佩，头戴蓝色束发帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "消瘦", "identity": "公子华安插在齐国的棋子", "other": "深谙人心，擅长挑拨离间", "from_chapter": 91, "action": "", "expression": ""}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": 1, "action": "", "expression": ""}], "scene": "产业园", "image_prompt": "中景，一个严肃认真的青年男子在产业园制盐厂中，一边拿走200斤盐，一边将剩下的盐分给周围人，强调着保密事宜。该男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241414_20250723_113015.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_15450bc9-2916-4bb3-a4b8-9ea717feb4bb_1.25x_20250723_105644.wav", "audio_duration": 10.389333, "video_url": "", "story_board_id": 13, "subtitle": [{"subtitle_id": 1, "text": "他还特别强调保密，", "timestamp": "00:00:00,160 --> 00:00:01,560", "duration": 1.4, "char_count": 9, "start_time_s": 0.16, "end_time_s": 1.56, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 400, "text": "特"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 560, "text": "别"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 680, "text": "强"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 840, "text": "调"}, {"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 960, "text": "保"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1160, "text": "密"}], "keyword": "保密"}, {"subtitle_id": 2, "text": "不允许员工互相交流，", "timestamp": "00:00:01,560 --> 00:00:03,240", "duration": 1.68, "char_count": 10, "start_time_s": 1.56, "end_time_s": 3.24, "words": [{"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1560, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1760, "text": "允"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1920, "text": "许"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2040, "text": "员"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2200, "text": "工"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2400, "text": "互"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2560, "text": "相"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2720, "text": "交"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 2880, "text": "流"}], "keyword": "交流"}, {"subtitle_id": 3, "text": "举报有奖，", "timestamp": "00:00:03,240 --> 00:00:04,000", "duration": 0.76, "char_count": 5, "start_time_s": 3.24, "end_time_s": 4.0, "words": [{"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3240, "text": "举"}, {"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3400, "text": "报"}, {"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3520, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3720, "text": "奖"}], "keyword": "举报"}, {"subtitle_id": 4, "text": "防止泄密。", "timestamp": "00:00:04,000 --> 00:00:05,060", "duration": 1.06, "char_count": 5, "start_time_s": 4.0, "end_time_s": 5.06, "words": [{"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4000, "text": "防"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4280, "text": "止"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4440, "text": "泄"}, {"attribute": {"event": "speech"}, "end_time": 5060, "start_time": 4600, "text": "密"}], "keyword": "泄密"}, {"subtitle_id": 5, "text": "当天，制盐厂就铲除了金盐公司，", "timestamp": "00:00:05,160 --> 00:00:07,720", "duration": 2.56, "char_count": 15, "start_time_s": 5.16, "end_time_s": 7.72, "words": [{"attribute": {"event": "speech"}, "end_time": 5320, "start_time": 5160, "text": "当"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5320, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5760, "text": "制"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5920, "text": "盐"}, {"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 6080, "text": "厂"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6240, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6400, "text": "铲"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6600, "text": "除"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6760, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6880, "text": "金"}, {"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 7040, "text": "盐"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7400, "text": "公"}, {"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7560, "text": "司"}], "keyword": "制盐厂"}, {"subtitle_id": 6, "text": "华拿走金，", "timestamp": "00:00:07,720 --> 00:00:08,560", "duration": 0.84, "char_count": 5, "start_time_s": 7.72, "end_time_s": 8.56, "words": [{"attribute": {"event": "speech"}, "end_time": 7880, "start_time": 7720, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7880, "text": "拿"}, {"attribute": {"event": "speech"}, "end_time": 8160, "start_time": 8040, "text": "走"}, {"attribute": {"event": "speech"}, "end_time": 8560, "start_time": 8200, "text": "金"}], "keyword": "金"}, {"subtitle_id": 7, "text": "剩下的分给了股东。", "timestamp": "00:00:08,560 --> 00:00:10,020", "duration": 1.46, "char_count": 9, "start_time_s": 8.56, "end_time_s": 10.02, "words": [{"attribute": {"event": "speech"}, "end_time": 8760, "start_time": 8560, "text": "剩"}, {"attribute": {"event": "speech"}, "end_time": 8880, "start_time": 8760, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8880, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 9160, "start_time": 9000, "text": "分"}, {"attribute": {"event": "speech"}, "end_time": 9320, "start_time": 9200, "text": "给"}, {"attribute": {"event": "speech"}, "end_time": 9440, "start_time": 9320, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 9600, "start_time": 9440, "text": "股"}, {"attribute": {"event": "speech"}, "end_time": 10020, "start_time": 9600, "text": "东"}], "keyword": "股东"}], "keywords": ["保密", "交流", "举报", "泄密", "制盐厂", "金", "股东"]}, {"chapter": 21, "story_board": "他知道自己做的不只是生意，更是为国家储备资源。回到军营后，他看到士兵们正在学习兵法和识字，叔孙通安排的课程很有成效。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "走进军营，看着士兵们学习，满意地点点头", "expression": "欣慰"}, {"name": "黑衣人", "gender": "男", "age": "青年", "clothes": "高领圆领劲装，主色调为黑色，搭配深褐色，衣服有暗纹，脚穿黑色靴子，腰系黑色腰带，无过多配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "瘦削敏捷", "identity": "公子华的护卫", "other": "忠诚勇敢", "from_chapter": [0], "action": "愣在原地后转身逃跑，被护卫一剑刺穿", "expression": "惊恐"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "认真地整理和汇报六国消息", "expression": "沉稳"}, {"name": "龙且", "gender": "男", "age": "青年", "clothes": "高领圆领秦朝黑色古代战甲，配红色腰带，主色调为黑色和红色，战甲上有简单的几何花纹，头戴黑色头盔，腰带挂短刀圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "健壮", "identity": "士兵", "other": "刀法娴熟，动作利落", "from_chapter": 28, "action": "在军营中展示实力", "expression": "自信"}, {"name": "英布", "gender": "男", "age": "青年", "clothes": "高领圆领灰色秦代长袍，脚蹬黑色靴子，主色调为灰色，搭配白色，长袍有淡蓝色水纹，头戴灰色毡帽，腰系灰色腰带圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "公子华的心腹", "other": "常低声说话", "from_chapter": 121, "action": "在军营中展示实力", "expression": "坚毅"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "叔孙通", "gender": "男", "age": "中年", "clothes": "高领圆领蓝色齐国长袍，主色调为蓝色，搭配浅蓝色和白色，衣服上有波浪纹图案，腰间束蓝色丝带，配蓝色玉佩，头戴蓝色束发帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "消瘦", "identity": "公子华安插在齐国的棋子", "other": "深谙人心，擅长挑拨离间", "from_chapter": 91, "action": "站在一旁，面带微笑", "expression": "自信"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "", "expression": ""}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": 1, "action": "", "expression": ""}], "scene": "军营", "image_prompt": "中景，走进军营看着士兵们学习后满意点头、面带欣慰的青年男子，旁边站着面带微笑、神情自信的中年男子。青年男子挺拔，身穿高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发；中年男子消瘦，身着高领圆领蓝色齐国长袍，主色调为蓝色，搭配浅蓝色和白色，衣服上有波浪纹图案，腰间束蓝色丝带，配蓝色玉佩，头戴蓝色束发帽，圆形衣领，黑色束发，背景是军营, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241438_20250723_113039.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_23e7962f-3017-45da-a96c-294f94042a7e_1.25x_20250723_105652.wav", "audio_duration": 9.986667, "video_url": "", "story_board_id": 14, "subtitle": [{"subtitle_id": 1, "text": "他知道自己做的不只是生意，", "timestamp": "00:00:00,200 --> 00:00:02,040", "duration": 1.84, "char_count": 13, "start_time_s": 0.2, "end_time_s": 2.04, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 400, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 640, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 840, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 960, "text": "做"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1080, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1200, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1320, "text": "只"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1480, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1640, "text": "生"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1760, "text": "意"}], "keyword": "生意"}, {"subtitle_id": 2, "text": "更是为国家储备资源。", "timestamp": "00:00:02,040 --> 00:00:03,820", "duration": 1.78, "char_count": 10, "start_time_s": 2.04, "end_time_s": 3.82, "words": [{"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2040, "text": "更"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2240, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 2520, "start_time": 2360, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2520, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2680, "text": "家"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2840, "text": "储"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3040, "text": "备"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3200, "text": "资"}, {"attribute": {"event": "speech"}, "end_time": 3820, "start_time": 3360, "text": "源"}], "keyword": "国家"}, {"subtitle_id": 3, "text": "回到军营后，", "timestamp": "00:00:03,960 --> 00:00:04,960", "duration": 1.0, "char_count": 6, "start_time_s": 3.96, "end_time_s": 4.96, "words": [{"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 3960, "text": "回"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4160, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4280, "text": "军"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4400, "text": "营"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4560, "text": "后"}], "keyword": "军营"}, {"subtitle_id": 4, "text": "他看到", "timestamp": "00:00:04,960 --> 00:00:05,400", "duration": 0.44, "char_count": 3, "start_time_s": 4.96, "end_time_s": 5.4, "words": [{"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 4960, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5160, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5280, "text": "到"}], "keyword": "士兵"}, {"subtitle_id": 5, "text": "士兵们正在学习兵法和识字书，", "timestamp": "00:00:05,400 --> 00:00:07,680", "duration": 2.28, "char_count": 14, "start_time_s": 5.4, "end_time_s": 7.68, "words": [{"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5400, "text": "士"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5560, "text": "兵"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5720, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5840, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6000, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6160, "text": "学"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6320, "text": "习"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6440, "text": "兵"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6640, "text": "法"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6840, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 6960, "text": "识"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7160, "text": "字"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7560, "text": "书"}], "keyword": "兵法"}, {"subtitle_id": 6, "text": "孙通安排的课程很有成效。", "timestamp": "00:00:07,680 --> 00:00:09,660", "duration": 1.98, "char_count": 12, "start_time_s": 7.68, "end_time_s": 9.66, "words": [{"attribute": {"event": "speech"}, "end_time": 7840, "start_time": 7680, "text": "孙"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7840, "text": "通"}, {"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7960, "text": "安"}, {"attribute": {"event": "speech"}, "end_time": 8240, "start_time": 8080, "text": "排"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8240, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8320, "text": "课"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8480, "text": "程"}, {"attribute": {"event": "speech"}, "end_time": 8880, "start_time": 8680, "text": "很"}, {"attribute": {"event": "speech"}, "end_time": 9040, "start_time": 8880, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 9160, "start_time": 9040, "text": "成"}, {"attribute": {"event": "speech"}, "end_time": 9660, "start_time": 9200, "text": "效"}], "keyword": "课程"}], "keywords": ["生意", "国家", "军营", "士兵", "兵法", "课程"]}, {"chapter": 21, "story_board": "公子华满意地点点头，然后带着章邯离开了。晚上，他回宫见了赢政，将新产的盐呈上。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "满意地点点头，带着章邯离开，晚上回宫将新产的盐呈给嬴政", "expression": "满意"}, {"name": "黑衣人", "gender": "男", "age": "青年", "clothes": "高领圆领劲装，主色调为黑色，搭配深褐色，衣服有暗纹，脚穿黑色靴子，腰系黑色腰带，无过多配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "瘦削敏捷", "identity": "公子华的护卫", "other": "忠诚勇敢", "from_chapter": [0], "action": "愣在原地后转身逃跑，被护卫一剑刺穿", "expression": "惊恐"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "认真地整理和汇报六国消息", "expression": "沉稳"}, {"name": "龙且", "gender": "男", "age": "青年", "clothes": "高领圆领秦朝黑色古代战甲，配红色腰带，主色调为黑色和红色，战甲上有简单的几何花纹，头戴黑色头盔，腰带挂短刀圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "健壮", "identity": "士兵", "other": "刀法娴熟，动作利落", "from_chapter": 28, "action": "在军营中展示实力", "expression": "自信"}, {"name": "英布", "gender": "男", "age": "青年", "clothes": "高领圆领灰色秦代长袍，脚蹬黑色靴子，主色调为灰色，搭配白色，长袍有淡蓝色水纹，头戴灰色毡帽，腰系灰色腰带圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "公子华的心腹", "other": "常低声说话", "from_chapter": 121, "action": "在军营中展示实力", "expression": "坚毅"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "叔孙通", "gender": "男", "age": "中年", "clothes": "高领圆领蓝色齐国长袍，主色调为蓝色，搭配浅蓝色和白色，衣服上有波浪纹图案，腰间束蓝色丝带，配蓝色玉佩，头戴蓝色束发帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "消瘦", "identity": "公子华安插在齐国的棋子", "other": "深谙人心，擅长挑拨离间", "from_chapter": 91, "action": "站在一旁，面带微笑", "expression": "自信"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "跟着公子华离开", "expression": "无明确表情"}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": 1, "action": "坐在殿中接收公子华呈上的盐", "expression": "无明确表情"}], "scene": "皇宫大殿", "image_prompt": "中景，皇宫大殿中，满意地点点头的青年男子挺拔地向前走，旁边魁梧的青年男子跟随着离开，随后画面转换到殿中，中年男子霸气威严地坐在那里接收呈上的盐。向前走的青年男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，黑色束发；跟随的青年男子穿着高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾，黑色束发；坐在殿中的中年男子穿着战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241461_20250723_113102.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_f59e8a21-72d1-418c-adeb-b42ef94ddae8_1.25x_20250723_105625.wav", "audio_duration": 7.069333, "video_url": "", "story_board_id": 15, "subtitle": [{"subtitle_id": 1, "text": "宫子华满意地点点头，", "timestamp": "00:00:00,160 --> 00:00:01,680", "duration": 1.52, "char_count": 10, "start_time_s": 0.16, "end_time_s": 1.68, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 440, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 600, "text": "满"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 720, "text": "意"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 880, "text": "地"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 1000, "text": "点"}, {"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 1040, "text": "点"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1320, "text": "头"}], "keyword": "宫子华"}, {"subtitle_id": 2, "text": "然后带着张涵离开了。", "timestamp": "00:00:01,680 --> 00:00:03,580", "duration": 1.9, "char_count": 10, "start_time_s": 1.68, "end_time_s": 3.58, "words": [{"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1680, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1880, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2040, "text": "带"}, {"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2200, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2320, "text": "张"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2480, "text": "涵"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2680, "text": "离"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2840, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 3580, "start_time": 3040, "text": "了"}], "keyword": "张涵"}, {"subtitle_id": 3, "text": "晚上，他回宫见了嬴政，", "timestamp": "00:00:03,600 --> 00:00:05,440", "duration": 1.84, "char_count": 11, "start_time_s": 3.6, "end_time_s": 5.44, "words": [{"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3600, "text": "晚"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3800, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4320, "text": "回"}, {"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4520, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4680, "text": "见"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4800, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4920, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5080, "text": "政"}], "keyword": "嬴政"}, {"subtitle_id": 4, "text": "将新产的盐盛上。", "timestamp": "00:00:05,440 --> 00:00:06,980", "duration": 1.54, "char_count": 8, "start_time_s": 5.44, "end_time_s": 6.98, "words": [{"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5440, "text": "将"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5600, "text": "新"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5840, "text": "产"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6040, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6160, "text": "盐"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6360, "text": "盛"}, {"attribute": {"event": "speech"}, "end_time": 6980, "start_time": 6520, "text": "上"}], "keyword": "盐"}], "keywords": ["宫子华", "张涵", "嬴政", "盐"]}, {"chapter": 21, "story_board": "赢政尝了一口，惊讶不已，连连称赞。他问公子华今天产了多少，公子华回答是500斤，赢政不敢相信，但事实摆在眼前。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站着回答赢政的问题", "expression": "平静"}, {"name": "黑衣人", "gender": "男", "age": "青年", "clothes": "高领圆领劲装，主色调为黑色，搭配深褐色，衣服有暗纹，脚穿黑色靴子，腰系黑色腰带，无过多配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "瘦削敏捷", "identity": "公子华的护卫", "other": "忠诚勇敢", "from_chapter": [0], "action": "愣在原地后转身逃跑，被护卫一剑刺穿", "expression": "惊恐"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "认真地整理和汇报六国消息", "expression": "沉稳"}, {"name": "龙且", "gender": "男", "age": "青年", "clothes": "高领圆领秦朝黑色古代战甲，配红色腰带，主色调为黑色和红色，战甲上有简单的几何花纹，头戴黑色头盔，腰带挂短刀圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "健壮", "identity": "士兵", "other": "刀法娴熟，动作利落", "from_chapter": 28, "action": "在军营中展示实力", "expression": "自信"}, {"name": "英布", "gender": "男", "age": "青年", "clothes": "高领圆领灰色秦代长袍，脚蹬黑色靴子，主色调为灰色，搭配白色，长袍有淡蓝色水纹，头戴灰色毡帽，腰系灰色腰带圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "公子华的心腹", "other": "常低声说话", "from_chapter": 121, "action": "在军营中展示实力", "expression": "坚毅"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "叔孙通", "gender": "男", "age": "中年", "clothes": "高领圆领蓝色齐国长袍，主色调为蓝色，搭配浅蓝色和白色，衣服上有波浪纹图案，腰间束蓝色丝带，配蓝色玉佩，头戴蓝色束发帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "消瘦", "identity": "公子华安插在齐国的棋子", "other": "深谙人心，擅长挑拨离间", "from_chapter": 91, "action": "站在一旁，面带微笑", "expression": "自信"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "跟着公子华离开", "expression": "无明确表情"}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": 1, "action": "坐在殿中接收公子华呈上的盐", "expression": "无明确表情"}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿中，一位神情平静、挺拔站立的青年男子，似乎正在回应他人。该青年男子身着高领圆领的汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束着黑色腰带并佩戴玉佩，有着圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241486_20250723_113126.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_2c414b83-1804-4fd4-986d-9060331cd4e4_1.25x_20250723_105648.wav", "audio_duration": 9.256, "video_url": "", "story_board_id": 16, "subtitle": [{"subtitle_id": 1, "text": "嬴政尝了一口，", "timestamp": "00:00:00,200 --> 00:00:01,200", "duration": 1.0, "char_count": 7, "start_time_s": 0.2, "end_time_s": 1.2, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 480, "text": "尝"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 640, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 720, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 840, "text": "口"}], "keyword": "嬴政"}, {"subtitle_id": 2, "text": "惊讶不已，", "timestamp": "00:00:01,200 --> 00:00:02,000", "duration": 0.8, "char_count": 5, "start_time_s": 1.2, "end_time_s": 2.0, "words": [{"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1200, "text": "惊"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1400, "text": "讶"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1520, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1680, "text": "已"}], "keyword": "惊讶"}, {"subtitle_id": 3, "text": "连连称赞。", "timestamp": "00:00:02,000 --> 00:00:03,100", "duration": 1.1, "char_count": 5, "start_time_s": 2.0, "end_time_s": 3.1, "words": [{"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2000, "text": "连"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2240, "text": "连"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2440, "text": "称"}, {"attribute": {"event": "speech"}, "end_time": 3100, "start_time": 2640, "text": "赞"}], "keyword": "称赞"}, {"subtitle_id": 4, "text": "他问宫子华今天产了多少，", "timestamp": "00:00:03,200 --> 00:00:05,120", "duration": 1.92, "char_count": 12, "start_time_s": 3.2, "end_time_s": 5.12, "words": [{"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3360, "text": "问"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3480, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3640, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3760, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4040, "text": "今"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4200, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4360, "text": "产"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4520, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4640, "text": "多"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 4800, "text": "少"}], "keyword": "宫子华"}, {"subtitle_id": 5, "text": "宫子华回答", "timestamp": "00:00:05,120 --> 00:00:05,880", "duration": 0.76, "char_count": 5, "start_time_s": 5.12, "end_time_s": 5.88, "words": [{"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5120, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5280, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5400, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5560, "text": "回"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5680, "text": "答"}], "keyword": "回答"}, {"subtitle_id": 6, "text": "至今嬴政不敢相信，", "timestamp": "00:00:05,880 --> 00:00:07,400", "duration": 1.52, "char_count": 9, "start_time_s": 5.88, "end_time_s": 7.4, "words": [{"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5880, "text": "至"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 6000, "text": "今"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6360, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6560, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6720, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6800, "text": "敢"}, {"attribute": {"event": "speech"}, "end_time": 7120, "start_time": 6960, "text": "相"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7120, "text": "信"}], "keyword": "嬴政"}, {"subtitle_id": 7, "text": "但事实摆在眼前。", "timestamp": "00:00:07,400 --> 00:00:08,900", "duration": 1.5, "char_count": 8, "start_time_s": 7.4, "end_time_s": 8.9, "words": [{"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7400, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7600, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7800, "text": "实"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 7960, "text": "摆"}, {"attribute": {"event": "speech"}, "end_time": 8280, "start_time": 8120, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8280, "text": "眼"}, {"attribute": {"event": "speech"}, "end_time": 8900, "start_time": 8400, "text": "前"}], "keyword": "事实"}], "keywords": ["嬴政", "惊讶", "称赞", "宫子华", "回答", "嬴政", "事实"]}, {"chapter": 21, "story_board": "赢政高兴极了，决定赏赐长安县给公子华，并支持他把盐卖到六国。公子华没有多要，他知道自己需要的是资源和人才，而不是虚名。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "拱手站立，沉稳地回应", "expression": "淡定"}, {"name": "黑衣人", "gender": "男", "age": "青年", "clothes": "高领圆领劲装，主色调为黑色，搭配深褐色，衣服有暗纹，脚穿黑色靴子，腰系黑色腰带，无过多配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "瘦削敏捷", "identity": "公子华的护卫", "other": "忠诚勇敢", "from_chapter": [0], "action": "愣在原地后转身逃跑，被护卫一剑刺穿", "expression": "惊恐"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "认真地整理和汇报六国消息", "expression": "沉稳"}, {"name": "龙且", "gender": "男", "age": "青年", "clothes": "高领圆领秦朝黑色古代战甲，配红色腰带，主色调为黑色和红色，战甲上有简单的几何花纹，头戴黑色头盔，腰带挂短刀圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "健壮", "identity": "士兵", "other": "刀法娴熟，动作利落", "from_chapter": 28, "action": "在军营中展示实力", "expression": "自信"}, {"name": "英布", "gender": "男", "age": "青年", "clothes": "高领圆领灰色秦代长袍，脚蹬黑色靴子，主色调为灰色，搭配白色，长袍有淡蓝色水纹，头戴灰色毡帽，腰系灰色腰带圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "公子华的心腹", "other": "常低声说话", "from_chapter": 121, "action": "在军营中展示实力", "expression": "坚毅"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "叔孙通", "gender": "男", "age": "中年", "clothes": "高领圆领蓝色齐国长袍，主色调为蓝色，搭配浅蓝色和白色，衣服上有波浪纹图案，腰间束蓝色丝带，配蓝色玉佩，头戴蓝色束发帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "消瘦", "identity": "公子华安插在齐国的棋子", "other": "深谙人心，擅长挑拨离间", "from_chapter": 91, "action": "站在一旁，面带微笑", "expression": "自信"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "跟着公子华离开", "expression": "无明确表情"}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": 1, "action": "坐在宫殿中，兴奋地大手一挥", "expression": "喜悦"}], "scene": "皇宫大殿", "image_prompt": "中景，宫殿中，一位兴奋地大手一挥、满脸喜悦的中年男子坐在那里，显得魁梧且霸气威严；旁边一位拱手站立、淡定沉稳的青年男子挺拔地站着。坐在那里的中年男子穿着战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠，圆形衣领，黑色束发；拱手站立的青年男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241506_20250723_113146.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_6bcf2ef4-3eee-4360-a977-692ed92c6471_1.25x_20250723_105700.wav", "audio_duration": 10.469333, "video_url": "", "story_board_id": 17, "subtitle": [{"subtitle_id": 1, "text": "嬴政高兴极了，", "timestamp": "00:00:00,200 --> 00:00:01,280", "duration": 1.08, "char_count": 7, "start_time_s": 0.2, "end_time_s": 1.28, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 320, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 480, "text": "高"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 640, "text": "兴"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 800, "text": "极"}, {"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 920, "text": "了"}], "keyword": "嬴政"}, {"subtitle_id": 2, "text": "决定赏赐长安县给宫子华，", "timestamp": "00:00:01,280 --> 00:00:03,120", "duration": 1.84, "char_count": 12, "start_time_s": 1.28, "end_time_s": 3.12, "words": [{"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1280, "text": "决"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1440, "text": "定"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1600, "text": "赏"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1760, "text": "赐"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1920, "text": "长"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2040, "text": "安"}, {"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2200, "text": "县"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2320, "text": "给"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2480, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2640, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 2760, "text": "华"}], "keyword": "长安县"}, {"subtitle_id": 3, "text": "并支持他把盐卖到六国。", "timestamp": "00:00:03,120 --> 00:00:05,180", "duration": 2.06, "char_count": 11, "start_time_s": 3.12, "end_time_s": 5.18, "words": [{"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3120, "text": "并"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3320, "text": "支"}, {"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3480, "text": "持"}, {"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3600, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3880, "text": "把"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4080, "text": "盐"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4240, "text": "卖"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4400, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4520, "text": "六"}, {"attribute": {"event": "speech"}, "end_time": 5180, "start_time": 4720, "text": "国"}], "keyword": "宫子华"}, {"subtitle_id": 4, "text": "宫子华没有多要，", "timestamp": "00:00:05,360 --> 00:00:06,560", "duration": 1.2, "char_count": 8, "start_time_s": 5.36, "end_time_s": 6.56, "words": [{"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5360, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5520, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5680, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5840, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 6080, "start_time": 5960, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6080, "text": "多"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6200, "text": "要"}], "keyword": "宫子华"}, {"subtitle_id": 5, "text": "他知道自己需要的是资源和人才，", "timestamp": "00:00:06,560 --> 00:00:08,960", "duration": 2.4, "char_count": 15, "start_time_s": 6.56, "end_time_s": 8.96, "words": [{"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6560, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6760, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6880, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 7120, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7280, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7440, "text": "需"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7560, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 7840, "start_time": 7680, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7840, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 7960, "text": "资"}, {"attribute": {"event": "speech"}, "end_time": 8280, "start_time": 8160, "text": "源"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8280, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 8560, "start_time": 8400, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8640, "text": "才"}], "keyword": "资源"}, {"subtitle_id": 6, "text": "而不是虚名。", "timestamp": "00:00:08,960 --> 00:00:10,140", "duration": 1.18, "char_count": 6, "start_time_s": 8.96, "end_time_s": 10.14, "words": [{"attribute": {"event": "speech"}, "end_time": 9160, "start_time": 8960, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 9360, "start_time": 9160, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 9520, "start_time": 9360, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 9680, "start_time": 9520, "text": "虚"}, {"attribute": {"event": "speech"}, "end_time": 10140, "start_time": 9680, "text": "名"}], "keyword": "虚名"}], "keywords": ["嬴政", "长安县", "宫子华", "宫子华", "资源", "虚名"]}, {"chapter": 21, "story_board": "他提出再招5000人，专门负责产业园的安全，赢政立刻批准。夜深人静，父子俩坐在殿中批奏折，边批边聊，气氛温馨而庄重。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "向嬴政提出再招5000人负责产业园安全，之后与嬴政一起坐在殿中批奏折、聊天", "expression": "沉稳、坚定"}, {"name": "黑衣人", "gender": "男", "age": "青年", "clothes": "高领圆领劲装，主色调为黑色，搭配深褐色，衣服有暗纹，脚穿黑色靴子，腰系黑色腰带，无过多配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "瘦削敏捷", "identity": "公子华的护卫", "other": "忠诚勇敢", "from_chapter": [0], "action": "愣在原地后转身逃跑，被护卫一剑刺穿", "expression": "惊恐"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "认真地整理和汇报六国消息", "expression": "沉稳"}, {"name": "龙且", "gender": "男", "age": "青年", "clothes": "高领圆领秦朝黑色古代战甲，配红色腰带，主色调为黑色和红色，战甲上有简单的几何花纹，头戴黑色头盔，腰带挂短刀圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "健壮", "identity": "士兵", "other": "刀法娴熟，动作利落", "from_chapter": 28, "action": "在军营中展示实力", "expression": "自信"}, {"name": "英布", "gender": "男", "age": "青年", "clothes": "高领圆领灰色秦代长袍，脚蹬黑色靴子，主色调为灰色，搭配白色，长袍有淡蓝色水纹，头戴灰色毡帽，腰系灰色腰带圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "公子华的心腹", "other": "常低声说话", "from_chapter": 121, "action": "在军营中展示实力", "expression": "坚毅"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "叔孙通", "gender": "男", "age": "中年", "clothes": "高领圆领蓝色齐国长袍，主色调为蓝色，搭配浅蓝色和白色，衣服上有波浪纹图案，腰间束蓝色丝带，配蓝色玉佩，头戴蓝色束发帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "消瘦", "identity": "公子华安插在齐国的棋子", "other": "深谙人心，擅长挑拨离间", "from_chapter": 91, "action": "站在一旁，面带微笑", "expression": "自信"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "跟着公子华离开", "expression": "无明确表情"}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": 1, "action": "批准公子华的提议，与公子华一起坐在殿中批奏折、聊天", "expression": "欣慰、严肃"}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿中，两位男子并肩坐在桌前批阅奏折。其中一位中年男子神态欣慰且严肃，身形魁梧，霸气威严；另一位青年男子沉稳坚定，挺拔地坐着，言辞间犀利善辩。中年男子身着战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠，黑色束发；青年男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241531_20250723_113211.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_04f5c048-74ee-4403-9cbb-b5a2e7d0c711_1.25x_20250723_105640.wav", "audio_duration": 9.621333, "video_url": "", "story_board_id": 18, "subtitle": [{"subtitle_id": 1, "text": "他提出在招人，", "timestamp": "00:00:00,160 --> 00:00:01,200", "duration": 1.04, "char_count": 7, "start_time_s": 0.16, "end_time_s": 1.2, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "提"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 440, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 560, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 680, "text": "招"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 840, "text": "人"}], "keyword": "他"}, {"subtitle_id": 2, "text": "专门负责产业园的安全。", "timestamp": "00:00:01,200 --> 00:00:02,880", "duration": 1.68, "char_count": 11, "start_time_s": 1.2, "end_time_s": 2.88, "words": [{"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1200, "text": "专"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1360, "text": "门"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1520, "text": "负"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1640, "text": "责"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1760, "text": "产"}, {"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1960, "text": "业"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2080, "text": "园"}, {"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2240, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2320, "text": "安"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2480, "text": "全"}], "keyword": "产业园"}, {"subtitle_id": 3, "text": "嬴政立刻批准。", "timestamp": "00:00:02,880 --> 00:00:04,220", "duration": 1.34, "char_count": 7, "start_time_s": 2.88, "end_time_s": 4.22, "words": [{"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2880, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3080, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3200, "text": "立"}, {"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3440, "text": "刻"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3600, "text": "批"}, {"attribute": {"event": "speech"}, "end_time": 4220, "start_time": 3760, "text": "准"}], "keyword": "嬴政"}, {"subtitle_id": 4, "text": "夜深人静，", "timestamp": "00:00:04,320 --> 00:00:05,240", "duration": 0.92, "char_count": 5, "start_time_s": 4.32, "end_time_s": 5.24, "words": [{"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4320, "text": "夜"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4520, "text": "深"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4640, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 4840, "text": "静"}], "keyword": "夜深"}, {"subtitle_id": 5, "text": "父子俩坐在店中批奏折，", "timestamp": "00:00:05,240 --> 00:00:06,920", "duration": 1.68, "char_count": 11, "start_time_s": 5.24, "end_time_s": 6.92, "words": [{"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5240, "text": "父"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5400, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5520, "text": "俩"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5720, "text": "坐"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5840, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5960, "text": "店"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6120, "text": "中"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6280, "text": "批"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6480, "text": "奏"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6640, "text": "折"}], "keyword": "父子"}, {"subtitle_id": 6, "text": "边批边聊，", "timestamp": "00:00:06,920 --> 00:00:07,800", "duration": 0.88, "char_count": 5, "start_time_s": 6.92, "end_time_s": 7.8, "words": [{"attribute": {"event": "speech"}, "end_time": 7120, "start_time": 6920, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7160, "text": "批"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7320, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7480, "text": "聊"}], "keyword": "批奏折"}, {"subtitle_id": 7, "text": "气氛温馨而庄重。", "timestamp": "00:00:07,800 --> 00:00:09,300", "duration": 1.5, "char_count": 8, "start_time_s": 7.8, "end_time_s": 9.3, "words": [{"attribute": {"event": "speech"}, "end_time": 8000, "start_time": 7800, "text": "气"}, {"attribute": {"event": "speech"}, "end_time": 8160, "start_time": 8000, "text": "氛"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8160, "text": "温"}, {"attribute": {"event": "speech"}, "end_time": 8560, "start_time": 8360, "text": "馨"}, {"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8560, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 8880, "start_time": 8720, "text": "庄"}, {"attribute": {"event": "speech"}, "end_time": 9300, "start_time": 8880, "text": "重"}], "keyword": "气氛"}], "keywords": ["他", "产业园", "嬴政", "夜深", "父子", "批奏折", "气氛"]}, {"chapter": 21, "story_board": "公子华知道，他的路才刚刚开始，但他已经迈出了最关键的一步。从一个普通公子，到掌控军营、产业、甚至朝堂，他靠的不是运气，而是智慧、远见和坚定的信念。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站在宫殿中，微微抬头，眼神坚定地望向远方", "expression": "自信、坚毅"}, {"name": "黑衣人", "gender": "男", "age": "青年", "clothes": "高领圆领劲装，主色调为黑色，搭配深褐色，衣服有暗纹，脚穿黑色靴子，腰系黑色腰带，无过多配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "瘦削敏捷", "identity": "公子华的护卫", "other": "忠诚勇敢", "from_chapter": [0], "action": "愣在原地后转身逃跑，被护卫一剑刺穿", "expression": "惊恐"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "认真地整理和汇报六国消息", "expression": "沉稳"}, {"name": "龙且", "gender": "男", "age": "青年", "clothes": "高领圆领秦朝黑色古代战甲，配红色腰带，主色调为黑色和红色，战甲上有简单的几何花纹，头戴黑色头盔，腰带挂短刀圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "健壮", "identity": "士兵", "other": "刀法娴熟，动作利落", "from_chapter": 28, "action": "在军营中展示实力", "expression": "自信"}, {"name": "英布", "gender": "男", "age": "青年", "clothes": "高领圆领灰色秦代长袍，脚蹬黑色靴子，主色调为灰色，搭配白色，长袍有淡蓝色水纹，头戴灰色毡帽，腰系灰色腰带圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "公子华的心腹", "other": "常低声说话", "from_chapter": 121, "action": "在军营中展示实力", "expression": "坚毅"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "叔孙通", "gender": "男", "age": "中年", "clothes": "高领圆领蓝色齐国长袍，主色调为蓝色，搭配浅蓝色和白色，衣服上有波浪纹图案，腰间束蓝色丝带，配蓝色玉佩，头戴蓝色束发帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "消瘦", "identity": "公子华安插在齐国的棋子", "other": "深谙人心，擅长挑拨离间", "from_chapter": 91, "action": "站在一旁，面带微笑", "expression": "自信"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "跟着公子华离开", "expression": "无明确表情"}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": 1, "action": "批准公子华的提议，与公子华一起坐在殿中批奏折、聊天", "expression": "欣慰、严肃"}], "scene": "皇宫大殿", "image_prompt": "中景，站在皇宫大殿中的青年男子，微微抬头，眼神坚定地望向远方，神情自信、坚毅，穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发，身姿挺拔, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://p9-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/2025072311322577E5189EE9839EBAD37C-7692-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1753327949&x-signature=%2Fy%2Fwk3CGe%2BvEjl%2F9J2aoVyqs6mI%3D", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_4f968a00-ce66-4b75-a1ab-9c686c1db16f_1.25x_20250723_105656.wav", "audio_duration": 12.370667, "video_url": "", "story_board_id": 19, "subtitle": [{"subtitle_id": 1, "text": "公子华知道，", "timestamp": "00:00:00,160 --> 00:00:01,040", "duration": 0.88, "char_count": 6, "start_time_s": 0.16, "end_time_s": 1.04, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "公"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 440, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 560, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 680, "text": "道"}], "keyword": "公子华"}, {"subtitle_id": 2, "text": "他的路才刚刚开始，", "timestamp": "00:00:01,040 --> 00:00:02,560", "duration": 1.52, "char_count": 9, "start_time_s": 1.04, "end_time_s": 2.56, "words": [{"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1040, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1200, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1320, "text": "路"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1480, "text": "才"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1640, "text": "刚"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1680, "text": "刚"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 1960, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2160, "text": "始"}], "keyword": "路"}, {"subtitle_id": 3, "text": "但他已经迈出了最关键的一步。", "timestamp": "00:00:02,560 --> 00:00:04,900", "duration": 2.34, "char_count": 14, "start_time_s": 2.56, "end_time_s": 4.9, "words": [{"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2560, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2720, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2880, "text": "已"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 3000, "text": "经"}, {"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3120, "text": "迈"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3320, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3480, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3600, "text": "最"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3800, "text": "关"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4040, "text": "键"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4160, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4440, "start_time": 4320, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 4900, "start_time": 4440, "text": "步"}], "keyword": "关键一步"}, {"subtitle_id": 4, "text": "从一个普通公子到掌控军营产业，", "timestamp": "00:00:05,000 --> 00:00:07,560", "duration": 2.56, "char_count": 15, "start_time_s": 5.0, "end_time_s": 7.56, "words": [{"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5000, "text": "从"}, {"attribute": {"event": "speech"}, "end_time": 5320, "start_time": 5200, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5320, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5440, "text": "普"}, {"attribute": {"event": "speech"}, "end_time": 5760, "start_time": 5600, "text": "通"}, {"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5760, "text": "公"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5920, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6200, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6400, "text": "掌"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6600, "text": "控"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6800, "text": "军"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6920, "text": "营"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7120, "text": "产"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7320, "text": "业"}], "keyword": "军营产业"}, {"subtitle_id": 5, "text": "甚至朝堂，", "timestamp": "00:00:07,560 --> 00:00:08,480", "duration": 0.92, "char_count": 5, "start_time_s": 7.56, "end_time_s": 8.48, "words": [{"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7560, "text": "甚"}, {"attribute": {"event": "speech"}, "end_time": 7840, "start_time": 7720, "text": "至"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7840, "text": "朝"}, {"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8080, "text": "堂"}], "keyword": "朝堂"}, {"subtitle_id": 6, "text": "他靠的不是运气，", "timestamp": "00:00:08,480 --> 00:00:09,600", "duration": 1.12, "char_count": 8, "start_time_s": 8.48, "end_time_s": 9.6, "words": [{"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8480, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8640, "text": "靠"}, {"attribute": {"event": "speech"}, "end_time": 8920, "start_time": 8800, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 9080, "start_time": 8920, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 9160, "start_time": 9080, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 9320, "start_time": 9160, "text": "运"}, {"attribute": {"event": "speech"}, "end_time": 9600, "start_time": 9360, "text": "气"}], "keyword": "运气"}, {"subtitle_id": 7, "text": "而是智慧、远见和坚定的信念。", "timestamp": "00:00:09,600 --> 00:00:12,020", "duration": 2.42, "char_count": 14, "start_time_s": 9.6, "end_time_s": 12.02, "words": [{"attribute": {"event": "speech"}, "end_time": 9800, "start_time": 9600, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 9960, "start_time": 9800, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 10160, "start_time": 9960, "text": "智"}, {"attribute": {"event": "speech"}, "end_time": 10320, "start_time": 10160, "text": "慧"}, {"attribute": {"event": "speech"}, "end_time": 10480, "start_time": 10320, "text": "远"}, {"attribute": {"event": "speech"}, "end_time": 10680, "start_time": 10520, "text": "见"}, {"attribute": {"event": "speech"}, "end_time": 10920, "start_time": 10760, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 11080, "start_time": 10960, "text": "坚"}, {"attribute": {"event": "speech"}, "end_time": 11280, "start_time": 11120, "text": "定"}, {"attribute": {"event": "speech"}, "end_time": 11440, "start_time": 11280, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 11560, "start_time": 11440, "text": "信"}, {"attribute": {"event": "speech"}, "end_time": 12020, "start_time": 11560, "text": "念"}], "keyword": "智慧信念"}], "keywords": ["公子华", "路", "关键一步", "军营产业", "朝堂", "运气", "智慧信念"]}, {"chapter": 21, "story_board": "他不贪图享乐，而是为了更大的目标努力。他的故事，才刚刚开始。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站在窗前，凝视远方", "expression": "坚定"}, {"name": "黑衣人", "gender": "男", "age": "青年", "clothes": "高领圆领劲装，主色调为黑色，搭配深褐色，衣服有暗纹，脚穿黑色靴子，腰系黑色腰带，无过多配饰圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "瘦削敏捷", "identity": "公子华的护卫", "other": "忠诚勇敢", "from_chapter": [0], "action": "愣在原地后转身逃跑，被护卫一剑刺穿", "expression": "惊恐"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "认真地整理和汇报六国消息", "expression": "沉稳"}, {"name": "龙且", "gender": "男", "age": "青年", "clothes": "高领圆领秦朝黑色古代战甲，配红色腰带，主色调为黑色和红色，战甲上有简单的几何花纹，头戴黑色头盔，腰带挂短刀圆形衣领，圆形衣领", "hairstyle": "黑色短发束起", "figure": "健壮", "identity": "士兵", "other": "刀法娴熟，动作利落", "from_chapter": 28, "action": "在军营中展示实力", "expression": "自信"}, {"name": "英布", "gender": "男", "age": "青年", "clothes": "高领圆领灰色秦代长袍，脚蹬黑色靴子，主色调为灰色，搭配白色，长袍有淡蓝色水纹，头戴灰色毡帽，腰系灰色腰带圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "精悍", "identity": "公子华的心腹", "other": "常低声说话", "from_chapter": 121, "action": "在军营中展示实力", "expression": "坚毅"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "叔孙通", "gender": "男", "age": "中年", "clothes": "高领圆领蓝色齐国长袍，主色调为蓝色，搭配浅蓝色和白色，衣服上有波浪纹图案，腰间束蓝色丝带，配蓝色玉佩，头戴蓝色束发帽圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "消瘦", "identity": "公子华安插在齐国的棋子", "other": "深谙人心，擅长挑拨离间", "from_chapter": 91, "action": "站在一旁，面带微笑", "expression": "自信"}, {"name": "章邯", "gender": "男", "age": "青年", "clothes": "高领圆领秦代汉服长袍，主色调为褐色，搭配黄色，绣有暗纹，腰间束褐色腰带，头戴黑色束发巾圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "与公子华相识的人", "other": "棋艺精湛", "from_chapter": 7, "action": "跟着公子华离开", "expression": "无明确表情"}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": 1, "action": "批准公子华的提议，与公子华一起坐在殿中批奏折、聊天", "expression": "欣慰、严肃"}], "scene": "中国古代建筑", "image_prompt": "中景，站在窗前凝视远方、神情坚定的青年男子，身着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，有着圆形衣领，黑色束发，背景是中国古代建筑, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753241569_20250723_113250.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_5f80ccde-95fd-4acf-b9a4-380453b1119f_1.25x_20250723_105637.wav", "audio_duration": 5.533333, "video_url": "", "story_board_id": 20, "subtitle": [{"subtitle_id": 1, "text": "他不贪图享乐，", "timestamp": "00:00:00,200 --> 00:00:01,120", "duration": 0.92, "char_count": 7, "start_time_s": 0.2, "end_time_s": 1.12, "words": [{"attribute": {"event": "speech"}, "end_time": 240, "start_time": 200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 240, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 400, "text": "贪"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 520, "text": "图"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 680, "text": "享"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 840, "text": "乐"}], "keyword": "他"}, {"subtitle_id": 2, "text": "而是为了更大的目标努力，", "timestamp": "00:00:01,120 --> 00:00:03,100", "duration": 1.98, "char_count": 12, "start_time_s": 1.12, "end_time_s": 3.1, "words": [{"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1120, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1320, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1440, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1560, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1680, "text": "更"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1840, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2040, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2160, "text": "目"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2320, "text": "标"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2480, "text": "努"}, {"attribute": {"event": "speech"}, "end_time": 3100, "start_time": 2640, "text": "力"}], "keyword": "目标"}, {"subtitle_id": 3, "text": "他的故事才刚刚开始。", "timestamp": "00:00:03,200 --> 00:00:05,220", "duration": 2.02, "char_count": 10, "start_time_s": 3.2, "end_time_s": 5.22, "words": [{"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3400, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3480, "text": "故"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3640, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4000, "text": "才"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4200, "text": "刚"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4400, "text": "刚"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4520, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 5220, "start_time": 4720, "text": "始"}], "keyword": "故事"}], "keywords": ["他", "目标", "故事"]}]