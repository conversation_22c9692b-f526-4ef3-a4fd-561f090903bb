[{"chapter": 1, "story_board": "秦风，一个在洛杉矶打拼的华人精英，事业有成、生活优渥。可谁能想到，这位曾经意气风发的年轻人，竟然在一次意外中，直接从现代穿越到了两千多年前的战国时期，还成了秦始皇嬴政的儿子！这剧情是不是有点太离谱了？", "characters": [{"name": "现代秦风", "gender": "男", "age": "青年", "clothes": "现代商务装，黑色西装为主色调，搭配白色衬衫和蓝色领带，脚穿黑色皮鞋，佩戴黑色商务手表圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "身材挺拔", "identity": "在洛杉矶打拼的华人精英", "other": "", "from_chapter": [0], "action": "先是眼神充满自信和从容，随后一脸震惊、茫然地环顾四周", "expression": "从意气风发变得错愕、疑惑"}, {"name": "婴儿公子华（秦风穿越后）", "gender": "男", "age": "婴儿", "clothes": "古代婴儿高圆领汉服襁褓，主色调为淡蓝色，搭配白色，无明显花纹，裹有柔软白色棉布带子圆形衣领，圆形衣领", "hairstyle": "黑色胎发", "figure": "瘦小", "identity": "秦始皇嬴政和阿房女的私生子", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "阿房女", "gender": "女", "age": "青年", "clothes": "战国时期高圆领汉服长裙，主色调为淡粉色，搭配浅紫色和白色，有花卉纹样，腰间系白色丝带，头戴白玉发簪圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "苗条", "identity": "公子华的母亲", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": [0], "action": "", "expression": ""}, {"name": "嬴政侍卫", "gender": "男", "age": "青年", "clothes": "战国时期高圆领铠甲，主色调为黑色，搭配红色，有虎纹图案，腰间系黑色腰带，头戴黑色头盔，佩长剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "身强力壮", "identity": "秦始皇嬴政身边的侍卫", "other": "", "from_chapter": [0], "action": "", "expression": ""}], "scene": "室外", "image_prompt": "中景，一个先是眼神充满自信和从容，随后一脸震惊、茫然地环顾四周，从意气风发变得错愕、疑惑的青年男子，站在室外。该男子身材挺拔，穿着以黑色西装为主色调，搭配白色衬衫和蓝色领带，脚穿黑色皮鞋，佩戴黑色商务手表，有着圆形衣领的现代商务装，留着黑色短发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://p9-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/202507222121574F1833E0C9FE5D67C1D7-5073-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1753276927&x-signature=U1aHu1WeIqd5FQ%2FaQ27dVJ5qMso%3D", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_32edf83f-c8de-45a6-8c9c-3013448a1456_1.25x_20250722_193406.wav", "audio_duration": 16.725333, "video_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/videos/video_1753194457_20250722_222738.mp4", "story_board_id": 1, "subtitle": [{"subtitle_id": 1, "text": "秦风，", "timestamp": "00:00:00,200 --> 00:00:00,760", "duration": 0.56, "char_count": 3, "start_time_s": 0.2, "end_time_s": 0.76, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 200, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 320, "text": "风"}], "keyword": "秦风"}, {"subtitle_id": 2, "text": "一个在洛杉矶打拼的华人精英，", "timestamp": "00:00:00,760 --> 00:00:03,000", "duration": 2.24, "char_count": 14, "start_time_s": 0.76, "end_time_s": 3.0, "words": [{"attribute": {"event": "speech"}, "end_time": 960, "start_time": 760, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 960, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1080, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1200, "text": "洛"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1360, "text": "杉"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1480, "text": "矶"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1640, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1800, "text": "拼"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1960, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2120, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2320, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2480, "text": "精"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2600, "text": "英"}], "keyword": "洛杉矶"}, {"subtitle_id": 3, "text": "事业有成，", "timestamp": "00:00:03,000 --> 00:00:03,680", "duration": 0.68, "char_count": 5, "start_time_s": 3.0, "end_time_s": 3.68, "words": [{"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 3000, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3160, "text": "业"}, {"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3280, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3440, "text": "成"}], "keyword": "华人精英"}, {"subtitle_id": 4, "text": "生活优渥。", "timestamp": "00:00:03,680 --> 00:00:04,620", "duration": 0.94, "char_count": 5, "start_time_s": 3.68, "end_time_s": 4.62, "words": [{"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3680, "text": "生"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3880, "text": "活"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4040, "text": "优"}, {"attribute": {"event": "speech"}, "end_time": 4620, "start_time": 4200, "text": "渥"}], "keyword": "事业有成"}, {"subtitle_id": 5, "text": "可谁能想到，", "timestamp": "00:00:04,760 --> 00:00:05,680", "duration": 0.92, "char_count": 6, "start_time_s": 4.76, "end_time_s": 5.68, "words": [{"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4760, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4920, "text": "谁"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5040, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 5320, "start_time": 5160, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5320, "text": "到"}], "keyword": "谁能想到"}, {"subtitle_id": 6, "text": "这位曾经意气风发的年轻人，", "timestamp": "00:00:05,680 --> 00:00:07,680", "duration": 2.0, "char_count": 13, "start_time_s": 5.68, "end_time_s": 7.68, "words": [{"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5680, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5840, "text": "位"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5960, "text": "曾"}, {"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 6120, "text": "经"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6240, "text": "意"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6440, "text": "气"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6600, "text": "风"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6760, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6920, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 7000, "text": "年"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7160, "text": "轻"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7320, "text": "人"}], "keyword": "意气风发"}, {"subtitle_id": 7, "text": "竟然在一次意外中，", "timestamp": "00:00:07,680 --> 00:00:08,960", "duration": 1.28, "char_count": 9, "start_time_s": 7.68, "end_time_s": 8.96, "words": [{"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7680, "text": "竟"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7800, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7920, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8080, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8200, "text": "次"}, {"attribute": {"event": "speech"}, "end_time": 8440, "start_time": 8320, "text": "意"}, {"attribute": {"event": "speech"}, "end_time": 8600, "start_time": 8440, "text": "外"}, {"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8600, "text": "中"}], "keyword": "意外"}, {"subtitle_id": 8, "text": "直接从现代", "timestamp": "00:00:08,960 --> 00:00:09,840", "duration": 0.88, "char_count": 5, "start_time_s": 8.96, "end_time_s": 9.84, "words": [{"attribute": {"event": "speech"}, "end_time": 9160, "start_time": 8960, "text": "直"}, {"attribute": {"event": "speech"}, "end_time": 9280, "start_time": 9160, "text": "接"}, {"attribute": {"event": "speech"}, "end_time": 9440, "start_time": 9280, "text": "从"}, {"attribute": {"event": "speech"}, "end_time": 9680, "start_time": 9480, "text": "现"}, {"attribute": {"event": "speech"}, "end_time": 9840, "start_time": 9680, "text": "代"}], "keyword": "现代"}, {"subtitle_id": 9, "text": "穿越到了2,000多年前的战国时期，", "timestamp": "00:00:09,840 --> 00:00:12,080", "duration": 2.24, "char_count": 18, "start_time_s": 9.84, "end_time_s": 12.08, "words": [{"attribute": {"event": "speech"}, "end_time": 10000, "start_time": 9840, "text": "穿"}, {"attribute": {"event": "speech"}, "end_time": 10120, "start_time": 10000, "text": "越"}, {"attribute": {"event": "speech"}, "end_time": 10280, "start_time": 10120, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 10360, "start_time": 10280, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 10680, "start_time": 10360, "text": "2,000"}, {"attribute": {"event": "speech"}, "end_time": 10840, "start_time": 10680, "text": "多"}, {"attribute": {"event": "speech"}, "end_time": 11000, "start_time": 10840, "text": "年"}, {"attribute": {"event": "speech"}, "end_time": 11160, "start_time": 11000, "text": "前"}, {"attribute": {"event": "speech"}, "end_time": 11280, "start_time": 11160, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 11440, "start_time": 11280, "text": "战"}, {"attribute": {"event": "speech"}, "end_time": 11600, "start_time": 11440, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 11760, "start_time": 11600, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 12080, "start_time": 11760, "text": "期"}], "keyword": "战国时期"}, {"subtitle_id": 10, "text": "还成了秦始皇嬴政的儿子。", "timestamp": "00:00:12,080 --> 00:00:14,220", "duration": 2.14, "char_count": 12, "start_time_s": 12.08, "end_time_s": 14.22, "words": [{"attribute": {"event": "speech"}, "end_time": 12280, "start_time": 12080, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 12440, "start_time": 12280, "text": "成"}, {"attribute": {"event": "speech"}, "end_time": 12560, "start_time": 12440, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 12720, "start_time": 12560, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 12880, "start_time": 12760, "text": "始"}, {"attribute": {"event": "speech"}, "end_time": 13040, "start_time": 12880, "text": "皇"}, {"attribute": {"event": "speech"}, "end_time": 13240, "start_time": 13080, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 13440, "start_time": 13280, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 13560, "start_time": 13440, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 13760, "start_time": 13560, "text": "儿"}, {"attribute": {"event": "speech"}, "end_time": 14220, "start_time": 13760, "text": "子"}], "keyword": "秦始皇嬴"}, {"subtitle_id": 11, "text": "这剧情是不是有点太离谱了？", "timestamp": "00:00:14,280 --> 00:00:16,660", "duration": 2.38, "char_count": 13, "start_time_s": 14.28, "end_time_s": 16.66, "words": [{"attribute": {"event": "speech"}, "end_time": 14480, "start_time": 14280, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 14600, "start_time": 14480, "text": "剧"}, {"attribute": {"event": "speech"}, "end_time": 14840, "start_time": 14640, "text": "情"}, {"attribute": {"event": "speech"}, "end_time": 15160, "start_time": 15000, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 15240, "start_time": 15160, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 15360, "start_time": 15240, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 15480, "start_time": 15360, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 15600, "start_time": 15480, "text": "点"}, {"attribute": {"event": "speech"}, "end_time": 15840, "start_time": 15600, "text": "太"}, {"attribute": {"event": "speech"}, "end_time": 15960, "start_time": 15840, "text": "离"}, {"attribute": {"event": "speech"}, "end_time": 16120, "start_time": 15960, "text": "谱"}, {"attribute": {"event": "speech"}, "end_time": 16660, "start_time": 16120, "text": "了"}], "keyword": "离谱"}], "keywords": ["秦风", "洛杉矶", "华人精英", "事业有成", "谁能想到", "意气风发", "意外", "现代", "战国时期", "秦始皇嬴", "离谱"]}, {"chapter": 1, "story_board": "但偏偏就发生了。事情要从秦风的一次酒后驾车说起。他原本只是想去墨西哥钓个鱼，放松一下心情。", "characters": [{"name": "现代秦风", "gender": "男", "age": "青年", "clothes": "现代商务装，黑色西装为主色调，搭配白色衬衫和蓝色领带，脚穿黑色皮鞋，佩戴黑色商务手表圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "身材挺拔", "identity": "在洛杉矶打拼的华人精英", "other": "", "from_chapter": [0], "action": "坐在驾驶座手握方向盘，开车行驶", "expression": "惬意"}, {"name": "婴儿公子华（秦风穿越后）", "gender": "男", "age": "婴儿", "clothes": "古代婴儿高圆领汉服襁褓，主色调为淡蓝色，搭配白色，无明显花纹，裹有柔软白色棉布带子圆形衣领，圆形衣领", "hairstyle": "黑色胎发", "figure": "瘦小", "identity": "秦始皇嬴政和阿房女的私生子", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "阿房女", "gender": "女", "age": "青年", "clothes": "战国时期高圆领汉服长裙，主色调为淡粉色，搭配浅紫色和白色，有花卉纹样，腰间系白色丝带，头戴白玉发簪圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "苗条", "identity": "公子华的母亲", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": [0], "action": "", "expression": ""}, {"name": "嬴政侍卫", "gender": "男", "age": "青年", "clothes": "战国时期高圆领铠甲，主色调为黑色，搭配红色，有虎纹图案，腰间系黑色腰带，头戴黑色头盔，佩长剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "身强力壮", "identity": "秦始皇嬴政身边的侍卫", "other": "", "from_chapter": [0], "action": "", "expression": ""}], "scene": "室外", "image_prompt": "中景，惬意地坐在驾驶座手握方向盘开车行驶的青年男子，穿着以黑色西装为主色调，搭配白色衬衫和蓝色领带，脚穿黑色皮鞋，佩戴黑色商务手表，有着圆形衣领，留着黑色短发，身材挺拔，画面为室外场景, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753194746_20250722_223226.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_101fc5d0-5850-46c0-bfd4-3f90dab50e9a_1.25x_20250722_193353.wav", "audio_duration": 7.744, "video_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/videos/video_1753195554_20250722_224554.mp4", "story_board_id": 2, "subtitle": [{"subtitle_id": 1, "text": "但偏偏就发生了", "timestamp": "00:00:00,200 --> 00:00:01,580", "duration": 1.38, "char_count": 7, "start_time_s": 0.2, "end_time_s": 1.58, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 320, "start_time": 280, "text": "偏"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 320, "text": "偏"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 640, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 800, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 960, "text": "生"}, {"attribute": {"event": "speech"}, "end_time": 1580, "start_time": 1120, "text": "了"}], "keyword": "事情"}, {"subtitle_id": 2, "text": "事情", "timestamp": "00:00:01,720 --> 00:00:01,960", "duration": 0.24, "char_count": 2, "start_time_s": 1.72, "end_time_s": 1.96, "words": [{"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1720, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1880, "text": "情"}], "keyword": "事情"}, {"subtitle_id": 3, "text": "要从秦风的一次酒后驾车说起，", "timestamp": "00:00:01,960 --> 00:00:04,300", "duration": 2.34, "char_count": 14, "start_time_s": 1.96, "end_time_s": 4.3, "words": [{"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1960, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2120, "text": "从"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2320, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2520, "text": "风"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2640, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2720, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2840, "text": "次"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3000, "text": "酒"}, {"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3200, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3320, "text": "驾"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3480, "text": "车"}, {"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3680, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 4300, "start_time": 3840, "text": "起"}], "keyword": "秦风"}, {"subtitle_id": 4, "text": "他原本只是想去墨西哥钓个鱼，", "timestamp": "00:00:04,360 --> 00:00:06,400", "duration": 2.04, "char_count": 14, "start_time_s": 4.36, "end_time_s": 6.4, "words": [{"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4360, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4560, "text": "原"}, {"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4680, "text": "本"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4880, "text": "只"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5040, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 5320, "start_time": 5160, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5320, "text": "去"}, {"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5440, "text": "墨"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5560, "text": "西"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5680, "text": "哥"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5840, "text": "钓"}, {"attribute": {"event": "speech"}, "end_time": 6080, "start_time": 6000, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6080, "text": "鱼"}], "keyword": "酒后驾车"}, {"subtitle_id": 5, "text": "放松一下心情。", "timestamp": "00:00:06,400 --> 00:00:07,660", "duration": 1.26, "char_count": 7, "start_time_s": 6.4, "end_time_s": 7.66, "words": [{"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6400, "text": "放"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6640, "text": "松"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6760, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6880, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 7000, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 7660, "start_time": 7160, "text": "情"}], "keyword": "墨西哥"}], "keywords": ["事情", "事情", "秦风", "酒后驾车", "墨西哥"]}, {"chapter": 1, "story_board": "结果呢？刚到边境附近，就撞上了毒枭和缉毒警的枪战。子弹呼啸而过，秦风连反应都没来得及，就被打了个措手不及。", "characters": [{"name": "现代秦风", "gender": "男", "age": "青年", "clothes": "现代商务装，黑色西装为主色调，搭配白色衬衫和蓝色领带，脚穿黑色皮鞋，佩戴黑色商务手表圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "身材挺拔", "identity": "在洛杉矶打拼的华人精英", "other": "", "from_chapter": [0], "action": "坐在车内，身体因震惊而僵硬", "expression": "惊恐"}, {"name": "婴儿公子华（秦风穿越后）", "gender": "男", "age": "婴儿", "clothes": "古代婴儿高圆领汉服襁褓，主色调为淡蓝色，搭配白色，无明显花纹，裹有柔软白色棉布带子圆形衣领，圆形衣领", "hairstyle": "黑色胎发", "figure": "瘦小", "identity": "秦始皇嬴政和阿房女的私生子", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "阿房女", "gender": "女", "age": "青年", "clothes": "战国时期高圆领汉服长裙，主色调为淡粉色，搭配浅紫色和白色，有花卉纹样，腰间系白色丝带，头戴白玉发簪圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "苗条", "identity": "公子华的母亲", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": [0], "action": "", "expression": ""}, {"name": "嬴政侍卫", "gender": "男", "age": "青年", "clothes": "战国时期高圆领铠甲，主色调为黑色，搭配红色，有虎纹图案，腰间系黑色腰带，头戴黑色头盔，佩长剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "身强力壮", "identity": "秦始皇嬴政身边的侍卫", "other": "", "from_chapter": [0], "action": "", "expression": ""}], "scene": "边境", "image_prompt": "中景，坐在车内身体因震惊而僵硬、表情惊恐的青年男子，该男子穿着以黑色西装为主色调、搭配白色衬衫和蓝色领带的现代商务装，脚穿黑色皮鞋，佩戴黑色商务手表，有圆形衣领，留着黑色短发，身材挺拔，背景是边境, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753195575_20250722_224615.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_677b6896-c36c-4bc3-a6c4-ea9202b1e602_1.25x_20250722_193425.wav", "audio_duration": 9.757333, "video_url": "", "story_board_id": 3, "subtitle": [{"subtitle_id": 1, "text": "结果呢，刚到边境附近，", "timestamp": "00:00:00,240 --> 00:00:02,280", "duration": 2.04, "char_count": 11, "start_time_s": 0.24, "end_time_s": 2.28, "words": [{"attribute": {"event": "speech"}, "end_time": 360, "start_time": 240, "text": "结"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 360, "text": "果"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 520, "text": "呢"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1160, "text": "刚"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1360, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1560, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1680, "text": "境"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1840, "text": "附"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2000, "text": "近"}], "keyword": "边境"}, {"subtitle_id": 2, "text": "就撞上了毒枭和缉毒警的枪战，", "timestamp": "00:00:02,280 --> 00:00:04,740", "duration": 2.46, "char_count": 14, "start_time_s": 2.28, "end_time_s": 4.74, "words": [{"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2280, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2480, "text": "撞"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2640, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2800, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2920, "text": "毒"}, {"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3120, "text": "枭"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3280, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3480, "text": "缉"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3640, "text": "毒"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3800, "text": "警"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3920, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4080, "text": "枪"}, {"attribute": {"event": "speech"}, "end_time": 4740, "start_time": 4240, "text": "战"}], "keyword": "枪战"}, {"subtitle_id": 3, "text": "子弹呼啸而过，", "timestamp": "00:00:04,800 --> 00:00:06,040", "duration": 1.24, "char_count": 7, "start_time_s": 4.8, "end_time_s": 6.04, "words": [{"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4800, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5000, "text": "弹"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5200, "text": "呼"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5360, "text": "啸"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5520, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5680, "text": "过"}], "keyword": "子弹"}, {"subtitle_id": 4, "text": "秦风连反应都没来得及，", "timestamp": "00:00:06,040 --> 00:00:07,800", "duration": 1.76, "char_count": 11, "start_time_s": 6.04, "end_time_s": 7.8, "words": [{"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6040, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6200, "text": "风"}, {"attribute": {"event": "speech"}, "end_time": 6480, "start_time": 6320, "text": "连"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6480, "text": "反"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6720, "text": "应"}, {"attribute": {"event": "speech"}, "end_time": 7120, "start_time": 6960, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7120, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7240, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7400, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7480, "text": "及"}], "keyword": "秦风"}, {"subtitle_id": 5, "text": "就被打了个措手不及。", "timestamp": "00:00:07,800 --> 00:00:09,380", "duration": 1.58, "char_count": 10, "start_time_s": 7.8, "end_time_s": 9.38, "words": [{"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7800, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7960, "text": "被"}, {"attribute": {"event": "speech"}, "end_time": 8240, "start_time": 8080, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8240, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 8440, "start_time": 8320, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8440, "text": "措"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8680, "text": "手"}, {"attribute": {"event": "speech"}, "end_time": 8920, "start_time": 8800, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 9380, "start_time": 8920, "text": "及"}], "keyword": "挫手不及"}], "keywords": ["边境", "枪战", "子弹", "秦风", "挫手不及"]}, {"chapter": 1, "story_board": "等他醒来的时候，发现自己躺在一片陌生的地方，手里握着一个婴儿的手——这不是他自己的手，而是别人的！他懵了，这是怎么回事？难道自己死了？", "characters": [{"name": "现代秦风", "gender": "男", "age": "青年", "clothes": "现代商务装，黑色西装为主色调，搭配白色衬衫和蓝色领带，脚穿黑色皮鞋，佩戴黑色商务手表圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "身材挺拔", "identity": "在洛杉矶打拼的华人精英", "other": "", "from_chapter": [0], "action": "躺在陌生地方，手握婴儿的手，低头看着自己的身体", "expression": "懵"}, {"name": "婴儿公子华（秦风穿越后）", "gender": "男", "age": "婴儿", "clothes": "古代婴儿高圆领汉服襁褓，主色调为淡蓝色，搭配白色，无明显花纹，裹有柔软白色棉布带子圆形衣领，圆形衣领", "hairstyle": "黑色胎发", "figure": "瘦小", "identity": "秦始皇嬴政和阿房女的私生子", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "阿房女", "gender": "女", "age": "青年", "clothes": "战国时期高圆领汉服长裙，主色调为淡粉色，搭配浅紫色和白色，有花卉纹样，腰间系白色丝带，头戴白玉发簪圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "苗条", "identity": "公子华的母亲", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": [0], "action": "", "expression": ""}, {"name": "嬴政侍卫", "gender": "男", "age": "青年", "clothes": "战国时期高圆领铠甲，主色调为黑色，搭配红色，有虎纹图案，腰间系黑色腰带，头戴黑色头盔，佩长剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "身强力壮", "identity": "秦始皇嬴政身边的侍卫", "other": "", "from_chapter": [0], "action": "", "expression": ""}], "scene": "室外", "image_prompt": "中景，室外，一个身材挺拔的青年男子躺在陌生地方，手握婴儿的手，低头看着自己的身体，一脸懵，他身着以黑色西装为主色调、搭配白色衬衫和蓝色领带的现代商务装，脚穿黑色皮鞋，佩戴黑色商务手表，有着圆形衣领和黑色短发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753195588_20250722_224628.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_d3ea7beb-843b-4617-a0a1-6f943555fab6_1.25x_20250722_193417.wav", "audio_duration": 10.984, "video_url": "", "story_board_id": 4, "subtitle": [{"subtitle_id": 1, "text": "等他醒来的时候，", "timestamp": "00:00:00,160 --> 00:00:01,160", "duration": 1.0, "char_count": 8, "start_time_s": 0.16, "end_time_s": 1.16, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "等"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 400, "text": "醒"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 520, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 640, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 720, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 800, "text": "候"}], "keyword": "他"}, {"subtitle_id": 2, "text": "发现自己躺在一片陌生的地方，", "timestamp": "00:00:01,160 --> 00:00:03,320", "duration": 2.16, "char_count": 14, "start_time_s": 1.16, "end_time_s": 3.32, "words": [{"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1160, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1360, "text": "现"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1480, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1600, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1720, "text": "躺"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1920, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2040, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2160, "text": "片"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2320, "text": "陌"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2520, "text": "生"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2640, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2760, "text": "地"}, {"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 2920, "text": "方"}], "keyword": "陌生地方"}, {"subtitle_id": 3, "text": "手里握着一个婴儿的手，", "timestamp": "00:00:03,320 --> 00:00:04,920", "duration": 1.6, "char_count": 11, "start_time_s": 3.32, "end_time_s": 4.92, "words": [{"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3320, "text": "手"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3480, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3720, "text": "握"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3880, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 4000, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4120, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4240, "text": "婴"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4360, "text": "儿"}, {"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4520, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4680, "text": "手"}], "keyword": "婴儿"}, {"subtitle_id": 4, "text": "这不是他自己的手，", "timestamp": "00:00:04,920 --> 00:00:06,200", "duration": 1.28, "char_count": 9, "start_time_s": 4.92, "end_time_s": 6.2, "words": [{"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4920, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5040, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5240, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5360, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5480, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5640, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5800, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 5920, "text": "手"}], "keyword": "手"}, {"subtitle_id": 5, "text": "而是别人的。", "timestamp": "00:00:06,200 --> 00:00:07,340", "duration": 1.14, "char_count": 6, "start_time_s": 6.2, "end_time_s": 7.34, "words": [{"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6200, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6400, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6520, "text": "别"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6720, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 7340, "start_time": 6880, "text": "的"}], "keyword": "别人"}, {"subtitle_id": 6, "text": "他懵了，这是怎么回事？", "timestamp": "00:00:07,480 --> 00:00:09,500", "duration": 2.02, "char_count": 11, "start_time_s": 7.48, "end_time_s": 9.5, "words": [{"attribute": {"event": "speech"}, "end_time": 7640, "start_time": 7480, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7640, "text": "懵"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7800, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8200, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8400, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8520, "text": "怎"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8680, "text": "么"}, {"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8800, "text": "回"}, {"attribute": {"event": "speech"}, "end_time": 9500, "start_time": 8960, "text": "事"}], "keyword": "懵"}, {"subtitle_id": 7, "text": "难道自己死了？", "timestamp": "00:00:09,560 --> 00:00:10,940", "duration": 1.38, "char_count": 7, "start_time_s": 9.56, "end_time_s": 10.94, "words": [{"attribute": {"event": "speech"}, "end_time": 9760, "start_time": 9560, "text": "难"}, {"attribute": {"event": "speech"}, "end_time": 9920, "start_time": 9760, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 10080, "start_time": 9920, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 10200, "start_time": 10080, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 10400, "start_time": 10200, "text": "死"}, {"attribute": {"event": "speech"}, "end_time": 10940, "start_time": 10400, "text": "了"}], "keyword": "死了"}], "keywords": ["他", "陌生地方", "婴儿", "手", "别人", "懵", "死了"]}, {"chapter": 1, "story_board": "还是……重生了？他低头看着自己的身体，发现完全变了样，变成了一个婴儿。更让他惊讶的是，耳边传来熟悉的语言——闽南语！", "characters": [{"name": "现代秦风", "gender": "男", "age": "青年", "clothes": "现代商务装，黑色西装为主色调，搭配白色衬衫和蓝色领带，脚穿黑色皮鞋，佩戴黑色商务手表圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "身材挺拔", "identity": "在洛杉矶打拼的华人精英", "other": "", "from_chapter": [0], "action": "躺在陌生地方，手握婴儿的手，低头看着自己的身体", "expression": "懵"}, {"name": "婴儿公子华（秦风穿越后）", "gender": "男", "age": "婴儿", "clothes": "古代婴儿高圆领汉服襁褓，主色调为淡蓝色，搭配白色，无明显花纹，裹有柔软白色棉布带子圆形衣领，圆形衣领", "hairstyle": "黑色胎发", "figure": "瘦小", "identity": "秦始皇嬴政和阿房女的私生子", "other": "", "from_chapter": [0], "action": "低头看着自己的身体，努力睁开眼睛", "expression": "惊讶、懵"}, {"name": "阿房女", "gender": "女", "age": "青年", "clothes": "战国时期高圆领汉服长裙，主色调为淡粉色，搭配浅紫色和白色，有花卉纹样，腰间系白色丝带，头戴白玉发簪圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "苗条", "identity": "公子华的母亲", "other": "", "from_chapter": [0], "action": "", "expression": ""}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": [0], "action": "", "expression": ""}, {"name": "嬴政侍卫", "gender": "男", "age": "青年", "clothes": "战国时期高圆领铠甲，主色调为黑色，搭配红色，有虎纹图案，腰间系黑色腰带，头戴黑色头盔，佩长剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "身强力壮", "identity": "秦始皇嬴政身边的侍卫", "other": "", "from_chapter": [0], "action": "", "expression": ""}], "scene": "室外", "image_prompt": "中景，室外场景，一个瘦小的婴儿正低头看着自己的身体，努力睁开眼睛，脸上露出惊讶、懵的表情。这个婴儿有着黑色胎发，穿着主色调为淡蓝色、搭配白色、无明显花纹的古代婴儿高圆领汉服襁褓，裹着柔软白色棉布带子，有着圆形衣领, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753195601_20250722_224642.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_e81559a3-b7de-4b91-9f99-fa67ba642c9a_1.25x_20250722_193401.wav", "audio_duration": 9.237333, "video_url": "", "story_board_id": 5, "subtitle": [{"subtitle_id": 1, "text": "还是重生了，", "timestamp": "00:00:00,200 --> 00:00:01,380", "duration": 1.18, "char_count": 6, "start_time_s": 0.2, "end_time_s": 1.38, "words": [{"attribute": {"event": "speech"}, "end_time": 360, "start_time": 200, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 360, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 480, "text": "重"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 680, "text": "生"}, {"attribute": {"event": "speech"}, "end_time": 1380, "start_time": 840, "text": "了"}], "keyword": "重生"}, {"subtitle_id": 2, "text": "他低头看着自己的身体，", "timestamp": "00:00:01,440 --> 00:00:03,080", "duration": 1.64, "char_count": 11, "start_time_s": 1.44, "end_time_s": 3.08, "words": [{"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1440, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1600, "text": "低"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1760, "text": "头"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1920, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2120, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2200, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2360, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2480, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2600, "text": "身"}, {"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2720, "text": "体"}], "keyword": "身体"}, {"subtitle_id": 3, "text": "发现完全变了样，", "timestamp": "00:00:03,080 --> 00:00:04,280", "duration": 1.2, "char_count": 8, "start_time_s": 3.08, "end_time_s": 4.28, "words": [{"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3080, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3280, "text": "现"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3400, "text": "完"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3560, "text": "全"}, {"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3760, "text": "变"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3920, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4000, "text": "样"}], "keyword": "样子"}, {"subtitle_id": 4, "text": "变成了一个婴儿。", "timestamp": "00:00:04,280 --> 00:00:05,620", "duration": 1.34, "char_count": 8, "start_time_s": 4.28, "end_time_s": 5.62, "words": [{"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4280, "text": "变"}, {"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4520, "text": "成"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4680, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4720, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4840, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 4960, "text": "婴"}, {"attribute": {"event": "speech"}, "end_time": 5620, "start_time": 5120, "text": "儿"}], "keyword": "婴儿"}, {"subtitle_id": 5, "text": "更让他惊讶的是，", "timestamp": "00:00:05,680 --> 00:00:06,880", "duration": 1.2, "char_count": 8, "start_time_s": 5.68, "end_time_s": 6.88, "words": [{"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5680, "text": "更"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5880, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6040, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6200, "text": "惊"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6320, "text": "讶"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6440, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6560, "text": "是"}], "keyword": "惊讶"}, {"subtitle_id": 6, "text": "耳边传来熟悉的语言闽南语。", "timestamp": "00:00:06,880 --> 00:00:09,180", "duration": 2.3, "char_count": 13, "start_time_s": 6.88, "end_time_s": 9.18, "words": [{"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6880, "text": "耳"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7080, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7240, "text": "传"}, {"attribute": {"event": "speech"}, "end_time": 7520, "start_time": 7400, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7560, "text": "熟"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7800, "text": "悉"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7920, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8040, "text": "语"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8200, "text": "言"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8400, "text": "闽"}, {"attribute": {"event": "speech"}, "end_time": 8720, "start_time": 8520, "text": "南"}, {"attribute": {"event": "speech"}, "end_time": 9180, "start_time": 8720, "text": "语"}], "keyword": "闽南语"}], "keywords": ["重生", "身体", "样子", "婴儿", "惊讶", "闽南语"]}, {"chapter": 1, "story_board": "他记得很清楚，这种方言在国内很少见，但在海外华人圈里却很常见。可现在，他居然在一个完全陌生的世界听到了它。他努力睁开眼睛，看到一位年轻女子正蹲在他旁边，满脸担忧地摸着他的脸。", "characters": [{"name": "现代秦风", "gender": "男", "age": "青年", "clothes": "现代商务装，黑色西装为主色调，搭配白色衬衫和蓝色领带，脚穿黑色皮鞋，佩戴黑色商务手表圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "身材挺拔", "identity": "在洛杉矶打拼的华人精英", "other": "", "from_chapter": [0], "action": "躺在陌生地方，手握婴儿的手，低头看着自己的身体", "expression": "懵"}, {"name": "婴儿公子华（秦风穿越后）", "gender": "男", "age": "婴儿", "clothes": "古代婴儿高圆领汉服襁褓，主色调为淡蓝色，搭配白色，无明显花纹，裹有柔软白色棉布带子圆形衣领，圆形衣领", "hairstyle": "黑色胎发", "figure": "瘦小", "identity": "秦始皇嬴政和阿房女的私生子", "other": "", "from_chapter": [0], "action": "努力睁开眼睛", "expression": "懵懂、惊讶"}, {"name": "阿房女", "gender": "女", "age": "青年", "clothes": "战国时期高圆领汉服长裙，主色调为淡粉色，搭配浅紫色和白色，有花卉纹样，腰间系白色丝带，头戴白玉发簪圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "苗条", "identity": "公子华的母亲", "other": "", "from_chapter": [0], "action": "蹲在婴儿旁边，摸着婴儿的脸", "expression": "担忧"}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": [0], "action": "", "expression": ""}, {"name": "嬴政侍卫", "gender": "男", "age": "青年", "clothes": "战国时期高圆领铠甲，主色调为黑色，搭配红色，有虎纹图案，腰间系黑色腰带，头戴黑色头盔，佩长剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "身强力壮", "identity": "秦始皇嬴政身边的侍卫", "other": "", "from_chapter": [0], "action": "", "expression": ""}], "scene": "室外", "image_prompt": "中景，室外场景里，一位苗条的青年女子蹲在婴儿旁边，伸手摸着婴儿的脸，脸上带着担忧的神情；婴儿努力地睁开眼睛，眼神中透露出懵懂和惊讶。青年女子穿着战国时期高圆领汉服长裙，主色调为淡粉色，搭配浅紫色和白色，有花卉纹样，腰间系着白色丝带，头戴白玉发簪，梳着黑色发髻；婴儿穿着古代婴儿高圆领汉服襁褓，主色调为淡蓝色，搭配白色，无明显花纹，裹着柔软白色棉布带子，有着圆形衣领，长着黑色胎发，体型瘦小。, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753195622_20250722_224702.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_467d5cfd-808b-4688-ba70-77f119c1264e_1.25x_20250722_193434.wav", "audio_duration": 15.232, "video_url": "", "story_board_id": 6, "subtitle": [{"subtitle_id": 1, "text": "他记得很清楚，", "timestamp": "00:00:00,200 --> 00:00:01,240", "duration": 1.04, "char_count": 7, "start_time_s": 0.2, "end_time_s": 1.24, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "记"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 400, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 520, "text": "很"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 680, "text": "清"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 840, "text": "楚"}], "keyword": "他"}, {"subtitle_id": 2, "text": "这种方言在国内很少见，", "timestamp": "00:00:01,240 --> 00:00:03,120", "duration": 1.88, "char_count": 11, "start_time_s": 1.24, "end_time_s": 3.12, "words": [{"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1240, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1400, "text": "种"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1520, "text": "方"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1720, "text": "言"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1880, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2040, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2200, "text": "内"}, {"attribute": {"event": "speech"}, "end_time": 2520, "start_time": 2360, "text": "很"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2560, "text": "少"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 2760, "text": "见"}], "keyword": "方言"}, {"subtitle_id": 3, "text": "但在海外华人圈里却很常见。", "timestamp": "00:00:03,120 --> 00:00:05,620", "duration": 2.5, "char_count": 13, "start_time_s": 3.12, "end_time_s": 5.62, "words": [{"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3120, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3280, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3400, "text": "海"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3560, "text": "外"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3720, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3880, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4040, "text": "圈"}, {"attribute": {"event": "speech"}, "end_time": 4440, "start_time": 4240, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4520, "text": "却"}, {"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4680, "text": "很"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 4880, "text": "常"}, {"attribute": {"event": "speech"}, "end_time": 5620, "start_time": 5160, "text": "见"}], "keyword": "华人圈"}, {"subtitle_id": 4, "text": "可现在，", "timestamp": "00:00:05,720 --> 00:00:06,400", "duration": 0.68, "char_count": 4, "start_time_s": 5.72, "end_time_s": 6.4, "words": [{"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5720, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5880, "text": "现"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6040, "text": "在"}], "keyword": "现在"}, {"subtitle_id": 5, "text": "他居然在一个完全陌生的世界", "timestamp": "00:00:06,400 --> 00:00:08,360", "duration": 1.96, "char_count": 13, "start_time_s": 6.4, "end_time_s": 8.36, "words": [{"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6400, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6560, "text": "居"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6680, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6800, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6960, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 7080, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 7360, "start_time": 7200, "text": "完"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7360, "text": "全"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7560, "text": "陌"}, {"attribute": {"event": "speech"}, "end_time": 7840, "start_time": 7720, "text": "生"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7840, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 7960, "text": "世"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8120, "text": "界"}], "keyword": "世界"}, {"subtitle_id": 6, "text": "听到了他。", "timestamp": "00:00:08,360 --> 00:00:09,300", "duration": 0.94, "char_count": 5, "start_time_s": 8.36, "end_time_s": 9.3, "words": [{"attribute": {"event": "speech"}, "end_time": 8560, "start_time": 8360, "text": "听"}, {"attribute": {"event": "speech"}, "end_time": 8760, "start_time": 8600, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 8840, "start_time": 8760, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 9300, "start_time": 8840, "text": "他"}], "keyword": "他"}, {"subtitle_id": 7, "text": "他努力睁开眼睛，", "timestamp": "00:00:09,440 --> 00:00:10,680", "duration": 1.24, "char_count": 8, "start_time_s": 9.44, "end_time_s": 10.68, "words": [{"attribute": {"event": "speech"}, "end_time": 9600, "start_time": 9440, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 9720, "start_time": 9600, "text": "努"}, {"attribute": {"event": "speech"}, "end_time": 9880, "start_time": 9720, "text": "力"}, {"attribute": {"event": "speech"}, "end_time": 10000, "start_time": 9880, "text": "睁"}, {"attribute": {"event": "speech"}, "end_time": 10160, "start_time": 10000, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 10320, "start_time": 10160, "text": "眼"}, {"attribute": {"event": "speech"}, "end_time": 10680, "start_time": 10360, "text": "睛"}], "keyword": "眼睛"}, {"subtitle_id": 8, "text": "看到一位年轻女子正蹲在他旁边，", "timestamp": "00:00:10,680 --> 00:00:13,120", "duration": 2.44, "char_count": 15, "start_time_s": 10.68, "end_time_s": 13.12, "words": [{"attribute": {"event": "speech"}, "end_time": 10880, "start_time": 10680, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 11000, "start_time": 10880, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 11120, "start_time": 11000, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 11240, "start_time": 11120, "text": "位"}, {"attribute": {"event": "speech"}, "end_time": 11400, "start_time": 11240, "text": "年"}, {"attribute": {"event": "speech"}, "end_time": 11560, "start_time": 11440, "text": "轻"}, {"attribute": {"event": "speech"}, "end_time": 11760, "start_time": 11600, "text": "女"}, {"attribute": {"event": "speech"}, "end_time": 11960, "start_time": 11760, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 12200, "start_time": 12040, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 12360, "start_time": 12200, "text": "蹲"}, {"attribute": {"event": "speech"}, "end_time": 12520, "start_time": 12360, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 12640, "start_time": 12520, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 12800, "start_time": 12640, "text": "旁"}, {"attribute": {"event": "speech"}, "end_time": 13120, "start_time": 12800, "text": "边"}], "keyword": "女子"}, {"subtitle_id": 9, "text": "满脸担忧的摸着他的脸。", "timestamp": "00:00:13,120 --> 00:00:14,900", "duration": 1.78, "char_count": 11, "start_time_s": 13.12, "end_time_s": 14.9, "words": [{"attribute": {"event": "speech"}, "end_time": 13280, "start_time": 13120, "text": "满"}, {"attribute": {"event": "speech"}, "end_time": 13440, "start_time": 13280, "text": "脸"}, {"attribute": {"event": "speech"}, "end_time": 13600, "start_time": 13440, "text": "担"}, {"attribute": {"event": "speech"}, "end_time": 13760, "start_time": 13640, "text": "忧"}, {"attribute": {"event": "speech"}, "end_time": 13920, "start_time": 13760, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 14080, "start_time": 13920, "text": "摸"}, {"attribute": {"event": "speech"}, "end_time": 14200, "start_time": 14120, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 14320, "start_time": 14200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 14440, "start_time": 14320, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 14900, "start_time": 14440, "text": "脸"}], "keyword": "担忧"}], "keywords": ["他", "方言", "华人圈", "现在", "世界", "他", "眼睛", "女子", "担忧"]}, {"chapter": 1, "story_board": "旁边还有一个穿黑袍的男人，神情复杂地看着他。秦风心里一震：这难道就是我这一世的父母？他还没来得及细想，一阵困意袭来，便又昏睡过去。", "characters": [{"name": "现代秦风", "gender": "男", "age": "青年", "clothes": "现代商务装，黑色西装为主色调，搭配白色衬衫和蓝色领带，脚穿黑色皮鞋，佩戴黑色商务手表圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "身材挺拔", "identity": "在洛杉矶打拼的华人精英", "other": "", "from_chapter": [0], "action": "躺在陌生地方，手握婴儿的手，低头看着自己的身体", "expression": "懵"}, {"name": "婴儿公子华（秦风穿越后）", "gender": "男", "age": "婴儿", "clothes": "古代婴儿高圆领汉服襁褓，主色调为淡蓝色，搭配白色，无明显花纹，裹有柔软白色棉布带子圆形衣领，圆形衣领", "hairstyle": "黑色胎发", "figure": "瘦小", "identity": "秦始皇嬴政和阿房女的私生子", "other": "", "from_chapter": [0], "action": "努力睁开眼睛", "expression": "懵懂、惊讶"}, {"name": "阿房女", "gender": "女", "age": "青年", "clothes": "战国时期高圆领汉服长裙，主色调为淡粉色，搭配浅紫色和白色，有花卉纹样，腰间系白色丝带，头戴白玉发簪圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "苗条", "identity": "公子华的母亲", "other": "", "from_chapter": [0], "action": "蹲在婴儿旁边，摸着婴儿的脸", "expression": "担忧"}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": [0], "action": "站在旁边，神情复杂地看着婴儿", "expression": "神情复杂"}, {"name": "嬴政侍卫", "gender": "男", "age": "青年", "clothes": "战国时期高圆领铠甲，主色调为黑色，搭配红色，有虎纹图案，腰间系黑色腰带，头戴黑色头盔，佩长剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "身强力壮", "identity": "秦始皇嬴政身边的侍卫", "other": "", "from_chapter": [0], "action": "", "expression": ""}], "scene": "室外", "image_prompt": "中景，室外场景中，一位神情担忧的青年女子蹲在婴儿旁边，伸手摸着婴儿的脸，一位神情复杂的中年男子站在旁边看着婴儿。青年女子身材苗条，穿着战国时期高圆领淡粉色汉服长裙，搭配浅紫色和白色，有花卉纹样，腰间系白色丝带，头戴白玉发簪，梳着黑色发髻，衣领为圆形；中年男子身材魁梧，霸气威严，穿着战国时期高圆领黑色帝王袍，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠，束着黑色头发，衣领为圆形, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753195652_20250722_224733.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_6738c3a4-4959-4d85-adf6-e1d8e7c89149_1.25x_20250722_193421.wav", "audio_duration": 11.218667, "video_url": "", "story_board_id": 7, "subtitle": [{"subtitle_id": 1, "text": "旁边还有一个穿黑袍的男人，", "timestamp": "00:00:00,200 --> 00:00:02,040", "duration": 1.84, "char_count": 13, "start_time_s": 0.2, "end_time_s": 2.04, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 200, "text": "旁"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 440, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 560, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 680, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 760, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 880, "text": "穿"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1040, "text": "黑"}, {"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1240, "text": "袍"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1440, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1560, "text": "男"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1680, "text": "人"}], "keyword": "黑袍男人"}, {"subtitle_id": 2, "text": "神情复杂的看着她，", "timestamp": "00:00:02,040 --> 00:00:03,620", "duration": 1.58, "char_count": 9, "start_time_s": 2.04, "end_time_s": 3.62, "words": [{"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2040, "text": "神"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2200, "text": "情"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2360, "text": "复"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2600, "text": "杂"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2720, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2840, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 3040, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 3620, "start_time": 3160, "text": "她"}], "keyword": "看着她"}, {"subtitle_id": 3, "text": "秦风心里一震，", "timestamp": "00:00:03,800 --> 00:00:04,920", "duration": 1.12, "char_count": 7, "start_time_s": 3.8, "end_time_s": 4.92, "words": [{"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3800, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 3960, "text": "风"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4120, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4280, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4360, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4520, "text": "震"}], "keyword": "秦风"}, {"subtitle_id": 4, "text": "这难道就是我这一世的父母？", "timestamp": "00:00:04,920 --> 00:00:07,180", "duration": 2.26, "char_count": 13, "start_time_s": 4.92, "end_time_s": 7.18, "words": [{"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 4920, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5080, "text": "难"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5240, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5400, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 5760, "start_time": 5520, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5760, "text": "我"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6000, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6160, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6280, "text": "世"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6400, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6520, "text": "父"}, {"attribute": {"event": "speech"}, "end_time": 7180, "start_time": 6680, "text": "母"}], "keyword": "父母"}, {"subtitle_id": 5, "text": "他还没来得及细想，", "timestamp": "00:00:07,320 --> 00:00:08,520", "duration": 1.2, "char_count": 9, "start_time_s": 7.32, "end_time_s": 8.52, "words": [{"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7320, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7480, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7600, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 7840, "start_time": 7720, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7840, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7920, "text": "及"}, {"attribute": {"event": "speech"}, "end_time": 8240, "start_time": 8080, "text": "细"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8240, "text": "想"}], "keyword": "细想"}, {"subtitle_id": 6, "text": "一阵困意袭来，", "timestamp": "00:00:08,520 --> 00:00:09,560", "duration": 1.04, "char_count": 7, "start_time_s": 8.52, "end_time_s": 9.56, "words": [{"attribute": {"event": "speech"}, "end_time": 8720, "start_time": 8520, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 8840, "start_time": 8720, "text": "阵"}, {"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8840, "text": "困"}, {"attribute": {"event": "speech"}, "end_time": 9120, "start_time": 9000, "text": "意"}, {"attribute": {"event": "speech"}, "end_time": 9280, "start_time": 9120, "text": "袭"}, {"attribute": {"event": "speech"}, "end_time": 9560, "start_time": 9280, "text": "来"}], "keyword": "困意"}, {"subtitle_id": 7, "text": "便又昏睡过去。", "timestamp": "00:00:09,560 --> 00:00:10,940", "duration": 1.38, "char_count": 7, "start_time_s": 9.56, "end_time_s": 10.94, "words": [{"attribute": {"event": "speech"}, "end_time": 9720, "start_time": 9560, "text": "便"}, {"attribute": {"event": "speech"}, "end_time": 9880, "start_time": 9720, "text": "又"}, {"attribute": {"event": "speech"}, "end_time": 10040, "start_time": 9880, "text": "昏"}, {"attribute": {"event": "speech"}, "end_time": 10280, "start_time": 10120, "text": "睡"}, {"attribute": {"event": "speech"}, "end_time": 10480, "start_time": 10320, "text": "过"}, {"attribute": {"event": "speech"}, "end_time": 10940, "start_time": 10480, "text": "去"}], "keyword": "昏睡"}], "keywords": ["黑袍男人", "看着她", "秦风", "父母", "细想", "困意", "昏睡"]}, {"chapter": 1, "story_board": "再醒来时，他发现自己被放在一张柔软的床上，周围是古色古香的房间。一个男人正在给一个女人说话：“辛苦夫人了，这孩子怎么不会哭？”女人回答：“大王，孩子身体健康，可能是不怕生。", "characters": [{"name": "现代秦风", "gender": "男", "age": "青年", "clothes": "现代商务装，黑色西装为主色调，搭配白色衬衫和蓝色领带，脚穿黑色皮鞋，佩戴黑色商务手表圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "身材挺拔", "identity": "在洛杉矶打拼的华人精英", "other": "", "from_chapter": [0], "action": "躺在陌生地方，手握婴儿的手，低头看着自己的身体", "expression": "懵"}, {"name": "婴儿公子华（秦风穿越后）", "gender": "男", "age": "婴儿", "clothes": "古代婴儿高圆领汉服襁褓，主色调为淡蓝色，搭配白色，无明显花纹，裹有柔软白色棉布带子圆形衣领，圆形衣领", "hairstyle": "黑色胎发", "figure": "瘦小", "identity": "秦始皇嬴政和阿房女的私生子", "other": "", "from_chapter": [0], "action": "躺在柔软的床上", "expression": "懵懂"}, {"name": "阿房女", "gender": "女", "age": "青年", "clothes": "战国时期高圆领汉服长裙，主色调为淡粉色，搭配浅紫色和白色，有花卉纹样，腰间系白色丝带，头戴白玉发簪圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "苗条", "identity": "公子华的母亲", "other": "", "from_chapter": [0], "action": "回应嬴政的话", "expression": "温柔"}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": [0], "action": "站着和女人说话", "expression": "疑惑"}, {"name": "嬴政侍卫", "gender": "男", "age": "青年", "clothes": "战国时期高圆领铠甲，主色调为黑色，搭配红色，有虎纹图案，腰间系黑色腰带，头戴黑色头盔，佩长剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "身强力壮", "identity": "秦始皇嬴政身边的侍卫", "other": "", "from_chapter": [0], "action": "", "expression": ""}], "scene": "室内", "image_prompt": "中景，室内，一位站着的中年男子满脸疑惑，旁边一位青年女子温柔回应，不远处一张柔软的床上躺着一个婴儿懵懂地看着周围。站着的中年男子身材魁梧，身穿战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠，圆形衣领，黑色束发，尽显霸气威严；青年女子身材苗条，身着战国时期高圆领汉服长裙，主色调为淡粉色，搭配浅紫色和白色，有花卉纹样，腰间系白色丝带，头戴白玉发簪，圆形衣领，梳着黑色发髻；婴儿体型瘦小，裹着古代婴儿高圆领汉服襁褓，主色调为淡蓝色，搭配白色，无明显花纹，裹有柔软白色棉布带子，圆形衣领，留着黑色胎发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://p9-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/20250722224748C7BFF8062A95D5775216-1319-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1753282075&x-signature=mL5N%2B7JFnxt6HZc47wcjrHQAS6Q%3D", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_d73cf8a7-9aae-4e78-be53-c523e2557355_1.25x_20250722_193412.wav", "audio_duration": 14.210667, "video_url": "", "story_board_id": 8, "subtitle": [{"subtitle_id": 1, "text": "在醒来时，", "timestamp": "00:00:00,200 --> 00:00:00,880", "duration": 0.68, "char_count": 5, "start_time_s": 0.2, "end_time_s": 0.88, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "醒"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 400, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 520, "text": "时"}], "keyword": "醒来"}, {"subtitle_id": 2, "text": "他发现自己被放在一张柔软的床上，", "timestamp": "00:00:00,880 --> 00:00:03,400", "duration": 2.52, "char_count": 16, "start_time_s": 0.88, "end_time_s": 3.4, "words": [{"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 880, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 1040, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1160, "text": "现"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1440, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1600, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1720, "text": "被"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1840, "text": "放"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 2000, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2120, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2240, "text": "张"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2400, "text": "柔"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2600, "text": "软"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2720, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2840, "text": "床"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3080, "text": "上"}], "keyword": "床"}, {"subtitle_id": 3, "text": "周围是古色古香的房间。", "timestamp": "00:00:03,400 --> 00:00:05,260", "duration": 1.86, "char_count": 11, "start_time_s": 3.4, "end_time_s": 5.26, "words": [{"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3400, "text": "周"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3560, "text": "围"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3720, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3840, "text": "古"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4040, "text": "色"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4160, "text": "古"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4360, "text": "香"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4520, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4640, "text": "房"}, {"attribute": {"event": "speech"}, "end_time": 5260, "start_time": 4840, "text": "间"}], "keyword": "房间"}, {"subtitle_id": 4, "text": "一个男人正在给一个女人说话。", "timestamp": "00:00:05,360 --> 00:00:07,520", "duration": 2.16, "char_count": 14, "start_time_s": 5.36, "end_time_s": 7.52, "words": [{"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5360, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5600, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5680, "text": "男"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5840, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5960, "text": "正"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6120, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6280, "text": "给"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6400, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6520, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6640, "text": "女"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6760, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6880, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 7520, "start_time": 7080, "text": "话"}], "keyword": "男人"}, {"subtitle_id": 5, "text": "辛苦夫人了，", "timestamp": "00:00:07,520 --> 00:00:08,440", "duration": 0.92, "char_count": 6, "start_time_s": 7.52, "end_time_s": 8.44, "words": [{"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7520, "text": "辛"}, {"attribute": {"event": "speech"}, "end_time": 7880, "start_time": 7680, "text": "苦"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7880, "text": "夫"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 8040, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 8440, "start_time": 8120, "text": "了"}], "keyword": "夫人"}, {"subtitle_id": 6, "text": "这孩子怎么不会哭？", "timestamp": "00:00:08,440 --> 00:00:10,060", "duration": 1.62, "char_count": 9, "start_time_s": 8.44, "end_time_s": 10.06, "words": [{"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8440, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8640, "text": "孩"}, {"attribute": {"event": "speech"}, "end_time": 8920, "start_time": 8800, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 9120, "start_time": 8920, "text": "怎"}, {"attribute": {"event": "speech"}, "end_time": 9240, "start_time": 9120, "text": "么"}, {"attribute": {"event": "speech"}, "end_time": 9360, "start_time": 9240, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 9560, "start_time": 9360, "text": "会"}, {"attribute": {"event": "speech"}, "end_time": 10060, "start_time": 9560, "text": "哭"}], "keyword": "孩子"}, {"subtitle_id": 7, "text": "女人回答大王，", "timestamp": "00:00:10,200 --> 00:00:11,640", "duration": 1.44, "char_count": 7, "start_time_s": 10.2, "end_time_s": 11.64, "words": [{"attribute": {"event": "speech"}, "end_time": 10360, "start_time": 10200, "text": "女"}, {"attribute": {"event": "speech"}, "end_time": 10480, "start_time": 10360, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 10680, "start_time": 10480, "text": "回"}, {"attribute": {"event": "speech"}, "end_time": 10840, "start_time": 10680, "text": "答"}, {"attribute": {"event": "speech"}, "end_time": 11280, "start_time": 11080, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 11640, "start_time": 11280, "text": "王"}], "keyword": "女人"}, {"subtitle_id": 8, "text": "孩子身体健康，", "timestamp": "00:00:11,640 --> 00:00:12,880", "duration": 1.24, "char_count": 7, "start_time_s": 11.64, "end_time_s": 12.88, "words": [{"attribute": {"event": "speech"}, "end_time": 11880, "start_time": 11640, "text": "孩"}, {"attribute": {"event": "speech"}, "end_time": 12000, "start_time": 11880, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 12120, "start_time": 12000, "text": "身"}, {"attribute": {"event": "speech"}, "end_time": 12280, "start_time": 12120, "text": "体"}, {"attribute": {"event": "speech"}, "end_time": 12480, "start_time": 12320, "text": "健"}, {"attribute": {"event": "speech"}, "end_time": 12880, "start_time": 12480, "text": "康"}], "keyword": "身体"}, {"subtitle_id": 9, "text": "可能是不怕生。", "timestamp": "00:00:12,880 --> 00:00:14,140", "duration": 1.26, "char_count": 7, "start_time_s": 12.88, "end_time_s": 14.14, "words": [{"attribute": {"event": "speech"}, "end_time": 13040, "start_time": 12880, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 13160, "start_time": 13040, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 13280, "start_time": 13160, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 13440, "start_time": 13280, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 13640, "start_time": 13440, "text": "怕"}, {"attribute": {"event": "speech"}, "end_time": 14140, "start_time": 13680, "text": "生"}], "keyword": "不怕生"}], "keywords": ["醒来", "床", "房间", "男人", "夫人", "孩子", "女人", "身体", "不怕生"]}, {"chapter": 1, "story_board": "”秦风一听，差点没忍住笑出声——这个“大王”是谁？难道他是秦始皇？这剧情也太夸张了吧！", "characters": [{"name": "现代秦风", "gender": "男", "age": "青年", "clothes": "现代商务装，黑色西装为主色调，搭配白色衬衫和蓝色领带，脚穿黑色皮鞋，佩戴黑色商务手表圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "身材挺拔", "identity": "在洛杉矶打拼的华人精英", "other": "", "from_chapter": [0], "action": "躺在柔软的床上，嘴巴微张想要笑", "expression": "震惊又觉得荒谬"}, {"name": "婴儿公子华（秦风穿越后）", "gender": "男", "age": "婴儿", "clothes": "古代婴儿高圆领汉服襁褓，主色调为淡蓝色，搭配白色，无明显花纹，裹有柔软白色棉布带子圆形衣领，圆形衣领", "hairstyle": "黑色胎发", "figure": "瘦小", "identity": "秦始皇嬴政和阿房女的私生子", "other": "", "from_chapter": [0], "action": "躺在柔软的床上", "expression": "懵懂"}, {"name": "阿房女", "gender": "女", "age": "青年", "clothes": "战国时期高圆领汉服长裙，主色调为淡粉色，搭配浅紫色和白色，有花卉纹样，腰间系白色丝带，头戴白玉发簪圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "苗条", "identity": "公子华的母亲", "other": "", "from_chapter": [0], "action": "回应嬴政的话", "expression": "温柔"}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": [0], "action": "站着和女人说话", "expression": "疑惑"}, {"name": "嬴政侍卫", "gender": "男", "age": "青年", "clothes": "战国时期高圆领铠甲，主色调为黑色，搭配红色，有虎纹图案，腰间系黑色腰带，头戴黑色头盔，佩长剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "身强力壮", "identity": "秦始皇嬴政身边的侍卫", "other": "", "from_chapter": [0], "action": "", "expression": ""}], "scene": "室内", "image_prompt": "近景，躺在柔软床上、嘴巴微张似要笑、表情震惊又觉得荒谬的青年男子，穿着以黑色西装为主色调、搭配白色衬衫和蓝色领带的现代商务装，脚穿黑色皮鞋，佩戴黑色商务手表，有着圆形衣领，留着黑色短发，身材挺拔，室内背景, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753195727_20250722_224847.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_0cfe1d39-0afc-489f-ac1a-7f8c6a35ccb8_1.25x_20250722_193358.wav", "audio_duration": 7.552, "video_url": "", "story_board_id": 9, "subtitle": [{"subtitle_id": 1, "text": "秦枫一听，", "timestamp": "00:00:00,200 --> 00:00:01,000", "duration": 0.8, "char_count": 5, "start_time_s": 0.2, "end_time_s": 1.0, "words": [{"attribute": {"event": "speech"}, "end_time": 360, "start_time": 200, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 360, "text": "枫"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 480, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 600, "text": "听"}], "keyword": "秦枫"}, {"subtitle_id": 2, "text": "差点没忍住笑出声，", "timestamp": "00:00:01,000 --> 00:00:02,400", "duration": 1.4, "char_count": 9, "start_time_s": 1.0, "end_time_s": 2.4, "words": [{"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1000, "text": "差"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1240, "text": "点"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1360, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1560, "text": "忍"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1680, "text": "住"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1800, "text": "笑"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2000, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2160, "text": "声"}], "keyword": "笑出声"}, {"subtitle_id": 3, "text": "这个大王是谁？", "timestamp": "00:00:02,400 --> 00:00:03,620", "duration": 1.22, "char_count": 7, "start_time_s": 2.4, "end_time_s": 3.62, "words": [{"attribute": {"event": "speech"}, "end_time": 2520, "start_time": 2400, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2520, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2600, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2800, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 2960, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 3620, "start_time": 3120, "text": "谁"}], "keyword": "大王"}, {"subtitle_id": 4, "text": "难道他是秦始皇？", "timestamp": "00:00:03,680 --> 00:00:05,220", "duration": 1.54, "char_count": 8, "start_time_s": 3.68, "end_time_s": 5.22, "words": [{"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3680, "text": "难"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3920, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4080, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4240, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4400, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4560, "text": "始"}, {"attribute": {"event": "speech"}, "end_time": 5220, "start_time": 4720, "text": "皇"}], "keyword": "秦始皇"}, {"subtitle_id": 5, "text": "这剧情也太夸张了吧！", "timestamp": "00:00:05,400 --> 00:00:07,460", "duration": 2.06, "char_count": 10, "start_time_s": 5.4, "end_time_s": 7.46, "words": [{"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5400, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5600, "text": "剧"}, {"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5760, "text": "情"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6040, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 6480, "start_time": 6200, "text": "太"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6560, "text": "夸"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6760, "text": "张"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6880, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 7460, "start_time": 7000, "text": "吧"}], "keyword": "剧情"}], "keywords": ["秦枫", "笑出声", "大王", "秦始皇", "剧情"]}, {"chapter": 1, "story_board": "后来他才知道，自己这一世的名字叫公子华，是秦始皇嬴政和阿房女的孩子。虽然他是私生子，但因为嬴政对他的重视，日子过得还算不错。每天喝奶、睡觉、尿床，生活简单又规律。", "characters": [{"name": "现代秦风", "gender": "男", "age": "青年", "clothes": "现代商务装，黑色西装为主色调，搭配白色衬衫和蓝色领带，脚穿黑色皮鞋，佩戴黑色商务手表圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "身材挺拔", "identity": "在洛杉矶打拼的华人精英", "other": "", "from_chapter": [0], "action": "躺在柔软的床上，嘴巴微张想要笑", "expression": "震惊又觉得荒谬"}, {"name": "婴儿公子华（秦风穿越后）", "gender": "男", "age": "婴儿", "clothes": "古代婴儿高圆领汉服襁褓，主色调为淡蓝色，搭配白色，无明显花纹，裹有柔软白色棉布带子圆形衣领，圆形衣领", "hairstyle": "黑色胎发", "figure": "瘦小", "identity": "秦始皇嬴政和阿房女的私生子", "other": "", "from_chapter": [0], "action": "在柔软的床上喝奶、睡觉、躺着", "expression": "懵懂"}, {"name": "阿房女", "gender": "女", "age": "青年", "clothes": "战国时期高圆领汉服长裙，主色调为淡粉色，搭配浅紫色和白色，有花卉纹样，腰间系白色丝带，头戴白玉发簪圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "苗条", "identity": "公子华的母亲", "other": "", "from_chapter": [0], "action": "无", "expression": "无"}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": [0], "action": "无", "expression": "无"}, {"name": "嬴政侍卫", "gender": "男", "age": "青年", "clothes": "战国时期高圆领铠甲，主色调为黑色，搭配红色，有虎纹图案，腰间系黑色腰带，头戴黑色头盔，佩长剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "身强力壮", "identity": "秦始皇嬴政身边的侍卫", "other": "", "from_chapter": [0], "action": "", "expression": ""}], "scene": "室内", "image_prompt": "中景，室内柔软的床上，一个懵懂的婴儿躺着喝奶、睡觉，旁边站着一位魁梧霸气威严的中年男子和一位苗条的青年女子。婴儿穿着主色调为淡蓝色、搭配白色、无明显花纹的古代婴儿高圆领汉服襁褓，裹着柔软白色棉布带子圆形衣领，有黑色胎发，身形瘦小；中年男子穿着主色调为黑色、搭配金色和红色、有龙纹图案的战国时期高圆领帝王袍，腰间束金色腰带，头戴黑色冕旒冠，圆形衣领，黑色束发；青年女子穿着主色调为淡粉色、搭配浅紫色和白色、有花卉纹样的战国时期高圆领汉服长裙，腰间系白色丝带，头戴白玉发簪，圆形衣领，梳着黑色发髻, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753195793_20250722_224954.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_2a531ed4-fc77-4cdc-9482-458cb37082d2_1.25x_20250722_193429.wav", "audio_duration": 13.653333, "video_url": "", "story_board_id": 10, "subtitle": [{"subtitle_id": 1, "text": "后来他才知道", "timestamp": "00:00:00,200 --> 00:00:01,120", "duration": 0.92, "char_count": 6, "start_time_s": 0.2, "end_time_s": 1.12, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 400, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 520, "text": "才"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 640, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 800, "text": "道"}], "keyword": "他"}, {"subtitle_id": 2, "text": "自己这一世的名字叫宫子华，", "timestamp": "00:00:01,120 --> 00:00:03,280", "duration": 2.16, "char_count": 13, "start_time_s": 1.12, "end_time_s": 3.28, "words": [{"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1120, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1320, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1440, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1600, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1760, "text": "世"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1840, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1960, "text": "名"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2120, "text": "字"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2400, "text": "叫"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2560, "text": "宫"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2760, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 2920, "text": "华"}], "keyword": "宫子华"}, {"subtitle_id": 3, "text": "是秦始皇嬴政和阿房女的孩子。", "timestamp": "00:00:03,280 --> 00:00:05,900", "duration": 2.62, "char_count": 14, "start_time_s": 3.28, "end_time_s": 5.9, "words": [{"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3280, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3440, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3680, "text": "始"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3840, "text": "皇"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4000, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4200, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4440, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4600, "text": "阿"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4760, "text": "房"}, {"attribute": {"event": "speech"}, "end_time": 5120, "start_time": 4960, "text": "女"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5120, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5240, "text": "孩"}, {"attribute": {"event": "speech"}, "end_time": 5900, "start_time": 5400, "text": "子"}], "keyword": "嬴政"}, {"subtitle_id": 4, "text": "虽然他是私生子，", "timestamp": "00:00:06,040 --> 00:00:07,200", "duration": 1.16, "char_count": 8, "start_time_s": 6.04, "end_time_s": 7.2, "words": [{"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6040, "text": "虽"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6160, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6280, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6440, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6560, "text": "私"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6720, "text": "生"}, {"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 6840, "text": "子"}], "keyword": "私生子"}, {"subtitle_id": 5, "text": "但因为嬴政对他的重视，", "timestamp": "00:00:07,200 --> 00:00:08,760", "duration": 1.56, "char_count": 11, "start_time_s": 7.2, "end_time_s": 8.76, "words": [{"attribute": {"event": "speech"}, "end_time": 7360, "start_time": 7200, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7360, "text": "因"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7480, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7600, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7760, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7920, "text": "对"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8040, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 8280, "start_time": 8200, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 8440, "start_time": 8280, "text": "重"}, {"attribute": {"event": "speech"}, "end_time": 8760, "start_time": 8480, "text": "视"}], "keyword": "嬴政"}, {"subtitle_id": 6, "text": "日子过得还算不错，", "timestamp": "00:00:08,760 --> 00:00:10,180", "duration": 1.42, "char_count": 9, "start_time_s": 8.76, "end_time_s": 10.18, "words": [{"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8760, "text": "日"}, {"attribute": {"event": "speech"}, "end_time": 9040, "start_time": 8960, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 9200, "start_time": 9040, "text": "过"}, {"attribute": {"event": "speech"}, "end_time": 9320, "start_time": 9200, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 9440, "start_time": 9320, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 9560, "start_time": 9440, "text": "算"}, {"attribute": {"event": "speech"}, "end_time": 9680, "start_time": 9560, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 10180, "start_time": 9680, "text": "错"}], "keyword": "日子"}, {"subtitle_id": 7, "text": "每天喝奶、睡觉、尿床，", "timestamp": "00:00:10,240 --> 00:00:11,880", "duration": 1.64, "char_count": 11, "start_time_s": 10.24, "end_time_s": 11.88, "words": [{"attribute": {"event": "speech"}, "end_time": 10480, "start_time": 10240, "text": "每"}, {"attribute": {"event": "speech"}, "end_time": 10600, "start_time": 10480, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 10760, "start_time": 10600, "text": "喝"}, {"attribute": {"event": "speech"}, "end_time": 10920, "start_time": 10760, "text": "奶"}, {"attribute": {"event": "speech"}, "end_time": 11120, "start_time": 10960, "text": "睡"}, {"attribute": {"event": "speech"}, "end_time": 11240, "start_time": 11120, "text": "觉"}, {"attribute": {"event": "speech"}, "end_time": 11400, "start_time": 11240, "text": "尿"}, {"attribute": {"event": "speech"}, "end_time": 11880, "start_time": 11480, "text": "床"}], "keyword": "喝奶"}, {"subtitle_id": 8, "text": "生活简单又规律。", "timestamp": "00:00:11,880 --> 00:00:13,340", "duration": 1.46, "char_count": 8, "start_time_s": 11.88, "end_time_s": 13.34, "words": [{"attribute": {"event": "speech"}, "end_time": 12040, "start_time": 11880, "text": "生"}, {"attribute": {"event": "speech"}, "end_time": 12200, "start_time": 12040, "text": "活"}, {"attribute": {"event": "speech"}, "end_time": 12360, "start_time": 12200, "text": "简"}, {"attribute": {"event": "speech"}, "end_time": 12520, "start_time": 12360, "text": "单"}, {"attribute": {"event": "speech"}, "end_time": 12720, "start_time": 12560, "text": "又"}, {"attribute": {"event": "speech"}, "end_time": 12880, "start_time": 12720, "text": "规"}, {"attribute": {"event": "speech"}, "end_time": 13340, "start_time": 12880, "text": "律"}], "keyword": "生活"}], "keywords": ["他", "宫子华", "嬴政", "私生子", "嬴政", "日子", "喝奶", "生活"]}, {"chapter": 1, "story_board": "不过很快，他就发现这个世界和他原来的世界完全不同。他开始观察周围的环境，发现这里的一切都像是古代中国，甚至能闻到饭菜的味道，看到的器物也都是那个时代的风格。", "characters": [{"name": "现代秦风", "gender": "男", "age": "青年", "clothes": "现代商务装，黑色西装为主色调，搭配白色衬衫和蓝色领带，脚穿黑色皮鞋，佩戴黑色商务手表圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "身材挺拔", "identity": "在洛杉矶打拼的华人精英", "other": "", "from_chapter": [0], "action": "躺在柔软的床上，嘴巴微张想要笑", "expression": "震惊又觉得荒谬"}, {"name": "婴儿公子华（秦风穿越后）", "gender": "男", "age": "婴儿", "clothes": "古代婴儿高圆领汉服襁褓，主色调为淡蓝色，搭配白色，无明显花纹，裹有柔软白色棉布带子圆形衣领，圆形衣领", "hairstyle": "黑色胎发", "figure": "瘦小", "identity": "秦始皇嬴政和阿房女的私生子", "other": "", "from_chapter": [0], "action": "四处张望、观察周围环境", "expression": "好奇"}, {"name": "阿房女", "gender": "女", "age": "青年", "clothes": "战国时期高圆领汉服长裙，主色调为淡粉色，搭配浅紫色和白色，有花卉纹样，腰间系白色丝带，头戴白玉发簪圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "苗条", "identity": "公子华的母亲", "other": "", "from_chapter": [0], "action": "无", "expression": "无"}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": [0], "action": "无", "expression": "无"}, {"name": "嬴政侍卫", "gender": "男", "age": "青年", "clothes": "战国时期高圆领铠甲，主色调为黑色，搭配红色，有虎纹图案，腰间系黑色腰带，头戴黑色头盔，佩长剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "身强力壮", "identity": "秦始皇嬴政身边的侍卫", "other": "", "from_chapter": [0], "action": "", "expression": ""}], "scene": "室内", "image_prompt": "中景，一个四处张望、充满好奇地观察周围环境的婴儿，身处室内。这个婴儿穿着古代婴儿高圆领汉服襁褓，主色调为淡蓝色，搭配白色，无明显花纹，裹有柔软白色棉布带子圆形衣领，有黑色胎发，身形瘦小, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753195894_20250722_225134.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_e6bafc18-d72d-4e63-8b39-d8ec601a9715_1.25x_20250722_193445.wav", "audio_duration": 12.482667, "video_url": "", "story_board_id": 11, "subtitle": [{"subtitle_id": 1, "text": "不过很快他就发现", "timestamp": "00:00:00,160 --> 00:00:01,360", "duration": 1.2, "char_count": 8, "start_time_s": 0.16, "end_time_s": 1.36, "words": [{"attribute": {"event": "speech"}, "end_time": 240, "start_time": 160, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 320, "start_time": 240, "text": "过"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 320, "text": "很"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 480, "text": "快"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 800, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 920, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 1040, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1160, "text": "现"}], "keyword": "他"}, {"subtitle_id": 2, "text": "这个世界和他原来的世界完全不同，", "timestamp": "00:00:01,360 --> 00:00:04,100", "duration": 2.74, "char_count": 16, "start_time_s": 1.36, "end_time_s": 4.1, "words": [{"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1360, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1520, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1640, "text": "世"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1760, "text": "界"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2000, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2280, "text": "原"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2440, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2600, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2720, "text": "世"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2840, "text": "界"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3080, "text": "完"}, {"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3320, "text": "全"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3520, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 4100, "start_time": 3640, "text": "同"}], "keyword": "世界"}, {"subtitle_id": 3, "text": "他开始观察周围的环境，", "timestamp": "00:00:04,240 --> 00:00:05,760", "duration": 1.52, "char_count": 11, "start_time_s": 4.24, "end_time_s": 5.76, "words": [{"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4240, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4400, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4520, "text": "始"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4640, "text": "观"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4760, "text": "察"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4920, "text": "周"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5040, "text": "围"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5160, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5240, "text": "环"}, {"attribute": {"event": "speech"}, "end_time": 5760, "start_time": 5440, "text": "境"}], "keyword": "环境"}, {"subtitle_id": 4, "text": "发现这里的一切都像是古代中国，", "timestamp": "00:00:05,760 --> 00:00:08,200", "duration": 2.44, "char_count": 15, "start_time_s": 5.76, "end_time_s": 8.2, "words": [{"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5760, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5920, "text": "现"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6040, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6200, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6280, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6400, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6560, "text": "切"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6760, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 7120, "start_time": 6960, "text": "像"}, {"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 7120, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7280, "text": "古"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7480, "text": "代"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7600, "text": "中"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 7760, "text": "国"}], "keyword": "古代中国"}, {"subtitle_id": 5, "text": "甚至能闻到饭菜的味道，", "timestamp": "00:00:08,200 --> 00:00:09,840", "duration": 1.64, "char_count": 11, "start_time_s": 8.2, "end_time_s": 9.84, "words": [{"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8200, "text": "甚"}, {"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8360, "text": "至"}, {"attribute": {"event": "speech"}, "end_time": 8600, "start_time": 8480, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 8720, "start_time": 8600, "text": "闻"}, {"attribute": {"event": "speech"}, "end_time": 8880, "start_time": 8720, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 9040, "start_time": 8880, "text": "饭"}, {"attribute": {"event": "speech"}, "end_time": 9280, "start_time": 9080, "text": "菜"}, {"attribute": {"event": "speech"}, "end_time": 9360, "start_time": 9280, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 9520, "start_time": 9360, "text": "味"}, {"attribute": {"event": "speech"}, "end_time": 9840, "start_time": 9520, "text": "道"}], "keyword": "饭菜"}, {"subtitle_id": 6, "text": "看到的器物也都是那个时代的风格。", "timestamp": "00:00:09,840 --> 00:00:12,420", "duration": 2.58, "char_count": 16, "start_time_s": 9.84, "end_time_s": 12.42, "words": [{"attribute": {"event": "speech"}, "end_time": 10040, "start_time": 9840, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 10200, "start_time": 10040, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 10320, "start_time": 10200, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 10480, "start_time": 10320, "text": "器"}, {"attribute": {"event": "speech"}, "end_time": 10640, "start_time": 10480, "text": "物"}, {"attribute": {"event": "speech"}, "end_time": 10840, "start_time": 10680, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 11000, "start_time": 10840, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 11120, "start_time": 11000, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 11280, "start_time": 11120, "text": "那"}, {"attribute": {"event": "speech"}, "end_time": 11360, "start_time": 11280, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 11560, "start_time": 11360, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 11720, "start_time": 11560, "text": "代"}, {"attribute": {"event": "speech"}, "end_time": 11800, "start_time": 11720, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 11920, "start_time": 11800, "text": "风"}, {"attribute": {"event": "speech"}, "end_time": 12420, "start_time": 11920, "text": "格"}], "keyword": "器物"}], "keywords": ["他", "世界", "环境", "古代中国", "饭菜", "器物"]}, {"chapter": 1, "story_board": "他试着和母亲交流，学会了简单的说话方式，也开始慢慢适应这个新身份。一年后的周岁宴上，嬴政亲自来到，带着三个身强力壮的侍卫来看望儿子。", "characters": [{"name": "现代秦风", "gender": "男", "age": "青年", "clothes": "现代商务装，黑色西装为主色调，搭配白色衬衫和蓝色领带，脚穿黑色皮鞋，佩戴黑色商务手表圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "身材挺拔", "identity": "在洛杉矶打拼的华人精英", "other": "", "from_chapter": [0], "action": "躺在柔软的床上，嘴巴微张想要笑", "expression": "震惊又觉得荒谬"}, {"name": "婴儿公子华（秦风穿越后）", "gender": "男", "age": "婴儿", "clothes": "古代婴儿高圆领汉服襁褓，主色调为淡蓝色，搭配白色，无明显花纹，裹有柔软白色棉布带子圆形衣领，圆形衣领", "hairstyle": "黑色胎发", "figure": "瘦小", "identity": "秦始皇嬴政和阿房女的私生子", "other": "", "from_chapter": [0], "action": "四处张望、观察周围环境", "expression": "好奇"}, {"name": "阿房女", "gender": "女", "age": "青年", "clothes": "战国时期高圆领汉服长裙，主色调为淡粉色，搭配浅紫色和白色，有花卉纹样，腰间系白色丝带，头戴白玉发簪圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "苗条", "identity": "公子华的母亲", "other": "", "from_chapter": [0], "action": "无", "expression": "无"}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": [0], "action": "带着三个侍卫走进来，走向儿子", "expression": "期待"}, {"name": "嬴政侍卫", "gender": "男", "age": "青年", "clothes": "战国时期高圆领铠甲，主色调为黑色，搭配红色，有虎纹图案，腰间系黑色腰带，头戴黑色头盔，佩长剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "身强力壮", "identity": "秦始皇嬴政身边的侍卫", "other": "", "from_chapter": [0], "action": "跟在嬴政身后，站立警戒", "expression": "严肃"}], "scene": "中国古代建筑", "image_prompt": "中景，一位霸气威严且满脸期待的中年男子带着三个严肃的青年男子走进中国古代建筑，中年男子正走向前方，三个青年男子跟在中年男子身后站立警戒。中年男子身材魁梧，身着战国时期主色调为黑色、搭配金色和红色、带有龙纹图案的高圆领帝王袍，腰间束金色腰带，头戴黑色冕旒冠，圆形衣领，黑色束发；青年男子身强力壮，身着战国时期主色调为黑色、搭配红色、带有虎纹图案的高圆领铠甲，腰间系黑色腰带，头戴黑色头盔，佩长剑，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://p9-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/20250722225341B8BF367D9C9CC38519E8-2514-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1753282427&x-signature=%2BsqPwLKhkO4gq9UnR2KpA8np%2Foc%3D", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_0c525fb5-be38-44d2-b07a-9b781bd4a69f_1.25x_20250722_193514.wav", "audio_duration": 10.872, "video_url": "", "story_board_id": 12, "subtitle": [{"subtitle_id": 1, "text": "他试着和母亲交流，", "timestamp": "00:00:00,160 --> 00:00:01,400", "duration": 1.24, "char_count": 9, "start_time_s": 0.16, "end_time_s": 1.4, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "试"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 440, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 520, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 640, "text": "母"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 800, "text": "亲"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 920, "text": "交"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1120, "text": "流"}], "keyword": "他"}, {"subtitle_id": 2, "text": "学会了简单的说话方式，", "timestamp": "00:00:01,400 --> 00:00:02,960", "duration": 1.56, "char_count": 11, "start_time_s": 1.4, "end_time_s": 2.96, "words": [{"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1400, "text": "学"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1600, "text": "会"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1720, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1840, "text": "简"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 2000, "text": "单"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2120, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2240, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2360, "text": "话"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2480, "text": "方"}, {"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2640, "text": "式"}], "keyword": "说话"}, {"subtitle_id": 3, "text": "也开始慢慢适应这个新身份。", "timestamp": "00:00:02,960 --> 00:00:05,140", "duration": 2.18, "char_count": 13, "start_time_s": 2.96, "end_time_s": 5.14, "words": [{"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 2960, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3120, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3280, "text": "始"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3440, "text": "慢"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3480, "text": "慢"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3800, "text": "适"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 4000, "text": "应"}, {"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4120, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4240, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4360, "text": "新"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4520, "text": "身"}, {"attribute": {"event": "speech"}, "end_time": 5140, "start_time": 4640, "text": "份"}], "keyword": "身份"}, {"subtitle_id": 4, "text": "一年后的周岁宴上，", "timestamp": "00:00:05,200 --> 00:00:06,560", "duration": 1.36, "char_count": 9, "start_time_s": 5.2, "end_time_s": 6.56, "words": [{"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5200, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5400, "text": "年"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5560, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5680, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5800, "text": "周"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5920, "text": "岁"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6040, "text": "宴"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6240, "text": "上"}], "keyword": "周岁宴"}, {"subtitle_id": 5, "text": "嬴政亲自来到，", "timestamp": "00:00:06,560 --> 00:00:07,760", "duration": 1.2, "char_count": 7, "start_time_s": 6.56, "end_time_s": 7.76, "words": [{"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6560, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6760, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 7000, "text": "亲"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7200, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7320, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7440, "text": "到"}], "keyword": "嬴政"}, {"subtitle_id": 6, "text": "带着三个身强力壮的侍卫", "timestamp": "00:00:07,760 --> 00:00:09,400", "duration": 1.64, "char_count": 11, "start_time_s": 7.76, "end_time_s": 9.4, "words": [{"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7760, "text": "带"}, {"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7960, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 8240, "start_time": 8080, "text": "三"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8240, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8360, "text": "身"}, {"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8520, "text": "强"}, {"attribute": {"event": "speech"}, "end_time": 8840, "start_time": 8680, "text": "力"}, {"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8840, "text": "壮"}, {"attribute": {"event": "speech"}, "end_time": 9120, "start_time": 9000, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 9240, "start_time": 9120, "text": "侍"}, {"attribute": {"event": "speech"}, "end_time": 9400, "start_time": 9240, "text": "卫"}], "keyword": "侍卫"}, {"subtitle_id": 7, "text": "来看望儿子。", "timestamp": "00:00:09,400 --> 00:00:10,580", "duration": 1.18, "char_count": 6, "start_time_s": 9.4, "end_time_s": 10.58, "words": [{"attribute": {"event": "speech"}, "end_time": 9600, "start_time": 9400, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 9800, "start_time": 9600, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 9920, "start_time": 9800, "text": "望"}, {"attribute": {"event": "speech"}, "end_time": 10120, "start_time": 9920, "text": "儿"}, {"attribute": {"event": "speech"}, "end_time": 10580, "start_time": 10120, "text": "子"}], "keyword": "看望"}], "keywords": ["他", "说话", "身份", "周岁宴", "嬴政", "侍卫", "看望"]}, {"chapter": 1, "story_board": "秦风看着他们，心里明白，这些人应该是嬴政身边的重要人物。虽然他是个私生子，但身份也不低，毕竟他是秦始皇的亲生儿子。", "characters": [{"name": "现代秦风", "gender": "男", "age": "青年", "clothes": "现代商务装，黑色西装为主色调，搭配白色衬衫和蓝色领带，脚穿黑色皮鞋，佩戴黑色商务手表圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "身材挺拔", "identity": "在洛杉矶打拼的华人精英", "other": "", "from_chapter": [0], "action": "躺在柔软的床上，嘴巴微张想要笑", "expression": "震惊又觉得荒谬"}, {"name": "婴儿公子华（秦风穿越后）", "gender": "男", "age": "婴儿", "clothes": "古代婴儿高圆领汉服襁褓，主色调为淡蓝色，搭配白色，无明显花纹，裹有柔软白色棉布带子圆形衣领，圆形衣领", "hairstyle": "黑色胎发", "figure": "瘦小", "identity": "秦始皇嬴政和阿房女的私生子", "other": "", "from_chapter": [0], "action": "四处张望、观察周围环境", "expression": "好奇"}, {"name": "阿房女", "gender": "女", "age": "青年", "clothes": "战国时期高圆领汉服长裙，主色调为淡粉色，搭配浅紫色和白色，有花卉纹样，腰间系白色丝带，头戴白玉发簪圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "苗条", "identity": "公子华的母亲", "other": "", "from_chapter": [0], "action": "无", "expression": "无"}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": [0], "action": "带着三个侍卫走进来，走向儿子", "expression": "期待"}, {"name": "嬴政侍卫", "gender": "男", "age": "青年", "clothes": "战国时期高圆领铠甲，主色调为黑色，搭配红色，有虎纹图案，腰间系黑色腰带，头戴黑色头盔，佩长剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "身强力壮", "identity": "秦始皇嬴政身边的侍卫", "other": "", "from_chapter": [0], "action": "跟在嬴政身后，站立警戒", "expression": "严肃"}], "scene": "中国古代建筑", "image_prompt": "中景，中国古代建筑，建筑风格大气磅礴，飞檐斗拱，红墙绿瓦，屋顶的琉璃瓦在阳光下闪耀着光芒，建筑周围有一些花草树木作为点缀, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753196202_20250722_225643.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_84aac9ae-8359-4cf1-83c6-b016dd9613d3_1.25x_20250722_193453.wav", "audio_duration": 9.448, "video_url": "", "story_board_id": 13, "subtitle": [{"subtitle_id": 1, "text": "秦枫看着他们，", "timestamp": "00:00:00,200 --> 00:00:01,200", "duration": 1.0, "char_count": 7, "start_time_s": 0.2, "end_time_s": 1.2, "words": [{"attribute": {"event": "speech"}, "end_time": 360, "start_time": 200, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 360, "text": "枫"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 520, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 680, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 760, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 960, "text": "们"}], "keyword": "秦枫"}, {"subtitle_id": 2, "text": "心里明白，", "timestamp": "00:00:01,200 --> 00:00:01,960", "duration": 0.76, "char_count": 5, "start_time_s": 1.2, "end_time_s": 1.96, "words": [{"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1200, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1360, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1440, "text": "明"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1600, "text": "白"}], "keyword": "心里"}, {"subtitle_id": 3, "text": "这些人应该是嬴政身边的重要人物，", "timestamp": "00:00:01,960 --> 00:00:04,580", "duration": 2.62, "char_count": 16, "start_time_s": 1.96, "end_time_s": 4.58, "words": [{"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 1960, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2160, "text": "些"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2280, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2440, "text": "应"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2560, "text": "该"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2680, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2840, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 3000, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3200, "text": "身"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3320, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3480, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3640, "text": "重"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3800, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 3960, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 4580, "start_time": 4120, "text": "物"}], "keyword": "嬴政"}, {"subtitle_id": 4, "text": "虽然他是个私生子，", "timestamp": "00:00:04,720 --> 00:00:05,920", "duration": 1.2, "char_count": 9, "start_time_s": 4.72, "end_time_s": 5.92, "words": [{"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4720, "text": "虽"}, {"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4880, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5000, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5160, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5280, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5360, "text": "私"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5480, "text": "生"}, {"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5640, "text": "子"}], "keyword": "私生子"}, {"subtitle_id": 5, "text": "但身份也不低，", "timestamp": "00:00:05,920 --> 00:00:06,960", "duration": 1.04, "char_count": 7, "start_time_s": 5.92, "end_time_s": 6.96, "words": [{"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5920, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6120, "text": "身"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6320, "text": "份"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6440, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6560, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6680, "text": "低"}], "keyword": "身份"}, {"subtitle_id": 6, "text": "毕竟他是秦始皇的亲生儿子。", "timestamp": "00:00:06,960 --> 00:00:09,140", "duration": 2.18, "char_count": 13, "start_time_s": 6.96, "end_time_s": 9.14, "words": [{"attribute": {"event": "speech"}, "end_time": 7120, "start_time": 6960, "text": "毕"}, {"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 7120, "text": "竟"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7280, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7400, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7560, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 7880, "start_time": 7760, "text": "始"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7880, "text": "皇"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8040, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8200, "text": "亲"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8360, "text": "生"}, {"attribute": {"event": "speech"}, "end_time": 8720, "start_time": 8520, "text": "儿"}, {"attribute": {"event": "speech"}, "end_time": 9140, "start_time": 8720, "text": "子"}], "keyword": "秦始皇"}], "keywords": ["秦枫", "心里", "嬴政", "私生子", "身份", "秦始皇"]}, {"chapter": 1, "story_board": "抓周仪式开始了，红布上摆满了各种物品，金银珠宝、玉佩、剑、竹简和谷物。秦风本想抓钱，但嬴政却强行把他拉到了剑前。", "characters": [{"name": "现代秦风", "gender": "男", "age": "青年", "clothes": "现代商务装，黑色西装为主色调，搭配白色衬衫和蓝色领带，脚穿黑色皮鞋，佩戴黑色商务手表圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "身材挺拔", "identity": "在洛杉矶打拼的华人精英", "other": "", "from_chapter": [0], "action": "躺在柔软的床上，嘴巴微张想要笑", "expression": "震惊又觉得荒谬"}, {"name": "婴儿公子华（秦风穿越后）", "gender": "男", "age": "婴儿", "clothes": "古代婴儿高圆领汉服襁褓，主色调为淡蓝色，搭配白色，无明显花纹，裹有柔软白色棉布带子圆形衣领，圆形衣领", "hairstyle": "黑色胎发", "figure": "瘦小", "identity": "秦始皇嬴政和阿房女的私生子", "other": "", "from_chapter": [0], "action": "四处张望、观察周围环境", "expression": "好奇"}, {"name": "阿房女", "gender": "女", "age": "青年", "clothes": "战国时期高圆领汉服长裙，主色调为淡粉色，搭配浅紫色和白色，有花卉纹样，腰间系白色丝带，头戴白玉发簪圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "苗条", "identity": "公子华的母亲", "other": "", "from_chapter": [0], "action": "无", "expression": "无"}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": [0], "action": "强行拉着秦风到剑前", "expression": "严肃且带着期待"}, {"name": "嬴政侍卫", "gender": "男", "age": "青年", "clothes": "战国时期高圆领铠甲，主色调为黑色，搭配红色，有虎纹图案，腰间系黑色腰带，头戴黑色头盔，佩长剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "身强力壮", "identity": "秦始皇嬴政身边的侍卫", "other": "", "from_chapter": [0], "action": "跟在嬴政身后，站立警戒", "expression": "严肃"}], "scene": "中国古代建筑", "image_prompt": "中景，一个中年男子强行拉着一个年轻男子走向剑前，表情严肃且带着期待，背景是中国古代建筑。中年男子身材魁梧，神情霸气威严，头戴黑色冕旒冠，黑色束发，身着战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，衣领为圆形。年轻男子的外貌特征暂未明确, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753196432_20250722_230032.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_2e2335c1-8b99-4102-bad1-e8f71ea2d9fe_1.25x_20250722_193457.wav", "audio_duration": 9.448, "video_url": "", "story_board_id": 14, "subtitle": [{"subtitle_id": 1, "text": "抓周仪式开始了，", "timestamp": "00:00:00,160 --> 00:00:01,400", "duration": 1.24, "char_count": 8, "start_time_s": 0.16, "end_time_s": 1.4, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 160, "text": "抓"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "周"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 440, "text": "仪"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 600, "text": "式"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 720, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 920, "text": "始"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1080, "text": "了"}], "keyword": "抓周仪式"}, {"subtitle_id": 2, "text": "红布上摆满了各种物品，", "timestamp": "00:00:01,400 --> 00:00:03,120", "duration": 1.72, "char_count": 11, "start_time_s": 1.4, "end_time_s": 3.12, "words": [{"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1400, "text": "红"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1560, "text": "布"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1680, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1800, "text": "摆"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2040, "text": "满"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2160, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2240, "text": "各"}, {"attribute": {"event": "speech"}, "end_time": 2520, "start_time": 2400, "text": "种"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2520, "text": "物"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 2640, "text": "品"}], "keyword": "红布"}, {"subtitle_id": 3, "text": "金银珠宝欲配建竹简和古物。", "timestamp": "00:00:03,120 --> 00:00:05,460", "duration": 2.34, "char_count": 13, "start_time_s": 3.12, "end_time_s": 5.46, "words": [{"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3120, "text": "金"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3280, "text": "银"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3400, "text": "珠"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3600, "text": "宝"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3800, "text": "欲"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 3960, "text": "配"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4160, "text": "建"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4360, "text": "竹"}, {"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4520, "text": "简"}, {"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4720, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4880, "text": "古"}, {"attribute": {"event": "speech"}, "end_time": 5460, "start_time": 5040, "text": "物"}], "keyword": "金银珠宝"}, {"subtitle_id": 4, "text": "秦风本想抓钱，", "timestamp": "00:00:05,680 --> 00:00:06,800", "duration": 1.12, "char_count": 7, "start_time_s": 5.68, "end_time_s": 6.8, "words": [{"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5680, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5840, "text": "风"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 6000, "text": "本"}, {"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 6120, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6240, "text": "抓"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6400, "text": "钱"}], "keyword": "秦风"}, {"subtitle_id": 5, "text": "但嬴政却强行把他拉到了剑前。", "timestamp": "00:00:06,800 --> 00:00:09,100", "duration": 2.3, "char_count": 14, "start_time_s": 6.8, "end_time_s": 9.1, "words": [{"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6800, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 7120, "start_time": 6960, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7160, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7320, "text": "却"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7480, "text": "强"}, {"attribute": {"event": "speech"}, "end_time": 7880, "start_time": 7720, "text": "行"}, {"attribute": {"event": "speech"}, "end_time": 8000, "start_time": 7880, "text": "把"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 8000, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 8280, "start_time": 8120, "text": "拉"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8280, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8400, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8480, "text": "剑"}, {"attribute": {"event": "speech"}, "end_time": 9100, "start_time": 8640, "text": "前"}], "keyword": "剑"}], "keywords": ["抓周仪式", "红布", "金银珠宝", "秦风", "剑"]}, {"chapter": 1, "story_board": "他只能无奈地选了剑，心里暗暗嘀咕：这老爹真是霸道，连抓周都不让我自己做主。赢政把一枚带有玄鸟图案的玉佩系在秦风身上，脸上满是笑容。", "characters": [{"name": "现代秦风", "gender": "男", "age": "青年", "clothes": "现代商务装，黑色西装为主色调，搭配白色衬衫和蓝色领带，脚穿黑色皮鞋，佩戴黑色商务手表圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "身材挺拔", "identity": "在洛杉矶打拼的华人精英", "other": "", "from_chapter": [0], "action": "躺在柔软的床上，嘴巴微张想要笑", "expression": "震惊又觉得荒谬"}, {"name": "婴儿公子华（秦风穿越后）", "gender": "男", "age": "婴儿", "clothes": "古代婴儿高圆领汉服襁褓，主色调为淡蓝色，搭配白色，无明显花纹，裹有柔软白色棉布带子圆形衣领，圆形衣领", "hairstyle": "黑色胎发", "figure": "瘦小", "identity": "秦始皇嬴政和阿房女的私生子", "other": "", "from_chapter": [0], "action": "四处张望、观察周围环境", "expression": "好奇"}, {"name": "阿房女", "gender": "女", "age": "青年", "clothes": "战国时期高圆领汉服长裙，主色调为淡粉色，搭配浅紫色和白色，有花卉纹样，腰间系白色丝带，头戴白玉发簪圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "苗条", "identity": "公子华的母亲", "other": "", "from_chapter": [0], "action": "无", "expression": "无"}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": [0], "action": "把带有玄鸟图案的玉佩系在秦风身上", "expression": "笑容满面"}, {"name": "嬴政侍卫", "gender": "男", "age": "青年", "clothes": "战国时期高圆领铠甲，主色调为黑色，搭配红色，有虎纹图案，腰间系黑色腰带，头戴黑色头盔，佩长剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "身强力壮", "identity": "秦始皇嬴政身边的侍卫", "other": "", "from_chapter": [0], "action": "跟在嬴政身后，站立警戒", "expression": "严肃"}], "scene": "中国古代建筑", "image_prompt": "中景，笑容满面地将带有玄鸟图案的玉佩系在他人身上的中年男子，霸气威严，站在中国古代建筑前。中年男子魁梧，黑色束发，头戴黑色冕旒冠，身穿战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，圆形衣领, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753196669_20250722_230430.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_40ea967d-d5f3-44cc-9361-46d9917e8663_1.25x_20250722_193506.wav", "audio_duration": 11.064, "video_url": "", "story_board_id": 15, "subtitle": [{"subtitle_id": 1, "text": "他只能无奈的选了剑，", "timestamp": "00:00:00,160 --> 00:00:01,560", "duration": 1.4, "char_count": 10, "start_time_s": 0.16, "end_time_s": 1.56, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "只"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 400, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 560, "text": "无"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 720, "text": "奈"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 880, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 1000, "text": "选"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1120, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1240, "text": "剑"}], "keyword": "剑"}, {"subtitle_id": 2, "text": "心里暗暗嘀咕这老爹真是霸道，", "timestamp": "00:00:01,560 --> 00:00:03,960", "duration": 2.4, "char_count": 14, "start_time_s": 1.56, "end_time_s": 3.96, "words": [{"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1560, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1720, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1800, "text": "暗"}, {"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1840, "text": "暗"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2080, "text": "嘀"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2320, "text": "咕"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2720, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2880, "text": "老"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3080, "text": "爹"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3200, "text": "真"}, {"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3360, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3520, "text": "霸"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3680, "text": "道"}], "keyword": "老爹"}, {"subtitle_id": 3, "text": "连抓周都不让我自己做主。", "timestamp": "00:00:03,960 --> 00:00:05,860", "duration": 1.9, "char_count": 12, "start_time_s": 3.96, "end_time_s": 5.86, "words": [{"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 3960, "text": "连"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4160, "text": "抓"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4360, "text": "周"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4480, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4600, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4720, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4800, "text": "我"}, {"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 4960, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5080, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5200, "text": "做"}, {"attribute": {"event": "speech"}, "end_time": 5860, "start_time": 5400, "text": "主"}], "keyword": "抓周"}, {"subtitle_id": 4, "text": "嬴政把一枚带有玄鸟图案的玉佩", "timestamp": "00:00:06,000 --> 00:00:08,320", "duration": 2.32, "char_count": 14, "start_time_s": 6.0, "end_time_s": 8.32, "words": [{"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6000, "text": "嬴"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6200, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 6480, "start_time": 6360, "text": "把"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6480, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6600, "text": "枚"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6760, "text": "带"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6920, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7080, "text": "玄"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7280, "text": "鸟"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7440, "text": "图"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7600, "text": "案"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7760, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 8160, "start_time": 7960, "text": "玉"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8160, "text": "佩"}], "keyword": "玉佩"}, {"subtitle_id": 5, "text": "系在秦风身上，", "timestamp": "00:00:08,320 --> 00:00:09,440", "duration": 1.12, "char_count": 7, "start_time_s": 8.32, "end_time_s": 9.44, "words": [{"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8320, "text": "系"}, {"attribute": {"event": "speech"}, "end_time": 8600, "start_time": 8480, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8640, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8840, "text": "风"}, {"attribute": {"event": "speech"}, "end_time": 9160, "start_time": 8960, "text": "身"}, {"attribute": {"event": "speech"}, "end_time": 9440, "start_time": 9160, "text": "上"}], "keyword": "秦风"}, {"subtitle_id": 6, "text": "脸上满是笑容。", "timestamp": "00:00:09,440 --> 00:00:10,740", "duration": 1.3, "char_count": 7, "start_time_s": 9.44, "end_time_s": 10.74, "words": [{"attribute": {"event": "speech"}, "end_time": 9640, "start_time": 9440, "text": "脸"}, {"attribute": {"event": "speech"}, "end_time": 9760, "start_time": 9640, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 9920, "start_time": 9760, "text": "满"}, {"attribute": {"event": "speech"}, "end_time": 10080, "start_time": 9960, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 10280, "start_time": 10080, "text": "笑"}, {"attribute": {"event": "speech"}, "end_time": 10740, "start_time": 10280, "text": "容"}], "keyword": "笑容"}], "keywords": ["剑", "老爹", "抓周", "玉佩", "秦风", "笑容"]}, {"chapter": 1, "story_board": "他知道，这个儿子将来一定不简单。而秦风也在心里默默盘算：既然我来了，那就要好好活下去，不能白白浪费这次机会。", "characters": [{"name": "现代秦风", "gender": "男", "age": "青年", "clothes": "现代商务装，黑色西装为主色调，搭配白色衬衫和蓝色领带，脚穿黑色皮鞋，佩戴黑色商务手表圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "身材挺拔", "identity": "在洛杉矶打拼的华人精英", "other": "", "from_chapter": [0], "action": "躺在柔软的床上，嘴巴微张想要笑", "expression": "震惊又觉得荒谬"}, {"name": "婴儿公子华（秦风穿越后）", "gender": "男", "age": "婴儿", "clothes": "古代婴儿高圆领汉服襁褓，主色调为淡蓝色，搭配白色，无明显花纹，裹有柔软白色棉布带子圆形衣领，圆形衣领", "hairstyle": "黑色胎发", "figure": "瘦小", "identity": "秦始皇嬴政和阿房女的私生子", "other": "", "from_chapter": [0], "action": "四处张望、观察周围环境", "expression": "好奇"}, {"name": "阿房女", "gender": "女", "age": "青年", "clothes": "战国时期高圆领汉服长裙，主色调为淡粉色，搭配浅紫色和白色，有花卉纹样，腰间系白色丝带，头戴白玉发簪圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "苗条", "identity": "公子华的母亲", "other": "", "from_chapter": [0], "action": "无", "expression": "无"}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": [0], "action": "面带笑容，看着秦风", "expression": "欣慰"}, {"name": "嬴政侍卫", "gender": "男", "age": "青年", "clothes": "战国时期高圆领铠甲，主色调为黑色，搭配红色，有虎纹图案，腰间系黑色腰带，头戴黑色头盔，佩长剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "身强力壮", "identity": "秦始皇嬴政身边的侍卫", "other": "", "from_chapter": [0], "action": "跟在嬴政身后，站立警戒", "expression": "严肃"}], "scene": "中国古代建筑", "image_prompt": "中景，面带欣慰笑容看着对方的中年男子，身穿战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠，圆形衣领，黑色束发，身材魁梧，霸气威严，背景为中国古代建筑, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753196960_20250722_230920.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_57247a7c-2989-401a-84b4-fc2f8c2984ee_1.25x_20250722_193510.wav", "audio_duration": 9.256, "video_url": "", "story_board_id": 16, "subtitle": [{"subtitle_id": 1, "text": "他知道，这个儿子将来一定不简单。", "timestamp": "00:00:00,160 --> 00:00:02,900", "duration": 2.74, "char_count": 16, "start_time_s": 0.16, "end_time_s": 2.9, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 440, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 760, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 960, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1040, "text": "儿"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1200, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1480, "text": "将"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1600, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1760, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1960, "text": "定"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2120, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2240, "text": "简"}, {"attribute": {"event": "speech"}, "end_time": 2900, "start_time": 2440, "text": "单"}], "keyword": "儿子"}, {"subtitle_id": 2, "text": "而秦枫也在心里默默盘算，", "timestamp": "00:00:03,000 --> 00:00:05,040", "duration": 2.04, "char_count": 12, "start_time_s": 3.0, "end_time_s": 5.04, "words": [{"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3000, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3240, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3480, "text": "枫"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3600, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3760, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3880, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 4000, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4120, "text": "默"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4160, "text": "默"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4400, "text": "盘"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4600, "text": "算"}], "keyword": "秦枫"}, {"subtitle_id": 3, "text": "既然我来了，", "timestamp": "00:00:05,040 --> 00:00:05,800", "duration": 0.76, "char_count": 6, "start_time_s": 5.04, "end_time_s": 5.8, "words": [{"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5040, "text": "既"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5160, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5280, "text": "我"}, {"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5400, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5560, "text": "了"}], "keyword": "我"}, {"subtitle_id": 4, "text": "那就要好好活下去，", "timestamp": "00:00:05,800 --> 00:00:07,080", "duration": 1.28, "char_count": 9, "start_time_s": 5.8, "end_time_s": 7.08, "words": [{"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5800, "text": "那"}, {"attribute": {"event": "speech"}, "end_time": 6080, "start_time": 6000, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6080, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 6200, "text": "好"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6240, "text": "好"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6520, "text": "活"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6680, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6800, "text": "去"}], "keyword": "生活"}, {"subtitle_id": 5, "text": "不能白白浪费这次机会。", "timestamp": "00:00:07,080 --> 00:00:08,940", "duration": 1.86, "char_count": 11, "start_time_s": 7.08, "end_time_s": 8.94, "words": [{"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7080, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 7360, "start_time": 7240, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7360, "text": "白"}, {"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7600, "text": "白"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7760, "text": "浪"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7920, "text": "费"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8080, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8200, "text": "次"}, {"attribute": {"event": "speech"}, "end_time": 8480, "start_time": 8320, "text": "机"}, {"attribute": {"event": "speech"}, "end_time": 8940, "start_time": 8480, "text": "会"}], "keyword": "机会"}], "keywords": ["儿子", "秦枫", "我", "生活", "机会"]}, {"chapter": 1, "story_board": "接下来的日子里，秦风一边适应新的生活，一边悄悄观察这个世界。他意识到，自己可能不仅仅是一个普通的王子，而是肩负着某种使命。", "characters": [{"name": "现代秦风", "gender": "男", "age": "青年", "clothes": "现代商务装，黑色西装为主色调，搭配白色衬衫和蓝色领带，脚穿黑色皮鞋，佩戴黑色商务手表圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "身材挺拔", "identity": "在洛杉矶打拼的华人精英", "other": "", "from_chapter": [0], "action": "一边在房间内踱步适应环境，一边偷偷观察周围", "expression": "若有所思"}, {"name": "婴儿公子华（秦风穿越后）", "gender": "男", "age": "婴儿", "clothes": "古代婴儿高圆领汉服襁褓，主色调为淡蓝色，搭配白色，无明显花纹，裹有柔软白色棉布带子圆形衣领，圆形衣领", "hairstyle": "黑色胎发", "figure": "瘦小", "identity": "秦始皇嬴政和阿房女的私生子", "other": "", "from_chapter": [0], "action": "四处张望、观察周围环境", "expression": "好奇"}, {"name": "阿房女", "gender": "女", "age": "青年", "clothes": "战国时期高圆领汉服长裙，主色调为淡粉色，搭配浅紫色和白色，有花卉纹样，腰间系白色丝带，头戴白玉发簪圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "苗条", "identity": "公子华的母亲", "other": "", "from_chapter": [0], "action": "无", "expression": "无"}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": [0], "action": "面带笑容，看着秦风", "expression": "欣慰"}, {"name": "嬴政侍卫", "gender": "男", "age": "青年", "clothes": "战国时期高圆领铠甲，主色调为黑色，搭配红色，有虎纹图案，腰间系黑色腰带，头戴黑色头盔，佩长剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "身强力壮", "identity": "秦始皇嬴政身边的侍卫", "other": "", "from_chapter": [0], "action": "跟在嬴政身后，站立警戒", "expression": "严肃"}], "scene": "室内", "image_prompt": "中景，一个在室内一边踱步适应环境一边偷偷观察周围、若有所思的青年男子，穿着以黑色西装为主色调、搭配白色衬衫和蓝色领带的现代商务装，脚穿黑色皮鞋，佩戴黑色商务手表，有着圆形衣领，留着黑色短发，身材挺拔, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753197514_20250722_231835.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_7cc7d609-0b11-4749-90f8-8bcee82ac4aa_1.25x_20250722_193441.wav", "audio_duration": 9.832, "video_url": "", "story_board_id": 17, "subtitle": [{"subtitle_id": 1, "text": "接下来的日子里，", "timestamp": "00:00:00,200 --> 00:00:01,200", "duration": 1.0, "char_count": 8, "start_time_s": 0.2, "end_time_s": 1.2, "words": [{"attribute": {"event": "speech"}, "end_time": 360, "start_time": 200, "text": "接"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 360, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 480, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 560, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 640, "text": "日"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 760, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 880, "text": "里"}], "keyword": "日子"}, {"subtitle_id": 2, "text": "秦风一边适应新的生活，", "timestamp": "00:00:01,200 --> 00:00:02,840", "duration": 1.64, "char_count": 11, "start_time_s": 1.2, "end_time_s": 2.84, "words": [{"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1200, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1400, "text": "风"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1520, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1640, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1840, "text": "适"}, {"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1960, "text": "应"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2120, "text": "新"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2280, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2400, "text": "生"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2560, "text": "活"}], "keyword": "秦风"}, {"subtitle_id": 3, "text": "一边悄悄观察这个世界。", "timestamp": "00:00:02,840 --> 00:00:04,780", "duration": 1.94, "char_count": 11, "start_time_s": 2.84, "end_time_s": 4.78, "words": [{"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2840, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 3000, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3200, "text": "悄"}, {"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3240, "text": "悄"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3560, "text": "观"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3760, "text": "察"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3960, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4080, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4200, "text": "世"}, {"attribute": {"event": "speech"}, "end_time": 4780, "start_time": 4320, "text": "界"}], "keyword": "世界"}, {"subtitle_id": 4, "text": "他意识到，", "timestamp": "00:00:04,840 --> 00:00:05,600", "duration": 0.76, "char_count": 5, "start_time_s": 4.84, "end_time_s": 5.6, "words": [{"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4840, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5040, "text": "意"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5160, "text": "识"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5240, "text": "到"}], "keyword": "意识"}, {"subtitle_id": 5, "text": "自己可能不仅仅是一个普通的王子，", "timestamp": "00:00:05,600 --> 00:00:07,960", "duration": 2.36, "char_count": 16, "start_time_s": 5.6, "end_time_s": 7.96, "words": [{"attribute": {"event": "speech"}, "end_time": 5760, "start_time": 5600, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5760, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5880, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6040, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6160, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6280, "text": "仅"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6320, "text": "仅"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6600, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6720, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6840, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 6960, "text": "普"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7160, "text": "通"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7320, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7480, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7640, "text": "子"}], "keyword": "王子"}, {"subtitle_id": 6, "text": "而是肩负着某种使命。", "timestamp": "00:00:07,960 --> 00:00:09,780", "duration": 1.82, "char_count": 10, "start_time_s": 7.96, "end_time_s": 9.78, "words": [{"attribute": {"event": "speech"}, "end_time": 8160, "start_time": 7960, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 8280, "start_time": 8160, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 8440, "start_time": 8280, "text": "肩"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8480, "text": "负"}, {"attribute": {"event": "speech"}, "end_time": 8760, "start_time": 8640, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 8920, "start_time": 8760, "text": "某"}, {"attribute": {"event": "speech"}, "end_time": 9080, "start_time": 8920, "text": "种"}, {"attribute": {"event": "speech"}, "end_time": 9280, "start_time": 9120, "text": "使"}, {"attribute": {"event": "speech"}, "end_time": 9780, "start_time": 9320, "text": "命"}], "keyword": "使命"}], "keywords": ["日子", "秦风", "世界", "意识", "王子", "使命"]}, {"chapter": 1, "story_board": "他开始学习古代的文化、礼仪，甚至偷偷研究历史，试图找到让自己在这个世界立足的方法。他明白，要想活得好，就必须比别人更聪明、更谨慎。", "characters": [{"name": "现代秦风", "gender": "男", "age": "青年", "clothes": "现代商务装，黑色西装为主色调，搭配白色衬衫和蓝色领带，脚穿黑色皮鞋，佩戴黑色商务手表圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "身材挺拔", "identity": "在洛杉矶打拼的华人精英", "other": "", "from_chapter": [0], "action": "坐在桌前翻阅竹简，时而皱眉思考，时而奋笔疾书，眼睛不时警惕地看向周围", "expression": "专注、坚定"}, {"name": "婴儿公子华（秦风穿越后）", "gender": "男", "age": "婴儿", "clothes": "古代婴儿高圆领汉服襁褓，主色调为淡蓝色，搭配白色，无明显花纹，裹有柔软白色棉布带子圆形衣领，圆形衣领", "hairstyle": "黑色胎发", "figure": "瘦小", "identity": "秦始皇嬴政和阿房女的私生子", "other": "", "from_chapter": [0], "action": "四处张望、观察周围环境", "expression": "好奇"}, {"name": "阿房女", "gender": "女", "age": "青年", "clothes": "战国时期高圆领汉服长裙，主色调为淡粉色，搭配浅紫色和白色，有花卉纹样，腰间系白色丝带，头戴白玉发簪圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "苗条", "identity": "公子华的母亲", "other": "", "from_chapter": [0], "action": "无", "expression": "无"}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": [0], "action": "面带笑容，看着秦风", "expression": "欣慰"}, {"name": "嬴政侍卫", "gender": "男", "age": "青年", "clothes": "战国时期高圆领铠甲，主色调为黑色，搭配红色，有虎纹图案，腰间系黑色腰带，头戴黑色头盔，佩长剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "身强力壮", "identity": "秦始皇嬴政身边的侍卫", "other": "", "from_chapter": [0], "action": "跟在嬴政身后，站立警戒", "expression": "严肃"}], "scene": "室内", "image_prompt": "中景，坐在桌前专注、坚定地翻阅竹简，时而皱眉思考，时而奋笔疾书，眼睛不时警惕地看向周围的青年男子。室内背景。该男子身材挺拔，穿着以黑色西装为主色调、搭配白色衬衫和蓝色领带的现代商务装，脚穿黑色皮鞋，佩戴黑色商务手表，有着圆形衣领，留着黑色短发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753197930_20250722_232530.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_8d85a57d-4501-4f69-b71f-8b11cce2a17e_1.25x_20250722_193501.wav", "audio_duration": 10.834667, "video_url": "", "story_board_id": 18, "subtitle": [{"subtitle_id": 1, "text": "他开始学习古代的文化、礼仪，", "timestamp": "00:00:00,200 --> 00:00:02,120", "duration": 1.92, "char_count": 14, "start_time_s": 0.2, "end_time_s": 2.12, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 400, "text": "始"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 560, "text": "学"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 720, "text": "习"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 840, "text": "古"}, {"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 1040, "text": "代"}, {"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 1160, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1280, "text": "文"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1440, "text": "化"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1600, "text": "礼"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1720, "text": "仪"}], "keyword": "他"}, {"subtitle_id": 2, "text": "甚至偷偷研究历史，", "timestamp": "00:00:02,120 --> 00:00:03,560", "duration": 1.44, "char_count": 9, "start_time_s": 2.12, "end_time_s": 3.56, "words": [{"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2120, "text": "甚"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2280, "text": "至"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2440, "text": "偷"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2480, "text": "偷"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2760, "text": "研"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2920, "text": "究"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3040, "text": "历"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3200, "text": "史"}], "keyword": "历史"}, {"subtitle_id": 3, "text": "试图找到", "timestamp": "00:00:03,560 --> 00:00:04,280", "duration": 0.72, "char_count": 4, "start_time_s": 3.56, "end_time_s": 4.28, "words": [{"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3560, "text": "试"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3760, "text": "图"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3880, "text": "找"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4040, "text": "到"}], "keyword": "研究"}, {"subtitle_id": 4, "text": "让自己在这个世界立足的方法。", "timestamp": "00:00:04,280 --> 00:00:06,460", "duration": 2.18, "char_count": 14, "start_time_s": 4.28, "end_time_s": 6.46, "words": [{"attribute": {"event": "speech"}, "end_time": 4440, "start_time": 4280, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4440, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4600, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4720, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4840, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 4960, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5080, "text": "世"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5200, "text": "界"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5360, "text": "立"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5560, "text": "足"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5680, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5800, "text": "方"}, {"attribute": {"event": "speech"}, "end_time": 6460, "start_time": 6000, "text": "法"}], "keyword": "方法"}, {"subtitle_id": 5, "text": "他明白，要想活得好，", "timestamp": "00:00:06,560 --> 00:00:08,080", "duration": 1.52, "char_count": 10, "start_time_s": 6.56, "end_time_s": 8.08, "words": [{"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6560, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6680, "text": "明"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6840, "text": "白"}, {"attribute": {"event": "speech"}, "end_time": 7360, "start_time": 7160, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 7520, "start_time": 7360, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 7640, "start_time": 7520, "text": "活"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7640, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 8080, "start_time": 7760, "text": "好"}], "keyword": "活得好"}, {"subtitle_id": 6, "text": "就必须比别人更聪明、更谨慎。", "timestamp": "00:00:08,080 --> 00:00:10,500", "duration": 2.42, "char_count": 14, "start_time_s": 8.08, "end_time_s": 10.5, "words": [{"attribute": {"event": "speech"}, "end_time": 8240, "start_time": 8080, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8240, "text": "必"}, {"attribute": {"event": "speech"}, "end_time": 8600, "start_time": 8440, "text": "须"}, {"attribute": {"event": "speech"}, "end_time": 8760, "start_time": 8600, "text": "比"}, {"attribute": {"event": "speech"}, "end_time": 8920, "start_time": 8760, "text": "别"}, {"attribute": {"event": "speech"}, "end_time": 9040, "start_time": 8920, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 9200, "start_time": 9040, "text": "更"}, {"attribute": {"event": "speech"}, "end_time": 9400, "start_time": 9240, "text": "聪"}, {"attribute": {"event": "speech"}, "end_time": 9600, "start_time": 9400, "text": "明"}, {"attribute": {"event": "speech"}, "end_time": 9840, "start_time": 9640, "text": "更"}, {"attribute": {"event": "speech"}, "end_time": 10040, "start_time": 9880, "text": "谨"}, {"attribute": {"event": "speech"}, "end_time": 10500, "start_time": 10080, "text": "慎"}], "keyword": "聪明"}], "keywords": ["他", "历史", "研究", "方法", "活得好", "聪明"]}, {"chapter": 1, "story_board": "他不再只是一个商人，而是一个身处乱世的皇子，必须学会权谋与智慧。他知道，未来充满了未知，但他已经做好了准备。", "characters": [{"name": "现代秦风", "gender": "男", "age": "青年", "clothes": "现代商务装，黑色西装为主色调，搭配白色衬衫和蓝色领带，脚穿黑色皮鞋，佩戴黑色商务手表圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "身材挺拔", "identity": "在洛杉矶打拼的华人精英", "other": "", "from_chapter": [0], "action": "坐在桌前翻阅竹简，时而皱眉思考，时而奋笔疾书，眼睛不时警惕地看向周围", "expression": "专注、坚定"}, {"name": "婴儿公子华（秦风穿越后）", "gender": "男", "age": "婴儿", "clothes": "古代婴儿高圆领汉服襁褓，主色调为淡蓝色，搭配白色，无明显花纹，裹有柔软白色棉布带子圆形衣领，圆形衣领", "hairstyle": "黑色胎发", "figure": "瘦小", "identity": "秦始皇嬴政和阿房女的私生子", "other": "", "from_chapter": [0], "action": "四处张望、观察周围环境", "expression": "好奇"}, {"name": "阿房女", "gender": "女", "age": "青年", "clothes": "战国时期高圆领汉服长裙，主色调为淡粉色，搭配浅紫色和白色，有花卉纹样，腰间系白色丝带，头戴白玉发簪圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "苗条", "identity": "公子华的母亲", "other": "", "from_chapter": [0], "action": "无", "expression": "无"}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": [0], "action": "面带笑容，看着秦风", "expression": "欣慰"}, {"name": "嬴政侍卫", "gender": "男", "age": "青年", "clothes": "战国时期高圆领铠甲，主色调为黑色，搭配红色，有虎纹图案，腰间系黑色腰带，头戴黑色头盔，佩长剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "身强力壮", "identity": "秦始皇嬴政身边的侍卫", "other": "", "from_chapter": [0], "action": "跟在嬴政身后，站立警戒", "expression": "严肃"}], "scene": "室内", "image_prompt": "中景，室内场景, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753198312_20250722_233153.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_c8ea001c-81f1-4241-ae0c-7454249cf35b_1.25x_20250722_193518.wav", "audio_duration": 9.392, "video_url": "", "story_board_id": 19, "subtitle": [{"subtitle_id": 1, "text": "他不再只是一个商人，", "timestamp": "00:00:00,160 --> 00:00:01,600", "duration": 1.44, "char_count": 10, "start_time_s": 0.16, "end_time_s": 1.6, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 440, "text": "再"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 600, "text": "只"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 760, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 880, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 1000, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1080, "text": "商"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1240, "text": "人"}], "keyword": "商人"}, {"subtitle_id": 2, "text": "而是一个身处乱世的皇子，", "timestamp": "00:00:01,600 --> 00:00:03,400", "duration": 1.8, "char_count": 12, "start_time_s": 1.6, "end_time_s": 3.4, "words": [{"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1600, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1800, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1880, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2000, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2160, "text": "身"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2320, "text": "处"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2480, "text": "乱"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2640, "text": "世"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2760, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2880, "text": "皇"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3080, "text": "子"}], "keyword": "皇子"}, {"subtitle_id": 3, "text": "必须学会权谋与智慧。", "timestamp": "00:00:03,400 --> 00:00:05,300", "duration": 1.9, "char_count": 10, "start_time_s": 3.4, "end_time_s": 5.3, "words": [{"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3400, "text": "必"}, {"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3600, "text": "须"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3800, "text": "学"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 3960, "text": "会"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4120, "text": "权"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4360, "text": "谋"}, {"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4560, "text": "与"}, {"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4680, "text": "智"}, {"attribute": {"event": "speech"}, "end_time": 5300, "start_time": 4840, "text": "慧"}], "keyword": "权谋"}, {"subtitle_id": 4, "text": "他知道未来充满了未知，", "timestamp": "00:00:05,400 --> 00:00:07,400", "duration": 2.0, "char_count": 11, "start_time_s": 5.4, "end_time_s": 7.4, "words": [{"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5400, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5560, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5720, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 6040, "text": "未"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6240, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6440, "text": "充"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6600, "text": "满"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6720, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6840, "text": "未"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7000, "text": "知"}], "keyword": "未来"}, {"subtitle_id": 5, "text": "但他已经做好了准备。", "timestamp": "00:00:07,400 --> 00:00:09,020", "duration": 1.62, "char_count": 10, "start_time_s": 7.4, "end_time_s": 9.02, "words": [{"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7400, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7560, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7680, "text": "已"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7800, "text": "经"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 7920, "text": "做"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8120, "text": "好"}, {"attribute": {"event": "speech"}, "end_time": 8440, "start_time": 8320, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 8600, "start_time": 8440, "text": "准"}, {"attribute": {"event": "speech"}, "end_time": 9020, "start_time": 8600, "text": "备"}], "keyword": "准备"}], "keywords": ["商人", "皇子", "权谋", "未来", "准备"]}, {"chapter": 1, "story_board": "从一个现代精英，到一个古代王子，秦风的故事才刚刚开始。他不知道自己是否真的能改变历史，但他知道，这一次，他一定要活得精彩。", "characters": [{"name": "现代秦风", "gender": "男", "age": "青年", "clothes": "现代商务装，黑色西装为主色调，搭配白色衬衫和蓝色领带，脚穿黑色皮鞋，佩戴黑色商务手表圆形衣领，圆形衣领", "hairstyle": "黑色短发", "figure": "身材挺拔", "identity": "在洛杉矶打拼的华人精英", "other": "", "from_chapter": [0], "action": "双手握拳，眼神坚定地望向远方", "expression": "充满斗志"}, {"name": "婴儿公子华（秦风穿越后）", "gender": "男", "age": "婴儿", "clothes": "古代婴儿高圆领汉服襁褓，主色调为淡蓝色，搭配白色，无明显花纹，裹有柔软白色棉布带子圆形衣领，圆形衣领", "hairstyle": "黑色胎发", "figure": "瘦小", "identity": "秦始皇嬴政和阿房女的私生子", "other": "", "from_chapter": [0], "action": "坐在柔软的床上，眼神灵动地四处张望", "expression": "好奇"}, {"name": "阿房女", "gender": "女", "age": "青年", "clothes": "战国时期高圆领汉服长裙，主色调为淡粉色，搭配浅紫色和白色，有花卉纹样，腰间系白色丝带，头戴白玉发簪圆形衣领，圆形衣领", "hairstyle": "黑色发髻", "figure": "苗条", "identity": "公子华的母亲", "other": "", "from_chapter": [0], "action": "无", "expression": "无"}, {"name": "嬴政", "gender": "男", "age": "中年", "clothes": "战国时期高圆领帝王袍，主色调为黑色，搭配金色和红色，有龙纹图案，腰间束金色腰带，头戴黑色冕旒冠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "魁梧", "identity": "秦始皇", "other": "霸气威严", "from_chapter": [0], "action": "面带笑容，看着秦风", "expression": "欣慰"}, {"name": "嬴政侍卫", "gender": "男", "age": "青年", "clothes": "战国时期高圆领铠甲，主色调为黑色，搭配红色，有虎纹图案，腰间系黑色腰带，头戴黑色头盔，佩长剑圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "身强力壮", "identity": "秦始皇嬴政身边的侍卫", "other": "", "from_chapter": [0], "action": "跟在嬴政身后，站立警戒", "expression": "严肃"}], "scene": "室内", "image_prompt": "中景，画面为室内，一个双手握拳、眼神坚定地望向远方、充满斗志的青年站着，旁边是一个眼神灵动地四处张望、好奇地坐在柔软床上的婴儿。站着的青年身材挺拔，留着黑色短发，身穿以黑色西装为主色调、搭配白色衬衫和蓝色领带的现代商务装，脚穿黑色皮鞋，佩戴黑色商务手表，有着圆形衣领；坐着的婴儿瘦小，有着黑色胎发，穿着以淡蓝色为主色调、搭配白色、无明显花纹、裹有柔软白色棉布带子的古代婴儿高圆领汉服襁褓，有圆形衣领, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753198728_20250722_233849.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_bf0e5e1d-0d3b-49aa-817b-2285a4b7cc9c_1.25x_20250722_193449.wav", "audio_duration": 10.352, "video_url": "", "story_board_id": 20, "subtitle": [{"subtitle_id": 1, "text": "从一个现代精英到一个古代王子，", "timestamp": "00:00:00,200 --> 00:00:02,800", "duration": 2.6, "char_count": 15, "start_time_s": 0.2, "end_time_s": 2.8, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 200, "text": "从"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 440, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 640, "text": "现"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 800, "text": "代"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 1000, "text": "精"}, {"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 1120, "text": "英"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1520, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1720, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1840, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1960, "text": "古"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2160, "text": "代"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2320, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2440, "text": "子"}], "keyword": "秦风"}, {"subtitle_id": 2, "text": "秦风的故事才刚刚开始。", "timestamp": "00:00:02,800 --> 00:00:04,700", "duration": 1.9, "char_count": 11, "start_time_s": 2.8, "end_time_s": 4.7, "words": [{"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2800, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 3040, "text": "风"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3120, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3240, "text": "故"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3400, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3560, "text": "才"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3720, "text": "刚"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3920, "text": "刚"}, {"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4040, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 4700, "start_time": 4240, "text": "始"}], "keyword": "历史"}, {"subtitle_id": 3, "text": "他不知道自己是否真的能改变历史，", "timestamp": "00:00:04,800 --> 00:00:07,280", "duration": 2.48, "char_count": 16, "start_time_s": 4.8, "end_time_s": 7.28, "words": [{"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4800, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 4960, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5080, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5160, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5440, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5600, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5720, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5880, "text": "否"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 6040, "text": "真"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6200, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6320, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6440, "text": "改"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6640, "text": "变"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6760, "text": "历"}, {"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 6920, "text": "史"}], "keyword": "精英"}, {"subtitle_id": 4, "text": "但他知道，", "timestamp": "00:00:07,280 --> 00:00:08,040", "duration": 0.76, "char_count": 5, "start_time_s": 7.28, "end_time_s": 8.04, "words": [{"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7280, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7480, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7600, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7760, "text": "道"}], "keyword": "王子"}, {"subtitle_id": 5, "text": "这一次，他一定要活得精彩。", "timestamp": "00:00:08,040 --> 00:00:10,300", "duration": 2.26, "char_count": 13, "start_time_s": 8.04, "end_time_s": 10.3, "words": [{"attribute": {"event": "speech"}, "end_time": 8240, "start_time": 8040, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8240, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8320, "text": "次"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8640, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 9000, "start_time": 8800, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 9160, "start_time": 9000, "text": "定"}, {"attribute": {"event": "speech"}, "end_time": 9320, "start_time": 9160, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 9520, "start_time": 9360, "text": "活"}, {"attribute": {"event": "speech"}, "end_time": 9640, "start_time": 9520, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 9760, "start_time": 9640, "text": "精"}, {"attribute": {"event": "speech"}, "end_time": 10300, "start_time": 9800, "text": "彩"}], "keyword": "精彩"}], "keywords": ["秦风", "历史", "精英", "王子", "精彩"]}]