[{"chapter": 79, "story_board": "清晨的阳光刚洒进东宫，一阵急促的脚步声打破了平静。公子华猛地推开书房门，脸色阴沉得像要下雨。他盯着桌上那张地图，手指重重敲在上面，声音低沉却带着怒意：“这不可能！", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "猛地推开书房门，盯着桌上的地图，手指重重敲在上面", "expression": "愤怒"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "郑国渠", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为蓝色，搭配灰色，上有灰色线条装饰，头戴黑色方巾，可配蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "水利部官员", "other": "面容朴实，神情专注", "from_chapter": [0], "action": "", "expression": ""}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "", "expression": ""}, {"name": "冯去疾", "gender": "男", "age": "中年", "clothes": "高领圆领绿色朝服，主色调绿色，搭配白色，有树叶纹，头戴乌纱帽，腰间系绿丝带，配玉坠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "适中", "identity": "秦国大臣", "other": "", "from_chapter": 67, "action": "", "expression": ""}, {"name": "财政部长", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为深灰色，搭配金色，有金色丝线勾勒图案，头戴黑色官帽，可配黑色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色官帽", "figure": "微胖", "identity": "财政部官员", "other": "眉头微皱，忧心忡忡", "from_chapter": [0], "action": "", "expression": ""}, {"name": "税务部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为棕色，搭配白色，衣摆有白色镶边，头戴黑色小冠，可配棕色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色小冠", "figure": "中等身材", "identity": "税务部官员", "other": "神情严肃", "from_chapter": [0], "action": "", "expression": ""}, {"name": "兵部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服铁黑色铠甲，搭配红色，外罩红色披风，头戴黑色头盔，可配红色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色头盔", "figure": "魁梧", "identity": "兵部官员", "other": "面容刚毅", "from_chapter": [0], "action": "", "expression": ""}, {"name": "工部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为土黄色，搭配绿色，袖口绣绿色花纹，头戴黑色斗笠状官帽，可配黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色斗笠状官帽", "figure": "身材壮实", "identity": "工部官员", "other": "皮肤黝黑", "from_chapter": [0], "action": "", "expression": ""}, {"name": "战备部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为藏青色，搭配蓝色，配有蓝色腰带，头戴黑色毡帽，可配藏青色香囊圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色毡帽", "figure": "中等身材", "identity": "战备部官员", "other": "目光沉稳", "from_chapter": [0], "action": "", "expression": ""}], "scene": "书房", "image_prompt": "中景，一个愤怒的青年男子猛地推开书房门，盯着桌上的地图，手指重重敲在上面。该男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发，身形挺拔，背景是书房, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753194563_20250722_222924.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_e6f007fb-bf0a-4b1d-a1c4-b96d09b5af45_1.25x_20250722_201926.wav", "audio_duration": 14.210667, "video_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/videos/video_1753194914_20250722_223515.mp4", "story_board_id": 1, "subtitle": [{"subtitle_id": 1, "text": "清晨的阳光刚洒进东宫，", "timestamp": "00:00:00,240 --> 00:00:01,880", "duration": 1.64, "char_count": 11, "start_time_s": 0.24, "end_time_s": 1.88, "words": [{"attribute": {"event": "speech"}, "end_time": 360, "start_time": 240, "text": "清"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 360, "text": "晨"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 480, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 720, "start_time": 600, "text": "阳"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 720, "text": "光"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 880, "text": "刚"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1080, "text": "洒"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1240, "text": "进"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1360, "text": "东"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1520, "text": "宫"}], "keyword": "东宫"}, {"subtitle_id": 2, "text": "一阵急促的脚步声打破了平静。", "timestamp": "00:00:01,880 --> 00:00:04,500", "duration": 2.62, "char_count": 14, "start_time_s": 1.88, "end_time_s": 4.5, "words": [{"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1880, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2080, "text": "阵"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2240, "text": "急"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2480, "text": "促"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2600, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2720, "text": "脚"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2880, "text": "步"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3080, "text": "声"}, {"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3280, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3520, "text": "破"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3720, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3840, "text": "平"}, {"attribute": {"event": "speech"}, "end_time": 4500, "start_time": 4040, "text": "静"}], "keyword": "脚步声"}, {"subtitle_id": 3, "text": "龚子华猛地推开书房门，", "timestamp": "00:00:04,560 --> 00:00:06,360", "duration": 1.8, "char_count": 11, "start_time_s": 4.56, "end_time_s": 6.36, "words": [{"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4560, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4760, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 4920, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5080, "text": "猛"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5240, "text": "地"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5360, "text": "推"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5560, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5720, "text": "书"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5880, "text": "房"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6000, "text": "门"}], "keyword": "龚子华"}, {"subtitle_id": 4, "text": "脸色阴沉的像要下雨。", "timestamp": "00:00:06,360 --> 00:00:08,180", "duration": 1.82, "char_count": 10, "start_time_s": 6.36, "end_time_s": 8.18, "words": [{"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6360, "text": "脸"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6560, "text": "色"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6720, "text": "阴"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6920, "text": "沉"}, {"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 7080, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7360, "start_time": 7200, "text": "像"}, {"attribute": {"event": "speech"}, "end_time": 7520, "start_time": 7360, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7520, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 8180, "start_time": 7720, "text": "雨"}], "keyword": "脸色"}, {"subtitle_id": 5, "text": "他盯着桌上那张地图，", "timestamp": "00:00:08,240 --> 00:00:09,800", "duration": 1.56, "char_count": 10, "start_time_s": 8.24, "end_time_s": 9.8, "words": [{"attribute": {"event": "speech"}, "end_time": 8440, "start_time": 8240, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 8560, "start_time": 8440, "text": "盯"}, {"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8560, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 8840, "start_time": 8680, "text": "桌"}, {"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8840, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 9120, "start_time": 8960, "text": "那"}, {"attribute": {"event": "speech"}, "end_time": 9240, "start_time": 9120, "text": "张"}, {"attribute": {"event": "speech"}, "end_time": 9400, "start_time": 9240, "text": "地"}, {"attribute": {"event": "speech"}, "end_time": 9800, "start_time": 9400, "text": "图"}], "keyword": "地图"}, {"subtitle_id": 6, "text": "手指重重敲在上面，", "timestamp": "00:00:09,800 --> 00:00:11,280", "duration": 1.48, "char_count": 9, "start_time_s": 9.8, "end_time_s": 11.28, "words": [{"attribute": {"event": "speech"}, "end_time": 9960, "start_time": 9800, "text": "手"}, {"attribute": {"event": "speech"}, "end_time": 10160, "start_time": 10000, "text": "指"}, {"attribute": {"event": "speech"}, "end_time": 10200, "start_time": 10160, "text": "重"}, {"attribute": {"event": "speech"}, "end_time": 10480, "start_time": 10200, "text": "重"}, {"attribute": {"event": "speech"}, "end_time": 10680, "start_time": 10520, "text": "敲"}, {"attribute": {"event": "speech"}, "end_time": 10800, "start_time": 10680, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 10960, "start_time": 10800, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 11280, "start_time": 10960, "text": "面"}], "keyword": "敲击"}, {"subtitle_id": 7, "text": "声音低沉却带着怒意", "timestamp": "00:00:11,280 --> 00:00:13,120", "duration": 1.84, "char_count": 9, "start_time_s": 11.28, "end_time_s": 13.12, "words": [{"attribute": {"event": "speech"}, "end_time": 11400, "start_time": 11280, "text": "声"}, {"attribute": {"event": "speech"}, "end_time": 11520, "start_time": 11400, "text": "音"}, {"attribute": {"event": "speech"}, "end_time": 11680, "start_time": 11520, "text": "低"}, {"attribute": {"event": "speech"}, "end_time": 11920, "start_time": 11720, "text": "沉"}, {"attribute": {"event": "speech"}, "end_time": 12200, "start_time": 12040, "text": "却"}, {"attribute": {"event": "speech"}, "end_time": 12400, "start_time": 12200, "text": "带"}, {"attribute": {"event": "speech"}, "end_time": 12520, "start_time": 12400, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 12680, "start_time": 12520, "text": "怒"}, {"attribute": {"event": "speech"}, "end_time": 13120, "start_time": 12680, "text": "意"}], "keyword": "怒意"}, {"subtitle_id": 8, "text": "这不可能！", "timestamp": "00:00:13,120 --> 00:00:14,100", "duration": 0.98, "char_count": 5, "start_time_s": 13.12, "end_time_s": 14.1, "words": [{"attribute": {"event": "speech"}, "end_time": 13320, "start_time": 13120, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 13440, "start_time": 13320, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 13600, "start_time": 13440, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 14100, "start_time": 13600, "text": "能"}], "keyword": "不可能"}], "keywords": ["东宫", "脚步声", "龚子华", "脸色", "地图", "敲击", "怒意", "不可能"]}, {"chapter": 79, "story_board": "他们又喝上了？”  \n\n原来，昨天夜里，王离等人又偷偷喝了回魂酒。这种酒在民间流传已久，据说能让人忘掉烦恼，但对身体伤害极大。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "猛地推开书房门，盯着桌上地图，手指重重敲在上面", "expression": "愤怒"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "郑国渠", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为蓝色，搭配灰色，上有灰色线条装饰，头戴黑色方巾，可配蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "水利部官员", "other": "面容朴实，神情专注", "from_chapter": [0], "action": "", "expression": ""}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "", "expression": ""}, {"name": "冯去疾", "gender": "男", "age": "中年", "clothes": "高领圆领绿色朝服，主色调绿色，搭配白色，有树叶纹，头戴乌纱帽，腰间系绿丝带，配玉坠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "适中", "identity": "秦国大臣", "other": "", "from_chapter": 67, "action": "", "expression": ""}, {"name": "财政部长", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为深灰色，搭配金色，有金色丝线勾勒图案，头戴黑色官帽，可配黑色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色官帽", "figure": "微胖", "identity": "财政部官员", "other": "眉头微皱，忧心忡忡", "from_chapter": [0], "action": "", "expression": ""}, {"name": "税务部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为棕色，搭配白色，衣摆有白色镶边，头戴黑色小冠，可配棕色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色小冠", "figure": "中等身材", "identity": "税务部官员", "other": "神情严肃", "from_chapter": [0], "action": "", "expression": ""}, {"name": "兵部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服铁黑色铠甲，搭配红色，外罩红色披风，头戴黑色头盔，可配红色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色头盔", "figure": "魁梧", "identity": "兵部官员", "other": "面容刚毅", "from_chapter": [0], "action": "", "expression": ""}, {"name": "工部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为土黄色，搭配绿色，袖口绣绿色花纹，头戴黑色斗笠状官帽，可配黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色斗笠状官帽", "figure": "身材壮实", "identity": "工部官员", "other": "皮肤黝黑", "from_chapter": [0], "action": "", "expression": ""}, {"name": "战备部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为藏青色，搭配蓝色，配有蓝色腰带，头戴黑色毡帽，可配藏青色香囊圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色毡帽", "figure": "中等身材", "identity": "战备部官员", "other": "目光沉稳", "from_chapter": [0], "action": "", "expression": ""}], "scene": "书房", "image_prompt": "中景，愤怒地猛地推开书房门，盯着桌上地图并用手指重重敲在上面的青年男子，旁边站着一个少年。青年男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发；少年穿着高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带，圆形衣领，黑色束发，背景是书房, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753194929_20250722_223529.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_2d7c8947-e853-43d9-9bf2-ae7e1128e59e_1.25x_20250722_201939.wav", "audio_duration": 10.661333, "video_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/videos/video_1753196304_20250722_225825.mp4", "story_board_id": 2, "subtitle": [{"subtitle_id": 1, "text": "他们又喝上了。", "timestamp": "00:00:00,240 --> 00:00:01,420", "duration": 1.18, "char_count": 7, "start_time_s": 0.24, "end_time_s": 1.42, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 240, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 440, "text": "又"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 600, "text": "喝"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 800, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 1420, "start_time": 920, "text": "了"}], "keyword": "喝上"}, {"subtitle_id": 2, "text": "原来，昨天夜里，", "timestamp": "00:00:01,480 --> 00:00:02,840", "duration": 1.36, "char_count": 8, "start_time_s": 1.48, "end_time_s": 2.84, "words": [{"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1480, "text": "原"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1640, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2040, "text": "昨"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2240, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2360, "text": "夜"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2560, "text": "里"}], "keyword": "昨天"}, {"subtitle_id": 3, "text": "王礼等人又偷偷喝了回魂酒。", "timestamp": "00:00:02,840 --> 00:00:05,220", "duration": 2.38, "char_count": 13, "start_time_s": 2.84, "end_time_s": 5.22, "words": [{"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2840, "text": "王"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3040, "text": "礼"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3200, "text": "等"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3360, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3560, "text": "又"}, {"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3760, "text": "偷"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3800, "text": "偷"}, {"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4080, "text": "喝"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4240, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4360, "text": "回"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4560, "text": "魂"}, {"attribute": {"event": "speech"}, "end_time": 5220, "start_time": 4760, "text": "酒"}], "keyword": "王礼"}, {"subtitle_id": 4, "text": "这种酒在民间流传已久，", "timestamp": "00:00:05,320 --> 00:00:07,200", "duration": 1.88, "char_count": 11, "start_time_s": 5.32, "end_time_s": 7.2, "words": [{"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5320, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5520, "text": "种"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5640, "text": "酒"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5840, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6000, "text": "民"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6160, "text": "间"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6280, "text": "流"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6440, "text": "传"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6600, "text": "已"}, {"attribute": {"event": "speech"}, "end_time": 7200, "start_time": 6760, "text": "久"}], "keyword": "酒"}, {"subtitle_id": 5, "text": "据说能让人忘掉烦恼，", "timestamp": "00:00:07,200 --> 00:00:08,760", "duration": 1.56, "char_count": 10, "start_time_s": 7.2, "end_time_s": 8.76, "words": [{"attribute": {"event": "speech"}, "end_time": 7360, "start_time": 7200, "text": "据"}, {"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7360, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7440, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 7720, "start_time": 7600, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 7880, "start_time": 7720, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7880, "text": "忘"}, {"attribute": {"event": "speech"}, "end_time": 8160, "start_time": 8040, "text": "掉"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8200, "text": "烦"}, {"attribute": {"event": "speech"}, "end_time": 8760, "start_time": 8400, "text": "恼"}], "keyword": "烦恼"}, {"subtitle_id": 6, "text": "但对身体伤害极大。", "timestamp": "00:00:08,760 --> 00:00:10,340", "duration": 1.58, "char_count": 9, "start_time_s": 8.76, "end_time_s": 10.34, "words": [{"attribute": {"event": "speech"}, "end_time": 8920, "start_time": 8760, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 9040, "start_time": 8920, "text": "对"}, {"attribute": {"event": "speech"}, "end_time": 9200, "start_time": 9040, "text": "身"}, {"attribute": {"event": "speech"}, "end_time": 9320, "start_time": 9200, "text": "体"}, {"attribute": {"event": "speech"}, "end_time": 9520, "start_time": 9360, "text": "伤"}, {"attribute": {"event": "speech"}, "end_time": 9680, "start_time": 9520, "text": "害"}, {"attribute": {"event": "speech"}, "end_time": 9840, "start_time": 9720, "text": "极"}, {"attribute": {"event": "speech"}, "end_time": 10340, "start_time": 9840, "text": "大"}], "keyword": "身体"}], "keywords": ["喝上", "昨天", "王礼", "酒", "烦恼", "身体"]}, {"chapter": 79, "story_board": "公子华知道，明天就要上早朝，可他们居然还敢喝酒。他本想训斥几句，可一想到自己还得陪他们喝完，心里更是火大。最后，他只能以茶代酒，陪着他们喝了几杯。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "以茶代酒陪着众人喝酒", "expression": "愤怒"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "郑国渠", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为蓝色，搭配灰色，上有灰色线条装饰，头戴黑色方巾，可配蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "水利部官员", "other": "面容朴实，神情专注", "from_chapter": [0], "action": "", "expression": ""}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "", "expression": ""}, {"name": "冯去疾", "gender": "男", "age": "中年", "clothes": "高领圆领绿色朝服，主色调绿色，搭配白色，有树叶纹，头戴乌纱帽，腰间系绿丝带，配玉坠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "适中", "identity": "秦国大臣", "other": "", "from_chapter": 67, "action": "", "expression": ""}, {"name": "财政部长", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为深灰色，搭配金色，有金色丝线勾勒图案，头戴黑色官帽，可配黑色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色官帽", "figure": "微胖", "identity": "财政部官员", "other": "眉头微皱，忧心忡忡", "from_chapter": [0], "action": "", "expression": ""}, {"name": "税务部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为棕色，搭配白色，衣摆有白色镶边，头戴黑色小冠，可配棕色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色小冠", "figure": "中等身材", "identity": "税务部官员", "other": "神情严肃", "from_chapter": [0], "action": "", "expression": ""}, {"name": "兵部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服铁黑色铠甲，搭配红色，外罩红色披风，头戴黑色头盔，可配红色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色头盔", "figure": "魁梧", "identity": "兵部官员", "other": "面容刚毅", "from_chapter": [0], "action": "", "expression": ""}, {"name": "工部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为土黄色，搭配绿色，袖口绣绿色花纹，头戴黑色斗笠状官帽，可配黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色斗笠状官帽", "figure": "身材壮实", "identity": "工部官员", "other": "皮肤黝黑", "from_chapter": [0], "action": "", "expression": ""}, {"name": "战备部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为藏青色，搭配蓝色，配有蓝色腰带，头戴黑色毡帽，可配藏青色香囊圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色毡帽", "figure": "中等身材", "identity": "战备部官员", "other": "目光沉稳", "from_chapter": [0], "action": "", "expression": ""}], "scene": "府邸", "image_prompt": "中景，在府邸中，愤怒地以茶代酒陪众人喝酒的青年男子，挺拔站立。该男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753196530_20250722_230211.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_2ae7da92-19ca-42df-ac01-7450289a0882_1.25x_20250722_201930.wav", "audio_duration": 11.888, "video_url": "", "story_board_id": 3, "subtitle": [{"subtitle_id": 1, "text": "龚子华知道明天就要上早朝，", "timestamp": "00:00:00,160 --> 00:00:02,280", "duration": 2.12, "char_count": 13, "start_time_s": 0.16, "end_time_s": 2.28, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 440, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 560, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 680, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1000, "text": "明"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1200, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1360, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1480, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1600, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1720, "text": "早"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 1920, "text": "朝"}], "keyword": "龚子华"}, {"subtitle_id": 2, "text": "可他们居然还敢喝酒。", "timestamp": "00:00:02,280 --> 00:00:03,820", "duration": 1.54, "char_count": 10, "start_time_s": 2.28, "end_time_s": 3.82, "words": [{"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2280, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 2520, "start_time": 2400, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2520, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2640, "text": "居"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2760, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2840, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 3000, "text": "敢"}, {"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3120, "text": "喝"}, {"attribute": {"event": "speech"}, "end_time": 3820, "start_time": 3360, "text": "酒"}], "keyword": "喝酒"}, {"subtitle_id": 3, "text": "他本想训斥几句，", "timestamp": "00:00:03,920 --> 00:00:05,240", "duration": 1.32, "char_count": 8, "start_time_s": 3.92, "end_time_s": 5.24, "words": [{"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3920, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4080, "text": "本"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4200, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4400, "text": "训"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4640, "text": "斥"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4760, "text": "几"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 4920, "text": "句"}], "keyword": "训斥"}, {"subtitle_id": 4, "text": "可一想到自己还得陪他们喝完，", "timestamp": "00:00:05,240 --> 00:00:07,240", "duration": 2.0, "char_count": 14, "start_time_s": 5.24, "end_time_s": 7.24, "words": [{"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5240, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5360, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5480, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5600, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5720, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5880, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6080, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6280, "text": "得"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6360, "text": "陪"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6520, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6640, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6720, "text": "喝"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 6880, "text": "完"}], "keyword": "陪喝"}, {"subtitle_id": 5, "text": "心里更是火大。", "timestamp": "00:00:07,240 --> 00:00:08,500", "duration": 1.26, "char_count": 7, "start_time_s": 7.24, "end_time_s": 8.5, "words": [{"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7240, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7400, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7480, "text": "更"}, {"attribute": {"event": "speech"}, "end_time": 7880, "start_time": 7720, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 8000, "start_time": 7880, "text": "火"}, {"attribute": {"event": "speech"}, "end_time": 8500, "start_time": 8000, "text": "大"}], "keyword": "心里"}, {"subtitle_id": 6, "text": "最后，他只能以茶代酒，", "timestamp": "00:00:08,640 --> 00:00:10,400", "duration": 1.76, "char_count": 11, "start_time_s": 8.64, "end_time_s": 10.4, "words": [{"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8640, "text": "最"}, {"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8800, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 9280, "start_time": 9160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 9440, "start_time": 9280, "text": "只"}, {"attribute": {"event": "speech"}, "end_time": 9560, "start_time": 9440, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 9720, "start_time": 9560, "text": "以"}, {"attribute": {"event": "speech"}, "end_time": 9880, "start_time": 9720, "text": "茶"}, {"attribute": {"event": "speech"}, "end_time": 10040, "start_time": 9880, "text": "代"}, {"attribute": {"event": "speech"}, "end_time": 10400, "start_time": 10040, "text": "酒"}], "keyword": "茶代酒"}, {"subtitle_id": 7, "text": "陪着他们喝了几杯。", "timestamp": "00:00:10,400 --> 00:00:11,820", "duration": 1.42, "char_count": 9, "start_time_s": 10.4, "end_time_s": 11.82, "words": [{"attribute": {"event": "speech"}, "end_time": 10560, "start_time": 10400, "text": "陪"}, {"attribute": {"event": "speech"}, "end_time": 10680, "start_time": 10560, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 10800, "start_time": 10680, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 10920, "start_time": 10800, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 11080, "start_time": 10920, "text": "喝"}, {"attribute": {"event": "speech"}, "end_time": 11200, "start_time": 11080, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 11360, "start_time": 11200, "text": "几"}, {"attribute": {"event": "speech"}, "end_time": 11820, "start_time": 11360, "text": "杯"}], "keyword": "喝几杯"}], "keywords": ["龚子华", "喝酒", "训斥", "陪喝", "心里", "茶代酒", "喝几杯"]}, {"chapter": 79, "story_board": "等众人散去，他独自走进书房，拿起笔开始画府邸图。这座府邸足足占地五千多亩，光是规划就花了两个月。他打算把其中两千亩改成高尔夫球场，用硬木做球，虽然粗糙点，但能玩就行。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "走进书房，拿起笔开始画府邸图，画了一会儿放下笔揉太阳穴", "expression": "略显疲惫"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "郑国渠", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为蓝色，搭配灰色，上有灰色线条装饰，头戴黑色方巾，可配蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "水利部官员", "other": "面容朴实，神情专注", "from_chapter": [0], "action": "", "expression": ""}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "", "expression": ""}, {"name": "冯去疾", "gender": "男", "age": "中年", "clothes": "高领圆领绿色朝服，主色调绿色，搭配白色，有树叶纹，头戴乌纱帽，腰间系绿丝带，配玉坠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "适中", "identity": "秦国大臣", "other": "", "from_chapter": 67, "action": "", "expression": ""}, {"name": "财政部长", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为深灰色，搭配金色，有金色丝线勾勒图案，头戴黑色官帽，可配黑色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色官帽", "figure": "微胖", "identity": "财政部官员", "other": "眉头微皱，忧心忡忡", "from_chapter": [0], "action": "", "expression": ""}, {"name": "税务部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为棕色，搭配白色，衣摆有白色镶边，头戴黑色小冠，可配棕色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色小冠", "figure": "中等身材", "identity": "税务部官员", "other": "神情严肃", "from_chapter": [0], "action": "", "expression": ""}, {"name": "兵部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服铁黑色铠甲，搭配红色，外罩红色披风，头戴黑色头盔，可配红色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色头盔", "figure": "魁梧", "identity": "兵部官员", "other": "面容刚毅", "from_chapter": [0], "action": "", "expression": ""}, {"name": "工部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为土黄色，搭配绿色，袖口绣绿色花纹，头戴黑色斗笠状官帽，可配黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色斗笠状官帽", "figure": "身材壮实", "identity": "工部官员", "other": "皮肤黝黑", "from_chapter": [0], "action": "", "expression": ""}, {"name": "战备部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为藏青色，搭配蓝色，配有蓝色腰带，头戴黑色毡帽，可配藏青色香囊圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色毡帽", "figure": "中等身材", "identity": "战备部官员", "other": "目光沉稳", "from_chapter": [0], "action": "", "expression": ""}], "scene": "书房", "image_prompt": "中景，走进书房拿起笔开始画府邸图，画了一会儿放下笔揉太阳穴、略显疲惫的青年男子。该男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发，整体形象挺拔。背景为书房, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753196782_20250722_230622.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_c213df9b-3da3-4fc4-8b39-5761a73eb7d8_1.25x_20250722_202009.wav", "audio_duration": 14.594667, "video_url": "", "story_board_id": 4, "subtitle": [{"subtitle_id": 1, "text": "等众人散去，", "timestamp": "00:00:00,160 --> 00:00:01,080", "duration": 0.92, "char_count": 6, "start_time_s": 0.16, "end_time_s": 1.08, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "等"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "众"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 440, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 560, "text": "散"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 760, "text": "去"}], "keyword": "众人"}, {"subtitle_id": 2, "text": "他独自走进书房，", "timestamp": "00:00:01,080 --> 00:00:02,360", "duration": 1.28, "char_count": 8, "start_time_s": 1.08, "end_time_s": 2.36, "words": [{"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1080, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1240, "text": "独"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1400, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1560, "text": "走"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1720, "text": "进"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1880, "text": "书"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2040, "text": "房"}], "keyword": "他"}, {"subtitle_id": 3, "text": "拿起笔开始画幅地图。", "timestamp": "00:00:02,360 --> 00:00:04,180", "duration": 1.82, "char_count": 10, "start_time_s": 2.36, "end_time_s": 4.18, "words": [{"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2360, "text": "拿"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2560, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2680, "text": "笔"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 2920, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3120, "text": "始"}, {"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3240, "text": "画"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3440, "text": "幅"}, {"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3560, "text": "地"}, {"attribute": {"event": "speech"}, "end_time": 4180, "start_time": 3680, "text": "图"}], "keyword": "地图"}, {"subtitle_id": 4, "text": "这座府邸足足占地5,000多亩，", "timestamp": "00:00:04,320 --> 00:00:06,440", "duration": 2.12, "char_count": 16, "start_time_s": 4.32, "end_time_s": 6.44, "words": [{"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4320, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4560, "text": "座"}, {"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4680, "text": "府"}, {"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4880, "text": "邸"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 5000, "text": "足"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5040, "text": "足"}, {"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5320, "text": "占"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5440, "text": "地"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5640, "text": "5,000"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6000, "text": "多"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6160, "text": "亩"}], "keyword": "府邸"}, {"subtitle_id": 5, "text": "光是规划就花了两个月。", "timestamp": "00:00:06,440 --> 00:00:08,300", "duration": 1.86, "char_count": 11, "start_time_s": 6.44, "end_time_s": 8.3, "words": [{"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6440, "text": "光"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6640, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6760, "text": "规"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6880, "text": "划"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7160, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7320, "text": "花"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7480, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7600, "text": "两"}, {"attribute": {"event": "speech"}, "end_time": 7840, "start_time": 7760, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 8300, "start_time": 7840, "text": "月"}], "keyword": "规划"}, {"subtitle_id": 6, "text": "他打算把其中2,000亩", "timestamp": "00:00:08,360 --> 00:00:09,920", "duration": 1.56, "char_count": 12, "start_time_s": 8.36, "end_time_s": 9.92, "words": [{"attribute": {"event": "speech"}, "end_time": 8560, "start_time": 8360, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8560, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 8800, "start_time": 8680, "text": "算"}, {"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8840, "text": "把"}, {"attribute": {"event": "speech"}, "end_time": 9120, "start_time": 9000, "text": "其"}, {"attribute": {"event": "speech"}, "end_time": 9240, "start_time": 9120, "text": "中"}, {"attribute": {"event": "speech"}, "end_time": 9600, "start_time": 9240, "text": "2,000"}, {"attribute": {"event": "speech"}, "end_time": 9920, "start_time": 9640, "text": "亩"}], "keyword": "2,00"}, {"subtitle_id": 7, "text": "改成高尔夫球场，", "timestamp": "00:00:09,920 --> 00:00:11,200", "duration": 1.28, "char_count": 8, "start_time_s": 9.92, "end_time_s": 11.2, "words": [{"attribute": {"event": "speech"}, "end_time": 10120, "start_time": 9920, "text": "改"}, {"attribute": {"event": "speech"}, "end_time": 10280, "start_time": 10120, "text": "成"}, {"attribute": {"event": "speech"}, "end_time": 10440, "start_time": 10280, "text": "高"}, {"attribute": {"event": "speech"}, "end_time": 10560, "start_time": 10440, "text": "尔"}, {"attribute": {"event": "speech"}, "end_time": 10680, "start_time": 10560, "text": "夫"}, {"attribute": {"event": "speech"}, "end_time": 10840, "start_time": 10680, "text": "球"}, {"attribute": {"event": "speech"}, "end_time": 11200, "start_time": 10840, "text": "场"}], "keyword": "高尔夫球"}, {"subtitle_id": 8, "text": "用硬木做球，", "timestamp": "00:00:11,200 --> 00:00:12,200", "duration": 1.0, "char_count": 6, "start_time_s": 11.2, "end_time_s": 12.2, "words": [{"attribute": {"event": "speech"}, "end_time": 11400, "start_time": 11200, "text": "用"}, {"attribute": {"event": "speech"}, "end_time": 11520, "start_time": 11400, "text": "硬"}, {"attribute": {"event": "speech"}, "end_time": 11640, "start_time": 11520, "text": "木"}, {"attribute": {"event": "speech"}, "end_time": 11800, "start_time": 11640, "text": "做"}, {"attribute": {"event": "speech"}, "end_time": 12200, "start_time": 11800, "text": "球"}], "keyword": "硬木球"}, {"subtitle_id": 9, "text": "虽然粗糙点，", "timestamp": "00:00:12,200 --> 00:00:13,080", "duration": 0.88, "char_count": 6, "start_time_s": 12.2, "end_time_s": 13.08, "words": [{"attribute": {"event": "speech"}, "end_time": 12320, "start_time": 12200, "text": "虽"}, {"attribute": {"event": "speech"}, "end_time": 12400, "start_time": 12320, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 12600, "start_time": 12440, "text": "粗"}, {"attribute": {"event": "speech"}, "end_time": 12720, "start_time": 12600, "text": "糙"}, {"attribute": {"event": "speech"}, "end_time": 13080, "start_time": 12760, "text": "点"}], "keyword": "粗糙"}, {"subtitle_id": 10, "text": "但能玩就行。", "timestamp": "00:00:13,080 --> 00:00:14,260", "duration": 1.18, "char_count": 6, "start_time_s": 13.08, "end_time_s": 14.26, "words": [{"attribute": {"event": "speech"}, "end_time": 13240, "start_time": 13080, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 13440, "start_time": 13240, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 13560, "start_time": 13440, "text": "玩"}, {"attribute": {"event": "speech"}, "end_time": 13760, "start_time": 13600, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 14260, "start_time": 13760, "text": "行"}], "keyword": "玩"}], "keywords": ["众人", "他", "地图", "府邸", "规划", "2,00", "高尔夫球", "硬木球", "粗糙", "玩"]}, {"chapter": 79, "story_board": "五百亩用来养犀牛、食铁兽和鹿，还有五百亩建温室大棚，剩下的两千亩则作为前后院。他想把房子修成苏州园林的样子，院子里要有假山、亭子、小桥流水，看起来优雅又气派。不过问题来了，泰山石和太湖石虽然好找，但运输麻烦，幸好他的商会代理会帮忙解决。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "坐在书房桌前，拿着笔看着纸上画的府邸图，时而思考时而写写画画", "expression": "专注"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "郑国渠", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为蓝色，搭配灰色，上有灰色线条装饰，头戴黑色方巾，可配蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "水利部官员", "other": "面容朴实，神情专注", "from_chapter": [0], "action": "", "expression": ""}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "", "expression": ""}, {"name": "冯去疾", "gender": "男", "age": "中年", "clothes": "高领圆领绿色朝服，主色调绿色，搭配白色，有树叶纹，头戴乌纱帽，腰间系绿丝带，配玉坠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "适中", "identity": "秦国大臣", "other": "", "from_chapter": 67, "action": "", "expression": ""}, {"name": "财政部长", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为深灰色，搭配金色，有金色丝线勾勒图案，头戴黑色官帽，可配黑色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色官帽", "figure": "微胖", "identity": "财政部官员", "other": "眉头微皱，忧心忡忡", "from_chapter": [0], "action": "", "expression": ""}, {"name": "税务部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为棕色，搭配白色，衣摆有白色镶边，头戴黑色小冠，可配棕色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色小冠", "figure": "中等身材", "identity": "税务部官员", "other": "神情严肃", "from_chapter": [0], "action": "", "expression": ""}, {"name": "兵部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服铁黑色铠甲，搭配红色，外罩红色披风，头戴黑色头盔，可配红色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色头盔", "figure": "魁梧", "identity": "兵部官员", "other": "面容刚毅", "from_chapter": [0], "action": "", "expression": ""}, {"name": "工部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为土黄色，搭配绿色，袖口绣绿色花纹，头戴黑色斗笠状官帽，可配黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色斗笠状官帽", "figure": "身材壮实", "identity": "工部官员", "other": "皮肤黝黑", "from_chapter": [0], "action": "", "expression": ""}, {"name": "战备部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为藏青色，搭配蓝色，配有蓝色腰带，头戴黑色毡帽，可配藏青色香囊圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色毡帽", "figure": "中等身材", "identity": "战备部官员", "other": "目光沉稳", "from_chapter": [0], "action": "", "expression": ""}], "scene": "书房", "image_prompt": "中景，一个专注的青年男子坐在书房桌前，拿着笔看着纸上画的府邸图，时而思考时而写写画画。男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发，整体形象挺拔，背景是书房, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753197230_20250722_231352.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_2203849a-5d13-4168-b197-6e6bd73d0072_1.25x_20250722_202054.wav", "audio_duration": 18.664, "video_url": "", "story_board_id": 5, "subtitle": [{"subtitle_id": 1, "text": "500亩用来养犀牛、石铁兽和鹿，", "timestamp": "00:00:00,200 --> 00:00:02,240", "duration": 2.04, "char_count": 16, "start_time_s": 0.2, "end_time_s": 2.24, "words": [{"attribute": {"event": "speech"}, "end_time": 400, "start_time": 200, "text": "500"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 440, "text": "亩"}, {"attribute": {"event": "speech"}, "end_time": 680, "start_time": 520, "text": "用"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 680, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 800, "text": "养"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 960, "text": "犀"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1120, "text": "牛"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1280, "text": "石"}, {"attribute": {"event": "speech"}, "end_time": 1560, "start_time": 1400, "text": "铁"}, {"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1600, "text": "兽"}, {"attribute": {"event": "speech"}, "end_time": 1880, "start_time": 1720, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 1880, "text": "鹿"}], "keyword": "犀牛"}, {"subtitle_id": 2, "text": "还有500亩建温石大棚，", "timestamp": "00:00:02,240 --> 00:00:04,000", "duration": 1.76, "char_count": 12, "start_time_s": 2.24, "end_time_s": 4.0, "words": [{"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2240, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 2520, "start_time": 2400, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2520, "text": "500"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2840, "text": "亩"}, {"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 3000, "text": "建"}, {"attribute": {"event": "speech"}, "end_time": 3280, "start_time": 3160, "text": "温"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3320, "text": "石"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3400, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 4000, "start_time": 3600, "text": "棚"}], "keyword": "温石大棚"}, {"subtitle_id": 3, "text": "剩下的2,000亩则作为前后院。", "timestamp": "00:00:04,000 --> 00:00:06,260", "duration": 2.26, "char_count": 16, "start_time_s": 4.0, "end_time_s": 6.26, "words": [{"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4000, "text": "剩"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4160, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4280, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4400, "text": "2,000"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4760, "text": "亩"}, {"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5040, "text": "则"}, {"attribute": {"event": "speech"}, "end_time": 5320, "start_time": 5200, "text": "作"}, {"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5320, "text": "为"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5440, "text": "前"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5640, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 6260, "start_time": 5800, "text": "院"}], "keyword": "前后院"}, {"subtitle_id": 4, "text": "他想把房子修成苏州园林的样子，", "timestamp": "00:00:06,320 --> 00:00:08,720", "duration": 2.4, "char_count": 15, "start_time_s": 6.32, "end_time_s": 8.72, "words": [{"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6320, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6520, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6640, "text": "把"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6720, "text": "房"}, {"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6920, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 7360, "start_time": 7200, "text": "修"}, {"attribute": {"event": "speech"}, "end_time": 7520, "start_time": 7360, "text": "成"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7520, "text": "苏"}, {"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7680, "text": "州"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7800, "text": "园"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 7960, "text": "林"}, {"attribute": {"event": "speech"}, "end_time": 8200, "start_time": 8120, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8200, "text": "样"}, {"attribute": {"event": "speech"}, "end_time": 8720, "start_time": 8360, "text": "子"}], "keyword": "苏州园林"}, {"subtitle_id": 5, "text": "院子里要有假山、亭子、小桥流水，", "timestamp": "00:00:08,720 --> 00:00:10,920", "duration": 2.2, "char_count": 16, "start_time_s": 8.72, "end_time_s": 10.92, "words": [{"attribute": {"event": "speech"}, "end_time": 8880, "start_time": 8720, "text": "院"}, {"attribute": {"event": "speech"}, "end_time": 9040, "start_time": 8880, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 9120, "start_time": 9040, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 9280, "start_time": 9120, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 9400, "start_time": 9280, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 9560, "start_time": 9400, "text": "假"}, {"attribute": {"event": "speech"}, "end_time": 9720, "start_time": 9600, "text": "山"}, {"attribute": {"event": "speech"}, "end_time": 9920, "start_time": 9720, "text": "亭"}, {"attribute": {"event": "speech"}, "end_time": 10040, "start_time": 9920, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 10240, "start_time": 10040, "text": "小"}, {"attribute": {"event": "speech"}, "end_time": 10400, "start_time": 10240, "text": "桥"}, {"attribute": {"event": "speech"}, "end_time": 10560, "start_time": 10400, "text": "流"}, {"attribute": {"event": "speech"}, "end_time": 10920, "start_time": 10560, "text": "水"}], "keyword": "假山"}, {"subtitle_id": 6, "text": "看起来优雅又气派。", "timestamp": "00:00:10,920 --> 00:00:12,460", "duration": 1.54, "char_count": 9, "start_time_s": 10.92, "end_time_s": 12.46, "words": [{"attribute": {"event": "speech"}, "end_time": 11120, "start_time": 10920, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 11240, "start_time": 11120, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 11320, "start_time": 11240, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 11480, "start_time": 11320, "text": "优"}, {"attribute": {"event": "speech"}, "end_time": 11640, "start_time": 11480, "text": "雅"}, {"attribute": {"event": "speech"}, "end_time": 11800, "start_time": 11640, "text": "又"}, {"attribute": {"event": "speech"}, "end_time": 12000, "start_time": 11800, "text": "气"}, {"attribute": {"event": "speech"}, "end_time": 12460, "start_time": 12000, "text": "派"}], "keyword": "优雅气派"}, {"subtitle_id": 7, "text": "不过问题来了，", "timestamp": "00:00:12,560 --> 00:00:13,440", "duration": 0.88, "char_count": 7, "start_time_s": 12.56, "end_time_s": 13.44, "words": [{"attribute": {"event": "speech"}, "end_time": 12720, "start_time": 12560, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 12800, "start_time": 12720, "text": "过"}, {"attribute": {"event": "speech"}, "end_time": 12920, "start_time": 12800, "text": "问"}, {"attribute": {"event": "speech"}, "end_time": 13040, "start_time": 12920, "text": "题"}, {"attribute": {"event": "speech"}, "end_time": 13200, "start_time": 13040, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 13440, "start_time": 13200, "text": "了"}], "keyword": "问题"}, {"subtitle_id": 8, "text": "泰山石和太湖石虽然好找，", "timestamp": "00:00:13,440 --> 00:00:15,320", "duration": 1.88, "char_count": 12, "start_time_s": 13.44, "end_time_s": 15.32, "words": [{"attribute": {"event": "speech"}, "end_time": 13640, "start_time": 13440, "text": "泰"}, {"attribute": {"event": "speech"}, "end_time": 13800, "start_time": 13680, "text": "山"}, {"attribute": {"event": "speech"}, "end_time": 13960, "start_time": 13800, "text": "石"}, {"attribute": {"event": "speech"}, "end_time": 14080, "start_time": 13960, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 14280, "start_time": 14080, "text": "太"}, {"attribute": {"event": "speech"}, "end_time": 14400, "start_time": 14280, "text": "湖"}, {"attribute": {"event": "speech"}, "end_time": 14560, "start_time": 14400, "text": "石"}, {"attribute": {"event": "speech"}, "end_time": 14680, "start_time": 14560, "text": "虽"}, {"attribute": {"event": "speech"}, "end_time": 14760, "start_time": 14680, "text": "然"}, {"attribute": {"event": "speech"}, "end_time": 14960, "start_time": 14760, "text": "好"}, {"attribute": {"event": "speech"}, "end_time": 15320, "start_time": 15000, "text": "找"}], "keyword": "泰山石"}, {"subtitle_id": 9, "text": "但运输麻烦，", "timestamp": "00:00:15,320 --> 00:00:16,280", "duration": 0.96, "char_count": 6, "start_time_s": 15.32, "end_time_s": 16.28, "words": [{"attribute": {"event": "speech"}, "end_time": 15480, "start_time": 15320, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 15640, "start_time": 15480, "text": "运"}, {"attribute": {"event": "speech"}, "end_time": 15760, "start_time": 15640, "text": "输"}, {"attribute": {"event": "speech"}, "end_time": 15960, "start_time": 15800, "text": "麻"}, {"attribute": {"event": "speech"}, "end_time": 16280, "start_time": 15960, "text": "烦"}], "keyword": "运输"}, {"subtitle_id": 10, "text": "幸好他的商会代理会帮忙解决。", "timestamp": "00:00:16,280 --> 00:00:18,580", "duration": 2.3, "char_count": 14, "start_time_s": 16.28, "end_time_s": 18.58, "words": [{"attribute": {"event": "speech"}, "end_time": 16440, "start_time": 16280, "text": "幸"}, {"attribute": {"event": "speech"}, "end_time": 16600, "start_time": 16440, "text": "好"}, {"attribute": {"event": "speech"}, "end_time": 16720, "start_time": 16600, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 16800, "start_time": 16720, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 16920, "start_time": 16800, "text": "商"}, {"attribute": {"event": "speech"}, "end_time": 17080, "start_time": 16960, "text": "会"}, {"attribute": {"event": "speech"}, "end_time": 17240, "start_time": 17080, "text": "代"}, {"attribute": {"event": "speech"}, "end_time": 17400, "start_time": 17240, "text": "理"}, {"attribute": {"event": "speech"}, "end_time": 17640, "start_time": 17480, "text": "会"}, {"attribute": {"event": "speech"}, "end_time": 17800, "start_time": 17640, "text": "帮"}, {"attribute": {"event": "speech"}, "end_time": 17920, "start_time": 17800, "text": "忙"}, {"attribute": {"event": "speech"}, "end_time": 18080, "start_time": 17960, "text": "解"}, {"attribute": {"event": "speech"}, "end_time": 18580, "start_time": 18080, "text": "决"}], "keyword": "商会代理"}], "keywords": ["犀牛", "温石大棚", "前后院", "苏州园林", "假山", "优雅气派", "问题", "泰山石", "运输", "商会代理"]}, {"chapter": 79, "story_board": "画了一会儿，他放下笔，揉了揉太阳穴。昨晚的酒劲还没完全过去，加上今天还要上朝，他决定早点休息。第二天早上，天还没亮，他就被一阵轻微的响动惊醒。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "放下笔，揉了揉太阳穴；被轻微响动惊醒，睁开眼", "expression": "疲惫、惊醒"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "郑国渠", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为蓝色，搭配灰色，上有灰色线条装饰，头戴黑色方巾，可配蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "水利部官员", "other": "面容朴实，神情专注", "from_chapter": [0], "action": "", "expression": ""}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "", "expression": ""}, {"name": "冯去疾", "gender": "男", "age": "中年", "clothes": "高领圆领绿色朝服，主色调绿色，搭配白色，有树叶纹，头戴乌纱帽，腰间系绿丝带，配玉坠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "适中", "identity": "秦国大臣", "other": "", "from_chapter": 67, "action": "", "expression": ""}, {"name": "财政部长", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为深灰色，搭配金色，有金色丝线勾勒图案，头戴黑色官帽，可配黑色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色官帽", "figure": "微胖", "identity": "财政部官员", "other": "眉头微皱，忧心忡忡", "from_chapter": [0], "action": "", "expression": ""}, {"name": "税务部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为棕色，搭配白色，衣摆有白色镶边，头戴黑色小冠，可配棕色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色小冠", "figure": "中等身材", "identity": "税务部官员", "other": "神情严肃", "from_chapter": [0], "action": "", "expression": ""}, {"name": "兵部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服铁黑色铠甲，搭配红色，外罩红色披风，头戴黑色头盔，可配红色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色头盔", "figure": "魁梧", "identity": "兵部官员", "other": "面容刚毅", "from_chapter": [0], "action": "", "expression": ""}, {"name": "工部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为土黄色，搭配绿色，袖口绣绿色花纹，头戴黑色斗笠状官帽，可配黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色斗笠状官帽", "figure": "身材壮实", "identity": "工部官员", "other": "皮肤黝黑", "from_chapter": [0], "action": "", "expression": ""}, {"name": "战备部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为藏青色，搭配蓝色，配有蓝色腰带，头戴黑色毡帽，可配藏青色香囊圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色毡帽", "figure": "中等身材", "identity": "战备部官员", "other": "目光沉稳", "from_chapter": [0], "action": "", "expression": ""}], "scene": "书房", "image_prompt": "中景，在书房里，一位青年男子放下笔，揉了揉太阳穴，随后被轻微响动惊醒，疲惫地睁开双眼。他穿着高领圆领的汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束着黑色腰带并佩戴玉佩，圆形衣领，黑色束发，身姿挺拔, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753197661_20250722_232102.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_638564ab-6357-451f-99ab-3fa4c76f2dec_1.25x_20250722_202041.wav", "audio_duration": 11.925333, "video_url": "", "story_board_id": 6, "subtitle": [{"subtitle_id": 1, "text": "画了一会，", "timestamp": "00:00:00,200 --> 00:00:00,920", "duration": 0.72, "char_count": 5, "start_time_s": 0.2, "end_time_s": 0.92, "words": [{"attribute": {"event": "speech"}, "end_time": 400, "start_time": 200, "text": "画"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 400, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 480, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 600, "text": "会"}], "keyword": "画"}, {"subtitle_id": 2, "text": "他放下笔，", "timestamp": "00:00:00,920 --> 00:00:01,840", "duration": 0.92, "char_count": 5, "start_time_s": 0.92, "end_time_s": 1.84, "words": [{"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 920, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 1120, "text": "放"}, {"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1280, "text": "下"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1440, "text": "笔"}], "keyword": "笔"}, {"subtitle_id": 3, "text": "揉了揉太阳穴。", "timestamp": "00:00:01,840 --> 00:00:03,060", "duration": 1.22, "char_count": 7, "start_time_s": 1.84, "end_time_s": 3.06, "words": [{"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1840, "text": "揉"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 2000, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2120, "text": "揉"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2280, "text": "太"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2440, "text": "阳"}, {"attribute": {"event": "speech"}, "end_time": 3060, "start_time": 2560, "text": "穴"}], "keyword": "太阳穴"}, {"subtitle_id": 4, "text": "昨晚的酒劲还没完全过去，", "timestamp": "00:00:03,200 --> 00:00:05,000", "duration": 1.8, "char_count": 12, "start_time_s": 3.2, "end_time_s": 5.0, "words": [{"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3200, "text": "昨"}, {"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3400, "text": "晚"}, {"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3520, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3600, "text": "酒"}, {"attribute": {"event": "speech"}, "end_time": 3880, "start_time": 3760, "text": "劲"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3920, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4040, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4160, "text": "完"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4320, "text": "全"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4480, "text": "过"}, {"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4640, "text": "去"}], "keyword": "酒劲"}, {"subtitle_id": 5, "text": "加上今天还要上潮，", "timestamp": "00:00:05,000 --> 00:00:06,280", "duration": 1.28, "char_count": 9, "start_time_s": 5.0, "end_time_s": 6.28, "words": [{"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5000, "text": "加"}, {"attribute": {"event": "speech"}, "end_time": 5240, "start_time": 5160, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5280, "text": "今"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5400, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5520, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5680, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5800, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 5960, "text": "潮"}], "keyword": "上潮"}, {"subtitle_id": 6, "text": "他决定早点休息。", "timestamp": "00:00:06,280 --> 00:00:07,700", "duration": 1.42, "char_count": 8, "start_time_s": 6.28, "end_time_s": 7.7, "words": [{"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6280, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6440, "text": "决"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6560, "text": "定"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6720, "text": "早"}, {"attribute": {"event": "speech"}, "end_time": 7040, "start_time": 6880, "text": "点"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7040, "text": "休"}, {"attribute": {"event": "speech"}, "end_time": 7700, "start_time": 7240, "text": "息"}], "keyword": "休息"}, {"subtitle_id": 7, "text": "第二天早上，", "timestamp": "00:00:07,760 --> 00:00:08,680", "duration": 0.92, "char_count": 6, "start_time_s": 7.76, "end_time_s": 8.68, "words": [{"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7760, "text": "第"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7920, "text": "二"}, {"attribute": {"event": "speech"}, "end_time": 8160, "start_time": 8040, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 8360, "start_time": 8160, "text": "早"}, {"attribute": {"event": "speech"}, "end_time": 8680, "start_time": 8360, "text": "上"}], "keyword": "第二天"}, {"subtitle_id": 8, "text": "天还没亮，", "timestamp": "00:00:08,680 --> 00:00:09,400", "duration": 0.72, "char_count": 5, "start_time_s": 8.68, "end_time_s": 9.4, "words": [{"attribute": {"event": "speech"}, "end_time": 8840, "start_time": 8680, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8840, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 9120, "start_time": 8960, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 9400, "start_time": 9120, "text": "亮"}], "keyword": "天"}, {"subtitle_id": 9, "text": "他就被一阵轻微的响动惊醒。", "timestamp": "00:00:09,400 --> 00:00:11,620", "duration": 2.22, "char_count": 13, "start_time_s": 9.4, "end_time_s": 11.62, "words": [{"attribute": {"event": "speech"}, "end_time": 9560, "start_time": 9400, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 9680, "start_time": 9560, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 9800, "start_time": 9680, "text": "被"}, {"attribute": {"event": "speech"}, "end_time": 9960, "start_time": 9800, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 10120, "start_time": 9960, "text": "阵"}, {"attribute": {"event": "speech"}, "end_time": 10320, "start_time": 10160, "text": "轻"}, {"attribute": {"event": "speech"}, "end_time": 10480, "start_time": 10320, "text": "微"}, {"attribute": {"event": "speech"}, "end_time": 10640, "start_time": 10480, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 10800, "start_time": 10640, "text": "响"}, {"attribute": {"event": "speech"}, "end_time": 10960, "start_time": 10800, "text": "动"}, {"attribute": {"event": "speech"}, "end_time": 11160, "start_time": 11000, "text": "惊"}, {"attribute": {"event": "speech"}, "end_time": 11620, "start_time": 11160, "text": "醒"}], "keyword": "响动"}], "keywords": ["画", "笔", "太阳穴", "酒劲", "上潮", "休息", "第二天", "天", "响动"]}, {"chapter": 79, "story_board": "他睁开眼，看着窗外微微泛白的天色，脑子里全是各种事务。监国的日子太难了，每天都要处理无数事情，协调各方关系，稍有不慎就可能出乱子。他迅速起身，穿好衣服，坐在铜镜前看着自己略显疲惫的脸。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "睁开眼，看着窗外天色，迅速起身穿好衣服，坐在铜镜前看着自己的脸", "expression": "疲惫、坚定"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "郑国渠", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为蓝色，搭配灰色，上有灰色线条装饰，头戴黑色方巾，可配蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "水利部官员", "other": "面容朴实，神情专注", "from_chapter": [0], "action": "", "expression": ""}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "", "expression": ""}, {"name": "冯去疾", "gender": "男", "age": "中年", "clothes": "高领圆领绿色朝服，主色调绿色，搭配白色，有树叶纹，头戴乌纱帽，腰间系绿丝带，配玉坠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "适中", "identity": "秦国大臣", "other": "", "from_chapter": 67, "action": "", "expression": ""}, {"name": "财政部长", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为深灰色，搭配金色，有金色丝线勾勒图案，头戴黑色官帽，可配黑色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色官帽", "figure": "微胖", "identity": "财政部官员", "other": "眉头微皱，忧心忡忡", "from_chapter": [0], "action": "", "expression": ""}, {"name": "税务部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为棕色，搭配白色，衣摆有白色镶边，头戴黑色小冠，可配棕色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色小冠", "figure": "中等身材", "identity": "税务部官员", "other": "神情严肃", "from_chapter": [0], "action": "", "expression": ""}, {"name": "兵部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服铁黑色铠甲，搭配红色，外罩红色披风，头戴黑色头盔，可配红色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色头盔", "figure": "魁梧", "identity": "兵部官员", "other": "面容刚毅", "from_chapter": [0], "action": "", "expression": ""}, {"name": "工部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为土黄色，搭配绿色，袖口绣绿色花纹，头戴黑色斗笠状官帽，可配黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色斗笠状官帽", "figure": "身材壮实", "identity": "工部官员", "other": "皮肤黝黑", "from_chapter": [0], "action": "", "expression": ""}, {"name": "战备部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为藏青色，搭配蓝色，配有蓝色腰带，头戴黑色毡帽，可配藏青色香囊圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色毡帽", "figure": "中等身材", "identity": "战备部官员", "other": "目光沉稳", "from_chapter": [0], "action": "", "expression": ""}], "scene": "府邸", "image_prompt": "中景，一位神情疲惫却坚定的青年男子迅速起身穿好衣服后，坐在铜镜前看着自己的脸，此前他睁开眼看着窗外天色。男子身处府邸之中。男子身着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，有圆形衣领，黑色束发，身姿挺拔, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753198064_20250722_232745.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_a0aac1eb-e17d-4af5-9e3d-7015880772af_1.25x_20250722_202031.wav", "audio_duration": 15.784, "video_url": "", "story_board_id": 7, "subtitle": [{"subtitle_id": 1, "text": "他睁开眼，", "timestamp": "00:00:00,160 --> 00:00:00,840", "duration": 0.68, "char_count": 5, "start_time_s": 0.16, "end_time_s": 0.84, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "睁"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 400, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 520, "text": "眼"}], "keyword": "他"}, {"subtitle_id": 2, "text": "看着窗外微微泛白的天色，", "timestamp": "00:00:00,840 --> 00:00:02,800", "duration": 1.96, "char_count": 12, "start_time_s": 0.84, "end_time_s": 2.8, "words": [{"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 840, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 1040, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1160, "text": "窗"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1320, "text": "外"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1480, "text": "微"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1520, "text": "微"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1760, "text": "泛"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 1960, "text": "白"}, {"attribute": {"event": "speech"}, "end_time": 2240, "start_time": 2120, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2240, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2400, "text": "色"}], "keyword": "窗外"}, {"subtitle_id": 3, "text": "脑子里全是各种事物。", "timestamp": "00:00:02,800 --> 00:00:04,580", "duration": 1.78, "char_count": 10, "start_time_s": 2.8, "end_time_s": 4.58, "words": [{"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2800, "text": "脑"}, {"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2960, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3080, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3240, "text": "全"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3480, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3640, "text": "各"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3840, "text": "种"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 3960, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 4580, "start_time": 4120, "text": "物"}], "keyword": "事物"}, {"subtitle_id": 4, "text": "建国的日子太难了，", "timestamp": "00:00:04,720 --> 00:00:06,120", "duration": 1.4, "char_count": 9, "start_time_s": 4.72, "end_time_s": 6.12, "words": [{"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4720, "text": "建"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4920, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5040, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 5320, "start_time": 5160, "text": "日"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5320, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5400, "text": "太"}, {"attribute": {"event": "speech"}, "end_time": 5760, "start_time": 5640, "text": "难"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5760, "text": "了"}], "keyword": "建国"}, {"subtitle_id": 5, "text": "每天都要处理无数事情，", "timestamp": "00:00:06,120 --> 00:00:07,760", "duration": 1.64, "char_count": 11, "start_time_s": 6.12, "end_time_s": 7.76, "words": [{"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6120, "text": "每"}, {"attribute": {"event": "speech"}, "end_time": 6480, "start_time": 6360, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6480, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6600, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6720, "text": "处"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6880, "text": "理"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 7000, "text": "无"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7160, "text": "数"}, {"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7320, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7440, "text": "情"}], "keyword": "事情"}, {"subtitle_id": 6, "text": "协调各方关系，", "timestamp": "00:00:07,760 --> 00:00:08,920", "duration": 1.16, "char_count": 7, "start_time_s": 7.76, "end_time_s": 8.92, "words": [{"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7760, "text": "协"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 7960, "text": "调"}, {"attribute": {"event": "speech"}, "end_time": 8280, "start_time": 8160, "text": "各"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8280, "text": "方"}, {"attribute": {"event": "speech"}, "end_time": 8600, "start_time": 8400, "text": "关"}, {"attribute": {"event": "speech"}, "end_time": 8920, "start_time": 8600, "text": "系"}], "keyword": "协调"}, {"subtitle_id": 7, "text": "稍有不慎就可能出乱子。", "timestamp": "00:00:08,920 --> 00:00:10,820", "duration": 1.9, "char_count": 11, "start_time_s": 8.92, "end_time_s": 10.82, "words": [{"attribute": {"event": "speech"}, "end_time": 9080, "start_time": 8920, "text": "稍"}, {"attribute": {"event": "speech"}, "end_time": 9200, "start_time": 9080, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 9360, "start_time": 9200, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 9520, "start_time": 9360, "text": "慎"}, {"attribute": {"event": "speech"}, "end_time": 9760, "start_time": 9600, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 9920, "start_time": 9760, "text": "可"}, {"attribute": {"event": "speech"}, "end_time": 10040, "start_time": 9920, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 10200, "start_time": 10040, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 10360, "start_time": 10200, "text": "乱"}, {"attribute": {"event": "speech"}, "end_time": 10820, "start_time": 10360, "text": "子"}], "keyword": "乱子"}, {"subtitle_id": 8, "text": "他迅速起身，", "timestamp": "00:00:10,920 --> 00:00:11,920", "duration": 1.0, "char_count": 6, "start_time_s": 10.92, "end_time_s": 11.92, "words": [{"attribute": {"event": "speech"}, "end_time": 11080, "start_time": 10920, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 11240, "start_time": 11120, "text": "迅"}, {"attribute": {"event": "speech"}, "end_time": 11400, "start_time": 11280, "text": "速"}, {"attribute": {"event": "speech"}, "end_time": 11600, "start_time": 11400, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 11920, "start_time": 11600, "text": "身"}], "keyword": "起身"}, {"subtitle_id": 9, "text": "穿好衣服，", "timestamp": "00:00:11,920 --> 00:00:12,720", "duration": 0.8, "char_count": 5, "start_time_s": 11.92, "end_time_s": 12.72, "words": [{"attribute": {"event": "speech"}, "end_time": 12080, "start_time": 11920, "text": "穿"}, {"attribute": {"event": "speech"}, "end_time": 12280, "start_time": 12120, "text": "好"}, {"attribute": {"event": "speech"}, "end_time": 12440, "start_time": 12280, "text": "衣"}, {"attribute": {"event": "speech"}, "end_time": 12720, "start_time": 12440, "text": "服"}], "keyword": "衣服"}, {"subtitle_id": 10, "text": "坐在铜镜前，", "timestamp": "00:00:12,720 --> 00:00:13,600", "duration": 0.88, "char_count": 6, "start_time_s": 12.72, "end_time_s": 13.6, "words": [{"attribute": {"event": "speech"}, "end_time": 12880, "start_time": 12720, "text": "坐"}, {"attribute": {"event": "speech"}, "end_time": 13000, "start_time": 12880, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 13120, "start_time": 13000, "text": "铜"}, {"attribute": {"event": "speech"}, "end_time": 13320, "start_time": 13160, "text": "镜"}, {"attribute": {"event": "speech"}, "end_time": 13600, "start_time": 13320, "text": "前"}], "keyword": "铜镜"}, {"subtitle_id": 11, "text": "看着自己略显疲惫的脸。", "timestamp": "00:00:13,600 --> 00:00:15,460", "duration": 1.86, "char_count": 11, "start_time_s": 13.6, "end_time_s": 15.46, "words": [{"attribute": {"event": "speech"}, "end_time": 13800, "start_time": 13600, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 13880, "start_time": 13800, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 14040, "start_time": 13880, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 14120, "start_time": 14040, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 14320, "start_time": 14160, "text": "略"}, {"attribute": {"event": "speech"}, "end_time": 14480, "start_time": 14320, "text": "显"}, {"attribute": {"event": "speech"}, "end_time": 14680, "start_time": 14480, "text": "疲"}, {"attribute": {"event": "speech"}, "end_time": 14880, "start_time": 14720, "text": "惫"}, {"attribute": {"event": "speech"}, "end_time": 15000, "start_time": 14880, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 15460, "start_time": 15000, "text": "脸"}], "keyword": "脸"}], "keywords": ["他", "窗外", "事物", "建国", "事情", "协调", "乱子", "起身", "衣服", "铜镜", "脸"]}, {"chapter": 79, "story_board": "眼神里却透着坚定，他知道，今天的朝会不会轻松。早餐过后，他坐上轿子，前往皇宫。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "坐在铜镜前看着自己略显疲惫的脸后起身，早餐过后坐上轿子", "expression": "坚定"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "郑国渠", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为蓝色，搭配灰色，上有灰色线条装饰，头戴黑色方巾，可配蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "水利部官员", "other": "面容朴实，神情专注", "from_chapter": [0], "action": "", "expression": ""}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "", "expression": ""}, {"name": "冯去疾", "gender": "男", "age": "中年", "clothes": "高领圆领绿色朝服，主色调绿色，搭配白色，有树叶纹，头戴乌纱帽，腰间系绿丝带，配玉坠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "适中", "identity": "秦国大臣", "other": "", "from_chapter": 67, "action": "", "expression": ""}, {"name": "财政部长", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为深灰色，搭配金色，有金色丝线勾勒图案，头戴黑色官帽，可配黑色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色官帽", "figure": "微胖", "identity": "财政部官员", "other": "眉头微皱，忧心忡忡", "from_chapter": [0], "action": "", "expression": ""}, {"name": "税务部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为棕色，搭配白色，衣摆有白色镶边，头戴黑色小冠，可配棕色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色小冠", "figure": "中等身材", "identity": "税务部官员", "other": "神情严肃", "from_chapter": [0], "action": "", "expression": ""}, {"name": "兵部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服铁黑色铠甲，搭配红色，外罩红色披风，头戴黑色头盔，可配红色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色头盔", "figure": "魁梧", "identity": "兵部官员", "other": "面容刚毅", "from_chapter": [0], "action": "", "expression": ""}, {"name": "工部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为土黄色，搭配绿色，袖口绣绿色花纹，头戴黑色斗笠状官帽，可配黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色斗笠状官帽", "figure": "身材壮实", "identity": "工部官员", "other": "皮肤黝黑", "from_chapter": [0], "action": "", "expression": ""}, {"name": "战备部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为藏青色，搭配蓝色，配有蓝色腰带，头戴黑色毡帽，可配藏青色香囊圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色毡帽", "figure": "中等身材", "identity": "战备部官员", "other": "目光沉稳", "from_chapter": [0], "action": "", "expression": ""}], "scene": "府邸", "image_prompt": "中景，在府邸内，先是坐在铜镜前看着自己略显疲惫的脸后起身，早餐过后坚定地坐上轿子的青年男子。该男子挺拔，敢言善辩，穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，有圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753198464_20250722_233425.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_99eea860-acc0-4d22-aa8f-452f8d2a1279_1.25x_20250722_202016.wav", "audio_duration": 7.205333, "video_url": "", "story_board_id": 8, "subtitle": [{"subtitle_id": 1, "text": "眼神里却透着坚定，", "timestamp": "00:00:00,200 --> 00:00:01,640", "duration": 1.44, "char_count": 9, "start_time_s": 0.2, "end_time_s": 1.64, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 200, "text": "眼"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 360, "text": "神"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 480, "text": "里"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 680, "text": "却"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 840, "text": "透"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 1000, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 1120, "text": "坚"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1280, "text": "定"}], "keyword": "眼神"}, {"subtitle_id": 2, "text": "他知道今天的朝会不会轻松。", "timestamp": "00:00:01,640 --> 00:00:04,020", "duration": 2.38, "char_count": 13, "start_time_s": 1.64, "end_time_s": 4.02, "words": [{"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1640, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1800, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1960, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2240, "text": "今"}, {"attribute": {"event": "speech"}, "end_time": 2520, "start_time": 2400, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2520, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2640, "text": "朝"}, {"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2920, "text": "会"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3120, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3240, "text": "会"}, {"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3400, "text": "轻"}, {"attribute": {"event": "speech"}, "end_time": 4020, "start_time": 3560, "text": "松"}], "keyword": "朝会"}, {"subtitle_id": 3, "text": "早餐过后，", "timestamp": "00:00:04,160 --> 00:00:05,000", "duration": 0.84, "char_count": 5, "start_time_s": 4.16, "end_time_s": 5.0, "words": [{"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4160, "text": "早"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4400, "text": "餐"}, {"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4560, "text": "过"}, {"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4680, "text": "后"}], "keyword": "早餐"}, {"subtitle_id": 4, "text": "他坐上轿子前往皇宫。", "timestamp": "00:00:05,000 --> 00:00:07,100", "duration": 2.1, "char_count": 10, "start_time_s": 5.0, "end_time_s": 7.1, "words": [{"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5000, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 5320, "start_time": 5160, "text": "坐"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5320, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5480, "text": "轿"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5680, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 6040, "text": "前"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6240, "text": "往"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6400, "text": "皇"}, {"attribute": {"event": "speech"}, "end_time": 7100, "start_time": 6640, "text": "宫"}], "keyword": "轿子"}], "keywords": ["眼神", "朝会", "早餐", "轿子"]}, {"chapter": 79, "story_board": "路上，他一边整理思绪，一边思考今天要面对的问题：水利部的郑国渠要修黄河堤坝，农业部的乌倮氏想推广新农具，教育部的冯去疾想扩大学堂，财政部的官员担心钱不够用，税务部想调整税收政策，兵部要扩充军备……  \n\n朝堂上，大臣们依次汇报各自部门的计划。郑国渠说，明年要在黄河流域修水利工程，疏通河道，防止洪水。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "坐在轿子里，一边用手整理衣服、理顺思绪，一边微微皱眉思考", "expression": "严肃"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "郑国渠", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为蓝色，搭配灰色，上有灰色线条装饰，头戴黑色方巾，可配蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "水利部官员", "other": "面容朴实，神情专注", "from_chapter": [0], "action": "站在朝堂上，身体微微前倾，双手摊开说话", "expression": "认真"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "", "expression": ""}, {"name": "冯去疾", "gender": "男", "age": "中年", "clothes": "高领圆领绿色朝服，主色调绿色，搭配白色，有树叶纹，头戴乌纱帽，腰间系绿丝带，配玉坠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "适中", "identity": "秦国大臣", "other": "", "from_chapter": 67, "action": "", "expression": ""}, {"name": "财政部长", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为深灰色，搭配金色，有金色丝线勾勒图案，头戴黑色官帽，可配黑色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色官帽", "figure": "微胖", "identity": "财政部官员", "other": "眉头微皱，忧心忡忡", "from_chapter": [0], "action": "", "expression": ""}, {"name": "税务部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为棕色，搭配白色，衣摆有白色镶边，头戴黑色小冠，可配棕色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色小冠", "figure": "中等身材", "identity": "税务部官员", "other": "神情严肃", "from_chapter": [0], "action": "", "expression": ""}, {"name": "兵部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服铁黑色铠甲，搭配红色，外罩红色披风，头戴黑色头盔，可配红色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色头盔", "figure": "魁梧", "identity": "兵部官员", "other": "面容刚毅", "from_chapter": [0], "action": "", "expression": ""}, {"name": "工部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为土黄色，搭配绿色，袖口绣绿色花纹，头戴黑色斗笠状官帽，可配黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色斗笠状官帽", "figure": "身材壮实", "identity": "工部官员", "other": "皮肤黝黑", "from_chapter": [0], "action": "", "expression": ""}, {"name": "战备部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为藏青色，搭配蓝色，配有蓝色腰带，头戴黑色毡帽，可配藏青色香囊圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色毡帽", "figure": "中等身材", "identity": "战备部官员", "other": "目光沉稳", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，坐在轿子里一边用手整理衣服、理顺思绪，一边微微皱眉思考，神情严肃的青年男子，他挺拔的身姿展现出敢言善辩的气质，穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发；旁边画面是站在皇宫大殿上身体微微前倾、双手摊开的中年男子，神情专注认真，面容朴实，中等身材，穿着高领圆领汉服官服，主色调为蓝色，搭配灰色，上有灰色线条装饰，头戴黑色方巾，束着黑色长发，配有蓝色腰带，圆形衣领, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753198877_20250722_234119.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_3bdb287c-0ec6-489e-9f2c-c29f184bfc77_1.25x_20250722_202036.wav", "audio_duration": 23.314667, "video_url": "", "story_board_id": 9, "subtitle": [{"subtitle_id": 1, "text": "路上，他一边整理思绪，", "timestamp": "00:00:00,160 --> 00:00:01,680", "duration": 1.52, "char_count": 11, "start_time_s": 0.16, "end_time_s": 1.68, "words": [{"attribute": {"event": "speech"}, "end_time": 240, "start_time": 160, "text": "路"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 240, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 600, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 760, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 840, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 1000, "text": "整"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1120, "text": "理"}, {"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1240, "text": "思"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1440, "text": "绪"}], "keyword": "他"}, {"subtitle_id": 2, "text": "一边思考今天要面对的问题。", "timestamp": "00:00:01,680 --> 00:00:03,720", "duration": 2.04, "char_count": 13, "start_time_s": 1.68, "end_time_s": 3.72, "words": [{"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1680, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1840, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 2120, "start_time": 2000, "text": "思"}, {"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2120, "text": "考"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2400, "text": "今"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2560, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2680, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2800, "text": "面"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 2960, "text": "对"}, {"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 3120, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3160, "text": "问"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3320, "text": "题"}], "keyword": "问题"}, {"subtitle_id": 3, "text": "水利部的郑国渠要修黄河堤坝，", "timestamp": "00:00:03,720 --> 00:00:05,960", "duration": 2.24, "char_count": 14, "start_time_s": 3.72, "end_time_s": 5.96, "words": [{"attribute": {"event": "speech"}, "end_time": 3920, "start_time": 3720, "text": "水"}, {"attribute": {"event": "speech"}, "end_time": 4040, "start_time": 3920, "text": "利"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 4040, "text": "部"}, {"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4160, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4440, "start_time": 4280, "text": "郑"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4440, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4600, "text": "渠"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4840, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5040, "text": "修"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5200, "text": "黄"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5360, "text": "河"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5520, "text": "堤"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5680, "text": "坝"}], "keyword": "郑国渠"}, {"subtitle_id": 4, "text": "农业部的乌鲁市想推广新农具，", "timestamp": "00:00:05,960 --> 00:00:08,320", "duration": 2.36, "char_count": 14, "start_time_s": 5.96, "end_time_s": 8.32, "words": [{"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 5960, "text": "农"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6160, "text": "业"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6280, "text": "部"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6440, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6520, "text": "乌"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6640, "text": "鲁"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6800, "text": "市"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7040, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 7400, "start_time": 7240, "text": "推"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7480, "text": "广"}, {"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7680, "text": "新"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7800, "text": "农"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8000, "text": "具"}], "keyword": "乌鲁市"}, {"subtitle_id": 5, "text": "教育部的冯趣吉想扩大学堂，", "timestamp": "00:00:08,320 --> 00:00:10,320", "duration": 2.0, "char_count": 13, "start_time_s": 8.32, "end_time_s": 10.32, "words": [{"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8320, "text": "教"}, {"attribute": {"event": "speech"}, "end_time": 8640, "start_time": 8520, "text": "育"}, {"attribute": {"event": "speech"}, "end_time": 8720, "start_time": 8640, "text": "部"}, {"attribute": {"event": "speech"}, "end_time": 8840, "start_time": 8720, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8840, "text": "冯"}, {"attribute": {"event": "speech"}, "end_time": 9160, "start_time": 9000, "text": "趣"}, {"attribute": {"event": "speech"}, "end_time": 9280, "start_time": 9160, "text": "吉"}, {"attribute": {"event": "speech"}, "end_time": 9480, "start_time": 9280, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 9600, "start_time": 9480, "text": "扩"}, {"attribute": {"event": "speech"}, "end_time": 9760, "start_time": 9600, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 9880, "start_time": 9760, "text": "学"}, {"attribute": {"event": "speech"}, "end_time": 10320, "start_time": 9880, "text": "堂"}], "keyword": "冯趣吉"}, {"subtitle_id": 6, "text": "财政部的官员担心钱不够用，", "timestamp": "00:00:10,320 --> 00:00:12,280", "duration": 1.96, "char_count": 13, "start_time_s": 10.32, "end_time_s": 12.28, "words": [{"attribute": {"event": "speech"}, "end_time": 10480, "start_time": 10320, "text": "财"}, {"attribute": {"event": "speech"}, "end_time": 10640, "start_time": 10520, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 10760, "start_time": 10640, "text": "部"}, {"attribute": {"event": "speech"}, "end_time": 10880, "start_time": 10760, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 11000, "start_time": 10880, "text": "官"}, {"attribute": {"event": "speech"}, "end_time": 11160, "start_time": 11000, "text": "员"}, {"attribute": {"event": "speech"}, "end_time": 11320, "start_time": 11160, "text": "担"}, {"attribute": {"event": "speech"}, "end_time": 11480, "start_time": 11320, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 11720, "start_time": 11520, "text": "钱"}, {"attribute": {"event": "speech"}, "end_time": 11880, "start_time": 11720, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 12000, "start_time": 11880, "text": "够"}, {"attribute": {"event": "speech"}, "end_time": 12280, "start_time": 12000, "text": "用"}], "keyword": "财政部"}, {"subtitle_id": 7, "text": "税务部想调整税收政策，", "timestamp": "00:00:12,280 --> 00:00:14,020", "duration": 1.74, "char_count": 11, "start_time_s": 12.28, "end_time_s": 14.02, "words": [{"attribute": {"event": "speech"}, "end_time": 12320, "start_time": 12280, "text": "税"}, {"attribute": {"event": "speech"}, "end_time": 12400, "start_time": 12320, "text": "务"}, {"attribute": {"event": "speech"}, "end_time": 12560, "start_time": 12400, "text": "部"}, {"attribute": {"event": "speech"}, "end_time": 12720, "start_time": 12560, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 12920, "start_time": 12720, "text": "调"}, {"attribute": {"event": "speech"}, "end_time": 13080, "start_time": 12920, "text": "整"}, {"attribute": {"event": "speech"}, "end_time": 13240, "start_time": 13120, "text": "税"}, {"attribute": {"event": "speech"}, "end_time": 13360, "start_time": 13240, "text": "收"}, {"attribute": {"event": "speech"}, "end_time": 13520, "start_time": 13360, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 14020, "start_time": 13520, "text": "策"}], "keyword": "税务部"}, {"subtitle_id": 8, "text": "兵部要扩充军备", "timestamp": "00:00:14,080 --> 00:00:15,080", "duration": 1.0, "char_count": 7, "start_time_s": 14.08, "end_time_s": 15.08, "words": [{"attribute": {"event": "speech"}, "end_time": 14160, "start_time": 14080, "text": "兵"}, {"attribute": {"event": "speech"}, "end_time": 14280, "start_time": 14160, "text": "部"}, {"attribute": {"event": "speech"}, "end_time": 14440, "start_time": 14280, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 14640, "start_time": 14440, "text": "扩"}, {"attribute": {"event": "speech"}, "end_time": 14760, "start_time": 14640, "text": "充"}, {"attribute": {"event": "speech"}, "end_time": 14920, "start_time": 14800, "text": "军"}, {"attribute": {"event": "speech"}, "end_time": 15080, "start_time": 14920, "text": "备"}], "keyword": "兵部"}, {"subtitle_id": 9, "text": "朝堂上，", "timestamp": "00:00:15,080 --> 00:00:15,760", "duration": 0.68, "char_count": 4, "start_time_s": 15.08, "end_time_s": 15.76, "words": [{"attribute": {"event": "speech"}, "end_time": 15240, "start_time": 15080, "text": "朝"}, {"attribute": {"event": "speech"}, "end_time": 15400, "start_time": 15240, "text": "堂"}, {"attribute": {"event": "speech"}, "end_time": 15760, "start_time": 15400, "text": "上"}], "keyword": "朝堂"}, {"subtitle_id": 10, "text": "大臣们依次汇报各自部门的计划。", "timestamp": "00:00:15,760 --> 00:00:18,140", "duration": 2.38, "char_count": 15, "start_time_s": 15.76, "end_time_s": 18.14, "words": [{"attribute": {"event": "speech"}, "end_time": 15960, "start_time": 15760, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 16080, "start_time": 15960, "text": "臣"}, {"attribute": {"event": "speech"}, "end_time": 16200, "start_time": 16080, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 16360, "start_time": 16200, "text": "依"}, {"attribute": {"event": "speech"}, "end_time": 16480, "start_time": 16360, "text": "次"}, {"attribute": {"event": "speech"}, "end_time": 16640, "start_time": 16480, "text": "汇"}, {"attribute": {"event": "speech"}, "end_time": 16800, "start_time": 16640, "text": "报"}, {"attribute": {"event": "speech"}, "end_time": 17040, "start_time": 16840, "text": "各"}, {"attribute": {"event": "speech"}, "end_time": 17200, "start_time": 17040, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 17320, "start_time": 17200, "text": "部"}, {"attribute": {"event": "speech"}, "end_time": 17440, "start_time": 17320, "text": "门"}, {"attribute": {"event": "speech"}, "end_time": 17560, "start_time": 17440, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 17680, "start_time": 17560, "text": "计"}, {"attribute": {"event": "speech"}, "end_time": 18140, "start_time": 17680, "text": "划"}], "keyword": "大臣"}, {"subtitle_id": 11, "text": "郑国渠说，", "timestamp": "00:00:18,280 --> 00:00:19,040", "duration": 0.76, "char_count": 5, "start_time_s": 18.28, "end_time_s": 19.04, "words": [{"attribute": {"event": "speech"}, "end_time": 18400, "start_time": 18280, "text": "郑"}, {"attribute": {"event": "speech"}, "end_time": 18520, "start_time": 18400, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 18640, "start_time": 18520, "text": "渠"}, {"attribute": {"event": "speech"}, "end_time": 19040, "start_time": 18640, "text": "说"}], "keyword": "郑国渠"}, {"subtitle_id": 12, "text": "明年要在黄河流域修水利工程，", "timestamp": "00:00:19,040 --> 00:00:21,400", "duration": 2.36, "char_count": 14, "start_time_s": 19.04, "end_time_s": 21.4, "words": [{"attribute": {"event": "speech"}, "end_time": 19200, "start_time": 19040, "text": "明"}, {"attribute": {"event": "speech"}, "end_time": 19320, "start_time": 19200, "text": "年"}, {"attribute": {"event": "speech"}, "end_time": 19520, "start_time": 19320, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 19680, "start_time": 19520, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 19880, "start_time": 19680, "text": "黄"}, {"attribute": {"event": "speech"}, "end_time": 20040, "start_time": 19880, "text": "河"}, {"attribute": {"event": "speech"}, "end_time": 20200, "start_time": 20040, "text": "流"}, {"attribute": {"event": "speech"}, "end_time": 20320, "start_time": 20200, "text": "域"}, {"attribute": {"event": "speech"}, "end_time": 20560, "start_time": 20360, "text": "修"}, {"attribute": {"event": "speech"}, "end_time": 20720, "start_time": 20560, "text": "水"}, {"attribute": {"event": "speech"}, "end_time": 20840, "start_time": 20720, "text": "利"}, {"attribute": {"event": "speech"}, "end_time": 21000, "start_time": 20840, "text": "工"}, {"attribute": {"event": "speech"}, "end_time": 21400, "start_time": 21000, "text": "程"}], "keyword": "黄河流域"}, {"subtitle_id": 13, "text": "疏通河道，", "timestamp": "00:00:21,400 --> 00:00:22,120", "duration": 0.72, "char_count": 5, "start_time_s": 21.4, "end_time_s": 22.12, "words": [{"attribute": {"event": "speech"}, "end_time": 21560, "start_time": 21400, "text": "疏"}, {"attribute": {"event": "speech"}, "end_time": 21680, "start_time": 21560, "text": "通"}, {"attribute": {"event": "speech"}, "end_time": 21840, "start_time": 21680, "text": "河"}, {"attribute": {"event": "speech"}, "end_time": 22120, "start_time": 21840, "text": "道"}], "keyword": "河道"}, {"subtitle_id": 14, "text": "防止洪水。", "timestamp": "00:00:22,120 --> 00:00:23,220", "duration": 1.1, "char_count": 5, "start_time_s": 22.12, "end_time_s": 23.22, "words": [{"attribute": {"event": "speech"}, "end_time": 22320, "start_time": 22120, "text": "防"}, {"attribute": {"event": "speech"}, "end_time": 22520, "start_time": 22400, "text": "止"}, {"attribute": {"event": "speech"}, "end_time": 22720, "start_time": 22560, "text": "洪"}, {"attribute": {"event": "speech"}, "end_time": 23220, "start_time": 22760, "text": "水"}], "keyword": "洪水"}], "keywords": ["他", "问题", "郑国渠", "乌鲁市", "冯趣吉", "财政部", "税务部", "兵部", "朝堂", "大臣", "郑国渠", "黄河流域", "河道", "洪水"]}, {"chapter": 79, "story_board": "需要1300万钱拨款，还要从韩国调人来干活。乌倮氏提出要推广秦辕犁，发展畜牧业，提高粮食产量。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "坐在轿子里，一边用手整理衣服、理顺思绪，一边微微皱眉思考", "expression": "严肃"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "郑国渠", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为蓝色，搭配灰色，上有灰色线条装饰，头戴黑色方巾，可配蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "水利部官员", "other": "面容朴实，神情专注", "from_chapter": [0], "action": "站在朝堂上，表情认真地向众人陈述", "expression": "严肃"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "站在一旁，随后上前提出建议", "expression": "期待"}, {"name": "冯去疾", "gender": "男", "age": "中年", "clothes": "高领圆领绿色朝服，主色调绿色，搭配白色，有树叶纹，头戴乌纱帽，腰间系绿丝带，配玉坠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "适中", "identity": "秦国大臣", "other": "", "from_chapter": 67, "action": "", "expression": ""}, {"name": "财政部长", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为深灰色，搭配金色，有金色丝线勾勒图案，头戴黑色官帽，可配黑色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色官帽", "figure": "微胖", "identity": "财政部官员", "other": "眉头微皱，忧心忡忡", "from_chapter": [0], "action": "", "expression": ""}, {"name": "税务部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为棕色，搭配白色，衣摆有白色镶边，头戴黑色小冠，可配棕色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色小冠", "figure": "中等身材", "identity": "税务部官员", "other": "神情严肃", "from_chapter": [0], "action": "", "expression": ""}, {"name": "兵部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服铁黑色铠甲，搭配红色，外罩红色披风，头戴黑色头盔，可配红色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色头盔", "figure": "魁梧", "identity": "兵部官员", "other": "面容刚毅", "from_chapter": [0], "action": "", "expression": ""}, {"name": "工部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为土黄色，搭配绿色，袖口绣绿色花纹，头戴黑色斗笠状官帽，可配黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色斗笠状官帽", "figure": "身材壮实", "identity": "工部官员", "other": "皮肤黝黑", "from_chapter": [0], "action": "", "expression": ""}, {"name": "战备部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为藏青色，搭配蓝色，配有蓝色腰带，头戴黑色毡帽，可配藏青色香囊圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色毡帽", "figure": "中等身材", "identity": "战备部官员", "other": "目光沉稳", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿上，一个神情专注、表情认真且严肃的中年男子穿着高领圆领、带圆形衣领的蓝色主色调汉服官服，搭配灰色，上有灰色线条装饰，头戴黑色方巾，束起黑色长发，系着蓝色腰带，中等身材、面容朴实，正站着向众人陈述；旁边一个富态且精明的中年男子，带着期待的神情，穿着高领圆领、带圆形衣领的灰色主色调秦代商人服饰，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子，束着黑色头发，正走上前, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753199281_20250722_234802.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_39a7fd38-1ebc-4c9c-86e6-7b5410a4f50a_1.25x_20250722_202020.wav", "audio_duration": 7.552, "video_url": "", "story_board_id": 10, "subtitle": [{"subtitle_id": 1, "text": "需要万千拨款，", "timestamp": "00:00:00,240 --> 00:00:01,320", "duration": 1.08, "char_count": 7, "start_time_s": 0.24, "end_time_s": 1.32, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 240, "text": "需"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 440, "text": "万"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 640, "text": "千"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 760, "text": "拨"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 920, "text": "款"}], "keyword": "拨款"}, {"subtitle_id": 2, "text": "还要从韩国条人来干活。", "timestamp": "00:00:01,320 --> 00:00:03,260", "duration": 1.94, "char_count": 11, "start_time_s": 1.32, "end_time_s": 3.26, "words": [{"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1320, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1480, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1600, "text": "从"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1800, "text": "韩"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 2000, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2160, "text": "条"}, {"attribute": {"event": "speech"}, "end_time": 2480, "start_time": 2360, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2480, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2640, "text": "干"}, {"attribute": {"event": "speech"}, "end_time": 3260, "start_time": 2800, "text": "活"}], "keyword": "韩国"}, {"subtitle_id": 3, "text": "乌洛什提出，", "timestamp": "00:00:03,400 --> 00:00:04,080", "duration": 0.68, "char_count": 6, "start_time_s": 3.4, "end_time_s": 4.08, "words": [{"attribute": {"event": "speech"}, "end_time": 3560, "start_time": 3400, "text": "乌"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3560, "text": "洛"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3640, "text": "什"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3800, "text": "提"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3960, "text": "出"}], "keyword": "乌洛什"}, {"subtitle_id": 4, "text": "要推广秦原梨，", "timestamp": "00:00:04,080 --> 00:00:05,280", "duration": 1.2, "char_count": 7, "start_time_s": 4.08, "end_time_s": 5.28, "words": [{"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4080, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4280, "text": "推"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4480, "text": "广"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4640, "text": "秦"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4800, "text": "原"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 4920, "text": "梨"}], "keyword": "秦原梨"}, {"subtitle_id": 5, "text": "发展畜牧业，", "timestamp": "00:00:05,280 --> 00:00:06,200", "duration": 0.92, "char_count": 6, "start_time_s": 5.28, "end_time_s": 6.2, "words": [{"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5280, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5480, "text": "展"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5680, "text": "畜"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5800, "text": "牧"}, {"attribute": {"event": "speech"}, "end_time": 6200, "start_time": 5880, "text": "业"}], "keyword": "畜牧业"}, {"subtitle_id": 6, "text": "提高粮食产量。", "timestamp": "00:00:06,200 --> 00:00:07,500", "duration": 1.3, "char_count": 7, "start_time_s": 6.2, "end_time_s": 7.5, "words": [{"attribute": {"event": "speech"}, "end_time": 6360, "start_time": 6200, "text": "提"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6360, "text": "高"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6520, "text": "粮"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6720, "text": "食"}, {"attribute": {"event": "speech"}, "end_time": 7000, "start_time": 6840, "text": "产"}, {"attribute": {"event": "speech"}, "end_time": 7500, "start_time": 7000, "text": "量"}], "keyword": "粮食产量"}], "keywords": ["拨款", "韩国", "乌洛什", "秦原梨", "畜牧业", "粮食产量"]}, {"chapter": 79, "story_board": "需要800万钱，用于买农具和牛羊。冯去疾说，教育不能落后，要建更多学堂，提高师资水平。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "坐在轿子里，一边用手整理衣服、理顺思绪，一边微微皱眉思考", "expression": "严肃"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "郑国渠", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为蓝色，搭配灰色，上有灰色线条装饰，头戴黑色方巾，可配蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "水利部官员", "other": "面容朴实，神情专注", "from_chapter": [0], "action": "站在朝堂上，表情认真地向众人陈述", "expression": "严肃"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "站在朝堂上，表情诚恳地向众人提出建议", "expression": "认真"}, {"name": "冯去疾", "gender": "男", "age": "中年", "clothes": "高领圆领绿色朝服，主色调绿色，搭配白色，有树叶纹，头戴乌纱帽，腰间系绿丝带，配玉坠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "适中", "identity": "秦国大臣", "other": "", "from_chapter": 67, "action": "站在朝堂上，神情严肃地陈述观点", "expression": "坚定"}, {"name": "财政部长", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为深灰色，搭配金色，有金色丝线勾勒图案，头戴黑色官帽，可配黑色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色官帽", "figure": "微胖", "identity": "财政部官员", "other": "眉头微皱，忧心忡忡", "from_chapter": [0], "action": "", "expression": ""}, {"name": "税务部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为棕色，搭配白色，衣摆有白色镶边，头戴黑色小冠，可配棕色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色小冠", "figure": "中等身材", "identity": "税务部官员", "other": "神情严肃", "from_chapter": [0], "action": "", "expression": ""}, {"name": "兵部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服铁黑色铠甲，搭配红色，外罩红色披风，头戴黑色头盔，可配红色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色头盔", "figure": "魁梧", "identity": "兵部官员", "other": "面容刚毅", "from_chapter": [0], "action": "", "expression": ""}, {"name": "工部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为土黄色，搭配绿色，袖口绣绿色花纹，头戴黑色斗笠状官帽，可配黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色斗笠状官帽", "figure": "身材壮实", "identity": "工部官员", "other": "皮肤黝黑", "from_chapter": [0], "action": "", "expression": ""}, {"name": "战备部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为藏青色，搭配蓝色，配有蓝色腰带，头戴黑色毡帽，可配藏青色香囊圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色毡帽", "figure": "中等身材", "identity": "战备部官员", "other": "目光沉稳", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿中，一位神情诚恳、认真的中年男子站着向众人提出建议，他穿着高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，黑色束发，富态且面容精明；旁边一位神情严肃、坚定的中年男子站着陈述观点，他身着高领圆领绿色朝服，主色调绿色，搭配白色，有树叶纹，头戴乌纱帽，腰间系绿丝带，配玉坠，圆形衣领，黑色束发，身材适中，画面整体为动漫分镜风格, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753199703_20250722_235504.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_05dd1232-fb0b-4a13-8c94-7e4007ca961f_1.25x_20250722_202028.wav", "audio_duration": 7.626667, "video_url": "", "story_board_id": 11, "subtitle": [{"subtitle_id": 1, "text": "需要万钱用于买农具和牛羊。", "timestamp": "00:00:00,240 --> 00:00:02,580", "duration": 2.34, "char_count": 13, "start_time_s": 0.24, "end_time_s": 2.58, "words": [{"attribute": {"event": "speech"}, "end_time": 360, "start_time": 240, "text": "需"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 360, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 440, "text": "万"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 600, "text": "钱"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1040, "text": "用"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1200, "text": "于"}, {"attribute": {"event": "speech"}, "end_time": 1440, "start_time": 1320, "text": "买"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1440, "text": "农"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1640, "text": "具"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1800, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1960, "text": "牛"}, {"attribute": {"event": "speech"}, "end_time": 2580, "start_time": 2120, "text": "羊"}], "keyword": "农具"}, {"subtitle_id": 2, "text": "冯旭吉说，", "timestamp": "00:00:02,800 --> 00:00:03,640", "duration": 0.84, "char_count": 5, "start_time_s": 2.8, "end_time_s": 3.64, "words": [{"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2800, "text": "冯"}, {"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2960, "text": "旭"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3080, "text": "吉"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3200, "text": "说"}], "keyword": "冯旭吉"}, {"subtitle_id": 3, "text": "教育不能落后，", "timestamp": "00:00:03,640 --> 00:00:04,640", "duration": 1.0, "char_count": 7, "start_time_s": 3.64, "end_time_s": 4.64, "words": [{"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3640, "text": "教"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3840, "text": "育"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3960, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 4200, "start_time": 4080, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 4320, "start_time": 4200, "text": "落"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4320, "text": "后"}], "keyword": "教育"}, {"subtitle_id": 4, "text": "要建更多学堂，", "timestamp": "00:00:04,640 --> 00:00:05,880", "duration": 1.24, "char_count": 7, "start_time_s": 4.64, "end_time_s": 5.88, "words": [{"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4640, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4840, "text": "建"}, {"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5000, "text": "更"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5240, "text": "多"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5360, "text": "学"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5520, "text": "堂"}], "keyword": "学堂"}, {"subtitle_id": 5, "text": "提高师资水平。", "timestamp": "00:00:05,880 --> 00:00:07,260", "duration": 1.38, "char_count": 7, "start_time_s": 5.88, "end_time_s": 7.26, "words": [{"attribute": {"event": "speech"}, "end_time": 6080, "start_time": 5880, "text": "提"}, {"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 6080, "text": "高"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6240, "text": "师"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6440, "text": "资"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6600, "text": "水"}, {"attribute": {"event": "speech"}, "end_time": 7260, "start_time": 6760, "text": "平"}], "keyword": "师资"}], "keywords": ["农具", "冯旭吉", "教育", "学堂", "师资"]}, {"chapter": 79, "story_board": "需要100万钱，主要给老师发工资。财政部长忧心忡忡地说，国库现在压力很大，各部门的计划加起来，钱根本不够用。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "坐在轿子里，一边用手整理衣服、理顺思绪，一边微微皱眉思考", "expression": "严肃"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "郑国渠", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为蓝色，搭配灰色，上有灰色线条装饰，头戴黑色方巾，可配蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "水利部官员", "other": "面容朴实，神情专注", "from_chapter": [0], "action": "站在朝堂上，表情认真地向众人陈述", "expression": "严肃"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "站在朝堂上，表情诚恳地向众人提出建议", "expression": "认真"}, {"name": "冯去疾", "gender": "男", "age": "中年", "clothes": "高领圆领绿色朝服，主色调绿色，搭配白色，有树叶纹，头戴乌纱帽，腰间系绿丝带，配玉坠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "适中", "identity": "秦国大臣", "other": "", "from_chapter": 67, "action": "站在朝堂上陈述", "expression": "认真"}, {"name": "财政部长", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为深灰色，搭配金色，有金色丝线勾勒图案，头戴黑色官帽，可配黑色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色官帽", "figure": "微胖", "identity": "财政部官员", "other": "眉头微皱，忧心忡忡", "from_chapter": [0], "action": "站在朝堂上，拿出账本", "expression": "忧心忡忡"}, {"name": "税务部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为棕色，搭配白色，衣摆有白色镶边，头戴黑色小冠，可配棕色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色小冠", "figure": "中等身材", "identity": "税务部官员", "other": "神情严肃", "from_chapter": [0], "action": "", "expression": ""}, {"name": "兵部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服铁黑色铠甲，搭配红色，外罩红色披风，头戴黑色头盔，可配红色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色头盔", "figure": "魁梧", "identity": "兵部官员", "other": "面容刚毅", "from_chapter": [0], "action": "", "expression": ""}, {"name": "工部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为土黄色，搭配绿色，袖口绣绿色花纹，头戴黑色斗笠状官帽，可配黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色斗笠状官帽", "figure": "身材壮实", "identity": "工部官员", "other": "皮肤黝黑", "from_chapter": [0], "action": "", "expression": ""}, {"name": "战备部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为藏青色，搭配蓝色，配有蓝色腰带，头戴黑色毡帽，可配藏青色香囊圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色毡帽", "figure": "中等身材", "identity": "战备部官员", "other": "目光沉稳", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿上，一个认真陈述的中年男子站着，他穿着高领圆领绿色朝服，主色调为绿色，搭配白色，有树叶纹，头戴乌纱帽，腰间系绿丝带，配玉坠，圆形衣领，黑色束发；旁边一个微胖、眉头微皱、忧心忡忡的中年男子站着并拿出账本，他穿着高领圆领深灰色汉服官服，搭配金色，有金色丝线勾勒图案，头戴黑色官帽，系着黑色腰带，圆形衣领，黑色长发束起, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753200152_20250723_000233.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_c4d8bc2b-eaaa-4bba-8577-a48607fde510_1.25x_20250722_202045.wav", "audio_duration": 8.338667, "video_url": "", "story_board_id": 12, "subtitle": [{"subtitle_id": 1, "text": "需要万钱，", "timestamp": "00:00:00,240 --> 00:00:00,960", "duration": 0.72, "char_count": 5, "start_time_s": 0.24, "end_time_s": 0.96, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 240, "text": "需"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 320, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 440, "text": "万"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 600, "text": "钱"}], "keyword": "万钱"}, {"subtitle_id": 2, "text": "主要给老师发工资。", "timestamp": "00:00:00,960 --> 00:00:02,460", "duration": 1.5, "char_count": 9, "start_time_s": 0.96, "end_time_s": 2.46, "words": [{"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 960, "text": "主"}, {"attribute": {"event": "speech"}, "end_time": 1240, "start_time": 1120, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1240, "text": "给"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1360, "text": "老"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1520, "text": "师"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1640, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1800, "text": "工"}, {"attribute": {"event": "speech"}, "end_time": 2460, "start_time": 1960, "text": "资"}], "keyword": "老师工资"}, {"subtitle_id": 3, "text": "财政部长忧心忡忡的说，", "timestamp": "00:00:02,600 --> 00:00:04,160", "duration": 1.56, "char_count": 11, "start_time_s": 2.6, "end_time_s": 4.16, "words": [{"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2600, "text": "财"}, {"attribute": {"event": "speech"}, "end_time": 2880, "start_time": 2800, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2880, "text": "部"}, {"attribute": {"event": "speech"}, "end_time": 3160, "start_time": 3000, "text": "长"}, {"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3200, "text": "忧"}, {"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3320, "text": "心"}, {"attribute": {"event": "speech"}, "end_time": 3480, "start_time": 3440, "text": "忡"}, {"attribute": {"event": "speech"}, "end_time": 3720, "start_time": 3480, "text": "忡"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3720, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 4160, "start_time": 3840, "text": "说"}], "keyword": "财政部长"}, {"subtitle_id": 4, "text": "国库现在压力很大，", "timestamp": "00:00:04,160 --> 00:00:05,520", "duration": 1.36, "char_count": 9, "start_time_s": 4.16, "end_time_s": 5.52, "words": [{"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4160, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4360, "text": "库"}, {"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4480, "text": "现"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4640, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4760, "text": "压"}, {"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4920, "text": "力"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5000, "text": "很"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5160, "text": "大"}], "keyword": "国库压力"}, {"subtitle_id": 5, "text": "各部门的计划加起来，", "timestamp": "00:00:05,520 --> 00:00:06,920", "duration": 1.4, "char_count": 10, "start_time_s": 5.52, "end_time_s": 6.92, "words": [{"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5520, "text": "各"}, {"attribute": {"event": "speech"}, "end_time": 5840, "start_time": 5720, "text": "部"}, {"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5840, "text": "门"}, {"attribute": {"event": "speech"}, "end_time": 6040, "start_time": 5920, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 6040, "text": "计"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6160, "text": "划"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6320, "text": "加"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6520, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6640, "text": "来"}], "keyword": "部门计划"}, {"subtitle_id": 6, "text": "钱根本不够用。", "timestamp": "00:00:06,920 --> 00:00:08,260", "duration": 1.34, "char_count": 7, "start_time_s": 6.92, "end_time_s": 8.26, "words": [{"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6920, "text": "钱"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7160, "text": "根"}, {"attribute": {"event": "speech"}, "end_time": 7560, "start_time": 7360, "text": "本"}, {"attribute": {"event": "speech"}, "end_time": 7680, "start_time": 7560, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 7800, "start_time": 7680, "text": "够"}, {"attribute": {"event": "speech"}, "end_time": 8260, "start_time": 7800, "text": "用"}], "keyword": "钱不够"}], "keywords": ["万钱", "老师工资", "财政部长", "国库压力", "部门计划", "钱不够"]}, {"chapter": 79, "story_board": "他拿出账本，让所有人看清楚现状，希望各部门能精打细算。税务部的官员建议调整税收政策，打击偷税漏税，同时给新兴产业一些优惠，长远来看能增加收入。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "坐在轿子里，一边用手整理衣服、理顺思绪，一边微微皱眉思考", "expression": "严肃"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "郑国渠", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为蓝色，搭配灰色，上有灰色线条装饰，头戴黑色方巾，可配蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "水利部官员", "other": "面容朴实，神情专注", "from_chapter": [0], "action": "站在朝堂上，表情认真地向众人陈述", "expression": "严肃"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "站在朝堂上，表情诚恳地向众人提出建议", "expression": "认真"}, {"name": "冯去疾", "gender": "男", "age": "中年", "clothes": "高领圆领绿色朝服，主色调绿色，搭配白色，有树叶纹，头戴乌纱帽，腰间系绿丝带，配玉坠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "适中", "identity": "秦国大臣", "other": "", "from_chapter": 67, "action": "站在朝堂上陈述", "expression": "认真"}, {"name": "财政部长", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为深灰色，搭配金色，有金色丝线勾勒图案，头戴黑色官帽，可配黑色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色官帽", "figure": "微胖", "identity": "财政部官员", "other": "眉头微皱，忧心忡忡", "from_chapter": [0], "action": "拿出账本展示给众人看", "expression": "忧心忡忡"}, {"name": "税务部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为棕色，搭配白色，衣摆有白色镶边，头戴黑色小冠，可配棕色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色小冠", "figure": "中等身材", "identity": "税务部官员", "other": "神情严肃", "from_chapter": [0], "action": "提出建议", "expression": "严肃认真"}, {"name": "兵部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服铁黑色铠甲，搭配红色，外罩红色披风，头戴黑色头盔，可配红色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色头盔", "figure": "魁梧", "identity": "兵部官员", "other": "面容刚毅", "from_chapter": [0], "action": "", "expression": ""}, {"name": "工部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为土黄色，搭配绿色，袖口绣绿色花纹，头戴黑色斗笠状官帽，可配黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色斗笠状官帽", "figure": "身材壮实", "identity": "工部官员", "other": "皮肤黝黑", "from_chapter": [0], "action": "", "expression": ""}, {"name": "战备部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为藏青色，搭配蓝色，配有蓝色腰带，头戴黑色毡帽，可配藏青色香囊圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色毡帽", "figure": "中等身材", "identity": "战备部官员", "other": "目光沉稳", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿里，一位微胖、眉头微皱、忧心忡忡的中年男子正拿出账本展示，旁边是神情严肃、认真的中等身材中年男子。前者穿着主色调为深灰色、搭配金色、有金色丝线勾勒图案的高领圆领汉服官服，头戴黑色官帽，配有黑色腰带圆形衣领，黑色长发束起；后者穿着主色调为棕色、搭配白色、衣摆有白色镶边的高领圆领汉服官服，头戴黑色小冠，配有棕色腰带圆形衣领，黑色长发束起, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753200587_20250723_000948.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_ee98187d-6e60-4acb-9d38-67911c6a9d9b_1.25x_20250722_202049.wav", "audio_duration": 11.925333, "video_url": "", "story_board_id": 13, "subtitle": [{"subtitle_id": 1, "text": "他拿出账本，", "timestamp": "00:00:00,160 --> 00:00:01,000", "duration": 0.84, "char_count": 6, "start_time_s": 0.16, "end_time_s": 1.0, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "拿"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 400, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 520, "text": "账"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 680, "text": "本"}], "keyword": "账本"}, {"subtitle_id": 2, "text": "让所有人看清楚现状，", "timestamp": "00:00:01,000 --> 00:00:02,600", "duration": 1.6, "char_count": 10, "start_time_s": 1.0, "end_time_s": 2.6, "words": [{"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1000, "text": "让"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1200, "text": "所"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1400, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1520, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1640, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1800, "text": "清"}, {"attribute": {"event": "speech"}, "end_time": 2040, "start_time": 1960, "text": "楚"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2080, "text": "现"}, {"attribute": {"event": "speech"}, "end_time": 2600, "start_time": 2240, "text": "状"}], "keyword": "现状"}, {"subtitle_id": 3, "text": "希望各部门能精打细算。", "timestamp": "00:00:02,600 --> 00:00:04,420", "duration": 1.82, "char_count": 11, "start_time_s": 2.6, "end_time_s": 4.42, "words": [{"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2600, "text": "希"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2720, "text": "望"}, {"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2840, "text": "各"}, {"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2960, "text": "部"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3080, "text": "门"}, {"attribute": {"event": "speech"}, "end_time": 3400, "start_time": 3240, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3440, "text": "精"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3600, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3800, "text": "细"}, {"attribute": {"event": "speech"}, "end_time": 4420, "start_time": 3960, "text": "算"}], "keyword": "精打细算"}, {"subtitle_id": 4, "text": "税务部的官员建议，", "timestamp": "00:00:04,640 --> 00:00:05,720", "duration": 1.08, "char_count": 9, "start_time_s": 4.64, "end_time_s": 5.72, "words": [{"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4640, "text": "税"}, {"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4760, "text": "务"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4880, "text": "部"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4960, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5040, "text": "官"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5200, "text": "员"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5480, "text": "建"}, {"attribute": {"event": "speech"}, "end_time": 5720, "start_time": 5640, "text": "议"}], "keyword": "税务部"}, {"subtitle_id": 5, "text": "调整税收政策，", "timestamp": "00:00:05,720 --> 00:00:06,920", "duration": 1.2, "char_count": 7, "start_time_s": 5.72, "end_time_s": 6.92, "words": [{"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5720, "text": "调"}, {"attribute": {"event": "speech"}, "end_time": 6080, "start_time": 5920, "text": "整"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6120, "text": "税"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6280, "text": "收"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6400, "text": "政"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6560, "text": "策"}], "keyword": "税收政策"}, {"subtitle_id": 6, "text": "打击偷税漏税，", "timestamp": "00:00:06,920 --> 00:00:08,040", "duration": 1.12, "char_count": 7, "start_time_s": 6.92, "end_time_s": 8.04, "words": [{"attribute": {"event": "speech"}, "end_time": 7120, "start_time": 6920, "text": "打"}, {"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 7160, "text": "击"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7280, "text": "偷"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7480, "text": "税"}, {"attribute": {"event": "speech"}, "end_time": 7760, "start_time": 7600, "text": "漏"}, {"attribute": {"event": "speech"}, "end_time": 8040, "start_time": 7800, "text": "税"}], "keyword": "偷税漏税"}, {"subtitle_id": 7, "text": "同时给新兴产业一些优惠，", "timestamp": "00:00:08,040 --> 00:00:09,840", "duration": 1.8, "char_count": 12, "start_time_s": 8.04, "end_time_s": 9.84, "words": [{"attribute": {"event": "speech"}, "end_time": 8240, "start_time": 8040, "text": "同"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8240, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 8520, "start_time": 8400, "text": "给"}, {"attribute": {"event": "speech"}, "end_time": 8720, "start_time": 8560, "text": "新"}, {"attribute": {"event": "speech"}, "end_time": 8840, "start_time": 8720, "text": "兴"}, {"attribute": {"event": "speech"}, "end_time": 9040, "start_time": 8880, "text": "产"}, {"attribute": {"event": "speech"}, "end_time": 9160, "start_time": 9040, "text": "业"}, {"attribute": {"event": "speech"}, "end_time": 9240, "start_time": 9160, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 9360, "start_time": 9240, "text": "些"}, {"attribute": {"event": "speech"}, "end_time": 9520, "start_time": 9360, "text": "优"}, {"attribute": {"event": "speech"}, "end_time": 9840, "start_time": 9520, "text": "惠"}], "keyword": "新兴产业"}, {"subtitle_id": 8, "text": "长远来看能增加收入。", "timestamp": "00:00:09,840 --> 00:00:11,580", "duration": 1.74, "char_count": 10, "start_time_s": 9.84, "end_time_s": 11.58, "words": [{"attribute": {"event": "speech"}, "end_time": 10040, "start_time": 9840, "text": "长"}, {"attribute": {"event": "speech"}, "end_time": 10160, "start_time": 10080, "text": "远"}, {"attribute": {"event": "speech"}, "end_time": 10320, "start_time": 10160, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 10480, "start_time": 10320, "text": "看"}, {"attribute": {"event": "speech"}, "end_time": 10720, "start_time": 10560, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 10880, "start_time": 10720, "text": "增"}, {"attribute": {"event": "speech"}, "end_time": 11040, "start_time": 10880, "text": "加"}, {"attribute": {"event": "speech"}, "end_time": 11160, "start_time": 11040, "text": "收"}, {"attribute": {"event": "speech"}, "end_time": 11580, "start_time": 11160, "text": "入"}], "keyword": "收入"}], "keywords": ["账本", "现状", "精打细算", "税务部", "税收政策", "偷税漏税", "新兴产业", "收入"]}, {"chapter": 79, "story_board": "兵部的官员语气严肃，说边境并不安全，五国虎视眈眈，必须加强军队训练和装备。需要6000万钱，这笔钱相当庞大。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "坐在朝堂上，认真倾听", "expression": "凝重"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "郑国渠", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为蓝色，搭配灰色，上有灰色线条装饰，头戴黑色方巾，可配蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "水利部官员", "other": "面容朴实，神情专注", "from_chapter": [0], "action": "站在朝堂上，表情认真地向众人陈述", "expression": "严肃"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "站在朝堂上，表情诚恳地向众人提出建议", "expression": "认真"}, {"name": "冯去疾", "gender": "男", "age": "中年", "clothes": "高领圆领绿色朝服，主色调绿色，搭配白色，有树叶纹，头戴乌纱帽，腰间系绿丝带，配玉坠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "适中", "identity": "秦国大臣", "other": "", "from_chapter": 67, "action": "站在朝堂上陈述", "expression": "认真"}, {"name": "财政部长", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为深灰色，搭配金色，有金色丝线勾勒图案，头戴黑色官帽，可配黑色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色官帽", "figure": "微胖", "identity": "财政部官员", "other": "眉头微皱，忧心忡忡", "from_chapter": [0], "action": "拿出账本展示给众人看", "expression": "忧心忡忡"}, {"name": "税务部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为棕色，搭配白色，衣摆有白色镶边，头戴黑色小冠，可配棕色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色小冠", "figure": "中等身材", "identity": "税务部官员", "other": "神情严肃", "from_chapter": [0], "action": "提出建议", "expression": "严肃认真"}, {"name": "兵部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服铁黑色铠甲，搭配红色，外罩红色披风，头戴黑色头盔，可配红色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色头盔", "figure": "魁梧", "identity": "兵部官员", "other": "面容刚毅", "from_chapter": [0], "action": "站在朝堂上，语气严肃地汇报", "expression": "严肃"}, {"name": "工部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为土黄色，搭配绿色，袖口绣绿色花纹，头戴黑色斗笠状官帽，可配黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色斗笠状官帽", "figure": "身材壮实", "identity": "工部官员", "other": "皮肤黝黑", "from_chapter": [0], "action": "", "expression": ""}, {"name": "战备部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为藏青色，搭配蓝色，配有蓝色腰带，头戴黑色毡帽，可配藏青色香囊圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色毡帽", "figure": "中等身材", "identity": "战备部官员", "other": "目光沉稳", "from_chapter": [0], "action": "", "expression": ""}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿上，一位面容刚毅、神情严肃的中年男子站着汇报情况，他身姿魁梧；旁边一位青年男子坐在那里认真倾听，表情凝重。站着的中年男子穿着高领圆领的铁黑色铠甲，搭配红色，外罩红色披风，头戴黑色头盔，束起黑色长发，配有红色腰带，圆形衣领；坐着的青年男子穿着高领圆领紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，束着黑色头发，身姿挺拔, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753200943_20250723_001544.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_0904c5d0-f460-494e-aca2-8e8cd964fd69_1.25x_20250722_202024.wav", "audio_duration": 9.125333, "video_url": "", "story_board_id": 14, "subtitle": [{"subtitle_id": 1, "text": "兵部的官员语气严肃，", "timestamp": "00:00:00,160 --> 00:00:01,600", "duration": 1.44, "char_count": 10, "start_time_s": 0.16, "end_time_s": 1.6, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "兵"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 280, "text": "部"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 400, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 520, "text": "官"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 640, "text": "员"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 800, "text": "语"}, {"attribute": {"event": "speech"}, "end_time": 1080, "start_time": 960, "text": "气"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1080, "text": "严"}, {"attribute": {"event": "speech"}, "end_time": 1600, "start_time": 1240, "text": "肃"}], "keyword": "兵部官员"}, {"subtitle_id": 2, "text": "说边境并不安全，", "timestamp": "00:00:01,600 --> 00:00:02,920", "duration": 1.32, "char_count": 8, "start_time_s": 1.6, "end_time_s": 2.92, "words": [{"attribute": {"event": "speech"}, "end_time": 1720, "start_time": 1600, "text": "说"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1760, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1960, "text": "境"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2080, "text": "并"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2280, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2400, "text": "安"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2560, "text": "全"}], "keyword": "边境安全"}, {"subtitle_id": 3, "text": "五国虎视眈眈，", "timestamp": "00:00:02,920 --> 00:00:04,080", "duration": 1.16, "char_count": 7, "start_time_s": 2.92, "end_time_s": 4.08, "words": [{"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 2920, "text": "五"}, {"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3120, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3320, "text": "虎"}, {"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3520, "text": "视"}, {"attribute": {"event": "speech"}, "end_time": 3640, "start_time": 3600, "text": "眈"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3640, "text": "眈"}], "keyword": "五国"}, {"subtitle_id": 4, "text": "必须加强军队训练和装备，", "timestamp": "00:00:04,080 --> 00:00:06,060", "duration": 1.98, "char_count": 12, "start_time_s": 4.08, "end_time_s": 6.06, "words": [{"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4080, "text": "必"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4240, "text": "须"}, {"attribute": {"event": "speech"}, "end_time": 4560, "start_time": 4400, "text": "加"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4560, "text": "强"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4760, "text": "军"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4920, "text": "队"}, {"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5080, "text": "训"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5200, "text": "练"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5360, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5480, "text": "装"}, {"attribute": {"event": "speech"}, "end_time": 6060, "start_time": 5600, "text": "备"}], "keyword": "军队训练"}, {"subtitle_id": 5, "text": "需要万钱，", "timestamp": "00:00:06,240 --> 00:00:07,160", "duration": 0.92, "char_count": 5, "start_time_s": 6.24, "end_time_s": 7.16, "words": [{"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6240, "text": "需"}, {"attribute": {"event": "speech"}, "end_time": 6520, "start_time": 6400, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6520, "text": "万"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 6680, "text": "钱"}], "keyword": "万钱"}, {"subtitle_id": 6, "text": "这笔钱相当庞大。", "timestamp": "00:00:07,160 --> 00:00:08,820", "duration": 1.66, "char_count": 8, "start_time_s": 7.16, "end_time_s": 8.82, "words": [{"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7160, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7320, "text": "笔"}, {"attribute": {"event": "speech"}, "end_time": 7640, "start_time": 7480, "text": "钱"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7720, "text": "相"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 7960, "text": "当"}, {"attribute": {"event": "speech"}, "end_time": 8320, "start_time": 8120, "text": "庞"}, {"attribute": {"event": "speech"}, "end_time": 8820, "start_time": 8320, "text": "大"}], "keyword": "钱数"}], "keywords": ["兵部官员", "边境安全", "五国", "军队训练", "万钱", "钱数"]}, {"chapter": 79, "story_board": "工部的人也提了计划，要修官道、加固城墙，预计需要600万钱。战备部则要储备粮食、兵器、盔甲，需要500万钱。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "坐在朝堂主位上，身体微微前倾，认真倾听", "expression": "凝重"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "郑国渠", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为蓝色，搭配灰色，上有灰色线条装饰，头戴黑色方巾，可配蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "水利部官员", "other": "面容朴实，神情专注", "from_chapter": [0], "action": "站在朝堂上，表情认真地向众人陈述", "expression": "严肃"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "站在朝堂上，表情诚恳地向众人提出建议", "expression": "认真"}, {"name": "冯去疾", "gender": "男", "age": "中年", "clothes": "高领圆领绿色朝服，主色调绿色，搭配白色，有树叶纹，头戴乌纱帽，腰间系绿丝带，配玉坠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "适中", "identity": "秦国大臣", "other": "", "from_chapter": 67, "action": "站在朝堂上陈述", "expression": "认真"}, {"name": "财政部长", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为深灰色，搭配金色，有金色丝线勾勒图案，头戴黑色官帽，可配黑色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色官帽", "figure": "微胖", "identity": "财政部官员", "other": "眉头微皱，忧心忡忡", "from_chapter": [0], "action": "拿出账本展示给众人看", "expression": "忧心忡忡"}, {"name": "税务部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为棕色，搭配白色，衣摆有白色镶边，头戴黑色小冠，可配棕色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色小冠", "figure": "中等身材", "identity": "税务部官员", "other": "神情严肃", "from_chapter": [0], "action": "提出建议", "expression": "严肃认真"}, {"name": "兵部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服铁黑色铠甲，搭配红色，外罩红色披风，头戴黑色头盔，可配红色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色头盔", "figure": "魁梧", "identity": "兵部官员", "other": "面容刚毅", "from_chapter": [0], "action": "站在朝堂上，语气严肃地汇报", "expression": "严肃"}, {"name": "工部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为土黄色，搭配绿色，袖口绣绿色花纹，头戴黑色斗笠状官帽，可配黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色斗笠状官帽", "figure": "身材壮实", "identity": "工部官员", "other": "皮肤黝黑", "from_chapter": [0], "action": "站在朝堂上，神情严肃地陈述计划", "expression": "认真"}, {"name": "战备部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为藏青色，搭配蓝色，配有蓝色腰带，头戴黑色毡帽，可配藏青色香囊圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色毡帽", "figure": "中等身材", "identity": "战备部官员", "other": "目光沉稳", "from_chapter": [0], "action": "站在朝堂上，有条不紊地说出需求", "expression": "沉稳"}], "scene": "皇宫大殿", "image_prompt": "中景，皇宫大殿中，坐在主位上身体微微前倾、神情凝重认真倾听的青年男子，挺拔身姿，身着高领圆领紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，圆形衣领，黑色束发；旁边站着神情严肃认真陈述计划的中年壮实男子，皮肤黝黑，身着高领圆领土黄色汉服官服，搭配绿色，袖口绣绿色花纹，头戴黑色斗笠状官帽，配黄色腰带，圆形衣领，黑色长发束起；还有站着有条不紊展现需求、目光沉稳的中年中等身材男子，身着高领圆领藏青色汉服官服，搭配蓝色，配有蓝色腰带，头戴黑色毡帽，配藏青色香囊，圆形衣领，黑色长发束起, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753201275_20250723_002115.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_181119d9-fe9c-4ba2-a2ce-8eef9422cf24_1.25x_20250722_202113.wav", "audio_duration": 7.818667, "video_url": "", "story_board_id": 15, "subtitle": [{"subtitle_id": 1, "text": "工部的人也提了计划，", "timestamp": "00:00:00,160 --> 00:00:01,480", "duration": 1.32, "char_count": 10, "start_time_s": 0.16, "end_time_s": 1.48, "words": [{"attribute": {"event": "speech"}, "end_time": 320, "start_time": 160, "text": "工"}, {"attribute": {"event": "speech"}, "end_time": 400, "start_time": 320, "text": "部"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 400, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 520, "text": "人"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 640, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 800, "text": "提"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 920, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 1040, "text": "计"}, {"attribute": {"event": "speech"}, "end_time": 1480, "start_time": 1160, "text": "划"}], "keyword": "工部"}, {"subtitle_id": 2, "text": "要修官道，", "timestamp": "00:00:01,480 --> 00:00:02,160", "duration": 0.68, "char_count": 5, "start_time_s": 1.48, "end_time_s": 2.16, "words": [{"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1480, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1680, "text": "修"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1800, "text": "官"}, {"attribute": {"event": "speech"}, "end_time": 2160, "start_time": 1960, "text": "道"}], "keyword": "官道"}, {"subtitle_id": 3, "text": "加固城墙，", "timestamp": "00:00:02,160 --> 00:00:03,000", "duration": 0.84, "char_count": 5, "start_time_s": 2.16, "end_time_s": 3.0, "words": [{"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 2160, "text": "加"}, {"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2320, "text": "固"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2440, "text": "城"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2640, "text": "墙"}], "keyword": "城墙"}, {"subtitle_id": 4, "text": "预计需要万千。", "timestamp": "00:00:03,000 --> 00:00:04,300", "duration": 1.3, "char_count": 7, "start_time_s": 3.0, "end_time_s": 4.3, "words": [{"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3000, "text": "预"}, {"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3200, "text": "计"}, {"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3360, "text": "需"}, {"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3520, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3600, "text": "万"}, {"attribute": {"event": "speech"}, "end_time": 4300, "start_time": 3800, "text": "千"}], "keyword": "需要"}, {"subtitle_id": 5, "text": "战备部则要储备粮食、兵器盔甲，", "timestamp": "00:00:04,440 --> 00:00:06,600", "duration": 2.16, "char_count": 15, "start_time_s": 4.44, "end_time_s": 6.6, "words": [{"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4440, "text": "战"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4600, "text": "备"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4760, "text": "部"}, {"attribute": {"event": "speech"}, "end_time": 5040, "start_time": 4960, "text": "则"}, {"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5040, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5200, "text": "储"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5360, "text": "备"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5520, "text": "粮"}, {"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5640, "text": "食"}, {"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5800, "text": "兵"}, {"attribute": {"event": "speech"}, "end_time": 6080, "start_time": 5920, "text": "器"}, {"attribute": {"event": "speech"}, "end_time": 6240, "start_time": 6080, "text": "盔"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6280, "text": "甲"}], "keyword": "战备部"}, {"subtitle_id": 6, "text": "需要万千。", "timestamp": "00:00:06,600 --> 00:00:07,460", "duration": 0.86, "char_count": 5, "start_time_s": 6.6, "end_time_s": 7.46, "words": [{"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6600, "text": "需"}, {"attribute": {"event": "speech"}, "end_time": 6840, "start_time": 6720, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6840, "text": "万"}, {"attribute": {"event": "speech"}, "end_time": 7460, "start_time": 7000, "text": "千"}], "keyword": "粮食"}], "keywords": ["工部", "官道", "城墙", "需要", "战备部", "粮食"]}, {"chapter": 79, "story_board": "公子华听着大家的汇报，眉头紧锁。每一个计划都关系到国家的未来，但钱的问题成了最大的难题。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站着认真听大臣汇报，身体微微前倾", "expression": "眉头紧锁，忧虑"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "郑国渠", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为蓝色，搭配灰色，上有灰色线条装饰，头戴黑色方巾，可配蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "水利部官员", "other": "面容朴实，神情专注", "from_chapter": [0], "action": "站在朝堂上，表情认真地向众人陈述", "expression": "严肃"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "站在朝堂上，表情诚恳地向众人提出建议", "expression": "认真"}, {"name": "冯去疾", "gender": "男", "age": "中年", "clothes": "高领圆领绿色朝服，主色调绿色，搭配白色，有树叶纹，头戴乌纱帽，腰间系绿丝带，配玉坠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "适中", "identity": "秦国大臣", "other": "", "from_chapter": 67, "action": "站在朝堂上陈述", "expression": "认真"}, {"name": "财政部长", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为深灰色，搭配金色，有金色丝线勾勒图案，头戴黑色官帽，可配黑色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色官帽", "figure": "微胖", "identity": "财政部官员", "other": "眉头微皱，忧心忡忡", "from_chapter": [0], "action": "拿出账本展示给众人看", "expression": "忧心忡忡"}, {"name": "税务部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为棕色，搭配白色，衣摆有白色镶边，头戴黑色小冠，可配棕色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色小冠", "figure": "中等身材", "identity": "税务部官员", "other": "神情严肃", "from_chapter": [0], "action": "提出建议", "expression": "严肃认真"}, {"name": "兵部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服铁黑色铠甲，搭配红色，外罩红色披风，头戴黑色头盔，可配红色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色头盔", "figure": "魁梧", "identity": "兵部官员", "other": "面容刚毅", "from_chapter": [0], "action": "站在朝堂上，语气严肃地汇报", "expression": "严肃"}, {"name": "工部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为土黄色，搭配绿色，袖口绣绿色花纹，头戴黑色斗笠状官帽，可配黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色斗笠状官帽", "figure": "身材壮实", "identity": "工部官员", "other": "皮肤黝黑", "from_chapter": [0], "action": "站在朝堂上，神情严肃地陈述计划", "expression": "认真"}, {"name": "战备部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为藏青色，搭配蓝色，配有蓝色腰带，头戴黑色毡帽，可配藏青色香囊圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色毡帽", "figure": "中等身材", "identity": "战备部官员", "other": "目光沉稳", "from_chapter": [0], "action": "站在朝堂上，有条不紊地说出需求", "expression": "沉稳"}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿里，一位身体微微前倾、眉头紧锁、满脸忧虑地站着认真聆听的青年男子，身着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，有着圆形衣领，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753201563_20250723_002603.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_04c387e5-a22e-40cd-b114-f6b70715bd1d_1.25x_20250722_202125.wav", "audio_duration": 8.258667, "video_url": "", "story_board_id": 16, "subtitle": [{"subtitle_id": 1, "text": "龚子华听着大家的汇报，", "timestamp": "00:00:00,160 --> 00:00:01,760", "duration": 1.6, "char_count": 11, "start_time_s": 0.16, "end_time_s": 1.76, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 560, "start_time": 440, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 600, "text": "听"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 760, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 840, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 960, "text": "家"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1120, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1200, "text": "汇"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1360, "text": "报"}], "keyword": "龚子华"}, {"subtitle_id": 2, "text": "眉头紧锁。", "timestamp": "00:00:01,760 --> 00:00:02,900", "duration": 1.14, "char_count": 5, "start_time_s": 1.76, "end_time_s": 2.9, "words": [{"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1760, "text": "眉"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2000, "text": "头"}, {"attribute": {"event": "speech"}, "end_time": 2360, "start_time": 2200, "text": "紧"}, {"attribute": {"event": "speech"}, "end_time": 2900, "start_time": 2400, "text": "锁"}], "keyword": "眉头"}, {"subtitle_id": 3, "text": "每一个计划都关系到国家的未来，", "timestamp": "00:00:03,000 --> 00:00:05,480", "duration": 2.48, "char_count": 15, "start_time_s": 3.0, "end_time_s": 5.48, "words": [{"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3000, "text": "每"}, {"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3240, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 3440, "start_time": 3320, "text": "个"}, {"attribute": {"event": "speech"}, "end_time": 3600, "start_time": 3440, "text": "计"}, {"attribute": {"event": "speech"}, "end_time": 3800, "start_time": 3600, "text": "划"}, {"attribute": {"event": "speech"}, "end_time": 4080, "start_time": 3920, "text": "都"}, {"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4080, "text": "关"}, {"attribute": {"event": "speech"}, "end_time": 4360, "start_time": 4240, "text": "系"}, {"attribute": {"event": "speech"}, "end_time": 4520, "start_time": 4360, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 4720, "start_time": 4520, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4720, "text": "家"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4840, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 4960, "text": "未"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5080, "text": "来"}], "keyword": "计划"}, {"subtitle_id": 4, "text": "但钱的问题成了最大的难题。", "timestamp": "00:00:05,480 --> 00:00:07,900", "duration": 2.42, "char_count": 13, "start_time_s": 5.48, "end_time_s": 7.9, "words": [{"attribute": {"event": "speech"}, "end_time": 5680, "start_time": 5480, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5720, "text": "钱"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5880, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 6000, "text": "问"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6120, "text": "题"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6440, "text": "成"}, {"attribute": {"event": "speech"}, "end_time": 6720, "start_time": 6600, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 6960, "start_time": 6720, "text": "最"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 6960, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 7280, "start_time": 7160, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7280, "text": "难"}, {"attribute": {"event": "speech"}, "end_time": 7900, "start_time": 7440, "text": "题"}], "keyword": "钱的问题"}], "keywords": ["龚子华", "眉头", "计划", "钱的问题"]}, {"chapter": 79, "story_board": "他明白，如果不能合理分配资源，国家的发展就会受阻。他深吸一口气，站起身来，开始逐一回应。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "深吸一口气，站起身来，开始逐一回应大臣们的汇报", "expression": "严肃"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "郑国渠", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为蓝色，搭配灰色，上有灰色线条装饰，头戴黑色方巾，可配蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "水利部官员", "other": "面容朴实，神情专注", "from_chapter": [0], "action": "站在朝堂上，表情认真地向众人陈述", "expression": "严肃"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "站在朝堂上，表情诚恳地向众人提出建议", "expression": "认真"}, {"name": "冯去疾", "gender": "男", "age": "中年", "clothes": "高领圆领绿色朝服，主色调绿色，搭配白色，有树叶纹，头戴乌纱帽，腰间系绿丝带，配玉坠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "适中", "identity": "秦国大臣", "other": "", "from_chapter": 67, "action": "站在朝堂上陈述", "expression": "认真"}, {"name": "财政部长", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为深灰色，搭配金色，有金色丝线勾勒图案，头戴黑色官帽，可配黑色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色官帽", "figure": "微胖", "identity": "财政部官员", "other": "眉头微皱，忧心忡忡", "from_chapter": [0], "action": "拿出账本展示给众人看", "expression": "忧心忡忡"}, {"name": "税务部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为棕色，搭配白色，衣摆有白色镶边，头戴黑色小冠，可配棕色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色小冠", "figure": "中等身材", "identity": "税务部官员", "other": "神情严肃", "from_chapter": [0], "action": "提出建议", "expression": "严肃认真"}, {"name": "兵部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服铁黑色铠甲，搭配红色，外罩红色披风，头戴黑色头盔，可配红色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色头盔", "figure": "魁梧", "identity": "兵部官员", "other": "面容刚毅", "from_chapter": [0], "action": "站在朝堂上，语气严肃地汇报", "expression": "严肃"}, {"name": "工部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为土黄色，搭配绿色，袖口绣绿色花纹，头戴黑色斗笠状官帽，可配黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色斗笠状官帽", "figure": "身材壮实", "identity": "工部官员", "other": "皮肤黝黑", "from_chapter": [0], "action": "站在朝堂上，神情严肃地陈述计划", "expression": "认真"}, {"name": "战备部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为藏青色，搭配蓝色，配有蓝色腰带，头戴黑色毡帽，可配藏青色香囊圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色毡帽", "figure": "中等身材", "identity": "战备部官员", "other": "目光沉稳", "from_chapter": [0], "action": "站在朝堂上，有条不紊地说出需求", "expression": "沉稳"}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿中，一位神情严肃的青年男子深吸一口气后站起身来，开始逐一回应汇报。该男子穿着高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩，有着圆形衣领，黑色束发，身姿挺拔, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753201842_20250723_003043.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_2a627c27-c898-45bd-9db6-cd7fc01b0dac_1.25x_20250722_202100.wav", "audio_duration": 7.336, "video_url": "", "story_board_id": 17, "subtitle": [{"subtitle_id": 1, "text": "他明白，如果不能合理分配资源，", "timestamp": "00:00:00,200 --> 00:00:02,280", "duration": 2.08, "char_count": 15, "start_time_s": 0.2, "end_time_s": 2.28, "words": [{"attribute": {"event": "speech"}, "end_time": 240, "start_time": 200, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 360, "start_time": 240, "text": "明"}, {"attribute": {"event": "speech"}, "end_time": 520, "start_time": 360, "text": "白"}, {"attribute": {"event": "speech"}, "end_time": 920, "start_time": 720, "text": "如"}, {"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 920, "text": "果"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 1000, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 1200, "start_time": 1120, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1200, "text": "合"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1400, "text": "理"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1520, "text": "分"}, {"attribute": {"event": "speech"}, "end_time": 1800, "start_time": 1680, "text": "配"}, {"attribute": {"event": "speech"}, "end_time": 1960, "start_time": 1840, "text": "资"}, {"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 1960, "text": "源"}], "keyword": "他"}, {"subtitle_id": 2, "text": "国家的发展就会受阻。", "timestamp": "00:00:02,280 --> 00:00:03,980", "duration": 1.7, "char_count": 10, "start_time_s": 2.28, "end_time_s": 3.98, "words": [{"attribute": {"event": "speech"}, "end_time": 2440, "start_time": 2280, "text": "国"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2440, "text": "家"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2560, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2680, "text": "发"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2840, "text": "展"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3040, "text": "就"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3200, "text": "会"}, {"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3360, "text": "受"}, {"attribute": {"event": "speech"}, "end_time": 3980, "start_time": 3520, "text": "阻"}], "keyword": "国家"}, {"subtitle_id": 3, "text": "他深吸一口气，", "timestamp": "00:00:04,120 --> 00:00:05,160", "duration": 1.04, "char_count": 7, "start_time_s": 4.12, "end_time_s": 5.16, "words": [{"attribute": {"event": "speech"}, "end_time": 4280, "start_time": 4120, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 4440, "start_time": 4280, "text": "深"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4480, "text": "吸"}, {"attribute": {"event": "speech"}, "end_time": 4680, "start_time": 4600, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 4840, "start_time": 4680, "text": "口"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 4840, "text": "气"}], "keyword": "吸气"}, {"subtitle_id": 4, "text": "站起身来，", "timestamp": "00:00:05,160 --> 00:00:05,960", "duration": 0.8, "char_count": 5, "start_time_s": 5.16, "end_time_s": 5.96, "words": [{"attribute": {"event": "speech"}, "end_time": 5360, "start_time": 5160, "text": "站"}, {"attribute": {"event": "speech"}, "end_time": 5520, "start_time": 5400, "text": "起"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5520, "text": "身"}, {"attribute": {"event": "speech"}, "end_time": 5960, "start_time": 5640, "text": "来"}], "keyword": "起身"}, {"subtitle_id": 5, "text": "开始逐一回应。", "timestamp": "00:00:05,960 --> 00:00:07,260", "duration": 1.3, "char_count": 7, "start_time_s": 5.96, "end_time_s": 7.26, "words": [{"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 5960, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 6320, "start_time": 6160, "text": "始"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6320, "text": "逐"}, {"attribute": {"event": "speech"}, "end_time": 6640, "start_time": 6440, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6640, "text": "回"}, {"attribute": {"event": "speech"}, "end_time": 7260, "start_time": 6840, "text": "应"}], "keyword": "回应"}], "keywords": ["他", "国家", "吸气", "起身", "回应"]}, {"chapter": 79, "story_board": "他没有直接拒绝任何一项提议，而是提出了自己的想法：首先，优先保障民生项目，比如水利、农业和教育；其次，对军事和边防也不能放松，但要控制开支；最后，税务改革要稳步推进，确保公平合理。朝堂上的气氛一时凝固，大臣们面面相觑。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "站起身，认真地提出自己的想法，之后站在朝堂上", "expression": "严肃、坚定"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "郑国渠", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为蓝色，搭配灰色，上有灰色线条装饰，头戴黑色方巾，可配蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "水利部官员", "other": "面容朴实，神情专注", "from_chapter": [0], "action": "站在朝堂上，表情认真地向众人陈述", "expression": "严肃"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "站在朝堂上，表情诚恳地向众人提出建议", "expression": "认真"}, {"name": "冯去疾", "gender": "男", "age": "中年", "clothes": "高领圆领绿色朝服，主色调绿色，搭配白色，有树叶纹，头戴乌纱帽，腰间系绿丝带，配玉坠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "适中", "identity": "秦国大臣", "other": "", "from_chapter": 67, "action": "站在朝堂上陈述", "expression": "认真"}, {"name": "财政部长", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为深灰色，搭配金色，有金色丝线勾勒图案，头戴黑色官帽，可配黑色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色官帽", "figure": "微胖", "identity": "财政部官员", "other": "眉头微皱，忧心忡忡", "from_chapter": [0], "action": "站在朝堂上，听公子华说话，面面相觑", "expression": "惊讶、思索"}, {"name": "税务部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为棕色，搭配白色，衣摆有白色镶边，头戴黑色小冠，可配棕色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色小冠", "figure": "中等身材", "identity": "税务部官员", "other": "神情严肃", "from_chapter": [0], "action": "站在朝堂上，听公子华说话，面面相觑", "expression": "惊讶、思索"}, {"name": "兵部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服铁黑色铠甲，搭配红色，外罩红色披风，头戴黑色头盔，可配红色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色头盔", "figure": "魁梧", "identity": "兵部官员", "other": "面容刚毅", "from_chapter": [0], "action": "站在朝堂上，语气严肃地汇报", "expression": "严肃"}, {"name": "工部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为土黄色，搭配绿色，袖口绣绿色花纹，头戴黑色斗笠状官帽，可配黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色斗笠状官帽", "figure": "身材壮实", "identity": "工部官员", "other": "皮肤黝黑", "from_chapter": [0], "action": "站在朝堂上，神情严肃地陈述计划", "expression": "认真"}, {"name": "战备部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为藏青色，搭配蓝色，配有蓝色腰带，头戴黑色毡帽，可配藏青色香囊圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色毡帽", "figure": "中等身材", "identity": "战备部官员", "other": "目光沉稳", "from_chapter": [0], "action": "站在朝堂上，有条不紊地说出需求", "expression": "沉稳"}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿中，一位青年男子站起身，神情严肃且坚定，认真地提出自己的想法，旁边两位中年男子站着，眉头微皱，一脸惊讶且思索地看着他。青年男子穿着高领圆领的紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带并佩戴玉佩，圆形衣领，黑色束发；其中一位中年男子微胖，穿着高领圆领的深灰色官服，搭配金色，有金色丝线勾勒图案，头戴黑色官帽，配黑色腰带，圆形衣领，黑色长发束起；另一位中等身材的中年男子穿着高领圆领的棕色官服，搭配白色，衣摆有白色镶边，头戴黑色小冠，配棕色腰带，圆形衣领，黑色长发束起, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753202215_20250723_003655.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_4ae70196-893f-4ac2-8d31-13b077b28bc0_1.25x_20250722_202129.wav", "audio_duration": 17.896, "video_url": "", "story_board_id": 18, "subtitle": [{"subtitle_id": 1, "text": "他没有直接拒绝任何一项提议，", "timestamp": "00:00:00,160 --> 00:00:02,080", "duration": 1.92, "char_count": 14, "start_time_s": 0.16, "end_time_s": 2.08, "words": [{"attribute": {"event": "speech"}, "end_time": 240, "start_time": 160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 360, "start_time": 240, "text": "没"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 360, "text": "有"}, {"attribute": {"event": "speech"}, "end_time": 640, "start_time": 480, "text": "直"}, {"attribute": {"event": "speech"}, "end_time": 800, "start_time": 640, "text": "接"}, {"attribute": {"event": "speech"}, "end_time": 960, "start_time": 800, "text": "拒"}, {"attribute": {"event": "speech"}, "end_time": 1120, "start_time": 960, "text": "绝"}, {"attribute": {"event": "speech"}, "end_time": 1280, "start_time": 1120, "text": "任"}, {"attribute": {"event": "speech"}, "end_time": 1400, "start_time": 1280, "text": "何"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1400, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1520, "text": "项"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1680, "text": "提"}, {"attribute": {"event": "speech"}, "end_time": 2080, "start_time": 1840, "text": "议"}], "keyword": "他"}, {"subtitle_id": 2, "text": "而是提出了自己的想法", "timestamp": "00:00:02,080 --> 00:00:03,760", "duration": 1.68, "char_count": 10, "start_time_s": 2.08, "end_time_s": 3.76, "words": [{"attribute": {"event": "speech"}, "end_time": 2280, "start_time": 2080, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 2400, "start_time": 2280, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 2560, "start_time": 2400, "text": "提"}, {"attribute": {"event": "speech"}, "end_time": 2720, "start_time": 2560, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 2800, "start_time": 2720, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 2960, "start_time": 2800, "text": "自"}, {"attribute": {"event": "speech"}, "end_time": 3080, "start_time": 2960, "text": "己"}, {"attribute": {"event": "speech"}, "end_time": 3200, "start_time": 3080, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3200, "text": "想"}, {"attribute": {"event": "speech"}, "end_time": 3760, "start_time": 3360, "text": "法"}], "keyword": "想法"}, {"subtitle_id": 3, "text": "首先，优先保障民生项目，", "timestamp": "00:00:03,760 --> 00:00:05,640", "duration": 1.88, "char_count": 12, "start_time_s": 3.76, "end_time_s": 5.64, "words": [{"attribute": {"event": "speech"}, "end_time": 3960, "start_time": 3760, "text": "首"}, {"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 3960, "text": "先"}, {"attribute": {"event": "speech"}, "end_time": 4480, "start_time": 4320, "text": "优"}, {"attribute": {"event": "speech"}, "end_time": 4600, "start_time": 4480, "text": "先"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4640, "text": "保"}, {"attribute": {"event": "speech"}, "end_time": 4920, "start_time": 4800, "text": "障"}, {"attribute": {"event": "speech"}, "end_time": 5080, "start_time": 4920, "text": "民"}, {"attribute": {"event": "speech"}, "end_time": 5280, "start_time": 5120, "text": "生"}, {"attribute": {"event": "speech"}, "end_time": 5440, "start_time": 5280, "text": "项"}, {"attribute": {"event": "speech"}, "end_time": 5640, "start_time": 5440, "text": "目"}], "keyword": "民生项目"}, {"subtitle_id": 4, "text": "比如水利、农业和教育", "timestamp": "00:00:05,640 --> 00:00:07,240", "duration": 1.6, "char_count": 10, "start_time_s": 5.64, "end_time_s": 7.24, "words": [{"attribute": {"event": "speech"}, "end_time": 5800, "start_time": 5640, "text": "比"}, {"attribute": {"event": "speech"}, "end_time": 5920, "start_time": 5800, "text": "如"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 5920, "text": "水"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6120, "text": "利"}, {"attribute": {"event": "speech"}, "end_time": 6400, "start_time": 6280, "text": "农"}, {"attribute": {"event": "speech"}, "end_time": 6560, "start_time": 6400, "text": "业"}, {"attribute": {"event": "speech"}, "end_time": 6680, "start_time": 6560, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 6880, "start_time": 6680, "text": "教"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 6880, "text": "育"}], "keyword": "水利"}, {"subtitle_id": 5, "text": "其次，对军事和边防也不能放松，", "timestamp": "00:00:07,240 --> 00:00:09,600", "duration": 2.36, "char_count": 15, "start_time_s": 7.24, "end_time_s": 9.6, "words": [{"attribute": {"event": "speech"}, "end_time": 7440, "start_time": 7240, "text": "其"}, {"attribute": {"event": "speech"}, "end_time": 7600, "start_time": 7440, "text": "次"}, {"attribute": {"event": "speech"}, "end_time": 7920, "start_time": 7760, "text": "对"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 7960, "text": "军"}, {"attribute": {"event": "speech"}, "end_time": 8280, "start_time": 8120, "text": "事"}, {"attribute": {"event": "speech"}, "end_time": 8400, "start_time": 8280, "text": "和"}, {"attribute": {"event": "speech"}, "end_time": 8560, "start_time": 8400, "text": "边"}, {"attribute": {"event": "speech"}, "end_time": 8720, "start_time": 8560, "text": "防"}, {"attribute": {"event": "speech"}, "end_time": 8880, "start_time": 8760, "text": "也"}, {"attribute": {"event": "speech"}, "end_time": 8960, "start_time": 8880, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 9080, "start_time": 8960, "text": "能"}, {"attribute": {"event": "speech"}, "end_time": 9240, "start_time": 9080, "text": "放"}, {"attribute": {"event": "speech"}, "end_time": 9600, "start_time": 9280, "text": "松"}], "keyword": "军事"}, {"subtitle_id": 6, "text": "但要控制开支", "timestamp": "00:00:09,600 --> 00:00:10,800", "duration": 1.2, "char_count": 6, "start_time_s": 9.6, "end_time_s": 10.8, "words": [{"attribute": {"event": "speech"}, "end_time": 9760, "start_time": 9600, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 9880, "start_time": 9760, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 10080, "start_time": 9920, "text": "控"}, {"attribute": {"event": "speech"}, "end_time": 10200, "start_time": 10080, "text": "制"}, {"attribute": {"event": "speech"}, "end_time": 10400, "start_time": 10200, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 10800, "start_time": 10400, "text": "支"}], "keyword": "开支"}, {"subtitle_id": 7, "text": "最后，税务改革要稳步推进，", "timestamp": "00:00:10,800 --> 00:00:12,840", "duration": 2.04, "char_count": 13, "start_time_s": 10.8, "end_time_s": 12.84, "words": [{"attribute": {"event": "speech"}, "end_time": 10960, "start_time": 10800, "text": "最"}, {"attribute": {"event": "speech"}, "end_time": 11120, "start_time": 10960, "text": "后"}, {"attribute": {"event": "speech"}, "end_time": 11400, "start_time": 11280, "text": "税"}, {"attribute": {"event": "speech"}, "end_time": 11480, "start_time": 11400, "text": "务"}, {"attribute": {"event": "speech"}, "end_time": 11680, "start_time": 11480, "text": "改"}, {"attribute": {"event": "speech"}, "end_time": 11840, "start_time": 11680, "text": "革"}, {"attribute": {"event": "speech"}, "end_time": 12000, "start_time": 11840, "text": "要"}, {"attribute": {"event": "speech"}, "end_time": 12200, "start_time": 12040, "text": "稳"}, {"attribute": {"event": "speech"}, "end_time": 12320, "start_time": 12200, "text": "步"}, {"attribute": {"event": "speech"}, "end_time": 12480, "start_time": 12320, "text": "推"}, {"attribute": {"event": "speech"}, "end_time": 12840, "start_time": 12520, "text": "进"}], "keyword": "税务改革"}, {"subtitle_id": 8, "text": "确保公平合理。", "timestamp": "00:00:12,840 --> 00:00:14,180", "duration": 1.34, "char_count": 7, "start_time_s": 12.84, "end_time_s": 14.18, "words": [{"attribute": {"event": "speech"}, "end_time": 13000, "start_time": 12840, "text": "确"}, {"attribute": {"event": "speech"}, "end_time": 13160, "start_time": 13000, "text": "保"}, {"attribute": {"event": "speech"}, "end_time": 13320, "start_time": 13160, "text": "公"}, {"attribute": {"event": "speech"}, "end_time": 13520, "start_time": 13360, "text": "平"}, {"attribute": {"event": "speech"}, "end_time": 13680, "start_time": 13520, "text": "合"}, {"attribute": {"event": "speech"}, "end_time": 14180, "start_time": 13680, "text": "理"}], "keyword": "公平"}, {"subtitle_id": 9, "text": "朝堂上的气氛一时凝固，", "timestamp": "00:00:14,320 --> 00:00:16,040", "duration": 1.72, "char_count": 11, "start_time_s": 14.32, "end_time_s": 16.04, "words": [{"attribute": {"event": "speech"}, "end_time": 14520, "start_time": 14320, "text": "朝"}, {"attribute": {"event": "speech"}, "end_time": 14680, "start_time": 14520, "text": "堂"}, {"attribute": {"event": "speech"}, "end_time": 14800, "start_time": 14680, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 14920, "start_time": 14800, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 15080, "start_time": 14920, "text": "气"}, {"attribute": {"event": "speech"}, "end_time": 15200, "start_time": 15080, "text": "氛"}, {"attribute": {"event": "speech"}, "end_time": 15400, "start_time": 15200, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 15520, "start_time": 15400, "text": "时"}, {"attribute": {"event": "speech"}, "end_time": 15680, "start_time": 15520, "text": "凝"}, {"attribute": {"event": "speech"}, "end_time": 16040, "start_time": 15720, "text": "固"}], "keyword": "朝堂"}, {"subtitle_id": 10, "text": "大臣们面面相觑。", "timestamp": "00:00:16,040 --> 00:00:17,620", "duration": 1.58, "char_count": 8, "start_time_s": 16.04, "end_time_s": 17.62, "words": [{"attribute": {"event": "speech"}, "end_time": 16280, "start_time": 16040, "text": "大"}, {"attribute": {"event": "speech"}, "end_time": 16440, "start_time": 16280, "text": "臣"}, {"attribute": {"event": "speech"}, "end_time": 16560, "start_time": 16440, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 16600, "start_time": 16560, "text": "面"}, {"attribute": {"event": "speech"}, "end_time": 16920, "start_time": 16600, "text": "面"}, {"attribute": {"event": "speech"}, "end_time": 17120, "start_time": 16920, "text": "相"}, {"attribute": {"event": "speech"}, "end_time": 17620, "start_time": 17120, "text": "觑"}], "keyword": "大臣"}], "keywords": ["他", "想法", "民生项目", "水利", "军事", "开支", "税务改革", "公平", "朝堂", "大臣"]}, {"chapter": 79, "story_board": "他们知道，公子华不是在敷衍，而是在认真考虑每一件事。这场朝会持续了很久，直到太阳高高升起。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "坐在朝堂上认真倾听大臣汇报，随后站起身逐一回应提议", "expression": "严肃、认真"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "郑国渠", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为蓝色，搭配灰色，上有灰色线条装饰，头戴黑色方巾，可配蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "水利部官员", "other": "面容朴实，神情专注", "from_chapter": [0], "action": "站在朝堂上，表情认真地向众人陈述", "expression": "严肃"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "站在朝堂上，表情诚恳地向众人提出建议", "expression": "认真"}, {"name": "冯去疾", "gender": "男", "age": "中年", "clothes": "高领圆领绿色朝服，主色调绿色，搭配白色，有树叶纹，头戴乌纱帽，腰间系绿丝带，配玉坠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "适中", "identity": "秦国大臣", "other": "", "from_chapter": 67, "action": "站在朝堂上陈述", "expression": "认真"}, {"name": "财政部长", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为深灰色，搭配金色，有金色丝线勾勒图案，头戴黑色官帽，可配黑色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色官帽", "figure": "微胖", "identity": "财政部官员", "other": "眉头微皱，忧心忡忡", "from_chapter": [0], "action": "站在朝堂上，听公子华说话，面面相觑", "expression": "惊讶、思索"}, {"name": "税务部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为棕色，搭配白色，衣摆有白色镶边，头戴黑色小冠，可配棕色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色小冠", "figure": "中等身材", "identity": "税务部官员", "other": "神情严肃", "from_chapter": [0], "action": "站在朝堂上，听公子华说话，面面相觑", "expression": "惊讶、思索"}, {"name": "兵部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服铁黑色铠甲，搭配红色，外罩红色披风，头戴黑色头盔，可配红色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色头盔", "figure": "魁梧", "identity": "兵部官员", "other": "面容刚毅", "from_chapter": [0], "action": "站在朝堂上，语气严肃地汇报", "expression": "严肃"}, {"name": "工部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为土黄色，搭配绿色，袖口绣绿色花纹，头戴黑色斗笠状官帽，可配黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色斗笠状官帽", "figure": "身材壮实", "identity": "工部官员", "other": "皮肤黝黑", "from_chapter": [0], "action": "站在朝堂上，神情严肃地陈述计划", "expression": "认真"}, {"name": "战备部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为藏青色，搭配蓝色，配有蓝色腰带，头戴黑色毡帽，可配藏青色香囊圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色毡帽", "figure": "中等身材", "identity": "战备部官员", "other": "目光沉稳", "from_chapter": [0], "action": "站在朝堂上，有条不紊地说出需求", "expression": "沉稳"}], "scene": "皇宫大殿", "image_prompt": "中景，在皇宫大殿中，一位先是坐在朝堂上认真倾听，随后站起身逐一回应提议，表情严肃认真、言辞犀利、敢言善辩且挺拔的青年男子，身着高领圆领、主色调为紫色搭配白色、绣有云纹的汉服紫色锦袍，腰间束黑色腰带并佩戴玉佩，黑色束发, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753202411_20250723_004011.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_27ee2473-e7dd-4ebe-a65d-8f5d15d06514_1.25x_20250722_202117.wav", "audio_duration": 8.104, "video_url": "", "story_board_id": 19, "subtitle": [{"subtitle_id": 1, "text": "他们知道，", "timestamp": "00:00:00,160 --> 00:00:00,840", "duration": 0.68, "char_count": 5, "start_time_s": 0.16, "end_time_s": 0.84, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 360, "start_time": 280, "text": "们"}, {"attribute": {"event": "speech"}, "end_time": 480, "start_time": 360, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 840, "start_time": 480, "text": "道"}], "keyword": "他们"}, {"subtitle_id": 2, "text": "龚子华不是在敷衍，", "timestamp": "00:00:00,840 --> 00:00:02,320", "duration": 1.48, "char_count": 9, "start_time_s": 0.84, "end_time_s": 2.32, "words": [{"attribute": {"event": "speech"}, "end_time": 1000, "start_time": 840, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 1160, "start_time": 1000, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 1320, "start_time": 1160, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1320, "text": "不"}, {"attribute": {"event": "speech"}, "end_time": 1640, "start_time": 1520, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 1760, "start_time": 1640, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 1920, "start_time": 1760, "text": "敷"}, {"attribute": {"event": "speech"}, "end_time": 2320, "start_time": 1960, "text": "衍"}], "keyword": "龚子华"}, {"subtitle_id": 3, "text": "而是在认真考虑每一件事。", "timestamp": "00:00:02,320 --> 00:00:04,380", "duration": 2.06, "char_count": 12, "start_time_s": 2.32, "end_time_s": 4.38, "words": [{"attribute": {"event": "speech"}, "end_time": 2520, "start_time": 2320, "text": "而"}, {"attribute": {"event": "speech"}, "end_time": 2640, "start_time": 2520, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 2760, "start_time": 2640, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 2920, "start_time": 2760, "text": "认"}, {"attribute": {"event": "speech"}, "end_time": 3040, "start_time": 2920, "text": "真"}, {"attribute": {"event": "speech"}, "end_time": 3240, "start_time": 3080, "text": "考"}, {"attribute": {"event": "speech"}, "end_time": 3360, "start_time": 3240, "text": "虑"}, {"attribute": {"event": "speech"}, "end_time": 3520, "start_time": 3400, "text": "每"}, {"attribute": {"event": "speech"}, "end_time": 3680, "start_time": 3520, "text": "一"}, {"attribute": {"event": "speech"}, "end_time": 3840, "start_time": 3680, "text": "件"}, {"attribute": {"event": "speech"}, "end_time": 4380, "start_time": 3880, "text": "事"}], "keyword": "考虑"}, {"subtitle_id": 4, "text": "这场朝会持续了很久，", "timestamp": "00:00:04,440 --> 00:00:06,160", "duration": 1.72, "char_count": 10, "start_time_s": 4.44, "end_time_s": 6.16, "words": [{"attribute": {"event": "speech"}, "end_time": 4640, "start_time": 4440, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 4800, "start_time": 4640, "text": "场"}, {"attribute": {"event": "speech"}, "end_time": 4960, "start_time": 4800, "text": "朝"}, {"attribute": {"event": "speech"}, "end_time": 5200, "start_time": 5000, "text": "会"}, {"attribute": {"event": "speech"}, "end_time": 5400, "start_time": 5200, "text": "持"}, {"attribute": {"event": "speech"}, "end_time": 5480, "start_time": 5400, "text": "续"}, {"attribute": {"event": "speech"}, "end_time": 5600, "start_time": 5480, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 5760, "start_time": 5600, "text": "很"}, {"attribute": {"event": "speech"}, "end_time": 6160, "start_time": 5800, "text": "久"}], "keyword": "朝会"}, {"subtitle_id": 5, "text": "直到太阳高高升起。", "timestamp": "00:00:06,160 --> 00:00:07,780", "duration": 1.62, "char_count": 9, "start_time_s": 6.16, "end_time_s": 7.78, "words": [{"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6160, "text": "直"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6280, "text": "到"}, {"attribute": {"event": "speech"}, "end_time": 6600, "start_time": 6440, "text": "太"}, {"attribute": {"event": "speech"}, "end_time": 6760, "start_time": 6600, "text": "阳"}, {"attribute": {"event": "speech"}, "end_time": 6800, "start_time": 6760, "text": "高"}, {"attribute": {"event": "speech"}, "end_time": 7160, "start_time": 6800, "text": "高"}, {"attribute": {"event": "speech"}, "end_time": 7320, "start_time": 7160, "text": "升"}, {"attribute": {"event": "speech"}, "end_time": 7780, "start_time": 7320, "text": "起"}], "keyword": "太阳"}], "keywords": ["他们", "龚子华", "考虑", "朝会", "太阳"]}, {"chapter": 79, "story_board": "公子华走出皇宫，站在台阶上，望着远方的天空。他知道，这只是开始，未来的路还很长，但他已经做好了准备。", "characters": [{"name": "公子华", "gender": "男", "age": "青年", "clothes": "高领圆领汉服紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束黑色腰带，佩戴玉佩圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "挺拔", "identity": "大秦公子", "other": "言辞犀利，敢言善辩", "from_chapter": 4, "action": "走出皇宫，站在台阶上，望着远方的天空", "expression": "坚定"}, {"name": "王离", "gender": "男", "age": "少年", "clothes": "高领圆领秦代汉服劲装，主色调为蓝色，搭配白色，绣有简单几何纹样，系蓝色腰带，头戴蓝色束发带圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "矫健", "identity": "与公子华相识的少年", "other": "活泼好动，对新鲜事物充满好奇", "from_chapter": 7, "action": "", "expression": ""}, {"name": "郑国渠", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为蓝色，搭配灰色，上有灰色线条装饰，头戴黑色方巾，可配蓝色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色方巾", "figure": "中等身材", "identity": "水利部官员", "other": "面容朴实，神情专注", "from_chapter": [0], "action": "站在朝堂上，表情认真地向众人陈述", "expression": "严肃"}, {"name": "乌倮氏", "gender": "男", "age": "中年", "clothes": "高领圆领秦代商人服饰，主色调为灰色，搭配白色，有铜钱纹图案，腰束布带，佩戴玛瑙珠子圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "富态", "identity": "以经商闻名的巨贾", "other": "精明", "from_chapter": 16, "action": "站在朝堂上，表情诚恳地向众人提出建议", "expression": "认真"}, {"name": "冯去疾", "gender": "男", "age": "中年", "clothes": "高领圆领绿色朝服，主色调绿色，搭配白色，有树叶纹，头戴乌纱帽，腰间系绿丝带，配玉坠圆形衣领，圆形衣领", "hairstyle": "黑色束发", "figure": "适中", "identity": "秦国大臣", "other": "", "from_chapter": 67, "action": "站在朝堂上陈述", "expression": "认真"}, {"name": "财政部长", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为深灰色，搭配金色，有金色丝线勾勒图案，头戴黑色官帽，可配黑色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色官帽", "figure": "微胖", "identity": "财政部官员", "other": "眉头微皱，忧心忡忡", "from_chapter": [0], "action": "站在朝堂上，听公子华说话，面面相觑", "expression": "惊讶、思索"}, {"name": "税务部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为棕色，搭配白色，衣摆有白色镶边，头戴黑色小冠，可配棕色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色小冠", "figure": "中等身材", "identity": "税务部官员", "other": "神情严肃", "from_chapter": [0], "action": "站在朝堂上，听公子华说话，面面相觑", "expression": "惊讶、思索"}, {"name": "兵部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服铁黑色铠甲，搭配红色，外罩红色披风，头戴黑色头盔，可配红色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色头盔", "figure": "魁梧", "identity": "兵部官员", "other": "面容刚毅", "from_chapter": [0], "action": "站在朝堂上，语气严肃地汇报", "expression": "严肃"}, {"name": "工部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为土黄色，搭配绿色，袖口绣绿色花纹，头戴黑色斗笠状官帽，可配黄色腰带圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色斗笠状官帽", "figure": "身材壮实", "identity": "工部官员", "other": "皮肤黝黑", "from_chapter": [0], "action": "站在朝堂上，神情严肃地陈述计划", "expression": "认真"}, {"name": "战备部官员", "gender": "男", "age": "中年", "clothes": "高领圆领汉服官服，主色调为藏青色，搭配蓝色，配有蓝色腰带，头戴黑色毡帽，可配藏青色香囊圆形衣领，圆形衣领", "hairstyle": "黑色长发束起，头戴黑色毡帽", "figure": "中等身材", "identity": "战备部官员", "other": "目光沉稳", "from_chapter": [0], "action": "站在朝堂上，有条不紊地说出需求", "expression": "沉稳"}], "scene": "皇宫大殿", "image_prompt": "中景，一位神情坚定、挺拔的青年男子走出皇宫，站在台阶上望着远方的天空。男子身着高领圆领的紫色锦袍，主色调为紫色，搭配白色，绣有云纹，腰间束着黑色腰带并佩戴玉佩，黑色束发，皇宫大殿作为背景, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，", "image_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/images/image_1753202571_20250723_004251.jpg", "audio_url": "https://ai-novel-wujiejvzhen.tos-cn-beijing.volces.com/audios/chapter_audio_4fca443e-65dc-4088-8d28-8e3952fc274e_1.25x_20250722_202109.wav", "audio_duration": 8.914667, "video_url": "", "story_board_id": 20, "subtitle": [{"subtitle_id": 1, "text": "龚子华走出皇宫，", "timestamp": "00:00:00,160 --> 00:00:01,360", "duration": 1.2, "char_count": 8, "start_time_s": 0.16, "end_time_s": 1.36, "words": [{"attribute": {"event": "speech"}, "end_time": 280, "start_time": 160, "text": "龚"}, {"attribute": {"event": "speech"}, "end_time": 440, "start_time": 280, "text": "子"}, {"attribute": {"event": "speech"}, "end_time": 600, "start_time": 440, "text": "华"}, {"attribute": {"event": "speech"}, "end_time": 760, "start_time": 640, "text": "走"}, {"attribute": {"event": "speech"}, "end_time": 880, "start_time": 760, "text": "出"}, {"attribute": {"event": "speech"}, "end_time": 1040, "start_time": 920, "text": "皇"}, {"attribute": {"event": "speech"}, "end_time": 1360, "start_time": 1080, "text": "宫"}], "keyword": "龚子华"}, {"subtitle_id": 2, "text": "站在台阶上望着远方的天空，", "timestamp": "00:00:01,360 --> 00:00:03,820", "duration": 2.46, "char_count": 13, "start_time_s": 1.36, "end_time_s": 3.82, "words": [{"attribute": {"event": "speech"}, "end_time": 1520, "start_time": 1360, "text": "站"}, {"attribute": {"event": "speech"}, "end_time": 1680, "start_time": 1520, "text": "在"}, {"attribute": {"event": "speech"}, "end_time": 1840, "start_time": 1680, "text": "台"}, {"attribute": {"event": "speech"}, "end_time": 2000, "start_time": 1880, "text": "阶"}, {"attribute": {"event": "speech"}, "end_time": 2200, "start_time": 2040, "text": "上"}, {"attribute": {"event": "speech"}, "end_time": 2520, "start_time": 2360, "text": "望"}, {"attribute": {"event": "speech"}, "end_time": 2680, "start_time": 2560, "text": "着"}, {"attribute": {"event": "speech"}, "end_time": 2840, "start_time": 2680, "text": "远"}, {"attribute": {"event": "speech"}, "end_time": 3000, "start_time": 2880, "text": "方"}, {"attribute": {"event": "speech"}, "end_time": 3120, "start_time": 3000, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 3320, "start_time": 3120, "text": "天"}, {"attribute": {"event": "speech"}, "end_time": 3820, "start_time": 3320, "text": "空"}], "keyword": "台阶"}, {"subtitle_id": 3, "text": "他知道这只是开始，", "timestamp": "00:00:03,960 --> 00:00:05,560", "duration": 1.6, "char_count": 9, "start_time_s": 3.96, "end_time_s": 5.56, "words": [{"attribute": {"event": "speech"}, "end_time": 4120, "start_time": 3960, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 4240, "start_time": 4120, "text": "知"}, {"attribute": {"event": "speech"}, "end_time": 4400, "start_time": 4240, "text": "道"}, {"attribute": {"event": "speech"}, "end_time": 4760, "start_time": 4600, "text": "这"}, {"attribute": {"event": "speech"}, "end_time": 4880, "start_time": 4760, "text": "只"}, {"attribute": {"event": "speech"}, "end_time": 5000, "start_time": 4880, "text": "是"}, {"attribute": {"event": "speech"}, "end_time": 5160, "start_time": 5000, "text": "开"}, {"attribute": {"event": "speech"}, "end_time": 5560, "start_time": 5160, "text": "始"}], "keyword": "开始"}, {"subtitle_id": 4, "text": "未来的路还很长，", "timestamp": "00:00:05,560 --> 00:00:06,920", "duration": 1.36, "char_count": 8, "start_time_s": 5.56, "end_time_s": 6.92, "words": [{"attribute": {"event": "speech"}, "end_time": 5760, "start_time": 5560, "text": "未"}, {"attribute": {"event": "speech"}, "end_time": 5880, "start_time": 5760, "text": "来"}, {"attribute": {"event": "speech"}, "end_time": 6000, "start_time": 5880, "text": "的"}, {"attribute": {"event": "speech"}, "end_time": 6120, "start_time": 6000, "text": "路"}, {"attribute": {"event": "speech"}, "end_time": 6280, "start_time": 6160, "text": "还"}, {"attribute": {"event": "speech"}, "end_time": 6440, "start_time": 6280, "text": "很"}, {"attribute": {"event": "speech"}, "end_time": 6920, "start_time": 6480, "text": "长"}], "keyword": "路"}, {"subtitle_id": 5, "text": "但他已经做好了准备。", "timestamp": "00:00:06,920 --> 00:00:08,540", "duration": 1.62, "char_count": 10, "start_time_s": 6.92, "end_time_s": 8.54, "words": [{"attribute": {"event": "speech"}, "end_time": 7080, "start_time": 6920, "text": "但"}, {"attribute": {"event": "speech"}, "end_time": 7240, "start_time": 7080, "text": "他"}, {"attribute": {"event": "speech"}, "end_time": 7360, "start_time": 7240, "text": "已"}, {"attribute": {"event": "speech"}, "end_time": 7480, "start_time": 7360, "text": "经"}, {"attribute": {"event": "speech"}, "end_time": 7640, "start_time": 7480, "text": "做"}, {"attribute": {"event": "speech"}, "end_time": 7840, "start_time": 7640, "text": "好"}, {"attribute": {"event": "speech"}, "end_time": 7960, "start_time": 7840, "text": "了"}, {"attribute": {"event": "speech"}, "end_time": 8120, "start_time": 7960, "text": "准"}, {"attribute": {"event": "speech"}, "end_time": 8540, "start_time": 8120, "text": "备"}], "keyword": "准备"}], "keywords": ["龚子华", "台阶", "开始", "路", "准备"]}]