2025-07-23 16:44:57,726 - INFO - 日志文件已创建: output/novel_step2/大秦二世公子华_去重/process_log.log
2025-07-23 16:44:57,841 - INFO - after-request: head_bucket exec httpCode: 200, requestId: fa0e0180a109e2776880a109-b91a91c-1ueV5t-HB-cb-tos-1az-front-aza-1, usedTime: 0.11069241689983755 s
2025-07-23 16:44:57,841 - INFO - 存储桶 ai-novel-wuji<PERSON>vzhen 已存在
2025-07-23 16:44:57,876 - INFO - after-request: put_object exec httpCode: 200, requestId: fa0e0180a109e27a6880a109-b91a91c-1ueV5t-PuO-cb-tos-1az-front-aza-1, usedTime: 0.03426220908295363 s
2025-07-23 16:44:57,877 - INFO - 创建文件夹: images/
2025-07-23 16:44:57,899 - INFO - after-request: put_object exec httpCode: 200, requestId: fa0e0180a109e27f6880a109-b91a91c-1ueV5t-PuO-cb-tos-1az-front-aza-1, usedTime: 0.021891959011554718 s
2025-07-23 16:44:57,899 - INFO - 创建文件夹: audios/
2025-07-23 16:44:57,920 - INFO - after-request: put_object exec httpCode: 200, requestId: fa0e0180a10ae2846880a10a-b91a91c-1ueV5u-PuO-cb-tos-1az-front-aza-1, usedTime: 0.020371209015138447 s
2025-07-23 16:44:57,920 - INFO - 创建文件夹: videos/
2025-07-23 16:45:27,937 - INFO - 表 character_info 创建成功
2025-07-23 16:45:27,939 - INFO - 表 draw_style 创建成功
2025-07-23 16:45:27,939 - INFO - 创建了以下数据表: character_info, draw_style
2025-07-23 16:45:27,940 - INFO - 数据库初始化完成: output/novel_step2/大秦二世公子华_去重/novel_setting.db
2025-07-23 16:45:27,940 - INFO - 数据库包含 3 个表，总记录数: 0
2025-07-23 16:45:27,940 - INFO - ==================================================
2025-07-23 16:45:27,940 - INFO - 小说分镜画面生成系统启动
2025-07-23 16:45:27,940 - INFO - 小说目录: output/novel_step1/大秦二世公子华_去重
2025-07-23 16:45:27,940 - INFO - 输出目录: output/novel_step2/大秦二世公子华_去重
2025-07-23 16:45:27,940 - INFO - 数据库路径: output/novel_step2/大秦二世公子华_去重/novel_setting.db
2025-07-23 16:45:27,940 - INFO - ==================================================
2025-07-23 16:45:27,941 - INFO - 开始处理小说: 大秦二世公子华_去重
2025-07-23 16:45:27,950 - INFO - 📋 章节 1 已完成，跳过处理
2025-07-23 16:45:27,955 - INFO - 📋 章节 4 已完成，跳过处理
2025-07-23 16:45:27,960 - INFO - 📋 章节 7 已完成，跳过处理
2025-07-23 16:45:27,964 - INFO - 📋 章节 10 已完成，跳过处理
2025-07-23 16:45:27,967 - INFO - 📋 章节 13 已完成，跳过处理
2025-07-23 16:45:27,970 - INFO - 📋 章节 16 已完成，跳过处理
2025-07-23 16:45:27,975 - INFO - 📋 章节 19 已完成，跳过处理
2025-07-23 16:45:27,978 - INFO - 📋 章节 21 已完成，跳过处理
2025-07-23 16:45:27,980 - INFO - 📋 章节 24 已完成，跳过处理
2025-07-23 16:45:27,983 - INFO - 📋 章节 28 已完成，跳过处理
2025-07-23 16:45:27,985 - INFO - 📋 章节 31 已完成，跳过处理
2025-07-23 16:45:27,988 - INFO - 📋 章节 34 已完成，跳过处理
2025-07-23 16:45:27,990 - INFO - 📋 章节 37 已完成，跳过处理
2025-07-23 16:45:27,993 - INFO - 📋 章节 40 已完成，跳过处理
2025-07-23 16:45:27,995 - INFO - 📋 章节 43 已完成，跳过处理
2025-07-23 16:45:27,998 - INFO - 📋 章节 46 已完成，跳过处理
2025-07-23 16:45:28,000 - INFO - 📋 章节 49 已完成，跳过处理
2025-07-23 16:45:28,003 - INFO - 📋 章节 52 已完成，跳过处理
2025-07-23 16:45:28,005 - INFO - 📋 章节 55 已完成，跳过处理
2025-07-23 16:45:28,007 - INFO - 📋 章节 58 已完成，跳过处理
2025-07-23 16:45:28,009 - INFO - 📋 章节 61 已完成，跳过处理
2025-07-23 16:45:28,011 - INFO - 📋 章节 64 已完成，跳过处理
2025-07-23 16:45:28,014 - INFO - 📋 章节 67 已完成，跳过处理
2025-07-23 16:45:28,017 - INFO - 📋 章节 70 已完成，跳过处理
2025-07-23 16:45:28,020 - INFO - 📋 章节 73 已完成，跳过处理
2025-07-23 16:45:28,022 - INFO - 📋 章节 76 已完成，跳过处理
2025-07-23 16:45:28,024 - INFO - 📋 章节 79 已完成，跳过处理
2025-07-23 16:45:28,027 - INFO - 📋 章节 82 已完成，跳过处理
2025-07-23 16:45:28,029 - INFO - 📋 章节 85 已完成，跳过处理
2025-07-23 16:45:28,031 - INFO - 📋 章节 88 已完成，跳过处理
2025-07-23 16:45:28,033 - INFO - 📋 章节 91 已完成，跳过处理
2025-07-23 16:45:28,036 - INFO - 📋 章节 94 已完成，跳过处理
2025-07-23 16:45:28,038 - INFO - 📋 章节 97 已完成，跳过处理
2025-07-23 16:45:28,040 - INFO - 📋 章节 100 已完成，跳过处理
2025-07-23 16:45:28,042 - INFO - 📋 章节 103 已完成，跳过处理
2025-07-23 16:45:28,045 - INFO - 📋 章节 106 已完成，跳过处理
2025-07-23 16:45:28,047 - INFO - 📋 章节 109 已完成，跳过处理
2025-07-23 16:45:28,049 - INFO - 📋 章节 112 已完成，跳过处理
2025-07-23 16:45:28,052 - INFO - 📋 章节 115 已完成，跳过处理
2025-07-23 16:45:28,054 - INFO - 📋 章节 118 已完成，跳过处理
2025-07-23 16:45:28,056 - INFO - 📋 章节 121 已完成，跳过处理
2025-07-23 16:45:28,058 - INFO - 📋 章节 124 已完成，跳过处理
2025-07-23 16:45:28,061 - INFO - 📋 章节 127 已完成，跳过处理
2025-07-23 16:45:28,063 - INFO - 📋 章节 130 已完成，跳过处理
2025-07-23 16:45:28,065 - INFO - 📋 章节 133 已完成，跳过处理
2025-07-23 16:45:28,067 - INFO - 📋 章节 136 已完成，跳过处理
2025-07-23 16:45:28,070 - INFO - 📋 章节 139 已完成，跳过处理
2025-07-23 16:45:28,072 - INFO - 📋 章节 140 已完成，跳过处理
2025-07-23 16:45:28,075 - INFO - 📋 章节 142 已完成，跳过处理
2025-07-23 16:45:28,078 - INFO - 📋 章节 145 已完成，跳过处理
2025-07-23 16:45:28,081 - INFO - 📋 章节 148 已完成，跳过处理
2025-07-23 16:45:28,083 - INFO - 📋 章节 150 已完成，跳过处理
2025-07-23 16:45:28,085 - INFO - 📋 章节 154 已完成，跳过处理
2025-07-23 16:45:28,088 - INFO - 📋 章节 157 已完成，跳过处理
2025-07-23 16:45:28,090 - INFO - 📋 章节 160 已完成，跳过处理
2025-07-23 16:45:28,093 - INFO - 📋 章节 163 已完成，跳过处理
2025-07-23 16:45:28,095 - INFO - 📋 章节 166 已完成，跳过处理
2025-07-23 16:45:28,097 - INFO - 📋 章节 169 已完成，跳过处理
2025-07-23 16:45:28,100 - INFO - 📋 章节 172 已完成，跳过处理
2025-07-23 16:45:28,118 - INFO - 📋 章节 175 已完成，跳过处理
2025-07-23 16:45:28,121 - INFO - 📋 章节 178 已完成，跳过处理
2025-07-23 16:45:33,215 - INFO - 📚 开始处理 1 个章节（异步并发）
2025-07-23 16:45:33,216 - INFO - 🎬 开始处理第22章
2025-07-23 16:45:33,220 - INFO - 当前章节共 13 个场景
2025-07-23 16:45:33,220 - INFO - 数据库中未找到画风信息
2025-07-23 16:45:33,220 - INFO - 从表 draw_style 删除 0 行
2025-07-23 16:45:33,221 - INFO - 向表 draw_style 插入 1 条记录
2025-07-23 16:45:33,221 - INFO - 画风信息已保存到数据库: 仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，
2025-07-23 16:45:33,227 - INFO - 场景信息已保存到 output/novel_step2/大秦二世公子华_去重/chapter22_忽悠熊犹/scene_info.json
2025-07-23 16:45:33,228 - INFO - 📝 第22章 - 分镜 1/20
2025-07-23 16:45:58,118 - INFO - 日志文件已创建: output/novel_step2/大秦二世公子华_去重/process_log.log
2025-07-23 16:45:58,248 - INFO - after-request: head_bucket exec httpCode: 200, requestId: 517f0180a146ba396880a146-b7a157a-1ueV6s-HB-cb-tos-1az-front-azc-2, usedTime: 0.12553408404346555 s
2025-07-23 16:45:58,248 - INFO - 存储桶 ai-novel-wujiejvzhen 已存在
2025-07-23 16:45:58,283 - INFO - after-request: put_object exec httpCode: 200, requestId: 517f0180a146ba3d6880a146-b7a157a-1ueV6s-PuO-cb-tos-1az-front-azc-2, usedTime: 0.034316916950047016 s
2025-07-23 16:45:58,283 - INFO - 创建文件夹: images/
2025-07-23 16:45:58,308 - INFO - after-request: put_object exec httpCode: 200, requestId: 517f0180a146ba416880a146-b7a157a-1ueV6s-PuO-cb-tos-1az-front-azc-2, usedTime: 0.023604041896760464 s
2025-07-23 16:45:58,308 - INFO - 创建文件夹: audios/
2025-07-23 16:45:58,332 - INFO - after-request: put_object exec httpCode: 200, requestId: 517f0180a146ba446880a146-b7a157a-1ueV6s-PuO-cb-tos-1az-front-azc-2, usedTime: 0.02349337504711002 s
2025-07-23 16:45:58,332 - INFO - 创建文件夹: videos/
2025-07-23 16:46:28,346 - INFO - 所有数据表已存在，无需创建
2025-07-23 16:46:28,348 - INFO - 数据库初始化完成: output/novel_step2/大秦二世公子华_去重/novel_setting.db
2025-07-23 16:46:28,348 - INFO - 数据库包含 3 个表，总记录数: 1
2025-07-23 16:46:28,348 - INFO - ==================================================
2025-07-23 16:46:28,348 - INFO - 小说分镜画面生成系统启动
2025-07-23 16:46:28,348 - INFO - 小说目录: output/novel_step1/大秦二世公子华_去重
2025-07-23 16:46:28,348 - INFO - 输出目录: output/novel_step2/大秦二世公子华_去重
2025-07-23 16:46:28,348 - INFO - 数据库路径: output/novel_step2/大秦二世公子华_去重/novel_setting.db
2025-07-23 16:46:28,348 - INFO - ==================================================
2025-07-23 16:46:28,349 - INFO - 开始处理小说: 大秦二世公子华_去重
2025-07-23 16:46:28,369 - INFO - 📋 章节 1 已完成，跳过处理
2025-07-23 16:46:28,372 - INFO - 📋 章节 4 已完成，跳过处理
2025-07-23 16:46:28,376 - INFO - 📋 章节 7 已完成，跳过处理
2025-07-23 16:46:28,380 - INFO - 📋 章节 10 已完成，跳过处理
2025-07-23 16:46:28,383 - INFO - 📋 章节 13 已完成，跳过处理
2025-07-23 16:46:28,386 - INFO - 📋 章节 16 已完成，跳过处理
2025-07-23 16:46:28,389 - INFO - 📋 章节 19 已完成，跳过处理
2025-07-23 16:46:28,392 - INFO - 📋 章节 21 已完成，跳过处理
2025-07-23 16:46:28,395 - INFO - 📋 章节 24 已完成，跳过处理
2025-07-23 16:46:28,397 - INFO - 📋 章节 28 已完成，跳过处理
2025-07-23 16:46:28,399 - INFO - 📋 章节 31 已完成，跳过处理
2025-07-23 16:46:28,401 - INFO - 📋 章节 34 已完成，跳过处理
2025-07-23 16:46:28,403 - INFO - 📋 章节 37 已完成，跳过处理
2025-07-23 16:46:28,405 - INFO - 📋 章节 40 已完成，跳过处理
2025-07-23 16:46:28,408 - INFO - 📋 章节 43 已完成，跳过处理
2025-07-23 16:46:28,410 - INFO - 📋 章节 46 已完成，跳过处理
2025-07-23 16:46:28,412 - INFO - 📋 章节 49 已完成，跳过处理
2025-07-23 16:46:28,414 - INFO - 📋 章节 52 已完成，跳过处理
2025-07-23 16:46:28,416 - INFO - 📋 章节 55 已完成，跳过处理
2025-07-23 16:46:28,418 - INFO - 📋 章节 58 已完成，跳过处理
2025-07-23 16:46:28,420 - INFO - 📋 章节 61 已完成，跳过处理
2025-07-23 16:46:28,422 - INFO - 📋 章节 64 已完成，跳过处理
2025-07-23 16:46:28,424 - INFO - 📋 章节 67 已完成，跳过处理
2025-07-23 16:46:28,427 - INFO - 📋 章节 70 已完成，跳过处理
2025-07-23 16:46:28,429 - INFO - 📋 章节 73 已完成，跳过处理
2025-07-23 16:46:28,430 - INFO - 📋 章节 76 已完成，跳过处理
2025-07-23 16:46:28,433 - INFO - 📋 章节 79 已完成，跳过处理
2025-07-23 16:46:28,435 - INFO - 📋 章节 82 已完成，跳过处理
2025-07-23 16:46:28,437 - INFO - 📋 章节 85 已完成，跳过处理
2025-07-23 16:46:28,439 - INFO - 📋 章节 88 已完成，跳过处理
2025-07-23 16:46:28,441 - INFO - 📋 章节 91 已完成，跳过处理
2025-07-23 16:46:28,444 - INFO - 📋 章节 94 已完成，跳过处理
2025-07-23 16:46:28,446 - INFO - 📋 章节 97 已完成，跳过处理
2025-07-23 16:46:28,449 - INFO - 📋 章节 100 已完成，跳过处理
2025-07-23 16:46:28,451 - INFO - 📋 章节 103 已完成，跳过处理
2025-07-23 16:46:28,453 - INFO - 📋 章节 106 已完成，跳过处理
2025-07-23 16:46:28,455 - INFO - 📋 章节 109 已完成，跳过处理
2025-07-23 16:46:28,457 - INFO - 📋 章节 112 已完成，跳过处理
2025-07-23 16:46:28,460 - INFO - 📋 章节 115 已完成，跳过处理
2025-07-23 16:46:28,462 - INFO - 📋 章节 118 已完成，跳过处理
2025-07-23 16:46:28,464 - INFO - 📋 章节 121 已完成，跳过处理
2025-07-23 16:46:28,466 - INFO - 📋 章节 124 已完成，跳过处理
2025-07-23 16:46:28,468 - INFO - 📋 章节 127 已完成，跳过处理
2025-07-23 16:46:28,470 - INFO - 📋 章节 130 已完成，跳过处理
2025-07-23 16:46:28,472 - INFO - 📋 章节 133 已完成，跳过处理
2025-07-23 16:46:28,474 - INFO - 📋 章节 136 已完成，跳过处理
2025-07-23 16:46:28,476 - INFO - 📋 章节 139 已完成，跳过处理
2025-07-23 16:46:28,478 - INFO - 📋 章节 140 已完成，跳过处理
2025-07-23 16:46:28,480 - INFO - 📋 章节 142 已完成，跳过处理
2025-07-23 16:46:28,482 - INFO - 📋 章节 145 已完成，跳过处理
2025-07-23 16:46:28,484 - INFO - 📋 章节 148 已完成，跳过处理
2025-07-23 16:46:28,486 - INFO - 📋 章节 150 已完成，跳过处理
2025-07-23 16:46:28,489 - INFO - 📋 章节 154 已完成，跳过处理
2025-07-23 16:46:28,491 - INFO - 📋 章节 157 已完成，跳过处理
2025-07-23 16:46:28,493 - INFO - 📋 章节 160 已完成，跳过处理
2025-07-23 16:46:28,496 - INFO - 📋 章节 163 已完成，跳过处理
2025-07-23 16:46:28,498 - INFO - 📋 章节 166 已完成，跳过处理
2025-07-23 16:46:28,500 - INFO - 📋 章节 169 已完成，跳过处理
2025-07-23 16:46:28,503 - INFO - 📋 章节 172 已完成，跳过处理
2025-07-23 16:46:28,537 - INFO - 📋 章节 175 已完成，跳过处理
2025-07-23 16:46:28,540 - INFO - 📋 章节 178 已完成，跳过处理
2025-07-23 16:46:33,562 - INFO - 📚 开始处理 1 个章节（异步并发）
2025-07-23 16:46:33,565 - INFO - 🎬 开始处理第22章
2025-07-23 16:48:48,775 - INFO - 添加新角色: 公子华
2025-07-23 16:48:48,777 - INFO - 添加新角色: 章邯
2025-07-23 16:48:48,777 - INFO - 添加新角色: 秋碟
2025-07-23 16:48:48,777 - INFO - 添加新角色: 曹金
2025-07-23 16:48:48,778 - INFO - 添加新角色: 王强
2025-07-23 16:48:48,778 - INFO - 添加新角色: 相里海
2025-07-23 16:48:48,778 - INFO - 添加新角色: 相里腾
2025-07-23 16:48:48,778 - INFO - 添加新角色: 熊犹
2025-07-23 16:48:48,780 - INFO - 第22章新增角色信息已保存到数据库，共 8 条记录
2025-07-23 16:48:48,781 - INFO - 当前章节共 6 个场景
2025-07-23 16:48:48,781 - INFO - 当前章节共 8 个角色
2025-07-23 16:48:48,783 - INFO - 从数据库获取画风信息: 仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，
2025-07-23 16:48:48,795 - INFO - 场景信息已保存到 output/novel_step2/大秦二世公子华_去重/chapter22_忽悠熊犹/scene_info.json
2025-07-23 16:48:48,795 - INFO - 📝 第22章 - 分镜 1/20
2025-07-23 16:48:52,726 - ERROR - ❌ 章节 22 处理失败: 'dict' object has no attribute 'lower'
2025-07-23 16:48:52,729 - INFO - 📊 章节处理完成统计:
2025-07-23 16:48:52,729 - ERROR - ❌ 处理失败: 1 个章节:
2025-07-23 16:48:52,729 - ERROR -   章节 22: ❌ 章节 22 处理失败: 'dict' object has no attribute 'lower'
2025-07-23 16:48:52,729 - INFO - 🎉 小说 大秦二世公子华_去重 处理完成，成功: 0/1
2025-07-23 16:48:52,729 - INFO - 程序执行完成
2025-07-23 17:01:34,336 - INFO - 日志文件已创建: output/novel_step2/大秦二世公子华_去重/process_log.log
2025-07-23 17:01:34,467 - INFO - after-request: head_bucket exec httpCode: 200, requestId: a2e0180a4ee0cde6880a4ee-bc72d08-1ueVLy-HB-cb-tos-1az-front-azc-2, usedTime: 0.12691720796283334 s
2025-07-23 17:01:34,467 - INFO - 存储桶 ai-novel-wujiejvzhen 已存在
2025-07-23 17:01:34,506 - INFO - after-request: put_object exec httpCode: 200, requestId: a2e0180a4ee0ce16880a4ee-bc72d08-1ueVLy-PuO-cb-tos-1az-front-azc-2, usedTime: 0.038037999998778105 s
2025-07-23 17:01:34,506 - INFO - 创建文件夹: images/
2025-07-23 17:01:34,536 - INFO - after-request: put_object exec httpCode: 200, requestId: a2e0180a4ee0ce66880a4ee-bc72d08-1ueVLy-PuO-cb-tos-1az-front-azc-2, usedTime: 0.028437625034712255 s
2025-07-23 17:01:34,536 - INFO - 创建文件夹: audios/
2025-07-23 17:01:34,566 - INFO - after-request: put_object exec httpCode: 200, requestId: a2e0180a4ee0ce96880a4ee-bc72d08-1ueVLy-PuO-cb-tos-1az-front-azc-2, usedTime: 0.029193792026489973 s
2025-07-23 17:01:34,566 - INFO - 创建文件夹: videos/
2025-07-23 17:02:04,575 - INFO - 所有数据表已存在，无需创建
2025-07-23 17:02:04,576 - INFO - 数据库初始化完成: output/novel_step2/大秦二世公子华_去重/novel_setting.db
2025-07-23 17:02:04,576 - INFO - 数据库包含 3 个表，总记录数: 10
2025-07-23 17:02:04,576 - INFO - ==================================================
2025-07-23 17:02:04,576 - INFO - 小说分镜画面生成系统启动
2025-07-23 17:02:04,576 - INFO - 小说目录: output/novel_step1/大秦二世公子华_去重
2025-07-23 17:02:04,576 - INFO - 输出目录: output/novel_step2/大秦二世公子华_去重
2025-07-23 17:02:04,576 - INFO - 数据库路径: output/novel_step2/大秦二世公子华_去重/novel_setting.db
2025-07-23 17:02:04,576 - INFO - ==================================================
2025-07-23 17:02:04,576 - INFO - 开始处理小说: 大秦二世公子华_去重
2025-07-23 17:02:04,585 - INFO - 📋 章节 1 已完成，跳过处理
2025-07-23 17:02:04,589 - INFO - 📋 章节 4 已完成，跳过处理
2025-07-23 17:02:04,593 - INFO - 📋 章节 7 已完成，跳过处理
2025-07-23 17:02:04,598 - INFO - 📋 章节 10 已完成，跳过处理
2025-07-23 17:02:04,602 - INFO - 📋 章节 13 已完成，跳过处理
2025-07-23 17:02:04,605 - INFO - 📋 章节 16 已完成，跳过处理
2025-07-23 17:02:04,609 - INFO - 📋 章节 19 已完成，跳过处理
2025-07-23 17:02:04,613 - INFO - 📋 章节 21 已完成，跳过处理
2025-07-23 17:02:04,616 - INFO - 📋 章节 24 已完成，跳过处理
2025-07-23 17:02:04,619 - INFO - 📋 章节 28 已完成，跳过处理
2025-07-23 17:02:04,621 - INFO - 📋 章节 31 已完成，跳过处理
2025-07-23 17:02:04,624 - INFO - 📋 章节 34 已完成，跳过处理
2025-07-23 17:02:04,627 - INFO - 📋 章节 37 已完成，跳过处理
2025-07-23 17:02:04,629 - INFO - 📋 章节 40 已完成，跳过处理
2025-07-23 17:02:04,632 - INFO - 📋 章节 43 已完成，跳过处理
2025-07-23 17:02:04,635 - INFO - 📋 章节 46 已完成，跳过处理
2025-07-23 17:02:04,637 - INFO - 📋 章节 49 已完成，跳过处理
2025-07-23 17:02:04,639 - INFO - 📋 章节 52 已完成，跳过处理
2025-07-23 17:02:04,642 - INFO - 📋 章节 55 已完成，跳过处理
2025-07-23 17:02:04,644 - INFO - 📋 章节 58 已完成，跳过处理
2025-07-23 17:02:04,647 - INFO - 📋 章节 61 已完成，跳过处理
2025-07-23 17:02:04,649 - INFO - 📋 章节 64 已完成，跳过处理
2025-07-23 17:02:04,652 - INFO - 📋 章节 67 已完成，跳过处理
2025-07-23 17:02:04,655 - INFO - 📋 章节 70 已完成，跳过处理
2025-07-23 17:02:04,657 - INFO - 📋 章节 73 已完成，跳过处理
2025-07-23 17:02:04,660 - INFO - 📋 章节 76 已完成，跳过处理
2025-07-23 17:02:04,662 - INFO - 📋 章节 79 已完成，跳过处理
2025-07-23 17:02:04,665 - INFO - 📋 章节 82 已完成，跳过处理
2025-07-23 17:02:04,667 - INFO - 📋 章节 85 已完成，跳过处理
2025-07-23 17:02:04,669 - INFO - 📋 章节 88 已完成，跳过处理
2025-07-23 17:02:04,671 - INFO - 📋 章节 91 已完成，跳过处理
2025-07-23 17:02:04,674 - INFO - 📋 章节 94 已完成，跳过处理
2025-07-23 17:02:04,676 - INFO - 📋 章节 97 已完成，跳过处理
2025-07-23 17:02:04,678 - INFO - 📋 章节 100 已完成，跳过处理
2025-07-23 17:02:04,680 - INFO - 📋 章节 103 已完成，跳过处理
2025-07-23 17:02:04,683 - INFO - 📋 章节 106 已完成，跳过处理
2025-07-23 17:02:04,685 - INFO - 📋 章节 109 已完成，跳过处理
2025-07-23 17:02:04,687 - INFO - 📋 章节 112 已完成，跳过处理
2025-07-23 17:02:04,690 - INFO - 📋 章节 115 已完成，跳过处理
2025-07-23 17:02:04,692 - INFO - 📋 章节 118 已完成，跳过处理
2025-07-23 17:02:04,694 - INFO - 📋 章节 121 已完成，跳过处理
2025-07-23 17:02:04,696 - INFO - 📋 章节 124 已完成，跳过处理
2025-07-23 17:02:04,699 - INFO - 📋 章节 127 已完成，跳过处理
2025-07-23 17:02:04,701 - INFO - 📋 章节 130 已完成，跳过处理
2025-07-23 17:02:04,703 - INFO - 📋 章节 133 已完成，跳过处理
2025-07-23 17:02:04,705 - INFO - 📋 章节 136 已完成，跳过处理
2025-07-23 17:02:04,707 - INFO - 📋 章节 139 已完成，跳过处理
2025-07-23 17:02:04,709 - INFO - 📋 章节 140 已完成，跳过处理
2025-07-23 17:02:04,712 - INFO - 📋 章节 142 已完成，跳过处理
2025-07-23 17:02:04,714 - INFO - 📋 章节 145 已完成，跳过处理
2025-07-23 17:02:04,716 - INFO - 📋 章节 148 已完成，跳过处理
2025-07-23 17:02:04,718 - INFO - 📋 章节 150 已完成，跳过处理
2025-07-23 17:02:04,736 - INFO - 📋 章节 154 已完成，跳过处理
2025-07-23 17:02:04,739 - INFO - 📋 章节 157 已完成，跳过处理
2025-07-23 17:02:04,741 - INFO - 📋 章节 160 已完成，跳过处理
2025-07-23 17:02:04,744 - INFO - 📋 章节 163 已完成，跳过处理
2025-07-23 17:02:04,746 - INFO - 📋 章节 166 已完成，跳过处理
2025-07-23 17:02:04,748 - INFO - 📋 章节 169 已完成，跳过处理
2025-07-23 17:02:04,750 - INFO - 📋 章节 172 已完成，跳过处理
2025-07-23 17:02:04,753 - INFO - 📋 章节 175 已完成，跳过处理
2025-07-23 17:02:04,755 - INFO - 📋 章节 178 已完成，跳过处理
2025-07-23 17:02:29,025 - INFO - 📚 开始处理 1 个章节（异步并发）
2025-07-23 17:02:29,033 - INFO - 🎬 开始处理第25章
2025-07-23 17:02:29,042 - INFO - 当前章节共 5 个场景
2025-07-23 17:02:29,043 - INFO - 从数据库获取画风信息: 仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，
2025-07-23 17:02:29,048 - INFO - 场景信息已保存到 output/novel_step2/大秦二世公子华_去重/chapter25_火药问世/scene_info.json
2025-07-23 17:02:29,049 - INFO - 📝 第25章 - 分镜 1/20
2025-07-23 17:03:00,402 - INFO - after-request: put_object exec httpCode: 200, requestId: a2e0180a5441a7e6880a544-bc72d08-1ueVNM-PuO-cb-tos-1az-front-azc-2, usedTime: 0.19112170906737447 s
2025-07-23 17:03:00,403 - INFO - 从URL上传文件成功: images/image_1753261379_20250723_170300.jpg
2025-07-23 17:04:01,948 - INFO - 视频生成任务创建成功，任务ID: cgt-20250723170400-bmkq4
2025-07-23 17:04:22,998 - INFO - after-request: put_object exec httpCode: 200, requestId: e24b0180a5961e886880a596-b24068d-1ueVOg-PuO-cb-tos-1az-front-azc-2, usedTime: 0.3791733330581337 s
2025-07-23 17:04:22,999 - INFO - 从URL上传文件成功: videos/video_1753261462_20250723_170422.mp4
2025-07-23 17:04:23,001 - INFO - 📝 第25章 - 分镜 2/20
2025-07-23 17:09:26,097 - INFO - after-request: put_object exec httpCode: 200, requestId: 13230180a6c61e8d6880a6c6-b29f68b-1ueVTa-PuO-cb-tos-1az-front-azc-2, usedTime: 0.19766204105690122 s
2025-07-23 17:09:26,098 - INFO - 从URL上传文件成功: images/image_1753261765_20250723_170925.jpg
2025-07-23 17:09:30,231 - INFO - 视频生成任务创建成功，任务ID: cgt-20250723170929-rsrpw
2025-07-23 17:09:51,192 - INFO - after-request: put_object exec httpCode: 200, requestId: 13230180a6df227e6880a6df-b29f68b-1ueVTz-PuO-cb-tos-1az-front-azc-2, usedTime: 0.24137612502090633 s
2025-07-23 17:09:51,193 - INFO - 从URL上传文件成功: videos/video_1753261790_20250723_170950.mp4
2025-07-23 17:09:51,195 - INFO - 📝 第25章 - 分镜 3/20
2025-07-23 17:13:56,742 - INFO - 日志文件已创建: output/novel_step2/大秦二世公子华_去重/process_log.log
2025-07-23 17:13:56,856 - INFO - after-request: head_bucket exec httpCode: 200, requestId: a2e0180a7d4855c6880a7d4-bc72d08-1ueVXw-HB-cb-tos-1az-front-azc-2, usedTime: 0.1086983330314979 s
2025-07-23 17:13:56,857 - INFO - 存储桶 ai-novel-wujiejvzhen 已存在
2025-07-23 17:13:56,893 - INFO - after-request: put_object exec httpCode: 200, requestId: a2e0180a7d5855f6880a7d5-bc72d08-1ueVXx-PuO-cb-tos-1az-front-azc-2, usedTime: 0.03543391707353294 s
2025-07-23 17:13:56,893 - INFO - 创建文件夹: images/
2025-07-23 17:13:56,917 - INFO - after-request: put_object exec httpCode: 200, requestId: a2e0180a7d585656880a7d5-bc72d08-1ueVXx-PuO-cb-tos-1az-front-azc-2, usedTime: 0.023440207936801016 s
2025-07-23 17:13:56,917 - INFO - 创建文件夹: audios/
2025-07-23 17:13:56,944 - INFO - after-request: put_object exec httpCode: 200, requestId: a2e0180a7d585696880a7d5-bc72d08-1ueVXx-PuO-cb-tos-1az-front-azc-2, usedTime: 0.026392624946311116 s
2025-07-23 17:13:56,944 - INFO - 创建文件夹: videos/
2025-07-23 17:14:26,960 - INFO - 所有数据表已存在，无需创建
2025-07-23 17:14:26,962 - INFO - 数据库初始化完成: output/novel_step2/大秦二世公子华_去重/novel_setting.db
2025-07-23 17:14:26,962 - INFO - 数据库包含 3 个表，总记录数: 10
2025-07-23 17:14:26,963 - INFO - ==================================================
2025-07-23 17:14:26,963 - INFO - 小说分镜画面生成系统启动
2025-07-23 17:14:26,963 - INFO - 小说目录: output/novel_step1/大秦二世公子华_去重
2025-07-23 17:14:26,963 - INFO - 输出目录: output/novel_step2/大秦二世公子华_去重
2025-07-23 17:14:26,963 - INFO - 数据库路径: output/novel_step2/大秦二世公子华_去重/novel_setting.db
2025-07-23 17:14:26,963 - INFO - ==================================================
2025-07-23 17:14:26,963 - INFO - 开始处理小说: 大秦二世公子华_去重
2025-07-23 17:14:26,979 - INFO - 📋 章节 1 已完成，跳过处理
2025-07-23 17:14:26,982 - INFO - 📋 章节 4 已完成，跳过处理
2025-07-23 17:14:26,987 - INFO - 📋 章节 7 已完成，跳过处理
2025-07-23 17:14:26,991 - INFO - 📋 章节 10 已完成，跳过处理
2025-07-23 17:14:26,994 - INFO - 📋 章节 13 已完成，跳过处理
2025-07-23 17:14:26,997 - INFO - 📋 章节 16 已完成，跳过处理
2025-07-23 17:14:27,002 - INFO - 📋 章节 19 已完成，跳过处理
2025-07-23 17:14:27,004 - INFO - 📋 章节 21 已完成，跳过处理
2025-07-23 17:14:27,006 - INFO - 📋 章节 24 已完成，跳过处理
2025-07-23 17:14:27,009 - INFO - 📋 章节 28 已完成，跳过处理
2025-07-23 17:14:27,010 - INFO - 📋 章节 31 已完成，跳过处理
2025-07-23 17:14:27,013 - INFO - 📋 章节 34 已完成，跳过处理
2025-07-23 17:14:27,015 - INFO - 📋 章节 37 已完成，跳过处理
2025-07-23 17:14:27,018 - INFO - 📋 章节 40 已完成，跳过处理
2025-07-23 17:14:27,020 - INFO - 📋 章节 43 已完成，跳过处理
2025-07-23 17:14:27,023 - INFO - 📋 章节 46 已完成，跳过处理
2025-07-23 17:14:27,025 - INFO - 📋 章节 49 已完成，跳过处理
2025-07-23 17:14:27,027 - INFO - 📋 章节 52 已完成，跳过处理
2025-07-23 17:14:27,030 - INFO - 📋 章节 55 已完成，跳过处理
2025-07-23 17:14:27,032 - INFO - 📋 章节 58 已完成，跳过处理
2025-07-23 17:14:27,035 - INFO - 📋 章节 61 已完成，跳过处理
2025-07-23 17:14:27,037 - INFO - 📋 章节 64 已完成，跳过处理
2025-07-23 17:14:27,039 - INFO - 📋 章节 67 已完成，跳过处理
2025-07-23 17:14:27,042 - INFO - 📋 章节 70 已完成，跳过处理
2025-07-23 17:14:27,044 - INFO - 📋 章节 73 已完成，跳过处理
2025-07-23 17:14:27,046 - INFO - 📋 章节 76 已完成，跳过处理
2025-07-23 17:14:27,048 - INFO - 📋 章节 79 已完成，跳过处理
2025-07-23 17:14:27,051 - INFO - 📋 章节 82 已完成，跳过处理
2025-07-23 17:14:27,053 - INFO - 📋 章节 85 已完成，跳过处理
2025-07-23 17:14:27,055 - INFO - 📋 章节 88 已完成，跳过处理
2025-07-23 17:14:27,057 - INFO - 📋 章节 91 已完成，跳过处理
2025-07-23 17:14:27,060 - INFO - 📋 章节 94 已完成，跳过处理
2025-07-23 17:14:27,062 - INFO - 📋 章节 97 已完成，跳过处理
2025-07-23 17:14:27,065 - INFO - 📋 章节 100 已完成，跳过处理
2025-07-23 17:14:27,067 - INFO - 📋 章节 103 已完成，跳过处理
2025-07-23 17:14:27,070 - INFO - 📋 章节 106 已完成，跳过处理
2025-07-23 17:14:27,072 - INFO - 📋 章节 109 已完成，跳过处理
2025-07-23 17:14:27,073 - INFO - 📋 章节 112 已完成，跳过处理
2025-07-23 17:14:27,077 - INFO - 📋 章节 115 已完成，跳过处理
2025-07-23 17:14:27,079 - INFO - 📋 章节 118 已完成，跳过处理
2025-07-23 17:14:27,081 - INFO - 📋 章节 121 已完成，跳过处理
2025-07-23 17:14:27,083 - INFO - 📋 章节 124 已完成，跳过处理
2025-07-23 17:14:27,086 - INFO - 📋 章节 127 已完成，跳过处理
2025-07-23 17:14:27,088 - INFO - 📋 章节 130 已完成，跳过处理
2025-07-23 17:14:27,091 - INFO - 📋 章节 133 已完成，跳过处理
2025-07-23 17:14:27,092 - INFO - 📋 章节 136 已完成，跳过处理
2025-07-23 17:14:27,095 - INFO - 📋 章节 139 已完成，跳过处理
2025-07-23 17:14:27,097 - INFO - 📋 章节 142 已完成，跳过处理
2025-07-23 17:14:27,100 - INFO - 📋 章节 145 已完成，跳过处理
2025-07-23 17:14:27,102 - INFO - 📋 章节 148 已完成，跳过处理
2025-07-23 17:14:27,104 - INFO - 📋 章节 150 已完成，跳过处理
2025-07-23 17:14:27,151 - INFO - 📋 章节 154 已完成，跳过处理
2025-07-23 17:14:27,154 - INFO - 📋 章节 157 已完成，跳过处理
2025-07-23 17:14:27,157 - INFO - 📋 章节 160 已完成，跳过处理
2025-07-23 17:14:27,159 - INFO - 📋 章节 163 已完成，跳过处理
2025-07-23 17:14:27,161 - INFO - 📋 章节 166 已完成，跳过处理
2025-07-23 17:14:27,164 - INFO - 📋 章节 169 已完成，跳过处理
2025-07-23 17:14:27,166 - INFO - 📋 章节 172 已完成，跳过处理
2025-07-23 17:14:27,170 - INFO - 📋 章节 175 已完成，跳过处理
2025-07-23 17:14:27,174 - INFO - 📋 章节 178 已完成，跳过处理
2025-07-23 17:15:36,282 - INFO - 📚 开始处理 1 个章节（异步并发）
2025-07-23 17:15:36,288 - INFO - 🎬 开始处理第140章
2025-07-23 17:17:53,110 - INFO - 添加新角色: 嬴政
2025-07-23 17:17:53,111 - INFO - 添加新角色: 公子华
2025-07-23 17:17:53,111 - INFO - 添加新角色: 赵姬
2025-07-23 17:17:53,112 - INFO - 添加新角色: 邓山
2025-07-23 17:17:53,112 - INFO - 添加新角色: 夏阿房
2025-07-23 17:17:53,112 - INFO - 添加新角色: 章邯
2025-07-23 17:17:53,112 - INFO - 添加新角色: 司马城
2025-07-23 17:17:53,112 - INFO - 添加新角色: 李牧
2025-07-23 17:17:53,112 - INFO - 添加新角色: 嬴阴曼
2025-07-23 17:17:53,113 - INFO - 添加新角色: 公子高
2025-07-23 17:17:53,113 - INFO - 添加新角色: 将闾
2025-07-23 17:17:53,113 - INFO - 添加新角色: 嬴月曼
2025-07-23 17:17:53,113 - INFO - 添加新角色: 晨曦
2025-07-23 17:17:53,116 - INFO - 第140章新增角色信息已保存到数据库，共 11 条记录
2025-07-23 17:17:53,117 - INFO - 当前章节共 4 个场景
2025-07-23 17:17:53,117 - INFO - 当前章节共 13 个角色
2025-07-23 17:17:53,118 - INFO - 从数据库获取画风信息: 仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，
2025-07-23 17:17:53,132 - INFO - 场景信息已保存到 output/novel_step2/大秦二世公子华_去重/chapter140_夏阿房探望赵姬/scene_info.json
2025-07-23 17:17:53,132 - INFO - 📝 第140章 - 分镜 1/20
2025-07-23 17:19:50,085 - INFO - after-request: put_object exec httpCode: 200, requestId: e24b0180a936b5766880a936-b24068d-1ueVde-PuO-cb-tos-1az-front-azc-2, usedTime: 0.14827424997929484 s
2025-07-23 17:19:50,085 - INFO - 从URL上传文件成功: images/image_1753262389_20250723_171949.jpg
2025-07-23 17:19:50,850 - INFO - 视频生成任务创建成功，任务ID: cgt-20250723171950-g7m8h
2025-07-23 17:20:11,987 - INFO - after-request: put_object exec httpCode: 200, requestId: e24b0180a94bb9066880a94b-b24068d-1ueVdz-PuO-cb-tos-1az-front-azc-2, usedTime: 0.28159433300606906 s
2025-07-23 17:20:11,988 - INFO - 从URL上传文件成功: videos/video_1753262411_20250723_172011.mp4
2025-07-23 17:20:11,989 - INFO - 📝 第140章 - 分镜 2/20
2025-07-23 17:20:38,179 - INFO - after-request: put_object exec httpCode: 200, requestId: e24b0180a966bd526880a966-b24068d-1ueVeQ-PuO-cb-tos-1az-front-azc-2, usedTime: 0.05217741592787206 s
2025-07-23 17:20:38,180 - INFO - 从URL上传文件成功: images/image_1753262437_20250723_172038.jpg
2025-07-23 17:20:39,040 - INFO - 视频生成任务创建成功，任务ID: cgt-20250723172038-8qn6q
2025-07-23 17:21:00,356 - INFO - after-request: put_object exec httpCode: 200, requestId: e24b0180a97cc0be6880a97c-b24068d-1ueVem-PuO-cb-tos-1az-front-azc-2, usedTime: 0.2407722920179367 s
2025-07-23 17:21:00,357 - INFO - 从URL上传文件成功: videos/video_1753262459_20250723_172100.mp4
2025-07-23 17:21:00,359 - INFO - 📝 第140章 - 分镜 3/20
2025-07-23 17:21:07,933 - WARNING - 图片生成失败，第 1 次重试: b'{"code":50430,"data":null,"message":"Request Has Reached API Concurrent Limit, Please Try Later","request_id":"2025072317210720460F92CAC757EBBA10","status":50430,"time_elapsed":"19.028723ms"}'
2025-07-23 17:21:09,007 - ERROR - 图片生成失败，已重试 1 次: b'{"code":50430,"data":null,"message":"Request Has Reached API Concurrent Limit, Please Try Later","request_id":"20250723172109D2B1CD67ED1D2FEDF8A1","status":50430,"time_elapsed":"16.046657ms"}'
2025-07-23 17:21:09,008 - WARNING - 图像生成失败，第 1 次重试: b'{"code":50430,"data":null,"message":"Request Has Reached API Concurrent Limit, Please Try Later","request_id":"20250723172109D2B1CD67ED1D2FEDF8A1","status":50430,"time_elapsed":"16.046657ms"}'
2025-07-23 17:21:11,119 - WARNING - 图片生成失败，第 1 次重试: b'{"code":50430,"data":null,"message":"Request Has Reached API Concurrent Limit, Please Try Later","request_id":"20250723172111FEFC592B6F9E7FEA1259","status":50430,"time_elapsed":"29.704392ms"}'
2025-07-23 17:21:12,178 - ERROR - 图片生成失败，已重试 1 次: b'{"code":50430,"data":null,"message":"Request Has Reached API Concurrent Limit, Please Try Later","request_id":"20250723172112456EF88D5F249DEFA9AF","status":50430,"time_elapsed":"2.189748ms"}'
2025-07-23 17:21:12,179 - WARNING - 图像生成失败，第 2 次重试: b'{"code":50430,"data":null,"message":"Request Has Reached API Concurrent Limit, Please Try Later","request_id":"20250723172112456EF88D5F249DEFA9AF","status":50430,"time_elapsed":"2.189748ms"}'
2025-07-23 17:21:34,925 - INFO - after-request: put_object exec httpCode: 200, requestId: e24b0180a99ec6656880a99e-b24068d-1ueVfK-PuO-cb-tos-1az-front-azc-2, usedTime: 0.048557542031630874 s
2025-07-23 17:21:34,927 - INFO - 从URL上传文件成功: images/image_1753262493_20250723_172134.jpg
2025-07-23 17:21:34,929 - INFO - 📝 第140章 - 分镜 4/20
2025-07-23 17:22:45,249 - INFO - after-request: put_object exec httpCode: 200, requestId: e24b0180a9e5d1fb6880a9e5-b24068d-1ueVgT-PuO-cb-tos-1az-front-azc-2, usedTime: 0.06688933295663446 s
2025-07-23 17:22:45,249 - INFO - 从URL上传文件成功: images/image_1753262564_20250723_172245.jpg
2025-07-23 17:22:45,251 - INFO - 📝 第140章 - 分镜 5/20
2025-07-23 17:23:17,662 - INFO - after-request: put_object exec httpCode: 200, requestId: e24b0180aa05d73a6880aa05-b24068d-1ueVgz-PuO-cb-tos-1az-front-azc-2, usedTime: 0.04573804198298603 s
2025-07-23 17:23:17,662 - INFO - 从URL上传文件成功: images/image_1753262597_20250723_172317.jpg
2025-07-23 17:23:17,664 - INFO - 📝 第140章 - 分镜 6/20
2025-07-23 17:24:11,093 - ERROR - 从URL上传文件失败: HTTPSConnectionPool(host='p9-aiop-sign.byteimg.com', port=443): Max retries exceeded with url: /tos-cn-i-vuqhorh59i/202507231723217AAB44BFD072C3EA1400-4126-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1753349009&x-signature=TsmkEK86uktEHHKeOQFcyNSObqE%3D (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1016)')))
2025-07-23 17:24:11,095 - INFO - 📝 第140章 - 分镜 7/20
2025-07-23 17:24:55,038 - INFO - after-request: put_object exec httpCode: 200, requestId: 13230180aa67b39c6880aa67-b29f68b-1ueViZ-PuO-cb-tos-1az-front-azc-2, usedTime: 0.16518824989907444 s
2025-07-23 17:24:55,040 - INFO - 从URL上传文件成功: images/image_1753262694_20250723_172454.jpg
2025-07-23 17:24:55,042 - INFO - 📝 第140章 - 分镜 8/20
2025-07-23 17:25:17,142 - ERROR - 从URL上传文件失败: HTTPSConnectionPool(host='p9-aiop-sign.byteimg.com', port=443): Max retries exceeded with url: /tos-cn-i-vuqhorh59i/20250723172502D87E6FD4627117EEBE20-7278-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1753349109&x-signature=hCUw0%2FUCjAX2AxYbeVU3Kgiloag%3D (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1016)')))
2025-07-23 17:25:17,144 - INFO - 📝 第140章 - 分镜 9/20
2025-07-23 17:25:24,762 - WARNING - 图片生成失败，第 1 次重试: b'{"code":50430,"data":null,"message":"Request Has Reached API Concurrent Limit, Please Try Later","request_id":"20250723172524863EE6F81CC814ECA388","status":50430,"time_elapsed":"17.616559ms"}'
2025-07-23 17:25:25,819 - ERROR - 图片生成失败，已重试 1 次: b'{"code":50430,"data":null,"message":"Request Has Reached API Concurrent Limit, Please Try Later","request_id":"20250723172525F43AA99F301E1EEA98FB","status":50430,"time_elapsed":"4.623257ms"}'
2025-07-23 17:25:25,819 - WARNING - 图像生成失败，第 1 次重试: b'{"code":50430,"data":null,"message":"Request Has Reached API Concurrent Limit, Please Try Later","request_id":"20250723172525F43AA99F301E1EEA98FB","status":50430,"time_elapsed":"4.623257ms"}'
2025-07-23 17:33:20,109 - INFO - after-request: put_object exec httpCode: 200, requestId: e24b0180ac6039636880ac60-b24068d-1ueVqi-PuO-cb-tos-1az-front-azc-2, usedTime: 0.20053662499412894 s
2025-07-23 17:33:20,111 - INFO - 从URL上传文件成功: images/image_1753263199_20250723_173319.jpg
2025-07-23 17:33:20,112 - INFO - 📝 第140章 - 分镜 10/20
2025-07-23 17:34:08,775 - INFO - after-request: put_object exec httpCode: 200, requestId: e24b0180ac9041446880ac90-b24068d-1ueVrU-PuO-cb-tos-1az-front-azc-2, usedTime: 0.06006291601806879 s
2025-07-23 17:34:08,778 - INFO - 从URL上传文件成功: images/image_1753263248_20250723_173408.jpg
2025-07-23 17:34:08,781 - INFO - 📝 第140章 - 分镜 11/20
