2025-07-28 16:46:13,843 - INFO - 日志文件已创建: output/novel_step2/以无敌证道/process_log.log
2025-07-28 16:46:13,994 - INFO - after-request: head_bucket exec httpCode: 200, requestId: 87d9018738d50ee0688738d5-b23e637-1ugJUs-HB-cb-tos-1az-front-aza-1, usedTime: 0.14695904101245105 s
2025-07-28 16:46:13,994 - INFO - 存储桶 ai-novel-wujiejvzhen 已存在
2025-07-28 16:46:14,036 - INFO - after-request: put_object exec httpCode: 200, requestId: 87d9018738d60ee2688738d6-b23e637-1ugJUs-PuO-cb-tos-1az-front-aza-1, usedTime: 0.04175179195590317 s
2025-07-28 16:46:14,037 - INFO - 创建文件夹: images/
2025-07-28 16:46:14,068 - INFO - after-request: put_object exec httpCode: 200, requestId: 87d9018738d60ee7688738d6-b23e637-1ugJUs-PuO-cb-tos-1az-front-aza-1, usedTime: 0.031287041027098894 s
2025-07-28 16:46:14,068 - INFO - 创建文件夹: audios/
2025-07-28 16:46:14,102 - INFO - after-request: put_object exec httpCode: 200, requestId: 87d9018738d60eed688738d6-b23e637-1ugJUs-PuO-cb-tos-1az-front-aza-1, usedTime: 0.03374508395791054 s
2025-07-28 16:46:14,103 - INFO - 创建文件夹: videos/
2025-07-28 16:46:44,115 - INFO - 表 character_info 创建成功
2025-07-28 16:46:44,116 - INFO - 表 draw_style 创建成功
2025-07-28 16:46:44,116 - INFO - 创建了以下数据表: character_info, draw_style
2025-07-28 16:46:44,116 - INFO - 数据库初始化完成: output/novel_step2/以无敌证道/novel_setting.db
2025-07-28 16:46:44,116 - INFO - 数据库包含 3 个表，总记录数: 0
2025-07-28 16:46:44,446 - INFO - 人物图像处理器初始化完成 - 处理区域: 200x250, 效果类型: gaussian_blur, 模糊核大小: 51, 最多检测: 3人 (新版API)
2025-07-28 16:46:44,446 - INFO - ==================================================
2025-07-28 16:46:44,447 - INFO - 小说分镜画面生成系统启动
2025-07-28 16:46:44,447 - INFO - 小说目录: output/novel_step1/以无敌证道
2025-07-28 16:46:44,447 - INFO - 输出目录: output/novel_step2/以无敌证道
2025-07-28 16:46:44,447 - INFO - 数据库路径: output/novel_step2/以无敌证道/novel_setting.db
2025-07-28 16:46:44,447 - INFO - ==================================================
2025-07-28 16:46:44,447 - INFO - 开始处理小说: 以无敌证道
2025-07-28 16:47:15,937 - INFO - 📚 开始处理 3 个章节（控制并发数）
2025-07-28 16:47:16,044 - INFO - 成功加载提示词文件: /Users/<USER>/projects/ai_novel_video/src/prompts/image_dectect_system.md
2025-07-28 16:47:16,044 - INFO - 图像违规检测器初始化完成
2025-07-28 16:47:16,044 - INFO - 图像违规检测器初始化成功
2025-07-28 16:47:16,044 - INFO - 🎬 开始处理第33章
2025-07-28 16:47:16,103 - INFO - 成功加载提示词文件: /Users/<USER>/projects/ai_novel_video/src/prompts/image_dectect_system.md
2025-07-28 16:47:16,103 - INFO - 图像违规检测器初始化完成
2025-07-28 16:47:16,103 - INFO - 图像违规检测器初始化成功
2025-07-28 16:47:16,103 - INFO - 🎬 开始处理第77章
2025-07-28 16:47:16,159 - INFO - 成功加载提示词文件: /Users/<USER>/projects/ai_novel_video/src/prompts/image_dectect_system.md
2025-07-28 16:47:16,159 - INFO - 图像违规检测器初始化完成
2025-07-28 16:47:16,159 - INFO - 图像违规检测器初始化成功
2025-07-28 16:47:16,159 - INFO - 🎬 开始处理第205章
2025-07-28 16:48:04,472 - INFO - 添加新角色: 衡权
2025-07-28 16:48:04,474 - INFO - 添加新角色: 满脸戾气的男人
2025-07-28 16:48:04,474 - INFO - 添加新角色: 少年衡权
2025-07-28 16:48:04,475 - INFO - 添加新角色: 衡权的父亲
2025-07-28 16:48:04,475 - INFO - 添加新角色: 衡权的母亲
2025-07-28 16:48:04,477 - INFO - 第205章新增角色信息已保存到数据库，共 5 条记录
2025-07-28 16:48:04,477 - INFO - 当前章节共 3 个场景
2025-07-28 16:48:04,477 - INFO - 当前章节共 5 个角色
2025-07-28 16:48:04,477 - INFO - 数据库中未找到画风信息
2025-07-28 16:48:04,478 - INFO - 从表 draw_style 删除 0 行
2025-07-28 16:48:04,478 - INFO - 向表 draw_style 插入 1 条记录
2025-07-28 16:48:04,478 - INFO - 画风信息已保存到数据库: 仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，
2025-07-28 16:48:04,479 - INFO - 场景信息已保存到 output/novel_step2/以无敌证道/chapter205_黑暗裂缝/scene_info.json
2025-07-28 16:48:34,489 - INFO - 添加新角色: 衡权
2025-07-28 16:48:34,489 - INFO - 添加新角色: 神秘老人
2025-07-28 16:48:34,490 - INFO - 添加新角色: 秦真
2025-07-28 16:48:34,490 - INFO - 添加新角色: 白妤卿
2025-07-28 16:48:34,490 - INFO - 添加新角色: 夏宏
2025-07-28 16:48:34,490 - INFO - 添加新角色: 四不像
2025-07-28 16:48:34,490 - INFO - 第77章新增角色信息已保存到数据库，共 5 条记录
2025-07-28 16:48:34,490 - INFO - 当前章节共 2 个场景
2025-07-28 16:48:34,490 - INFO - 当前章节共 6 个角色
2025-07-28 16:48:34,491 - INFO - 从数据库获取画风信息: 仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，
2025-07-28 16:48:34,492 - INFO - 场景信息已保存到 output/novel_step2/以无敌证道/chapter77_进入遗迹/scene_info.json
2025-07-28 16:49:04,521 - INFO - 添加新角色: 衡权
2025-07-28 16:49:04,528 - INFO - 添加新角色: 灵魂体
2025-07-28 16:49:04,528 - INFO - 添加新角色: 池底之物
2025-07-28 16:49:04,528 - INFO - 添加新角色: 蟒形魔兽
2025-07-28 16:49:04,528 - INFO - 第33章新增角色信息已保存到数据库，共 3 条记录
2025-07-28 16:49:04,529 - INFO - 当前章节共 2 个场景
2025-07-28 16:49:04,529 - INFO - 当前章节共 4 个角色
2025-07-28 16:49:04,530 - INFO - 从数据库获取画风信息: 仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，
2025-07-28 16:49:04,531 - INFO - 场景信息已保存到 output/novel_step2/以无敌证道/chapter33_紫心灵草/scene_info.json
2025-07-28 16:49:10,963 - INFO - 📝 第205章 - 分镜 1/20
: 中景，一个从空中重重摔落在荒原地面上的青年男子，脸上带着痛苦和凌厉的神情，眼神坚毅且决然。该男子瘦削，有着黑色短发，穿着立领的明朝服制劲装，主色调为深黑色，搭配暗灰色，有简单的线条花纹，配饰是一条破旧的黑色腰带圆形衣领，背景是古代室外荒原, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，
2025-07-28 16:49:12,093 - ERROR - ❌ 章节 33 处理失败: Cannot connect to host ark.cn-beijing.volces.com:443 ssl:default [None]
2025-07-28 16:49:14,539 - INFO - 📝 第77章 - 分镜 1/20
: 中景，在古代遗迹（大炎宗遗迹）中，一个眼神坚定、表情冷静的瘦削青年男子站在原地，身着立领深黑色主色调、搭配暗灰色、有简单线条花纹的明朝服制劲装，配饰是一条破旧的黑色腰带，圆形衣领，留着黑色短发，脸庞坚毅；旁边一个身体一震、手中刀微微颤抖、满脸震惊愤怒的健壮青年男子，身着立领黑色主色调、搭配暗红色和银色、有火焰纹样的明朝服制长袍，腰间束黑色皮革腰带，佩戴黑色护腕，圆形衣领，黑色短发束起，面容冷峻；不远处一个捂住嘴巴、眼睛瞪大、一脸难以置信的秀丽青年女子，身着立领粉色主色调、搭配淡紫色和白色、有花卉纹样的明朝服制长袍，腰间系粉色丝带，头戴粉色发簪，圆形衣领，黑色长发盘起, 都是圆形高衣领, 圆形高衣领,仙侠古风2：2d漫画，细线条，柔和的灯光，平面插画，动漫美感，数字技术技艺，游戏CG，影视级画面，高质感，仙侠古风，古风，
2025-07-28 16:49:23,898 - INFO - HTTP Request: POST https://ark.cn-beijing.volces.com/api/v3/chat/completions "HTTP/1.1 200 OK"
2025-07-28 16:49:23,906 - INFO - 检测结果: {'result': True, 'violation_points': ['1a']}
2025-07-28 16:49:23,906 - WARNING - 检测到违规图像: https://p26-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/202507281649110AB6AD87DEC1AB9D4A3F-4266-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1753778957&x-signature=jYkl5fuDaAneHsJp%2FYGqtmM3w10%3D
2025-07-28 16:49:23,906 - WARNING - 违规点: ['1a']
2025-07-28 16:49:28,921 - ERROR - 图片生成API调用失败: b''
2025-07-28 16:49:28,921 - WARNING - 图像生成失败，第 1 次重试: b''
2025-07-28 16:49:28,927 - WARNING - 视频生成失败，第 1 次重试: Cannot connect to host ark.cn-beijing.volces.com:443 ssl:default [None]
2025-07-28 16:49:34,934 - ERROR - 图片生成API调用失败: b''
2025-07-28 16:49:34,934 - WARNING - 图像生成失败，第 2 次重试: b''
2025-07-28 16:49:38,939 - WARNING - 视频生成失败，第 2 次重试: Cannot connect to host ark.cn-beijing.volces.com:443 ssl:default [None]
2025-07-28 16:49:40,957 - ERROR - 图片生成API调用失败: b''
2025-07-28 16:49:40,959 - ERROR - 图像生成失败，已重试 2 次: b''
2025-07-28 16:49:40,959 - ERROR - ❌ 章节 77 处理失败: b''
2025-07-28 16:49:44,928 - INFO - 视频生成任务创建成功，任务ID: cgt-20250728164944-w76jv
2025-07-28 16:50:00,045 - ERROR - 视频生成失败，已重试 2 次: Cannot connect to host ark.cn-beijing.volces.com:443 ssl:default [None]
2025-07-28 16:50:00,046 - ERROR - ❌ 章节 205 处理失败: Cannot connect to host ark.cn-beijing.volces.com:443 ssl:default [None]
2025-07-28 16:50:00,046 - INFO - 📊 章节处理完成统计:
2025-07-28 16:50:00,046 - ERROR - ❌ 处理失败: 3 个章节:
2025-07-28 16:50:00,046 - ERROR -   章节 33: ❌ 章节 33 处理失败: Cannot connect to host ark.cn-beijing.volces.com:443 ssl:default [None]
2025-07-28 16:50:00,046 - ERROR -   章节 77: ❌ 章节 77 处理失败: b''
2025-07-28 16:50:00,047 - ERROR -   章节 205: ❌ 章节 205 处理失败: Cannot connect to host ark.cn-beijing.volces.com:443 ssl:default [None]
2025-07-28 16:50:00,047 - INFO - 🎉 小说 以无敌证道 处理完成，成功: 0/3
2025-07-28 16:50:00,047 - INFO - 程序执行完成
2025-07-28 16:50:37,580 - INFO - 日志文件已创建: output/novel_step2/以无敌证道/process_log.log
2025-07-28 16:50:42,600 - INFO - Exception: HTTPSConnectionPool(host='ai-novel-wujiejvzhen.tos-cn-beijing.volces.com', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1016)')))
2025-07-28 16:50:42,601 - INFO - in-request do retry with, body:None, method:HEAD, func:head_bucket server_exp:None, client_exp
2025-07-28 16:50:42,601 - INFO - in-request: retry success data:None method:HEAD func_name:head_bucket, exp:{'message': 'http request timeout', 'case': "HTTPSConnectionPool(host='ai-novel-wujiejvzhen.tos-cn-beijing.volces.com', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1016)')))"}
2025-07-28 16:50:42,601 - INFO - in-request: sleep 0.1s
2025-07-28 16:50:47,713 - INFO - Exception: HTTPSConnectionPool(host='ai-novel-wujiejvzhen.tos-cn-beijing.volces.com', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1016)')))
2025-07-28 16:50:47,713 - INFO - in-request do retry with, body:None, method:HEAD, func:head_bucket server_exp:None, client_exp
2025-07-28 16:50:47,713 - INFO - in-request: retry success data:None method:HEAD func_name:head_bucket, exp:{'message': 'http request timeout', 'case': "HTTPSConnectionPool(host='ai-novel-wujiejvzhen.tos-cn-beijing.volces.com', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1016)')))"}
2025-07-28 16:50:47,713 - INFO - in-request: sleep 0.2s
2025-07-28 16:50:52,923 - INFO - Exception: HTTPSConnectionPool(host='ai-novel-wujiejvzhen.tos-cn-beijing.volces.com', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1016)')))
2025-07-28 16:50:52,923 - INFO - in-request do retry with, body:None, method:HEAD, func:head_bucket server_exp:None, client_exp
2025-07-28 16:50:52,924 - INFO - in-request: retry success data:None method:HEAD func_name:head_bucket, exp:{'message': 'http request timeout', 'case': "HTTPSConnectionPool(host='ai-novel-wujiejvzhen.tos-cn-beijing.volces.com', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1016)')))"}
2025-07-28 16:50:52,924 - INFO - in-request: sleep 0.4s
2025-07-28 16:50:53,544 - INFO - after-request: head_bucket exec httpCode: 200, requestId: 87d9018739ed3c1c688739ed-b23e637-1ugJZN-HB-cb-tos-1az-front-aza-1, usedTime: 0.619391666026786 s
2025-07-28 16:50:53,544 - INFO - 存储桶 ai-novel-wujiejvzhen 已存在
2025-07-28 16:50:53,591 - INFO - after-request: put_object exec httpCode: 200, requestId: 87d9018739ed3c1e688739ed-b23e637-1ugJZN-PuO-cb-tos-1az-front-aza-1, usedTime: 0.04465212509967387 s
2025-07-28 16:50:53,591 - INFO - 创建文件夹: images/
2025-07-28 16:50:53,624 - INFO - after-request: put_object exec httpCode: 200, requestId: 87d9018739ed3c23688739ed-b23e637-1ugJZN-PuO-cb-tos-1az-front-aza-1, usedTime: 0.0331895409617573 s
2025-07-28 16:50:53,624 - INFO - 创建文件夹: audios/
2025-07-28 16:50:53,658 - INFO - after-request: put_object exec httpCode: 200, requestId: 87d9018739ed3c27688739ed-b23e637-1ugJZN-PuO-cb-tos-1az-front-aza-1, usedTime: 0.033055749954655766 s
2025-07-28 16:50:53,658 - INFO - 创建文件夹: videos/
2025-07-28 16:51:35,830 - INFO - 日志文件已创建: output/novel_step2/以无敌证道/process_log.log
2025-07-28 16:51:39,463 - INFO - Exception: HTTPSConnectionPool(host='ai-novel-wujiejvzhen.tos-cn-beijing.volces.com', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1016)')))
2025-07-28 16:51:39,464 - INFO - in-request do retry with, body:None, method:HEAD, func:head_bucket server_exp:None, client_exp
2025-07-28 16:51:39,465 - INFO - in-request: retry success data:None method:HEAD func_name:head_bucket, exp:{'message': 'http request timeout', 'case': "HTTPSConnectionPool(host='ai-novel-wujiejvzhen.tos-cn-beijing.volces.com', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1016)')))"}
2025-07-28 16:51:39,468 - INFO - in-request: sleep 0.1s
2025-07-28 16:51:43,097 - INFO - Exception: HTTPSConnectionPool(host='ai-novel-wujiejvzhen.tos-cn-beijing.volces.com', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1016)')))
2025-07-28 16:51:43,099 - INFO - in-request do retry with, body:None, method:HEAD, func:head_bucket server_exp:None, client_exp
2025-07-28 16:51:43,099 - INFO - in-request: retry success data:None method:HEAD func_name:head_bucket, exp:{'message': 'http request timeout', 'case': "HTTPSConnectionPool(host='ai-novel-wujiejvzhen.tos-cn-beijing.volces.com', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1016)')))"}
2025-07-28 16:51:43,099 - INFO - in-request: sleep 0.2s
