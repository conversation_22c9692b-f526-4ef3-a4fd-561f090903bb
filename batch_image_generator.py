#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量图像生成器 - 基于JSON文件的分镜图像生成和管理
"""

import os
import json
import uuid
import logging
from datetime import datetime
from pathlib import Path
from flask import Flask, request, jsonify, render_template_string
from simple_image_generator import SimpleImageGenerator

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# 创建输出目录
OUTPUT_DIR = Path("batch_generated_images")
OUTPUT_DIR.mkdir(exist_ok=True)

# 全局生成器实例
generator = None

def init_generator():
    """初始化图像生成器"""
    global generator
    try:
        generator = SimpleImageGenerator()
        logger.info("图像生成器初始化成功")
        return True
    except Exception as e:
        logger.error(f"图像生成器初始化失败: {str(e)}")
        return False

# HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量图像生成器 - JSON分镜处理</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            text-align: center;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        textarea, input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 150px;
            resize: vertical;
            font-family: monospace;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .image-item {
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 15px;
            background: #f9f9f9;
        }
        .image-item.generating {
            background: #fff3cd;
            border-color: #ffeaa7;
        }
        .image-item.completed {
            background: #d4edda;
            border-color: #c3e6cb;
        }
        .image-item.error {
            background: #f8d7da;
            border-color: #f5c6cb;
        }
        .image-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .image-number {
            font-weight: bold;
            color: #007bff;
            font-size: 18px;
        }
        .image-status {
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-pending { background: #e9ecef; color: #6c757d; }
        .status-generating { background: #fff3cd; color: #856404; }
        .status-completed { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .prompt-text {
            font-size: 12px;
            color: #666;
            margin-bottom: 10px;
            max-height: 60px;
            overflow-y: auto;
            background: white;
            padding: 8px;
            border-radius: 3px;
        }
        .image-preview {
            width: 100%;
            max-height: 200px;
            object-fit: cover;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .image-controls {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }
        .control-btn {
            padding: 5px 10px;
            font-size: 12px;
            border-radius: 3px;
        }
        .btn-regenerate { background: #ffc107; color: #212529; }
        .btn-confirm { background: #28a745; }
        .btn-edit { background: #17a2b8; }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-fill {
            height: 100%;
            background: #007bff;
            transition: width 0.3s ease;
        }
        .result-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            display: none;
        }
        .json-output {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .global-controls {
            margin: 20px 0;
            text-align: center;
        }
        .edit-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 10px;
            width: 90%;
            max-width: 600px;
            max-height: 80%;
            overflow-y: auto;
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .close-btn {
            background: #dc3545;
            padding: 5px 10px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 批量图像生成器 - JSON分镜处理</h1>
        
        <!-- 上传JSON文件区域 -->
        <div class="upload-section">
            <h3>📁 上传分镜JSON文件</h3>
            <div class="form-group">
                <label for="jsonInput">粘贴JSON内容:</label>
                <textarea id="jsonInput" placeholder="请粘贴storyboards.json文件的内容..."></textarea>
            </div>
            
            <div class="form-group">
                <label for="prefixPrompt">全局提示词前缀:</label>
                <textarea id="prefixPrompt" placeholder="将添加到每个image_prompt前面的内容...">世界观设定：所有人都佩戴着大围脖，所有人都是平胸，平坦的胸部 。
画面风格：女性向漫画，小说配图，平面插画，简洁的笔触，古风，2d 。</textarea>
            </div>
            
            <button onclick="loadJSON()" id="loadBtn">📋 解析JSON文件</button>
        </div>
        
        <!-- 全局控制区域 -->
        <div class="global-controls" id="globalControls" style="display: none;">
            <button onclick="generateAll()" id="generateAllBtn">🚀 开始批量生成</button>
            <button onclick="exportJSON()" id="exportBtn" style="display: none;">📥 导出最终JSON</button>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%;"></div>
            </div>
            <div id="progressText">准备就绪</div>
        </div>
        
        <!-- 图像网格 -->
        <div class="image-grid" id="imageGrid"></div>
        
        <!-- 结果输出区域 -->
        <div class="result-section" id="resultSection">
            <h3>📄 最终JSON结果</h3>
            <button onclick="copyToClipboard()" style="margin-bottom: 10px;">📋 复制到剪贴板</button>
            <div class="json-output" id="jsonOutput"></div>
        </div>
    </div>
    
    <!-- 编辑模态框 -->
    <div class="edit-modal" id="editModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>✏️ 编辑提示词</h3>
                <button class="close-btn" onclick="closeEditModal()">✕</button>
            </div>
            <div class="form-group">
                <label>提示词:</label>
                <textarea id="editPrompt" rows="8"></textarea>
            </div>
            <div style="display: flex; gap: 10px;">
                <div class="form-group" style="flex: 1;">
                    <label>宽度:</label>
                    <input type="number" id="editWidth" value="720">
                </div>
                <div class="form-group" style="flex: 1;">
                    <label>高度:</label>
                    <input type="number" id="editHeight" value="1280">
                </div>
            </div>
            <div style="display: flex; gap: 10px;">
                <div class="form-group" style="flex: 1;">
                    <label>种子:</label>
                    <input type="number" id="editSeed" value="-1">
                </div>
                <div class="form-group" style="flex: 1;">
                    <label>引导比例:</label>
                    <input type="number" id="editScale" value="7.5" step="0.1">
                </div>
            </div>
            <button onclick="regenerateImage()" id="regenerateBtn">🔄 重新生成</button>
        </div>
    </div>

    <script>
        let storyboards = [];
        let currentEditIndex = -1;
        let generationProgress = 0;
        
        function loadJSON() {
            const jsonText = document.getElementById('jsonInput').value.trim();
            const prefix = document.getElementById('prefixPrompt').value.trim();
            
            if (!jsonText) {
                alert('请输入JSON内容');
                return;
            }
            
            try {
                storyboards = JSON.parse(jsonText);
                
                // 为每个分镜添加前缀和状态
                storyboards.forEach((item, index) => {
                    if (item.image_prompt) {
                        item.original_prompt = item.image_prompt;
                        item.image_prompt = prefix + item.image_prompt;
                    }
                    item.status = 'pending';
                    item.index = index;
                    item.new_image_url = null;
                });
                
                renderImageGrid();
                document.getElementById('globalControls').style.display = 'block';
                updateProgress();
                
            } catch (error) {
                alert('JSON格式错误: ' + error.message);
            }
        }
        
        function renderImageGrid() {
            const grid = document.getElementById('imageGrid');
            grid.innerHTML = '';
            
            storyboards.forEach((item, index) => {
                const div = document.createElement('div');
                div.className = `image-item ${item.status}`;
                div.innerHTML = `
                    <div class="image-header">
                        <span class="image-number">镜头 ${index + 1}</span>
                        <span class="image-status status-${item.status}">${getStatusText(item.status)}</span>
                    </div>
                    <div class="prompt-text">${item.image_prompt || '无提示词'}</div>
                    ${item.new_image_url ? `<img src="${item.new_image_url}" class="image-preview" alt="生成的图像">` : ''}
                    <div class="image-controls">
                        <button class="control-btn btn-regenerate" onclick="openEditModal(${index})">✏️ 编辑</button>
                        <button class="control-btn btn-regenerate" onclick="regenerateSingle(${index})">🔄 重生成</button>
                        ${item.new_image_url ? `<button class="control-btn btn-confirm" onclick="confirmImage(${index})">✅ 确认</button>` : ''}
                    </div>
                `;
                grid.appendChild(div);
            });
        }
        
        function getStatusText(status) {
            const statusMap = {
                'pending': '待生成',
                'generating': '生成中',
                'completed': '已完成',
                'confirmed': '已确认',
                'error': '生成失败'
            };
            return statusMap[status] || status;
        }
        
        async function generateAll() {
            const btn = document.getElementById('generateAllBtn');
            btn.disabled = true;
            btn.textContent = '🔄 生成中...';
            
            for (let i = 0; i < storyboards.length; i++) {
                if (storyboards[i].status === 'pending') {
                    await generateSingle(i);
                }
            }
            
            btn.disabled = false;
            btn.textContent = '🚀 开始批量生成';
            document.getElementById('exportBtn').style.display = 'inline-block';
        }
        
        async function generateSingle(index) {
            const item = storyboards[index];
            item.status = 'generating';
            renderImageGrid();
            updateProgress();
            
            try {
                const response = await fetch('/generate_single', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: item.image_prompt,
                        width: 720,
                        height: 1280,
                        seed: -1,
                        scale: 7.5
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    item.new_image_url = data.image_url || `/image/${data.filename}`;
                    item.status = 'completed';
                } else {
                    item.status = 'error';
                    item.error = data.error;
                }
                
            } catch (error) {
                item.status = 'error';
                item.error = error.message;
            }
            
            renderImageGrid();
            updateProgress();
        }
        
        async function regenerateSingle(index) {
            await generateSingle(index);
        }
        
        function confirmImage(index) {
            storyboards[index].status = 'confirmed';
            renderImageGrid();
            updateProgress();
        }
        
        function openEditModal(index) {
            currentEditIndex = index;
            const item = storyboards[index];
            
            document.getElementById('editPrompt').value = item.image_prompt;
            document.getElementById('editWidth').value = 720;
            document.getElementById('editHeight').value = 1280;
            document.getElementById('editSeed').value = -1;
            document.getElementById('editScale').value = 7.5;
            
            document.getElementById('editModal').style.display = 'block';
        }
        
        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
            currentEditIndex = -1;
        }
        
        async function regenerateImage() {
            if (currentEditIndex === -1) return;
            
            const prompt = document.getElementById('editPrompt').value;
            const width = parseInt(document.getElementById('editWidth').value);
            const height = parseInt(document.getElementById('editHeight').value);
            const seed = parseInt(document.getElementById('editSeed').value);
            const scale = parseFloat(document.getElementById('editScale').value);
            
            // 更新提示词
            storyboards[currentEditIndex].image_prompt = prompt;
            
            closeEditModal();
            
            // 重新生成
            const item = storyboards[currentEditIndex];
            item.status = 'generating';
            renderImageGrid();
            
            try {
                const response = await fetch('/generate_single', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        width: width,
                        height: height,
                        seed: seed,
                        scale: scale
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    item.new_image_url = data.image_url || `/image/${data.filename}`;
                    item.status = 'completed';
                } else {
                    item.status = 'error';
                    item.error = data.error;
                }
                
            } catch (error) {
                item.status = 'error';
                item.error = error.message;
            }
            
            renderImageGrid();
            updateProgress();
        }
        
        function updateProgress() {
            const total = storyboards.length;
            const completed = storyboards.filter(item => 
                item.status === 'completed' || item.status === 'confirmed'
            ).length;
            
            const percentage = total > 0 ? (completed / total) * 100 : 0;
            document.getElementById('progressFill').style.width = percentage + '%';
            document.getElementById('progressText').textContent = 
                `进度: ${completed}/${total} (${percentage.toFixed(1)}%)`;
        }
        
        function exportJSON() {
            // 创建最终的JSON，替换image_url
            const finalJSON = storyboards.map(item => {
                const newItem = { ...item };
                
                // 如果有新的图像URL，替换原来的
                if (item.new_image_url) {
                    newItem.image_url = item.new_image_url;
                }
                
                // 清理临时字段
                delete newItem.status;
                delete newItem.index;
                delete newItem.new_image_url;
                delete newItem.error;
                delete newItem.original_prompt;
                
                return newItem;
            });
            
            const jsonString = JSON.stringify(finalJSON, null, 2);
            document.getElementById('jsonOutput').textContent = jsonString;
            document.getElementById('resultSection').style.display = 'block';
        }
        
        function copyToClipboard() {
            const jsonText = document.getElementById('jsonOutput').textContent;
            navigator.clipboard.writeText(jsonText).then(() => {
                alert('JSON已复制到剪贴板！');
            }).catch(err => {
                console.error('复制失败:', err);
                alert('复制失败，请手动选择文本复制');
            });
        }
        
        // 点击模态框外部关闭
        document.getElementById('editModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeEditModal();
            }
        });
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """主页"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/generate_single', methods=['POST'])
def generate_single():
    """生成单个图像API"""
    try:
        if not generator:
            return jsonify({
                'success': False,
                'error': '图像生成器未初始化'
            })
        
        data = request.get_json()
        prompt = data.get('prompt', '').strip()
        
        if not prompt:
            return jsonify({
                'success': False,
                'error': '请输入图像提示词'
            })
        
        width = data.get('width', 720)
        height = data.get('height', 1280)
        seed = data.get('seed', -1)
        scale = data.get('scale', 7.5)
        
        logger.info(f"生成图像: {prompt[:50]}...")
        
        # 生成图像
        response = generator.generate_image_sync(
            prompt=prompt,
            width=width,
            height=height,
            seed=seed,
            scale=scale
        )
        
        # 处理响应
        image_url = generator.get_image_url_from_response(response)
        
        if image_url == "base64_data_available":
            # 保存base64图像
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"batch_{timestamp}_{uuid.uuid4().hex[:8]}.jpg"
            output_path = OUTPUT_DIR / filename
            
            if generator.save_base64_image(response, str(output_path)):
                return jsonify({
                    'success': True,
                    'filename': filename,
                    'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                })
            else:
                return jsonify({
                    'success': False,
                    'error': '保存图像失败'
                })
        elif image_url:
            return jsonify({
                'success': True,
                'image_url': image_url,
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })
        else:
            return jsonify({
                'success': False,
                'error': '未能获取图像'
            })
            
    except Exception as e:
        logger.error(f"生成图像时发生错误: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/image/<filename>')
def serve_image(filename):
    """提供图像文件"""
    try:
        image_path = OUTPUT_DIR / filename
        if image_path.exists():
            from flask import send_file
            return send_file(str(image_path))
        else:
            return "图像不存在", 404
    except Exception as e:
        return f"错误: {str(e)}", 500

@app.route('/health')
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'ok',
        'generator_ready': generator is not None
    })

if __name__ == '__main__':
    # 初始化生成器
    if init_generator():
        print("🚀 启动批量图像生成器...")
        print("📝 访问 http://localhost:8888 使用批量生成器")
        app.run(host='0.0.0.0', port=8888, debug=True)
    else:
        print("❌ 初始化失败，请检查环境变量配置")
