#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的火山引擎图像生成脚本
基于项目中的 generate_volcengine_image 函数
"""

import os
import asyncio
import logging
import base64
from pathlib import Path
from typing import Dict, Any, Optional
from dotenv import load_dotenv
from volcengine.visual.VisualService import VisualService

# 加载环境变量
load_dotenv()

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class SimpleImageGenerator:
    """简单的火山引擎图像生成器"""
    
    def __init__(self):
        """初始化生成器"""
        # 获取API密钥
        self.access_key = os.environ.get("VOLCENGINE_ACCESS_KEY")
        self.secret_key = os.environ.get("VOLCENGINE_SECRET_KEY")
        self.model = os.environ.get("VOLCENGINE_IMAGE_GEN_MODEL", "high_aes_general_v20_L")
        
        if not self.access_key:
            raise ValueError("请设置环境变量 VOLCENGINE_ACCESS_KEY")
        if not self.secret_key:
            raise ValueError("请设置环境变量 VOLCENGINE_SECRET_KEY")
            
        # 解码base64编码的密钥（如果需要）
        try:
            self.access_key = base64.b64decode(self.access_key).decode('utf-8')
            self.secret_key = base64.b64decode(self.secret_key).decode('utf-8')
            logger.info("使用解码后的API密钥")
        except:
            logger.info("使用原始API密钥")
            
        logger.info(f"使用模型: {self.model}")
        logger.info(f"Access Key: {self.access_key[:10]}...")
        
    def generate_image_sync(
        self,
        prompt: str,
        negative_prompt: str = "",
        width: int = 720,
        height: int = 1280,
        seed: int = -1,
        scale: float = 7.5,
        ddim_steps: int = 16,
        use_sr: bool = True,
        use_pre_llm: bool = True,
        return_url: bool = True,
        add_logo: bool = False
    ) -> Dict[str, Any]:
        """
        同步生成图像
        
        Args:
            prompt: 图像提示词
            negative_prompt: 负面提示词
            width: 图像宽度
            height: 图像高度
            seed: 随机种子 (-1为随机)
            scale: 引导比例
            ddim_steps: 推理步数
            use_sr: 是否使用超分辨率
            use_pre_llm: 是否使用预处理LLM
            return_url: 是否返回URL
            add_logo: 是否添加logo
            
        Returns:
            API响应结果
        """
        try:
            # 创建VisualService实例
            visual_service = VisualService()
            visual_service.set_ak(self.access_key)
            visual_service.set_sk(self.secret_key)
            
            # 构建请求参数
            form = {
                "req_key": self.model,
                "prompt": prompt,
                "negative_prompt": negative_prompt,
                "seed": seed,
                "scale": scale,
                "ddim_steps": ddim_steps,
                "width": width,
                "height": height,
                "use_sr": use_sr,
                "use_pre_llm": use_pre_llm,
                "return_url": return_url,
                "logo_info": {"add_logo": add_logo},
            }
            
            logger.info(f"正在生成图像: {prompt[:50]}...")
            logger.info(f"参数: {width}x{height}, seed={seed}, scale={scale}")
            
            # 调用API
            response = visual_service.cv_process(form)
            
            if response.get("code") == 10000:
                logger.info("图像生成成功")
                return response
            else:
                error_msg = response.get("message", "未知错误")
                logger.error(f"图像生成失败: {error_msg}")
                raise Exception(f"API调用失败: {error_msg}")
                
        except Exception as e:
            logger.error(f"生成图像时发生错误: {str(e)}")
            raise
    
    async def generate_image_async(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """异步生成图像"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.generate_image_sync, prompt, **kwargs)
    
    def get_image_url_from_response(self, response: Dict[str, Any]) -> Optional[str]:
        """从响应中提取图像URL"""
        try:
            data = response.get("data", {})
            
            # 尝试获取URL
            if "image_url" in data:
                return data["image_url"]
            elif "image_urls" in data and data["image_urls"]:
                return data["image_urls"][0]
            elif "binary_data_base64" in data and data["binary_data_base64"]:
                # 如果返回base64数据，可以保存为文件
                return "base64_data_available"
            else:
                logger.warning("响应中未找到图像URL")
                return None
                
        except Exception as e:
            logger.error(f"解析响应时发生错误: {str(e)}")
            return None
    
    def save_base64_image(self, response: Dict[str, Any], output_path: str) -> bool:
        """保存base64格式的图像"""
        try:
            data = response.get("data", {})
            base64_data = data.get("binary_data_base64")
            
            if not base64_data:
                logger.error("响应中没有base64图像数据")
                return False
                
            # 解码并保存
            image_data = base64.b64decode(base64_data[0])
            with open(output_path, 'wb') as f:
                f.write(image_data)
                
            logger.info(f"图像已保存到: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存图像时发生错误: {str(e)}")
            return False


def main():
    """主函数 - 命令行使用示例"""
    import argparse
    
    parser = argparse.ArgumentParser(description='火山引擎图像生成工具')
    parser.add_argument('prompt', help='图像提示词')
    parser.add_argument('--negative', default='', help='负面提示词')
    parser.add_argument('--width', type=int, default=720, help='图像宽度')
    parser.add_argument('--height', type=int, default=1280, help='图像高度')
    parser.add_argument('--seed', type=int, default=-1, help='随机种子')
    parser.add_argument('--scale', type=float, default=7.5, help='引导比例')
    parser.add_argument('--steps', type=int, default=16, help='推理步数')
    parser.add_argument('--output', default='generated_image.jpg', help='输出文件路径')
    
    args = parser.parse_args()
    
    try:
        # 创建生成器
        generator = SimpleImageGenerator()
        
        # 生成图像
        response = generator.generate_image_sync(
            prompt=args.prompt,
            negative_prompt=args.negative,
            width=args.width,
            height=args.height,
            seed=args.seed,
            scale=args.scale,
            ddim_steps=args.steps
        )
        
        # 获取图像URL或保存base64数据
        image_url = generator.get_image_url_from_response(response)
        
        if image_url == "base64_data_available":
            # 保存base64图像
            if generator.save_base64_image(response, args.output):
                print(f"✅ 图像已保存到: {args.output}")
            else:
                print("❌ 保存图像失败")
        elif image_url:
            print(f"✅ 图像URL: {image_url}")
        else:
            print("❌ 未能获取图像")
            
    except Exception as e:
        print(f"❌ 错误: {str(e)}")


if __name__ == "__main__":
    main()
