#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同步分镜数据脚本

将novel_step1中的字幕、关键词等数据同步到novel_step2的storyboards.json文件中
"""

import json
import os
import sys
from pathlib import Path

def sync_storyboard_data(step1_file: str, step2_file: str):
    """
    同步分镜数据
    
    Args:
        step1_file: novel_step1中的chapter1_story_boards.json文件路径
        step2_file: novel_step2中的storyboards.json文件路径
    """
    
    print(f"🔄 开始同步分镜数据")
    print(f"📖 源文件: {step1_file}")
    print(f"📝 目标文件: {step2_file}")
    
    # 检查文件是否存在
    if not os.path.exists(step1_file):
        print(f"❌ 源文件不存在: {step1_file}")
        return False
    
    if not os.path.exists(step2_file):
        print(f"❌ 目标文件不存在: {step2_file}")
        return False
    
    try:
        # 读取源文件 (step1)
        print("📖 读取源文件...")
        with open(step1_file, 'r', encoding='utf-8') as f:
            step1_data = json.load(f)
        
        # 读取目标文件 (step2)
        print("📖 读取目标文件...")
        with open(step2_file, 'r', encoding='utf-8') as f:
            step2_data = json.load(f)
        
        # 获取step1中的分镜数据
        step1_story_boards = step1_data.get('story_boards', [])
        print(f"📊 源文件包含 {len(step1_story_boards)} 个分镜")
        
        # 创建step1分镜数据的映射 (story_board_id -> 分镜数据)
        step1_mapping = {}
        for board in step1_story_boards:
            story_board_id = board.get('story_board_id')
            if story_board_id:
                step1_mapping[story_board_id] = board
        
        print(f"📊 目标文件包含 {len(step2_data)} 个分镜")
        
        # 同步数据
        updated_count = 0
        for i, step2_board in enumerate(step2_data):
            story_board_id = step2_board.get('story_board_id')
            
            if story_board_id and story_board_id in step1_mapping:
                step1_board = step1_mapping[story_board_id]
                
                print(f"🔄 同步分镜 {story_board_id}...")
                
                # 1. 替换 subtitle 为 short_subtitles
                if 'short_subtitles' in step1_board:
                    step2_board['subtitle'] = step1_board['short_subtitles']
                    print(f"   ✅ 更新字幕数据: {len(step1_board['short_subtitles'])} 个字幕")
                
                # 2. 复制 keywords
                if 'keywords' in step1_board:
                    step2_board['keywords'] = step1_board['keywords']
                    print(f"   ✅ 更新关键词: {len(step1_board['keywords'])} 个关键词")
                
                # 3. 复制 story_board
                if 'story_board' in step1_board:
                    step2_board['story_board'] = step1_board['story_board']
                    print(f"   ✅ 更新分镜描述: {len(step1_board['story_board'])} 字符")
                
                # 4. 复制 subtitle_count
                if 'subtitle_count' in step1_board:
                    step2_board['subtitle_count'] = step1_board['subtitle_count']
                    print(f"   ✅ 更新字幕数量: {step1_board['subtitle_count']}")
                
                # 5. 替换 audio_url 为 audio_oss_url
                if 'audio_oss_url' in step1_board:
                    step2_board['audio_url'] = step1_board['audio_oss_url']
                    print(f"   ✅ 更新音频URL")
                
                # 6. 复制 estimated_duration
                if 'estimated_duration' in step1_board:
                    step2_board['estimated_duration'] = step1_board['estimated_duration']
                    print(f"   ✅ 更新预估时长: {step1_board['estimated_duration']:.2f}秒")
                
                if 'audio_duration' in step1_board:
                    step2_board['audio_duration'] = step1_board['audio_duration']
                updated_count += 1
            else:
                print(f"⚠️ 分镜 {story_board_id} 在源文件中未找到对应数据")
        
        # 备份原文件
        backup_file = step2_file + '.backup'
        print(f"💾 备份原文件到: {backup_file}")
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(step2_data, f, ensure_ascii=False, indent=2)
        
        # 保存更新后的文件
        print(f"💾 保存更新后的文件...")
        with open(step2_file, 'w', encoding='utf-8') as f:
            json.dump(step2_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 同步完成！")
        print(f"📊 总共更新了 {updated_count} 个分镜")
        print(f"💾 原文件已备份到: {backup_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 同步过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    
    print("🚀 分镜数据同步脚本")
    print("=" * 60)
    
    # 定义文件路径
    step1_file = "output/novel_step1/大唐太子：开局硬刚李世民/chapter1_还请陛下，称我为太子！/chapter1_story_boards.json"
    step2_file = "output/novel_step2/大唐太子：开局硬刚李世民/chapter1_还请陛下，称我为太子！/storyboards.json"
    
    # 执行同步
    success = sync_storyboard_data(step1_file, step2_file)
    
    if success:
        print("\n🎉 数据同步成功完成！")
        print("\n📋 同步的数据项:")
        print("   • short_subtitles → subtitle")
        print("   • keywords → keywords")
        print("   • story_board → story_board")
        print("   • subtitle_count → subtitle_count")
        print("   • audio_oss_url → audio_url")
        print("   • estimated_duration → estimated_duration")
    else:
        print("\n❌ 数据同步失败，请检查错误信息")
        sys.exit(1)


if __name__ == "__main__":
    main()
